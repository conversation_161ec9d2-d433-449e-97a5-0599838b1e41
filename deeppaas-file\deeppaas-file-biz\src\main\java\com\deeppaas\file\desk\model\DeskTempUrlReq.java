package com.deeppaas.file.desk.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/21
 */
@Data
public class DeskTempUrlReq {
    public static final String KEY_FILE_ALIAS = "fileAlias";
    public static final String KEY_PROCESS = "process";
    public static final String KEY_EXPIRE_TIME = "expireTime";
    public static final String KEY_SIGN = "sign";

    private String fileGroup;
    private String fileKey;
    /** 文件别名 */
    private String fileAlias;
    /** 文件处理(如图片压缩) */
    private String process;
    /** 过期时间戳 */
    private Long expireTime;
    /**
     * 签名
     */
    private String sign;

    public DeskTempUrlReq(String fileGroup, String fileKey, String fileAlias, String process, long expireTime, String sign) {
        this.fileGroup = fileGroup;
        this.fileKey = fileKey;
        this.fileAlias = fileAlias;
        this.process = process;
        this.expireTime = expireTime;
        this.sign = sign;
    }
}
