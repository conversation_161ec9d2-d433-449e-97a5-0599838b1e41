package com.deeppaas.rule.biz.action;

import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;

/**
 * 子规则动作
 * <AUTHOR>
 * @date 2022/4/28
 */
@Data
public class RRuleAction extends RAction{
    /**
     * 规则ID
     */
    private String ruleId;
    /**
     * 注入子规则执行的数据
     */
    private RDataBind param;

    @Override
    public Object execute(RuleContext ruleContext) {
        RuleContext childContext = ruleContext.getRuleEngine().initContextById(ruleId);
        Object data = param.getBindData(ruleContext);
        childContext.setParam(data);
        Object childResult = childContext.getFirstAction().execute(childContext);
        return super.returnOrNext(childResult, ruleContext);
    }
}
