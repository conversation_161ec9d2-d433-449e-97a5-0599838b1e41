package com.deeppaas.common.web.handler;

import com.deeppaas.common.helper.StringHelper;

import javax.servlet.http.HttpServletRequest;

/**
 * 前端大佬需要的
 *
 * <AUTHOR>
 * @date 2021-01-27
 */
public class WebpImageHandler {

    private static final String WEBP_KEY = ",image/webp,";
    private static final String ACCEPT_HEADER_KEY = "accept";

    public static boolean isWebpAccept(HttpServletRequest request) {
        String accept = request.getHeader(ACCEPT_HEADER_KEY);
        return StringHelper.isNotEmpty(accept) && accept.contains(WEBP_KEY);
    }
}
