package com.deeppaas.common.helper;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description web相关工具
 * <AUTHOR>
 * @Date 2019-03-28
 */
public class WebHelper {
    /** 客户端-PC浏览器 */
    public static final String CLIENT_WEB = "WEB";
    /** 客户端-移动浏览器 */
    public static final String CLIENT_WAP = "WAP";
    /** 客户端-微信浏览器 */
    public static final String CLIENT_WX = "WX";

    /** 移动端agent集合 */
    private static final List<String> mobileAgents = Arrays.asList("ipad", "iphone os", "rv:*******", "ucweb", "android", "windows ce", "windows mobile");
    /** 微信agent */
    private static final String wechatAgent = "micromessenger";

    private static final String HEADER_X_REQ = "X-Requested-With";
    private static final String IDX_X_REQ = "XMLHttpRequest";

    /**
     * @Description: 获取请求客户端类型
     * <AUTHOR>
     * @date 2019-03-28
     */
    public static String getClient(HttpServletRequest request) {
        String ua = request.getHeader("User-Agent").toLowerCase();
        if(ua.indexOf(wechatAgent) > -1){
            return CLIENT_WX;
        }
        for (String sua : mobileAgents) {
            if (ua.indexOf(sua) > -1) {
                return CLIENT_WAP;
            }
        }
        return CLIENT_WEB;
    }

    public static boolean isAjax(HttpServletRequest request) {
        return IDX_X_REQ.equals(request.getHeader(HEADER_X_REQ));
    }

    public static boolean fromWechat(HttpServletRequest request) {
        return CLIENT_WX.equals(getClient(request));
    }

    public static String getUrlWithoutWechatCode(HttpServletRequest request) {
        String url = getUrl(request);
        String[] urlStr = StringHelper.split(url, "\\?");
        if (urlStr.length > 1) {
            String paramStr = urlStr[1];
            String[] params = StringHelper.split(paramStr, "&");
            List<String> paramList = new ArrayList<>();
            for (int i = 0; i < params.length; i++) {
                if (!params[i].startsWith("code=")) {
                    paramList.add(params[i]);
                }
            }
            paramStr = StringHelper.join(paramList, "&");
            url = StringHelper.isEmpty(paramStr) ? urlStr[0] : urlStr[0] + "?" + paramStr;
        }
        return url;
    }

    public static String getUrl(HttpServletRequest request) {
        String originalUrl = request.getRequestURI();
        if (StringHelper.isNotEmpty(request.getQueryString())) {
            originalUrl += ("?" + request.getQueryString());
        }
        try {
            return URLDecoder.decode(originalUrl, "utf-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}
