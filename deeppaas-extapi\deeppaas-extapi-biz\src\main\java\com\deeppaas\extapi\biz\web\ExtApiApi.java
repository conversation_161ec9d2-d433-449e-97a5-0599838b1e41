package com.deeppaas.extapi.biz.web;

import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.model.*;
import com.deeppaas.extapi.biz.service.ExtApiService;
import com.deeppaas.rule.api.model.RulePO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 接口管理API
 *
 * <AUTHOR>
 * @date 2022/2/25
 */
@RequestAuth(auth = false)
@RestController
@RequestMapping("/api/extApi")
public class ExtApiApi {
    private final ExtApiService apiService;

    public ExtApiApi(ExtApiService apiService) {
        this.apiService = apiService;
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public RestModel<ExtApiVO> get(@RequestParam String id) {
        ExtApiVO apiVO = apiService.getVO(id);
        return RestModel.data(apiVO);
    }

    /**
     * 批量查询
     */
    @GetMapping("/list")
    public RestModel<List<ExtApiSimpleVO>> listByIds(@RequestParam String[] ids) {
        List<ExtApiSimpleVO> voList = apiService.listByIds(Set.of(ids));
        return RestModel.data(voList);
    }

    /**
     * 分页查询
     */
    @GetMapping("/page")
    public RestModel<PageData<ExtApiVO>> page(PageSearch param) {
        PageData<ExtApiVO> apiVOPageData = apiService.page(param);
        return RestModel.data(apiVOPageData);
    }

    /**
     * 新增&修改
     *
     * @param param 参数
     * @return API主键ID
     */
    @PostMapping("/save")
    public RestModel<String> save(@RequestBody ExtApiParam param) {
        // 业务操作
        ExtApiDO apiDO = apiService.save(param);
        return RestModel.data(apiDO.getId());
    }

    /**
     * 删除
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    public RestModel<String> delete(@BodyParam String id) {
        apiService.delete(id);
        return RestModel.data("操作成功");
    }

    /**
     * 启用/禁用
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/usable")
    public RestModel<String> usable(@BodyParam String id, @BodyParam Integer usable) {
        apiService.usable(id, usable);
        return RestModel.data("操作成功");
    }

    /**
     * 根据模型生成API参数
     *
     * @param param 参数
     * @return 操作结果
     */
    @PostMapping("/param/convert")
    public RestModel<List<ExtApiParamDO>> convertParam(@RequestBody ExtApiParamConvert param) {
        // 业务操作
        apiService.convertParam(param);
        // 查询返回
        List<ExtApiParamDO> paramDOList = apiService.listParam(param.getId());
        return RestModel.data(paramDOList);
    }

    /**
     * 新增&修改接口参数
     *
     * @param param 参数
     * @return 操作结果
     */
    @PostMapping("/param/save")
    public RestModel<String> saveParam(@RequestBody ExtApiParamDO param) {
        // 业务操作
        apiService.saveParam(param);
        return RestModel.data("操作成功");
    }

    /**
     * 删除接口参数
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/param/delete")
    public RestModel<String> deleteParam(@BodyParam String id) {
        apiService.deleteParam(id);
        return RestModel.data("操作成功");
    }

    /**
     * 接口参数列表
     */
    @GetMapping("/param/list")
    public RestModel<List<ExtApiParamDO>> listParam(@RequestParam String apiId) {
        List<ExtApiParamDO> apiParamDOList = apiService.listParam(apiId);
        return RestModel.data(apiParamDOList);
    }

    /**
     * 获取接口规则信息
     */
    @GetMapping("/rule/get")
    public RestModel<RulePO> getApiRule(@RequestParam String apiId) {
        RulePO apiRuleDO = apiService.getApiRule(apiId);
        return RestModel.data(apiRuleDO);
    }

    /**
     * 新增&修改接口规则
     *
     * @param param API规则参数
     * @return 规则信息
     */
    @PostMapping("/rule/save")
    public RestModel<RulePO> saveRule(@RequestBody ExtApiRuleParam param) {
        // 业务操作
        RulePO ruleDO = apiService.saveRule(param);
        return RestModel.data(ruleDO);
    }
}
