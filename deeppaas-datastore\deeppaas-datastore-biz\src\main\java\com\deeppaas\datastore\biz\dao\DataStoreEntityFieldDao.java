package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityFieldDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
public interface DataStoreEntityFieldDao extends BaseJdbcDao<DataStoreEntityFieldDO, String> {

    List<DataStoreEntityFieldDO> findByCode(String code);

    boolean existsByEntityCodeAndCode(String entityCode, String code);

    boolean existsByEntityCodeAndName(String entityCode, String name);

    DataStoreEntityFieldDO findByEntityCodeAndCode(String entityCode, String code);

    List<DataStoreEntityFieldDO> findByEntityCodeOrderByIdAsc(String entityCode);

    List<DataStoreEntityFieldDO> findByEntityCodeAndJoinCodeOrderByIdAsc(String entityCode, String joinCode);

    @Modifying
    @Query("delete from datastore_entity_field where entity_code=:entityCode and join_code=:joinCode")
    void deleteByEntityCodeAndJoinCode(@Param("entityCode") String entityCode, @Param("joinCode") String joinCode);

    @Modifying
    @Query("delete from datastore_entity_field where entity_code=:entityCode")
    void deleteByEntityCode(@Param("entityCode") String entityCode);

    List<DataStoreEntityFieldDO> findByEntityCodeInAndColumnNameAndJoinCodeIsNull(List<String> entityCodes, String columnName);
}
