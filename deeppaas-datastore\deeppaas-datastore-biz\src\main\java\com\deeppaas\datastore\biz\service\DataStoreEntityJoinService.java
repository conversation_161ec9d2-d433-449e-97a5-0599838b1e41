package com.deeppaas.datastore.biz.service;

import com.deeppaas.datastore.biz.dao.DataStoreEntityFieldDao;
import com.deeppaas.datastore.biz.dao.DataStoreEntityJoinDao;
import com.deeppaas.datastore.biz.dao.DataStoreEntityJoinMatchDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityJoinDO;
import com.deeppaas.datastore.biz.entity.DataStoreEntityJoinMatchDO;
import com.deeppaas.datastore.common.DataStoreRepositoryCache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@Service
public class DataStoreEntityJoinService {
    private final DataStoreEntityJoinDao dataStoreEntityJoinDao;
    private final DataStoreEntityJoinMatchDao dataStoreEntityJoinMatchDao;
    private final DataStoreEntityFieldDao dataStoreEntityFieldDao;
    private final DataStoreRepositoryCache dataStoreRepositoryCache;

    public DataStoreEntityJoinService(DataStoreEntityJoinDao dataStoreEntityJoinDao, DataStoreEntityJoinMatchDao dataStoreEntityJoinMatchDao, DataStoreEntityFieldDao dataStoreEntityFieldDao, DataStoreRepositoryCache dataStoreRepositoryCache) {
        this.dataStoreEntityJoinDao = dataStoreEntityJoinDao;
        this.dataStoreEntityJoinMatchDao = dataStoreEntityJoinMatchDao;
        this.dataStoreEntityFieldDao = dataStoreEntityFieldDao;
        this.dataStoreRepositoryCache = dataStoreRepositoryCache;
    }

    public DataStoreEntityJoinDO get(String id) {
        return dataStoreEntityJoinDao.getById(id);
    }

    public DataStoreEntityJoinDO findByCode(String entityCode, String joinCode) {
        return dataStoreEntityJoinDao.findByEntityCodeAndCode(entityCode, joinCode);
    }

    public List<DataStoreEntityJoinDO> findByEntityCode(String entityCode) {
        return dataStoreEntityJoinDao.findByEntityCodeOrderByIdAsc(entityCode);
    }

    public List<DataStoreEntityJoinMatchDO> findMatchByEntityCode(String entityCode) {
        return dataStoreEntityJoinMatchDao.findByEntityCodeOrderByCreateTimeAsc(entityCode);
    }

    public List<DataStoreEntityJoinMatchDO> findMatchByJoinCode(String entityCode, String joinCode) {
        return dataStoreEntityJoinMatchDao.findByEntityCodeAndJoinCodeOrderByCreateTimeAsc(entityCode, joinCode);
    }

    @Transactional
    public DataStoreEntityJoinDO addJoin(DataStoreEntityJoinDO param) {
        DataStoreEntityJoinDO result = dataStoreEntityJoinDao.save(param);
        dataStoreRepositoryCache.remove(param.getEntityCode());
        return result;
    }

    @Transactional
    public void deleteJoin(String entityCode, String code) {
        // 获取子关联列表
        List<DataStoreEntityJoinDO> joinDOList = dataStoreEntityJoinDao.findByEntityCodeAndParentJoinCodeOrderByIdAsc(entityCode, code);
        for (DataStoreEntityJoinDO joinDO : joinDOList) {
            // 递归删除
            deleteJoin(joinDO.getEntityCode(), joinDO.getCode());
        }
        // 删除匹配关系
        dataStoreEntityFieldDao.deleteByEntityCodeAndJoinCode(entityCode, code);
        // 删除匹配关系
        dataStoreEntityJoinMatchDao.deleteByEntityCodeAndJoinCode(entityCode, code);
        // 删除模型连接
        dataStoreEntityJoinDao.deleteByEntityCodeAndJoinCode(entityCode, code);
        dataStoreRepositoryCache.remove(entityCode);
    }

    public DataStoreEntityJoinMatchDO getMatch(String id) {
        return dataStoreEntityJoinMatchDao.getById(id);
    }

    @Transactional
    public DataStoreEntityJoinMatchDO addJoinMatch(DataStoreEntityJoinMatchDO param) {
        DataStoreEntityJoinMatchDO result = dataStoreEntityJoinMatchDao.save(param);
        dataStoreRepositoryCache.remove(param.getEntityCode());
        return result;
    }

    @Transactional
    public void deleteJoinMatch(String id) {
        // 删除匹配关系
        DataStoreEntityJoinMatchDO matchDO = dataStoreEntityJoinMatchDao.getById(id);
        if(matchDO!=null){
            dataStoreEntityJoinMatchDao.deleteById(id);
            dataStoreRepositoryCache.remove(matchDO.getEntityCode());
        }
    }

    public List<DataStoreEntityJoinDO> findByEntityCodeAndParentJoinCode(String entityCode, String parentJoinCode) {
        return dataStoreEntityJoinDao.findByEntityCodeAndParentJoinCodeOrderByIdAsc(entityCode, parentJoinCode);
    }
}
