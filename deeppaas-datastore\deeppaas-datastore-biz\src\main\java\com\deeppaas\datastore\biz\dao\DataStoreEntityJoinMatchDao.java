package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityJoinMatchDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
public interface DataStoreEntityJoinMatchDao extends BaseJdbcDao<DataStoreEntityJoinMatchDO, String> {

    List<DataStoreEntityJoinMatchDO> findByEntityCodeOrderByCreateTimeAsc(String entityCode);

    List<DataStoreEntityJoinMatchDO> findByEntityCodeAndJoinCodeOrderByCreateTimeAsc(String entityCode, String joinCode);

    @Modifying
    @Query("delete from datastore_entity_join_match where entity_code=:entityCode and join_code = :joinCode")
    void deleteByEntityCodeAndJoinCode(@Param("entityCode") String entityCode, @Param("joinCode") String joinCode);
}
