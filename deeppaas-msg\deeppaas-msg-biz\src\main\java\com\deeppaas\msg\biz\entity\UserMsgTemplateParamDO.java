package com.deeppaas.msg.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 消息模版参数
 * <AUTHOR>
 * @date 2022/9/3
 */
@Data
@Table("deeppaas_user_msg_template_param")
public class UserMsgTemplateParamDO extends BaseUuidDO {
    /**
     * 所属模版
     */
    @Column("template_id")
    private String templateId;
    /**
     * 参数名称
     */
    @Column("name")
    private String name;

    /**
     * 参数备注
     */
    @Column("remark")
    private String remark;
}
