package com.deeppaas.datastore.api.client;

import com.deeppaas.common.data.DataClass;
import com.deeppaas.datastore.api.client.model.DataStoreEntityCO;
import com.deeppaas.datastore.api.client.model.DataStoreEntityFieldCO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 数据模型客户端
 *
 * <AUTHOR>
 * @date 2022/4/20
 */
public interface DataStoreEntityClient {

    DataStoreEntityCO findByCode(String entityCode);

    List<DataStoreEntityCO> findByCodes(Collection<String> entityCodes);

    Map<String, DataClass> getFieldClassMap(String entityCode);

    List<DataStoreEntityFieldCO> listEntityField(String entityCode);

    /**
     * 删除应用下所有模型
     *
     * @param appCode 应用编号
     */
    void deleteByApp(String appCode);
}
