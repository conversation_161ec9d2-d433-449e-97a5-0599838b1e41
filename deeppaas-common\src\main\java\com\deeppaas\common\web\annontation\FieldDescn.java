package com.deeppaas.common.web.annontation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段中文名称注解（暂未使用）
 * <p>
 * 在实体字段上使用-用于记录实体数据的更新日志
 *
 * <AUTHOR>
 * @date 2020-07-03
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldDescn {

    String cname() default "";
}
