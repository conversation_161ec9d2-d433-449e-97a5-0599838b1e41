package com.deeppaas.common.data.converter;

import com.deeppaas.common.helper.JsonHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.WritingConverter;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@WritingConverter
public class ArrayToJsonConverter implements Converter<Object[], String> {

    @Override
    public String convert(Object[] source) {
        if(source==null){
            return "[]";
        }
        try {
            return JsonHelper.writeValueAsString(source);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "[]";
    }
}
