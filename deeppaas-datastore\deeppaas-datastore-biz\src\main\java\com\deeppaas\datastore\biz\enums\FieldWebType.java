package com.deeppaas.datastore.biz.enums;

/**
 * <AUTHOR>
 * @date 2022/2/22
 */
public enum FieldWebType {
    Page,   // 页面
    Container,   // 容器
    Collapse,   // 折叠器
    Divider,   // 分割线
    Flex,   // 布局
    Grid,   // 水平分栏
    Grid_2D,   // 布局
    HBox,   // 布局
    PaginationWrapper,   // 分页容器
    Panel,   // 面板
    Tabs,   // 选项卡
    Wrapper,   // 包裹容器
    Portlet,   // 门户栏目
    // 功能
    Action,   // 行为按钮
    App,   // 多页应用
    Button,   // 按钮
    ButtonGroup,   // 按钮组
    Breadcrumb,   // 面包屑
    Custom,   // 自定义组件
    DropDownButton,   // 下拉按钮
    Service,   // 功能型容器
    Nav,   // 导航
    AnchorNav,   // 锚点导航
    // 数据输入
    Form,   // 表单
    FormItem,   // 表单项通用
    Options,   // 选择器表单项
    InputArray,   // 数组输入框
    ButtonToolbar,   // 按钮工具栏
    ButtonGroupSelect,   // 按钮点选
    ChainedSelect,   // 链式下拉框
    Checkbox,   // 勾选框
    Checkboxes,   // 复选框
    InputCity,   // 城市选择器
    InputColor,   // 颜色选择器
    Combo,   // 组合
    ConditionBuilder,   // 条件组合
    InputDate,   // 日期选择器
    InputDatetime,   // 日期时间选择器
    InputMonth,   // 月份选择器
    InputDateRange,   // 日期范围选择器
    InputDatetimeRange,   // 日期时间范围
    InputMonthRange,   // 月份范围
    InputKV,   // 键值对
    InputFormula,   // 公式编辑器
    DiffEditor,   // 对比编辑器
    Editor,   // 代码编辑器
    FieldSet,   // 表单项集合
    InputExcel,   // Excel 解析
    InputFile,   // 文件上传
    Formula,   // 公式
    Group,   // 表单项组
    Hidden,   // 隐藏字段
    InputImage,   // 图片
    InputGroup,   // 输入框组合
    ListSelect,   // 选择器
    LocationPicker,   // 地理位置
    UUID,   // 随机 ID
    MatrixCheckboxes,   // 矩阵勾选
    NestedSelect,   // 级联选择器
    InputNumber,   // 数字输入
    InputPassword,   // 密码输入框
    Picker,   // 列表选择器
    Radios,   // 单选框
    Chart,   // 单选框
    InputRating,   // 评分
    InputRange,   // 滑块
    InputRichText,   // 富文本编辑器
    Select,   // 下拉框
    InputSubForm,   // 子表单
    Switch,   // 开关
    Static,   // 静态展示
    InputTable,   // 表格
    InputTag,   // 标签选择器
    InputText,   // 输入框
    Textarea,   // 多行输入框
    InputTime,   // 时间
    InputTimeRange,   // 时间
    Transfer,   // 穿梭器
    TransferPicker,   // 穿梭选择器
    TabsTransfer,   // 组合穿梭器
    TabsTransferPicker,   // 组合穿梭选择器
    InputTree,   // 树形选择框
    TreeSelect,   // 树形选择器
    InputYear,   // 年输入
    InputYearRange,   // 年份范围
    // 数据展示
    CRUD,   // 增删改查
    Table,   // 表格
    TableView,   // 表格视图
    Calendar,   // 日历日程
    Card,   // 卡片
    Cards,   // 卡片组
    Carousel,   // 轮播图
    //Chart,   // 图表
    Code,   // 代码高亮
    Color,   // 颜色
    Date,   // 日期时间
    Icon,   // 图标
    iFrame,   //
    Image,   // 图片
    Images,   // 图片集
    GridNav,   // 宫格导航
    Progress,   // 进度条
    Steps,   // 步骤条
    Property,   // 属性表
    QRCode,   // 二维码
    BarCode,   // 条形码
    Remark,   // 标记
    SearchBox,   // 搜索框
    Sparkline,   // 走势图
    Status,   // 状态
    Video,   // 视频
    Timeline,   // 时间轴
    // 反馈
    Alert,   // 提示
    Dialog,   // 对话框
    Drawer,   // 抽屉
    Spinner,   // 加载中
    //其他
    Avatar,   // 头像
    Tasks,   // 任务操作集合
    Badge,   // 角标
    Wizard,   // 向导


}
