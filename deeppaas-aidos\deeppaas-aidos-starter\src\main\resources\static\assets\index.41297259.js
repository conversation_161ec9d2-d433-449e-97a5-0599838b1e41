import{r as n,D as Ht,E as $,F as ne,G as An,H as Bt,I as he,f as O,b as j,e as P,J as ut,K as dt,L as vt,M as ft,t as Ut,_ as B,P as $e,Q as zn,c as Fn,C as ke,d as Lt}from"./index.689bcdcb.js";import{s as Gt,o as De}from"./index.71d34973.js";import{u as Be,i as qe,c as pt,a as Vn}from"./reactNode.50ea6579.js";import{F as Te}from"./Overflow.1478d911.js";import{K as de,R as kt}from"./RightOutlined.ed31d8e3.js";import{a as jn,i as Wn,T as Hn}from"./index.1f7b2316.js";import{L as Dt}from"./LeftOutlined.6bce5536.js";var Bn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},Un=Bn,qt=function(t,a){return n.exports.createElement(Ht,$($({},t),{},{ref:a,icon:Un}))};qt.displayName="EllipsisOutlined";var Gn=n.exports.forwardRef(qt),qn=["children","locked"],re=n.exports.createContext(null);function Yn(e,t){var a=$({},e);return Object.keys(t).forEach(function(r){var o=t[r];o!==void 0&&(a[r]=o)}),a}function Le(e){var t=e.children,a=e.locked,r=ne(e,qn),o=n.exports.useContext(re),l=An(function(){return Yn(o,r)},[o,r],function(i,s){return!a&&(i[0]!==s[0]||!Gt(i[1],s[1]))});return n.exports.createElement(re.Provider,{value:l},t)}function Yt(e,t,a,r){var o=n.exports.useContext(re),l=o.activeKey,i=o.onActive,s=o.onInactive,c={active:l===e};return t||(c.onMouseEnter=function(u){a==null||a({key:e,domEvent:u}),i(e)},c.onMouseLeave=function(u){r==null||r({key:e,domEvent:u}),s(e)}),c}var Jn=["item"];function Ye(e){var t=e.item,a=ne(e,Jn);return Object.defineProperty(a,"item",{get:function(){return Bt(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),a}function Jt(e){var t=e.icon,a=e.props,r=e.children,o;return typeof t=="function"?o=n.exports.createElement(t,$({},a)):o=t,o||r||null}function Xt(e){var t=n.exports.useContext(re),a=t.mode,r=t.rtl,o=t.inlineIndent;if(a!=="inline")return null;var l=e;return r?{paddingRight:l*o}:{paddingLeft:l*o}}var Xn=[],Zt=n.exports.createContext(null);function Xe(){return n.exports.useContext(Zt)}var Qt=n.exports.createContext(Xn);function Ne(e){var t=n.exports.useContext(Qt);return n.exports.useMemo(function(){return e!==void 0?[].concat(he(t),[e]):t},[t,e])}var en=n.exports.createContext(null),tn=n.exports.createContext(null);function nn(e,t){return e===void 0?null:"".concat(e,"-").concat(t)}function rn(e){var t=n.exports.useContext(tn);return nn(t,e)}var mt=n.exports.createContext({}),Zn=["title","attribute","elementRef"],Qn=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],er=["active"],tr=function(e){ut(a,e);var t=dt(a);function a(){return vt(this,a),t.apply(this,arguments)}return ft(a,[{key:"render",value:function(){var o=this.props,l=o.title,i=o.attribute,s=o.elementRef,c=ne(o,Zn),u=De(c,["eventKey"]);return Bt(!i,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),n.exports.createElement(Te.Item,O({},i,{title:typeof l=="string"?l:void 0},u,{ref:s}))}}]),a}(n.exports.Component),nr=function(t){var a,r=t.style,o=t.className,l=t.eventKey;t.warnKey;var i=t.disabled,s=t.itemIcon,c=t.children,u=t.role,d=t.onMouseEnter,h=t.onMouseLeave,f=t.onClick,I=t.onKeyDown,p=t.onFocus,g=ne(t,Qn),M=rn(l),y=n.exports.useContext(re),C=y.prefixCls,m=y.onItemClick,x=y.disabled,b=y.overflowDisabled,v=y.itemIcon,T=y.selectedKeys,S=y.onActive,L=n.exports.useContext(mt),U=L._internalRenderMenuItem,k="".concat(C,"-item"),W=n.exports.useRef(),H=n.exports.useRef(),G=x||i,V=Ne(l),A=function(D){return{key:l,keyPath:he(V).reverse(),item:W.current,domEvent:D}},_=s||v,X=Yt(l,G,d,h),le=X.active,se=ne(X,er),te=T.includes(l),ce=Xt(V.length),ae=function(D){if(!G){var z=A(D);f==null||f(Ye(z)),m(z)}},q=function(D){if(I==null||I(D),D.which===de.ENTER){var z=A(D);f==null||f(Ye(z)),m(z)}},J=function(D){S(l),p==null||p(D)},R={};t.role==="option"&&(R["aria-selected"]=te);var N=n.exports.createElement(tr,O({ref:W,elementRef:H,role:u===null?"none":u||"menuitem",tabIndex:i?null:-1,"data-menu-id":b&&M?null:M},g,se,R,{component:"li","aria-disabled":i,style:$($({},ce),r),className:j(k,(a={},P(a,"".concat(k,"-active"),le),P(a,"".concat(k,"-selected"),te),P(a,"".concat(k,"-disabled"),G),a),o),onClick:ae,onKeyDown:q,onFocus:J}),c,n.exports.createElement(Jt,{props:$($({},t),{},{isSelected:te}),icon:_}));return U&&(N=U(N,t,{selected:te})),N};function xt(e){var t=e.eventKey,a=Xe(),r=Ne(t);return n.exports.useEffect(function(){if(a)return a.registerPath(t,r),function(){a.unregisterPath(t,r)}},[r]),a?null:n.exports.createElement(nr,e)}function ht(e,t){return Ut(e).map(function(a,r){if(n.exports.isValidElement(a)){var o,l,i=a.key,s=(o=(l=a.props)===null||l===void 0?void 0:l.eventKey)!==null&&o!==void 0?o:i,c=s==null;c&&(s="tmp_key-".concat([].concat(he(t),[r]).join("-")));var u={key:s,eventKey:s};return n.exports.cloneElement(a,u)}return a})}function Pe(e){var t=n.exports.useRef(e);t.current=e;var a=n.exports.useCallback(function(){for(var r,o=arguments.length,l=new Array(o),i=0;i<o;i++)l[i]=arguments[i];return(r=t.current)===null||r===void 0?void 0:r.call.apply(r,[t].concat(l))},[]);return e?a:void 0}var rr=["className","children"],ar=function(t,a){var r=t.className,o=t.children,l=ne(t,rr),i=n.exports.useContext(re),s=i.prefixCls,c=i.mode,u=i.rtl;return n.exports.createElement("ul",O({className:j(s,u&&"".concat(s,"-rtl"),"".concat(s,"-sub"),"".concat(s,"-").concat(c==="inline"?"inline":"vertical"),r)},l,{"data-menu-list":!0,ref:a}),o)},gt=n.exports.forwardRef(ar);gt.displayName="SubMenuList";var xe={adjustX:1,adjustY:1},or={topLeft:{points:["bl","tl"],overflow:xe,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:xe,offset:[0,7]},leftTop:{points:["tr","tl"],overflow:xe,offset:[-4,0]},rightTop:{points:["tl","tr"],overflow:xe,offset:[4,0]}},ir={topLeft:{points:["bl","tl"],overflow:xe,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:xe,offset:[0,7]},rightTop:{points:["tr","tl"],overflow:xe,offset:[-4,0]},leftTop:{points:["tl","tr"],overflow:xe,offset:[4,0]}};function an(e,t,a){if(t)return t;if(a)return a[e]||a.other}var lr={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function sr(e){var t=e.prefixCls,a=e.visible,r=e.children,o=e.popup,l=e.popupClassName,i=e.popupOffset,s=e.disabled,c=e.mode,u=e.onVisibleChange,d=n.exports.useContext(re),h=d.getPopupContainer,f=d.rtl,I=d.subMenuOpenDelay,p=d.subMenuCloseDelay,g=d.builtinPlacements,M=d.triggerSubMenuAction,y=d.forceSubMenuRender,C=d.motion,m=d.defaultMotions,x=n.exports.useState(!1),b=B(x,2),v=b[0],T=b[1],S=f?$($({},ir),g):$($({},or),g),L=lr[c],U=an(c,C,m),k=$($({},U),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),W=n.exports.useRef();return n.exports.useEffect(function(){return W.current=$e(function(){T(a)}),function(){$e.cancel(W.current)}},[a]),n.exports.createElement(jn,{prefixCls:t,popupClassName:j("".concat(t,"-popup"),P({},"".concat(t,"-rtl"),f),l),stretch:c==="horizontal"?"minWidth":null,getPopupContainer:h,builtinPlacements:S,popupPlacement:L,popupVisible:v,popup:o,popupAlign:i&&{offset:i},action:s?[]:[M],mouseEnterDelay:I,mouseLeaveDelay:p,onPopupVisibleChange:u,forceRender:y,popupMotion:k},r)}function cr(e){var t=e.id,a=e.open,r=e.keyPath,o=e.children,l="inline",i=n.exports.useContext(re),s=i.prefixCls,c=i.forceSubMenuRender,u=i.motion,d=i.defaultMotions,h=i.mode,f=n.exports.useRef(!1);f.current=h===l;var I=n.exports.useState(!f.current),p=B(I,2),g=p[0],M=p[1],y=f.current?a:!1;n.exports.useEffect(function(){f.current&&M(!1)},[h]);var C=$({},an(l,u,d));r.length>1&&(C.motionAppear=!1);var m=C.onVisibleChanged;return C.onVisibleChanged=function(x){return!f.current&&!x&&M(!0),m==null?void 0:m(x)},g?null:n.exports.createElement(Le,{mode:l,locked:!f.current},n.exports.createElement(zn,O({visible:y},C,{forceRender:c,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(x){var b=x.className,v=x.style;return n.exports.createElement(gt,{id:t,className:b,style:v},o)}))}var ur=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],dr=["active"],vr=function(t){var a,r=t.style,o=t.className,l=t.title,i=t.eventKey;t.warnKey;var s=t.disabled,c=t.internalPopupClose,u=t.children,d=t.itemIcon,h=t.expandIcon,f=t.popupClassName,I=t.popupOffset,p=t.onClick,g=t.onMouseEnter,M=t.onMouseLeave,y=t.onTitleClick,C=t.onTitleMouseEnter,m=t.onTitleMouseLeave,x=ne(t,ur),b=rn(i),v=n.exports.useContext(re),T=v.prefixCls,S=v.mode,L=v.openKeys,U=v.disabled,k=v.overflowDisabled,W=v.activeKey,H=v.selectedKeys,G=v.itemIcon,V=v.expandIcon,A=v.onItemClick,_=v.onOpenChange,X=v.onActive,le=n.exports.useContext(mt),se=le._internalRenderSubMenuItem,te=n.exports.useContext(en),ce=te.isSubPathKey,ae=Ne(),q="".concat(T,"-submenu"),J=U||s,R=n.exports.useRef(),N=n.exports.useRef(),w=d||G,D=h||V,z=L.includes(i),Y=!k&&z,oe=ce(H,i),ve=Yt(i,J,C,m),ge=ve.active,ye=ne(ve,dr),be=n.exports.useState(!1),Ie=B(be,2),ee=Ie[0],Qe=Ie[1],Ee=function(Q){J||Qe(Q)},Fe=function(Q){Ee(!0),g==null||g({key:i,domEvent:Q})},et=function(Q){Ee(!1),M==null||M({key:i,domEvent:Q})},Re=n.exports.useMemo(function(){return ge||(S!=="inline"?ee||ce([W],i):!1)},[S,ge,W,ee,i,ce]),tt=Xt(ae.length),Ve=function(Q){J||(y==null||y({key:i,domEvent:Q}),S==="inline"&&_(i,!z))},we=Pe(function(ie){p==null||p(Ye(ie)),A(ie)}),nt=function(Q){S!=="inline"&&_(i,Q)},je=function(){X(i)},Z=b&&"".concat(b,"-popup"),Oe=n.exports.createElement("div",O({role:"menuitem",style:tt,className:"".concat(q,"-title"),tabIndex:J?null:-1,ref:R,title:typeof l=="string"?l:null,"data-menu-id":k&&b?null:b,"aria-expanded":Y,"aria-haspopup":!0,"aria-controls":Z,"aria-disabled":J,onClick:Ve,onFocus:je},ye),l,n.exports.createElement(Jt,{icon:S!=="horizontal"?D:null,props:$($({},t),{},{isOpen:Y,isSubMenu:!0})},n.exports.createElement("i",{className:"".concat(q,"-arrow")}))),We=n.exports.useRef(S);if(S!=="inline"&&(We.current=ae.length>1?"vertical":S),!k){var Me=We.current;Oe=n.exports.createElement(sr,{mode:Me,prefixCls:q,visible:!c&&Y&&S!=="inline",popupClassName:f,popupOffset:I,popup:n.exports.createElement(Le,{mode:Me==="horizontal"?"vertical":Me},n.exports.createElement(gt,{id:Z,ref:N},u)),disabled:J,onVisibleChange:nt},Oe)}var fe=n.exports.createElement(Te.Item,O({role:"none"},x,{component:"li",style:r,className:j(q,"".concat(q,"-").concat(S),o,(a={},P(a,"".concat(q,"-open"),Y),P(a,"".concat(q,"-active"),Re),P(a,"".concat(q,"-selected"),oe),P(a,"".concat(q,"-disabled"),J),a)),onMouseEnter:Fe,onMouseLeave:et}),Oe,!k&&n.exports.createElement(cr,{id:Z,open:Y,keyPath:ae},u));return se&&(fe=se(fe,t,{selected:oe,active:Re,open:Y,disabled:J})),n.exports.createElement(Le,{onItemClick:we,mode:S==="horizontal"?"vertical":S,itemIcon:w,expandIcon:D},fe)};function Ct(e){var t=e.eventKey,a=e.children,r=Ne(t),o=ht(a,r),l=Xe();n.exports.useEffect(function(){if(l)return l.registerPath(t,r),function(){l.unregisterPath(t,r)}},[r]);var i;return l?i=o:i=n.exports.createElement(vr,e,o),n.exports.createElement(Qt.Provider,{value:r},i)}function At(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(Wn(e)){var a=e.nodeName.toLowerCase(),r=["input","select","textarea","button"].includes(a)||e.isContentEditable||a==="a"&&!!e.getAttribute("href"),o=e.getAttribute("tabindex"),l=Number(o),i=null;return o&&!Number.isNaN(l)?i=l:r&&i===null&&(i=0),r&&e.disabled&&(i=null),i!==null&&(i>=0||t&&i<0)}return!1}function fr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a=he(e.querySelectorAll("*")).filter(function(r){return At(r,t)});return At(e,t)&&a.unshift(e),a}var it=de.LEFT,lt=de.RIGHT,st=de.UP,Ue=de.DOWN,Ge=de.ENTER,on=de.ESC,_e=de.HOME,Ke=de.END,zt=[st,Ue,it,lt];function pr(e,t,a,r){var o,l,i,s,c="prev",u="next",d="children",h="parent";if(e==="inline"&&r===Ge)return{inlineTrigger:!0};var f=(o={},P(o,st,c),P(o,Ue,u),o),I=(l={},P(l,it,a?u:c),P(l,lt,a?c:u),P(l,Ue,d),P(l,Ge,d),l),p=(i={},P(i,st,c),P(i,Ue,u),P(i,Ge,d),P(i,on,h),P(i,it,a?d:h),P(i,lt,a?h:d),i),g={inline:f,horizontal:I,vertical:p,inlineSub:f,horizontalSub:p,verticalSub:p},M=(s=g["".concat(e).concat(t?"":"Sub")])===null||s===void 0?void 0:s[r];switch(M){case c:return{offset:-1,sibling:!0};case u:return{offset:1,sibling:!0};case h:return{offset:-1,sibling:!1};case d:return{offset:1,sibling:!1};default:return null}}function mr(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}function xr(e,t){for(var a=e||document.activeElement;a;){if(t.has(a))return a;a=a.parentElement}return null}function ln(e,t){var a=fr(e,!0);return a.filter(function(r){return t.has(r)})}function Ft(e,t,a){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;if(!e)return null;var o=ln(e,t),l=o.length,i=o.findIndex(function(s){return a===s});return r<0?i===-1?i=l-1:i-=1:r>0&&(i+=1),i=(i+l)%l,o[i]}function hr(e,t,a,r,o,l,i,s,c,u){var d=n.exports.useRef(),h=n.exports.useRef();h.current=t;var f=function(){$e.cancel(d.current)};return n.exports.useEffect(function(){return function(){f()}},[]),function(I){var p=I.which;if([].concat(zt,[Ge,on,_e,Ke]).includes(p)){var g,M,y,C=function(){g=new Set,M=new Map,y=new Map;var V=l();return V.forEach(function(A){var _=document.querySelector("[data-menu-id='".concat(nn(r,A),"']"));_&&(g.add(_),y.set(_,A),M.set(A,_))}),g};C();var m=M.get(t),x=xr(m,g),b=y.get(x),v=pr(e,i(b,!0).length===1,a,p);if(!v&&p!==_e&&p!==Ke)return;(zt.includes(p)||[_e,Ke].includes(p))&&I.preventDefault();var T=function(V){if(V){var A=V,_=V.querySelector("a");_!=null&&_.getAttribute("href")&&(A=_);var X=y.get(V);s(X),f(),d.current=$e(function(){h.current===X&&A.focus()})}};if([_e,Ke].includes(p)||v.sibling||!x){var S;!x||e==="inline"?S=o.current:S=mr(x);var L,U=ln(S,g);p===_e?L=U[0]:p===Ke?L=U[U.length-1]:L=Ft(S,g,x,v.offset),T(L)}else if(v.inlineTrigger)c(b);else if(v.offset>0)c(b,!0),f(),d.current=$e(function(){C();var G=x.getAttribute("aria-controls"),V=document.getElementById(G),A=Ft(V,g);T(A)},5);else if(v.offset<0){var k=i(b,!0),W=k[k.length-2],H=M.get(W);c(W,!1),T(H)}}u==null||u(I)}}var gr=Math.random().toFixed(5).toString().slice(2),Vt=0;function Cr(e){var t=Be(e,{value:e}),a=B(t,2),r=a[0],o=a[1];return n.exports.useEffect(function(){Vt+=1;var l="".concat(gr,"-").concat(Vt);o("rc-menu-uuid-".concat(l))},[]),r}function yr(e){Promise.resolve().then(e)}var yt="__RC_UTIL_PATH_SPLIT__",jt=function(t){return t.join(yt)},br=function(t){return t.split(yt)},ct="rc-menu-more";function Ir(){var e=n.exports.useState({}),t=B(e,2),a=t[1],r=n.exports.useRef(new Map),o=n.exports.useRef(new Map),l=n.exports.useState([]),i=B(l,2),s=i[0],c=i[1],u=n.exports.useRef(0),d=n.exports.useRef(!1),h=function(){d.current||a({})},f=n.exports.useCallback(function(m,x){var b=jt(x);o.current.set(b,m),r.current.set(m,b),u.current+=1;var v=u.current;yr(function(){v===u.current&&h()})},[]),I=n.exports.useCallback(function(m,x){var b=jt(x);o.current.delete(b),r.current.delete(m)},[]),p=n.exports.useCallback(function(m){c(m)},[]),g=n.exports.useCallback(function(m,x){var b=r.current.get(m)||"",v=br(b);return x&&s.includes(v[0])&&v.unshift(ct),v},[s]),M=n.exports.useCallback(function(m,x){return m.some(function(b){var v=g(b,!0);return v.includes(x)})},[g]),y=function(){var x=he(r.current.keys());return s.length&&x.push(ct),x},C=n.exports.useCallback(function(m){var x="".concat(r.current.get(m)).concat(yt),b=new Set;return he(o.current.keys()).forEach(function(v){v.startsWith(x)&&b.add(o.current.get(v))}),b},[]);return n.exports.useEffect(function(){return function(){d.current=!0}},[]),{registerPath:f,unregisterPath:I,refreshOverflowKeys:p,isSubPathKey:M,getKeyPath:g,getKeys:y,getSubPathKeys:C}}var Mr=["prefixCls","style","className","tabIndex","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem"],Se=[],Sr=n.exports.forwardRef(function(e,t){var a,r,o=e.prefixCls,l=o===void 0?"rc-menu":o,i=e.style,s=e.className,c=e.tabIndex,u=c===void 0?0:c,d=e.children,h=e.direction,f=e.id,I=e.mode,p=I===void 0?"vertical":I,g=e.inlineCollapsed,M=e.disabled,y=e.disabledOverflow,C=e.subMenuOpenDelay,m=C===void 0?.1:C,x=e.subMenuCloseDelay,b=x===void 0?.1:x,v=e.forceSubMenuRender,T=e.defaultOpenKeys,S=e.openKeys,L=e.activeKey,U=e.defaultActiveFirst,k=e.selectable,W=k===void 0?!0:k,H=e.multiple,G=H===void 0?!1:H,V=e.defaultSelectedKeys,A=e.selectedKeys,_=e.onSelect,X=e.onDeselect,le=e.inlineIndent,se=le===void 0?24:le,te=e.motion,ce=e.defaultMotions,ae=e.triggerSubMenuAction,q=ae===void 0?"hover":ae,J=e.builtinPlacements,R=e.itemIcon,N=e.expandIcon,w=e.overflowedIndicator,D=w===void 0?"...":w,z=e.overflowedIndicatorPopupClassName,Y=e.getPopupContainer,oe=e.onClick,ve=e.onOpenChange,ge=e.onKeyDown;e.openAnimation,e.openTransitionName;var ye=e._internalRenderMenuItem,be=e._internalRenderSubMenuItem,Ie=ne(e,Mr),ee=ht(d,Se),Qe=n.exports.useState(!1),Ee=B(Qe,2),Fe=Ee[0],et=Ee[1],Re=n.exports.useRef(),tt=Fn(Re,t),Ve=Cr(f),we=h==="rtl",nt=n.exports.useMemo(function(){return(p==="inline"||p==="vertical")&&g?["vertical",g]:[p,!1]},[p,g]),je=B(nt,2),Z=je[0],Oe=je[1],We=n.exports.useState(0),Me=B(We,2),fe=Me[0],ie=Me[1],Q=fe>=ee.length-1||Z!=="horizontal"||y,pn=Be(T,{value:S,postState:function(E){return E||Se}}),Mt=B(pn,2),pe=Mt[0],St=Mt[1],rt=function(E){St(E),ve==null||ve(E)},mn=n.exports.useState(pe),Pt=B(mn,2),xn=Pt[0],hn=Pt[1],at=Z==="inline",Nt=n.exports.useRef(!1);n.exports.useEffect(function(){at&&hn(pe)},[pe]),n.exports.useEffect(function(){if(!Nt.current){Nt.current=!0;return}at?St(xn):rt(Se)},[at]);var Ce=Ir(),Et=Ce.registerPath,Rt=Ce.unregisterPath,gn=Ce.refreshOverflowKeys,wt=Ce.isSubPathKey,Cn=Ce.getKeyPath,yn=Ce.getKeys,bn=Ce.getSubPathKeys,In=n.exports.useMemo(function(){return{registerPath:Et,unregisterPath:Rt}},[Et,Rt]),Mn=n.exports.useMemo(function(){return{isSubPathKey:wt}},[wt]);n.exports.useEffect(function(){gn(Q?Se:ee.slice(fe+1).map(function(K){return K.key}))},[fe,Q]);var Sn=Be(L||U&&((a=ee[0])===null||a===void 0?void 0:a.key),{value:L}),Ot=B(Sn,2),_t=Ot[0],ot=Ot[1],Pn=Pe(function(K){ot(K)}),Nn=Pe(function(){ot(void 0)}),En=Be(V||[],{value:A,postState:function(E){return Array.isArray(E)?E:E==null?Se:[E]}}),Kt=B(En,2),He=Kt[0],Rn=Kt[1],wn=function(E){if(W){var F=E.key,me=He.includes(F),ue;G?me?ue=He.filter(function(Dn){return Dn!==F}):ue=[].concat(he(He),[F]):ue=[F],Rn(ue);var Tt=$($({},E),{},{selectedKeys:ue});me?X==null||X(Tt):_==null||_(Tt)}!G&&pe.length&&Z!=="inline"&&rt(Se)},On=Pe(function(K){oe==null||oe(Ye(K)),wn(K)}),$t=Pe(function(K,E){var F=pe.filter(function(ue){return ue!==K});if(E)F.push(K);else if(Z!=="inline"){var me=bn(K);F=F.filter(function(ue){return!me.has(ue)})}Gt(pe,F)||rt(F)}),_n=Pe(Y),Kn=function(E,F){var me=F!=null?F:!pe.includes(E);$t(E,me)},$n=hr(Z,_t,we,Ve,Re,yn,Cn,ot,Kn,ge);n.exports.useEffect(function(){et(!0)},[]);var Tn=n.exports.useMemo(function(){return{_internalRenderMenuItem:ye,_internalRenderSubMenuItem:be}},[ye,be]),Ln=Z!=="horizontal"||y?ee:ee.map(function(K,E){return n.exports.createElement(Le,{key:K.key,overflowDisabled:E>fe},K)}),kn=n.exports.createElement(Te,O({id:f,ref:tt,prefixCls:"".concat(l,"-overflow"),component:"ul",itemComponent:xt,className:j(l,"".concat(l,"-root"),"".concat(l,"-").concat(Z),s,(r={},P(r,"".concat(l,"-inline-collapsed"),Oe),P(r,"".concat(l,"-rtl"),we),r)),dir:h,style:i,role:"menu",tabIndex:u,data:Ln,renderRawItem:function(E){return E},renderRawRest:function(E){var F=E.length,me=F?ee.slice(-F):null;return n.exports.createElement(Ct,{eventKey:ct,title:D,disabled:Q,internalPopupClose:F===0,popupClassName:z},me)},maxCount:Z!=="horizontal"||y?Te.INVALIDATE:Te.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(E){ie(E)},onKeyDown:$n},Ie));return n.exports.createElement(mt.Provider,{value:Tn},n.exports.createElement(tn.Provider,{value:Ve},n.exports.createElement(Le,{prefixCls:l,mode:Z,openKeys:pe,rtl:we,disabled:M,motion:Fe?te:null,defaultMotions:Fe?ce:null,activeKey:_t,onActive:Pn,onInactive:Nn,selectedKeys:He,inlineIndent:se,subMenuOpenDelay:m,subMenuCloseDelay:b,forceSubMenuRender:v,builtinPlacements:J,triggerSubMenuAction:q,getPopupContainer:_n,itemIcon:R,expandIcon:N,onItemClick:On,onOpenChange:$t},n.exports.createElement(en.Provider,{value:Mn},kn),n.exports.createElement("div",{style:{display:"none"},"aria-hidden":!0},n.exports.createElement(Zt.Provider,{value:In},ee)))))}),Pr=["className","title","eventKey","children"],Nr=["children"],Er=function(t){var a=t.className,r=t.title;t.eventKey;var o=t.children,l=ne(t,Pr),i=n.exports.useContext(re),s=i.prefixCls,c="".concat(s,"-item-group");return n.exports.createElement("li",O({},l,{onClick:function(d){return d.stopPropagation()},className:j(c,a)}),n.exports.createElement("div",{className:"".concat(c,"-title"),title:typeof r=="string"?r:void 0},r),n.exports.createElement("ul",{className:"".concat(c,"-list")},o))};function sn(e){var t=e.children,a=ne(e,Nr),r=Ne(a.eventKey),o=ht(t,r),l=Xe();return l?o:n.exports.createElement(Er,De(a,["warnKey"]),o)}function cn(e){var t=e.className,a=e.style,r=n.exports.useContext(re),o=r.prefixCls,l=Xe();return l?null:n.exports.createElement("li",{className:j("".concat(o,"-item-divider"),t),style:a})}var Rr=Ne,Ae=Sr;Ae.Item=xt;Ae.SubMenu=Ct;Ae.ItemGroup=sn;Ae.Divider=cn;var wr=n.exports.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1}),Je=wr;function Or(e){var t,a=e.popupClassName,r=e.icon,o=e.title,l=e.theme,i=n.exports.useContext(Je),s=i.prefixCls,c=i.inlineCollapsed,u=i.antdMenuTheme,d=Rr(),h;if(!r)h=c&&!d.length&&o&&typeof o=="string"?n.exports.createElement("div",{className:"".concat(s,"-inline-collapsed-noicon")},o.charAt(0)):n.exports.createElement("span",{className:"".concat(s,"-title-content")},o);else{var f=qe(o)&&o.type==="span";h=n.exports.createElement(n.exports.Fragment,null,pt(r,{className:j(qe(r)?(t=r.props)===null||t===void 0?void 0:t.className:"","".concat(s,"-item-icon"))}),f?o:n.exports.createElement("span",{className:"".concat(s,"-title-content")},o))}var I=n.exports.useMemo(function(){return O(O({},i),{firstLevel:!1})},[i]);return n.exports.createElement(Je.Provider,{value:I},n.exports.createElement(Ct,O({},De(e,["icon"]),{title:h,popupClassName:j(s,"".concat(s,"-").concat(l||u),a)})))}var _r={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},Kr=_r,un=function(t,a){return n.exports.createElement(Ht,$($({},t),{},{ref:a,icon:Kr}))};un.displayName="BarsOutlined";var $r=n.exports.forwardRef(un),dn=globalThis&&globalThis.__rest||function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(a[r[o]]=e[r[o]]);return a},vn=n.exports.createContext({siderHook:{addSider:function(){return null},removeSider:function(){return null}}});function Ze(e){var t=e.suffixCls,a=e.tagName,r=e.displayName;return function(o){var l=n.exports.forwardRef(function(i,s){var c=n.exports.useContext(ke),u=c.getPrefixCls,d=i.prefixCls,h=u(t,d);return n.exports.createElement(o,O({ref:s,prefixCls:h,tagName:a},i))});return l.displayName=r,l}}var bt=n.exports.forwardRef(function(e,t){var a=e.prefixCls,r=e.className,o=e.children,l=e.tagName,i=dn(e,["prefixCls","className","children","tagName"]),s=j(a,r);return n.exports.createElement(l,O(O({className:s},i),{ref:t}),o)}),Tr=n.exports.forwardRef(function(e,t){var a,r=n.exports.useContext(ke),o=r.direction,l=n.exports.useState([]),i=B(l,2),s=i[0],c=i[1],u=e.prefixCls,d=e.className,h=e.children,f=e.hasSider,I=e.tagName,p=dn(e,["prefixCls","className","children","hasSider","tagName"]),g=j(u,(a={},P(a,"".concat(u,"-has-sider"),typeof f=="boolean"?f:s.length>0),P(a,"".concat(u,"-rtl"),o==="rtl"),a),d),M=n.exports.useMemo(function(){return{siderHook:{addSider:function(C){c(function(m){return[].concat(he(m),[C])})},removeSider:function(C){c(function(m){return m.filter(function(x){return x!==C})})}}}},[]);return n.exports.createElement(vn.Provider,{value:M},n.exports.createElement(I,O({ref:t,className:g},p),h))});Ze({suffixCls:"layout",tagName:"section",displayName:"Layout"})(Tr);Ze({suffixCls:"layout-header",tagName:"header",displayName:"Header"})(bt);Ze({suffixCls:"layout-footer",tagName:"footer",displayName:"Footer"})(bt);Ze({suffixCls:"layout-content",tagName:"main",displayName:"Content"})(bt);var Lr=function(t){return!isNaN(parseFloat(t))&&isFinite(t)},kr=Lr,Dr=globalThis&&globalThis.__rest||function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(a[r[o]]=e[r[o]]);return a},Wt={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},It=n.exports.createContext({}),Ar=function(){var e=0;return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,"".concat(t).concat(e)}}(),zr=n.exports.forwardRef(function(e,t){var a=e.prefixCls,r=e.className,o=e.trigger,l=e.children,i=e.defaultCollapsed,s=i===void 0?!1:i,c=e.theme,u=c===void 0?"dark":c,d=e.style,h=d===void 0?{}:d,f=e.collapsible,I=f===void 0?!1:f,p=e.reverseArrow,g=p===void 0?!1:p,M=e.width,y=M===void 0?200:M,C=e.collapsedWidth,m=C===void 0?80:C,x=e.zeroWidthTriggerStyle,b=e.breakpoint,v=e.onCollapse,T=e.onBreakpoint,S=Dr(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),L=n.exports.useContext(vn),U=L.siderHook,k=n.exports.useState("collapsed"in S?S.collapsed:s),W=B(k,2),H=W[0],G=W[1],V=n.exports.useState(!1),A=B(V,2),_=A[0],X=A[1];n.exports.useEffect(function(){"collapsed"in S&&G(S.collapsed)},[S.collapsed]);var le=function(N,w){"collapsed"in S||G(N),v==null||v(N,w)},se=n.exports.useRef();se.current=function(R){X(R.matches),T==null||T(R.matches),H!==R.matches&&le(R.matches,"responsive")},n.exports.useEffect(function(){function R(z){return se.current(z)}var N;if(typeof window!="undefined"){var w=window,D=w.matchMedia;if(D&&b&&b in Wt){N=D("(max-width: ".concat(Wt[b],")"));try{N.addEventListener("change",R)}catch{N.addListener(R)}R(N)}}return function(){try{N==null||N.removeEventListener("change",R)}catch{N==null||N.removeListener(R)}}},[b]),n.exports.useEffect(function(){var R=Ar("ant-sider-");return U.addSider(R),function(){return U.removeSider(R)}},[]);var te=function(){le(!H,"clickTrigger")},ce=n.exports.useContext(ke),ae=ce.getPrefixCls,q=function(){var N,w=ae("layout-sider",a),D=De(S,["collapsed"]),z=H?m:y,Y=kr(z)?"".concat(z,"px"):String(z),oe=parseFloat(String(m||0))===0?n.exports.createElement("span",{onClick:te,className:j("".concat(w,"-zero-width-trigger"),"".concat(w,"-zero-width-trigger-").concat(g?"right":"left")),style:x},o||n.exports.createElement($r,null)):null,ve={expanded:g?n.exports.createElement(kt,null):n.exports.createElement(Dt,null),collapsed:g?n.exports.createElement(Dt,null):n.exports.createElement(kt,null)},ge=H?"collapsed":"expanded",ye=ve[ge],be=o!==null?oe||n.exports.createElement("div",{className:"".concat(w,"-trigger"),onClick:te,style:{width:Y}},o||ye):null,Ie=O(O({},h),{flex:"0 0 ".concat(Y),maxWidth:Y,minWidth:Y,width:Y}),ee=j(w,"".concat(w,"-").concat(u),(N={},P(N,"".concat(w,"-collapsed"),!!H),P(N,"".concat(w,"-has-trigger"),I&&o!==null&&!oe),P(N,"".concat(w,"-below"),!!_),P(N,"".concat(w,"-zero-width"),parseFloat(Y)===0),N),r);return n.exports.createElement("aside",O({className:ee},D,{style:Ie,ref:t}),n.exports.createElement("div",{className:"".concat(w,"-children")},l),I||_&&oe?be:null)},J=n.exports.useMemo(function(){return{siderCollapsed:H}},[H]);return n.exports.createElement(It.Provider,{value:J},q())});zr.displayName="Sider";var Fr=globalThis&&globalThis.__rest||function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(a[r[o]]=e[r[o]]);return a},fn=function(e){ut(a,e);var t=dt(a);function a(){var r;return vt(this,a),r=t.apply(this,arguments),r.renderItem=function(o){var l,i=o.siderCollapsed,s,c=r.context,u=c.prefixCls,d=c.firstLevel,h=c.inlineCollapsed,f=c.direction,I=c.disableMenuItemTitleTooltip,p=r.props,g=p.className,M=p.children,y=r.props,C=y.title,m=y.icon,x=y.danger,b=Fr(y,["title","icon","danger"]),v=C;typeof C=="undefined"?v=d?M:"":C===!1&&(v="");var T={title:v};!i&&!h&&(T.title=null,T.visible=!1);var S=Ut(M).length,L=n.exports.createElement(xt,O({},b,{className:j((l={},P(l,"".concat(u,"-item-danger"),x),P(l,"".concat(u,"-item-only-child"),(m?S+1:S)===1),l),g),title:typeof C=="string"?C:void 0}),pt(m,{className:j(qe(m)?(s=m.props)===null||s===void 0?void 0:s.className:"","".concat(u,"-item-icon"))}),r.renderItemChildren(h));return I||(L=n.exports.createElement(Hn,O({},T,{placement:f==="rtl"?"left":"right",overlayClassName:"".concat(u,"-inline-collapsed-tooltip")}),L)),L},r}return ft(a,[{key:"renderItemChildren",value:function(o){var l=this.context,i=l.prefixCls,s=l.firstLevel,c=this.props,u=c.icon,d=c.children,h=n.exports.createElement("span",{className:"".concat(i,"-title-content")},d);return(!u||qe(d)&&d.type==="span")&&d&&o&&s&&typeof d=="string"?n.exports.createElement("div",{className:"".concat(i,"-inline-collapsed-noicon")},d.charAt(0)):h}},{key:"render",value:function(){return n.exports.createElement(It.Consumer,null,this.renderItem)}}]),a}(n.exports.Component);fn.contextType=Je;var Vr=globalThis&&globalThis.__rest||function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(a[r[o]]=e[r[o]]);return a},jr=function(t){var a=t.prefixCls,r=t.className,o=t.dashed,l=Vr(t,["prefixCls","className","dashed"]),i=n.exports.useContext(ke),s=i.getPrefixCls,c=s("menu",a),u=j(P({},"".concat(c,"-item-divider-dashed"),!!o),r);return n.exports.createElement(cn,O({className:u},l))},Wr=jr,Hr=globalThis&&globalThis.__rest||function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(a[r[o]]=e[r[o]]);return a};function Br(e){var t=n.exports.useContext(ke),a=t.getPrefixCls,r=t.getPopupContainer,o=t.direction,l=a(),i=e.prefixCls,s=e.className,c=e.theme,u=c===void 0?"light":c,d=e.expandIcon,h=e._internalDisableMenuItemTitleTooltip,f=e.inlineCollapsed,I=e.siderCollapsed,p=Hr(e,["prefixCls","className","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed"]),g=De(p,["collapsedWidth"]);Lt(!("inlineCollapsed"in e&&e.mode!=="inline"),"Menu","`inlineCollapsed` should only be used when `mode` is inline."),Lt(!(e.siderCollapsed!==void 0&&"inlineCollapsed"in e),"Menu","`inlineCollapsed` not control Menu under Sider. Should set `collapsed` on Sider instead.");var M=n.exports.useMemo(function(){return I!==void 0?I:f},[f,I]),y={horizontal:{motionName:"".concat(l,"-slide-up")},inline:Vn,other:{motionName:"".concat(l,"-zoom-big")}},C=a("menu",i),m=j("".concat(C,"-").concat(u),s),x=n.exports.useMemo(function(){return{prefixCls:C,inlineCollapsed:M||!1,antdMenuTheme:u,direction:o,firstLevel:!0,disableMenuItemTitleTooltip:h}},[C,M,u,o,h]);return n.exports.createElement(Je.Provider,{value:x},n.exports.createElement(Ae,O({getPopupContainer:r,overflowedIndicator:n.exports.createElement(Gn,null),overflowedIndicatorPopupClassName:"".concat(C,"-").concat(u)},g,{inlineCollapsed:M,className:m,prefixCls:C,direction:o,defaultMotions:y,expandIcon:pt(d,{className:"".concat(C,"-submenu-expand-icon")})})))}var ze=function(e){ut(a,e);var t=dt(a);function a(){return vt(this,a),t.apply(this,arguments)}return ft(a,[{key:"render",value:function(){var o=this;return n.exports.createElement(It.Consumer,null,function(l){return n.exports.createElement(Br,O({},o.props,l))})}}]),a}(n.exports.Component);ze.Divider=Wr;ze.Item=fn;ze.SubMenu=Or;ze.ItemGroup=sn;var Qr=ze;export{Gn as E,Qr as M};
