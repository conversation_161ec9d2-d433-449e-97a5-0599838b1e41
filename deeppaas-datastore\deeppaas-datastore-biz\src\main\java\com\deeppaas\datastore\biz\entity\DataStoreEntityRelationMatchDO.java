package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据模型关系匹配字段
 * <AUTHOR>
 * @date 2022/4/6
 */
@Data
@Table("datastore_entity_relation_match")
public class DataStoreEntityRelationMatchDO extends BaseUuidDO {
    /**
     * 数据模型1编号
     **/
    @Column("entity1_code")
    private String entity1Code;
    /**
     * 数据模型1字段
     **/
    @Column("entity1_field_code")
    private String entity1FieldCode;
    /**
     * 数据模型2编号
     **/
    @Column("entity2_code")
    private String entity2Code;
    /**
     * 数据模型2字段
     **/
    @Column("entity2_field_code")
    private String entity2FieldCode;
}
