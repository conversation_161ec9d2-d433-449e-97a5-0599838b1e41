package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.dao.FlowPositionDao;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowPositionDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional(readOnly = true)
public class FlowPositionService {
	private FlowPositionDao flowPositionDao;

	public FlowPositionService(FlowPositionDao flowPositionDao) {
		this.flowPositionDao = flowPositionDao;
	}

	public FlowPositionDO findById(Long id) {
		return flowPositionDao.getById(id);
	}
	
	public FlowPositionDO findByInstanceIdAndNodeCode(Long instanceId, String nodeCode) {
		return flowPositionDao.findByInstanceIdAndNodeCode(instanceId, nodeCode);
	}
	
	@Transactional
	public void updatePosition(Long instanceId, String targetNode, String sourceNode) {
		FlowPositionDO targetPosition = flowPositionDao.findByInstanceIdAndNodeCode(instanceId, targetNode);
		if(targetPosition == null){	//第一次进入
			Long previousId = null;
			if(null != sourceNode){
				FlowPositionDO sourcePosition = flowPositionDao.findByInstanceIdAndNodeCode(instanceId,sourceNode);
				previousId = sourcePosition == null ? null : sourcePosition.getId();
			}
			targetPosition = new FlowPositionDO();
			targetPosition.setInstanceId(instanceId);
			targetPosition.setPrevId(previousId);
			targetPosition.setNodeCode(targetNode);
			flowPositionDao.save(targetPosition);
		}else{	//如果之前曾经有记录，则证实为跳转至该环节，需清除路由
			this.clearPosition(targetPosition);
		}
	}
	
	@Transactional
	public void clearPosition(FlowPositionDO startPosition) {
		if(startPosition!=null){
			List<FlowPositionDO> nextPositions = new ArrayList<>();
			this.getNextPositions(nextPositions, startPosition);
			if(!nextPositions.isEmpty()){
				flowPositionDao.deleteAll(nextPositions);
			}	
		}
	}
	private void getNextPositions(List<FlowPositionDO> positions, FlowPositionDO position){
		if(position!=null){
			List<FlowPositionDO>  nextPosition = flowPositionDao.findByPrevId(position.getId());
			if(nextPosition!=null){
				for(FlowPositionDO tempPosition : nextPosition){
					positions.add(tempPosition);
					getNextPositions(positions, tempPosition);
				}
			}
		}
	}

	@Transactional
    public void deleteByInstance(Long instanceId) {
        flowPositionDao.deleteByInstanceId(instanceId);
    }

    public List<FlowPositionDO> findByInstanceId(Long instanceId) {
		return flowPositionDao.findByInstanceId(instanceId);
    }
}
