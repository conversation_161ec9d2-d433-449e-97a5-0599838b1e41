package com.deeppaas.common.helper;

public class NumeralrHelper {

    //判断数字不等于空切大于0
    public static boolean isNotNull(Integer i){
        if (i!=null&&i>0)
            return true;
        return false;

    }

    //判断数字不等于空切大于0
    public static boolean isNotNull(Float i){
        if (i!=null&&i>0)
            return true;
        return false;

    }

    //判断数字不等于空切大于0
    public static boolean isNotNull(Double i){
        if (i!=null&&i>0)
            return true;
        return false;
    }


    /*
    * 是否是偶数
    * */
    public static boolean isOdd(int i) {
        if (i % 2 == 1) {
            return false;
        } else {
            return true;

        }
    }
}
