# 案卷级档案处理页号区分逻辑设计文档

## 1. 概述

### 1.1 背景
当前系统支持文件级档案处理，但对案卷级档案处理支持不足。案卷级档案通常包含多个件，每个件有不同的页号范围，需要根据Excel中的页号信息进行图像分组处理。

### 1.2 目标
实现案卷级档案处理中基于页号信息的图像分件逻辑，支持三种分件方式：
- 首页号+尾页号
- 首页号+页数  
- 起止页号
- 如果都没有则作为单件处理

### 1.3 设计原则
- **向后兼容**：不影响现有文件级处理逻辑
- **渐进实现**：可以先支持基础案卷级处理，后续再优化
- **配置灵活**：通过字段库配置决定处理方式
- **降低复杂度**：避免复杂的自动识别逻辑

## 2. 系统架构

### 2.1 核心概念澄清

基于代码分析，确认以下概念：

- **dataKey**: 案卷档号（不包含件号）
  - 例如：`JA001`（案卷级档号）
- **partNo/partNumber**: 件号（不是页号范围）  
  - 例如：`1`, `2`, `3`（表示第1件、第2件、第3件）
- **页号范围**: 从Excel条目字段中获取，存储在相关字段中
  - 例如：`"1-5"`, `"6-10"`（表示第1-5页、第6-10页）

### 2.2 处理方式分类

```java
public enum ImageGroupingType {
    SINGLE_PIECE,           // 全部图像作为一件
    START_END_PAGE,         // 首页号+尾页号
    START_PAGE_COUNT,       // 首页号+页数  
    PAGE_RANGE             // 起止页号（如"1-5"）
}
```

## 3. 技术方案

### 3.1 图像分件方式检测逻辑

```java
private ImageGroupingType detectGroupingType(ProjectTaskConfigDTO taskConfigDTO) {
    // 🔍 从字段库配置中查找关键字段
    Map<String, String> fieldMapping = taskConfigDTO.buildRuleMapping();
    
    // 检查是否存在分件相关字段
    boolean hasStartPage = fieldMapping.containsValue("首页号");
    boolean hasEndPage = fieldMapping.containsValue("尾页号");  
    boolean hasPageCount = fieldMapping.containsValue("页数");
    boolean hasPageRange = fieldMapping.containsValue("起止页号");
    
    if (hasPageRange) {
        return ImageGroupingType.PAGE_RANGE;
    } else if (hasStartPage && hasEndPage) {
        return ImageGroupingType.START_END_PAGE;
    } else if (hasStartPage && hasPageCount) {
        return ImageGroupingType.START_PAGE_COUNT;
    } else {
        return ImageGroupingType.SINGLE_PIECE;
    }
}
```

### 3.2 案卷级图像处理扩展

```java
private void buildCaseLevelImages(String taskId, ProjectTaskConfigDTO taskConfigDTO, String dataKey, String partNo) {
    // 🔍 检测图像分件方式
    ImageGroupingType groupingType = detectGroupingType(taskConfigDTO);
    
    switch (groupingType) {
        case SINGLE_PIECE:
            // 所有图像作为一件处理（现有逻辑）
            buildSinglePieceImages(taskId, taskConfigDTO, dataKey, partNo);
            break;
            
        case START_END_PAGE:
        case START_PAGE_COUNT: 
        case PAGE_RANGE:
            // 根据Excel字段进行图像分组
            buildMultiPieceImages(taskId, taskConfigDTO, dataKey, groupingType);
            break;
    }
}
```

### 3.3 页号范围解析器

```java
private PageRangeInfo parsePageRange(ProjectTaskFormDataDTO formData, ImageGroupingType groupingType, ProjectTaskConfigDTO taskConfigDTO) {
    Map<String, Object> taskData = JsonHelper.json2map(formData.getTaskJson());
    Map<String, String> fieldMapping = taskConfigDTO.buildRuleMapping();
    
    switch (groupingType) {
        case START_END_PAGE:
            String startPageField = getFieldByValue(fieldMapping, "首页号");
            String endPageField = getFieldByValue(fieldMapping, "尾页号");
            int startPage = Integer.parseInt(StringHelper.toString(taskData.get(startPageField)));
            int endPage = Integer.parseInt(StringHelper.toString(taskData.get(endPageField)));
            return new PageRangeInfo(startPage, endPage);
            
        case START_PAGE_COUNT:
            String startField = getFieldByValue(fieldMapping, "首页号");
            String countField = getFieldByValue(fieldMapping, "页数");
            int start = Integer.parseInt(StringHelper.toString(taskData.get(startField)));
            int count = Integer.parseInt(StringHelper.toString(taskData.get(countField)));
            return new PageRangeInfo(start, start + count - 1);
            
        case PAGE_RANGE:
            String rangeField = getFieldByValue(fieldMapping, "起止页号");
            String rangeValue = StringHelper.toString(taskData.get(rangeField));
            // 解析"1-5"格式
            String[] parts = rangeValue.split("-");
            return new PageRangeInfo(Integer.parseInt(parts[0]), Integer.parseInt(parts[1]));
            
        default:
            return new PageRangeInfo(1, Integer.MAX_VALUE); // 单件处理
    }
}
```

## 4. 数据结构

### 4.1 页号范围信息类

```java
public class PageRangeInfo {
    private int startPage;
    private int endPage;
    private int pageCount;
    
    public PageRangeInfo(int startPage, int endPage) {
        this.startPage = startPage;
        this.endPage = endPage;
        this.pageCount = endPage - startPage + 1;
    }
    
    // getters and setters
}
```

### 4.2 图像分组结果

```java
public class ImageGroupResult {
    private String dataKey;
    private String partNumber;
    private PageRangeInfo pageRange;
    private List<File> imageFiles;
    private int actualImageCount;
    
    // getters and setters
}
```

## 5. 处理流程

### 5.1 整体流程

```
1. 检测图像分件方式
   ├── 检查字段库配置
   ├── 识别页号相关字段
   └── 确定处理模式

2. 根据处理模式执行分组
   ├── SINGLE_PIECE: 使用现有逻辑
   └── 其他模式: 执行多件分组

3. 多件分组处理
   ├── 获取Excel数据
   ├── 解析页号范围
   ├── 按页号分组图像
   └── 创建图像数据记录
```

### 5.2 错误处理策略

- **字段缺失**: 降级为单件处理
- **页号格式错误**: 记录错误日志，跳过该记录
- **图像文件不足**: 记录警告，使用实际文件数量
- **页号重叠**: 记录错误，按顺序分配

## 6. 实现计划

### Phase 1: 基础实现
1. 实现图像分件方式检测逻辑
2. 支持`SINGLE_PIECE`模式（保持现有逻辑）
3. 添加配置识别机制

### Phase 2: 分件支持  
1. 实现`START_END_PAGE`模式
2. 实现`START_PAGE_COUNT`模式
3. 实现`PAGE_RANGE`模式

### Phase 3: 优化完善
1. 添加错误处理和验证
2. 支持页号不连续的情况
3. 添加调试日志和监控

## 7. 风险和缓解措施

### 7.1 潜在风险
- **字段名称匹配问题**: 字段库中的字段名可能不完全匹配
- **页号不连续问题**: 实际图像文件名可能不是连续的页号
- **Excel数据缺失问题**: 某些记录可能缺少页号信息

### 7.2 缓解措施
- **模糊匹配**: 支持字段名别名配置
- **文件排序**: 按文件排序顺序分配页号
- **默认策略**: 提供缺失数据的默认处理方式

## 8. 测试策略

### 8.1 单元测试
- 图像分件方式检测逻辑测试
- 页号范围解析器测试
- 边界条件和异常情况测试

### 8.2 集成测试
- 完整案卷级处理流程测试
- 与现有文件级处理的兼容性测试
- 不同配置场景的测试

### 8.3 性能测试
- 大量图像文件的处理性能
- 内存使用情况监控
- 并发处理能力测试
