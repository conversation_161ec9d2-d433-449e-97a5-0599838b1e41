package com.deeppaas.common.exception;

import java.util.Objects;

/**
 * @Description 权限异常
 * <AUTHOR>
 * @Date 2019-03-21
 */
public class AuthException extends BaseException {
    /**
     * 未登录异常码
     */
    public static final int NOT_LOGIN_CODE = 1001;
    /**
     * 未授权异常码
     */
    public static final int UNAUTHORIZED_CODE = 1002;
    /**
     * 用户不可用异常码
     */
    public static final int UNUSED_CODE = 1003;
    /**
     * WX未登录异常码
     */
    public static final int WX_NOT_LOGIN_CODE = 1004;
    /**
     * WX重定向参数code未删除异常码
     */
    public static final int WX_CODE_NOT_REMOVE_CODE = 1005;

    public static final int MORE_THAN_LIMIT_ONLINE_USER = 1006;

    public AuthException(int code, String message) {
        super(code, message);
    }

    /**
     * @Description: 未登录异常
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static AuthException wxNotLogin() {
        return new AuthException(WX_NOT_LOGIN_CODE, "非企业内部用户");
    }

    /**
     * @Description: WX重定向参数code未删除
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static AuthException wxCodeNotRemove() {
        return new AuthException(WX_CODE_NOT_REMOVE_CODE, "重定向参数code未删除");
    }

    /**
     * @Description: 未登录异常
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static AuthException notLogin() {
        return new AuthException(NOT_LOGIN_CODE, "未登录");
    }

    /**
     * @Description: 未授权异常
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static AuthException unAuthorized(String message) {
        return new AuthException(UNAUTHORIZED_CODE, message);
    }

    /**
     * @Description: 账号被禁用
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static AuthException unUsed() {
        return new AuthException(UNUSED_CODE, "账户被禁用");
    }

    public static AuthException limitLogin(int onlineUserLimit) {
        return new AuthException(MORE_THAN_LIMIT_ONLINE_USER, "当前在线用户数量已达授权限定人数"+onlineUserLimit+"人，不能登录了");
    }

    public boolean loginError() {
        return Objects.equals(NOT_LOGIN_CODE, this.getCode());
    }
}
