package com.deeppaas.quartz.job;

import com.deeppaas.quartz.core.QuartzJob;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 执行SQL定时器
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Component("quartz_QuartzSqlJob")
public class QuartzSqlJob extends QuartzJob {
    private static final Logger log = LoggerFactory.getLogger(QuartzSqlJob.class);

    @Override
    public void doExecute(JobExecutionContext context) {
        log.info("QuartzSqlJob doExecute Start");
        log.info("Do SomeThing..........");
        log.info("QuartzSqlJob doExecute End");
    }


    @Override
    public void rerun(Date targetTime) {
        log.info("QuartzSqlJob rerun Start");

        log.info("QuartzSqlJob rerun End");
    }
}
