package com.deeppaas.rule.biz.function;

import com.deeppaas.common.exception.RunException;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
public class SimpleFunction {

    public static boolean arrayContains(Object[] arguments) {
        Assert.isTrue(arguments.length == 2, "参数错误");
        if (arguments[0] == null) {
            return false;
        }
        if (arguments[0] instanceof Collection<?> argument0) {
            return argument0.contains(arguments[1]);
        }
        if (arguments[0].getClass().isArray()) {
            Object[] array = (Object[]) arguments[0];
            return Arrays.stream(array).anyMatch(Predicate.isEqual(arguments[1]));
        }
        return false;
    }

    public static Object[] arrayJoin(Object[] arguments) {
        Assert.isTrue(arguments.length == 2, "参数错误");
        Collection<Object> collection = new ArrayList<>();
        for (Object argument : arguments) {
            if (argument instanceof Collection<?>) {
                collection.addAll((Collection) argument);
            } else if (argument.getClass().isArray()) {
                Object[] array = (Object[]) argument;
                collection.addAll(Arrays.stream(array).toList());
            } else {
                collection.add(argument);
            }
        }
        return collection.toArray();
    }

    public static String toBase64(Object argument) {
        if(argument instanceof File imageFile){
            byte[] data = null;
            // 读取图片字节数组
            try {
                InputStream in = new FileInputStream(imageFile);
                data = new byte[in.available()];
                in.read(data);
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            // 对字节数组Base64编码
            return Base64.getEncoder().encodeToString(data);
        }
        if(argument instanceof InputStream inputStream){
            byte[] data = null;
            // 读取图片字节数组
            try {
                data = new byte[inputStream.available()];
                inputStream.read(data);
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            // 对字节数组Base64编码
            return Base64.getEncoder().encodeToString(data);
        }
        throw RunException.error("只能文件将转换为Base64");
    }
}
