package com.deeppaas.flow.biz.dao;

import java.util.List;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowPositionDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

public interface FlowPositionDao extends BaseJdbcDao<FlowPositionDO, Long> {

	List<FlowPositionDO> findByPrevId(Long prevId);

	@Modifying
	@Query(value="delete from flow_position where flow_id = :instanceId")
    void deleteByInstanceId(Long instanceId);

    List<FlowPositionDO> findByInstanceId(Long instanceId);

    FlowPositionDO findByInstanceIdAndNodeCode(Long instanceId, String nodeCode);
}
