package com.deeppaas.datastore.biz.service;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.data.jdbc.JConditionL;
import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.CodeHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.datastore.biz.convert.DataStoreTableFieldConvert;
import com.deeppaas.datastore.biz.dao.DataStoreTableDao;
import com.deeppaas.datastore.biz.dao.DataStoreTableFieldDao;
import com.deeppaas.datastore.biz.dao.DataStoreTableFieldOptionDao;
import com.deeppaas.datastore.biz.dao.DataStoreTableIndexDao;
import com.deeppaas.datastore.biz.entity.*;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import com.deeppaas.datastore.common.mapping.DBColumn;
import com.deeppaas.datastore.common.mapping.DataStoreSelf;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 数据仓库自建表服务层
 *
 * <AUTHOR>
 * @date 2022/2/19
 */
@Service
public class DataStoreTableService {
    private final DataStoreTableDao tableDao;
    private final DataStoreTableFieldDao tableFieldDao;
    private final DataStoreTableFieldOptionDao tableFieldOptionDao;
    private final DataStoreTableIndexDao tableIndexDao;
    private final DataStoreDBService dbService;
    private final DataStoreEntityService dataStoreEntityService;

    public DataStoreTableService(DataStoreTableDao tableDao,
                                 DataStoreTableFieldDao tableFieldDao,
                                 DataStoreTableFieldOptionDao tableFieldOptionDao,
                                 DataStoreTableIndexDao tableIndexDao,
                                 DataStoreDBService dbService,
                                 DataStoreEntityService dataStoreEntityService) {
        this.tableDao = tableDao;
        this.tableFieldDao = tableFieldDao;
        this.tableFieldOptionDao = tableFieldOptionDao;
        this.tableIndexDao = tableIndexDao;
        this.dbService = dbService;
        this.dataStoreEntityService = dataStoreEntityService;
    }

    /**
     * 获取表
     */
    public DataStoreTableDO get(String id) {
        return tableDao.getById(id);
    }

    /**
     * 获取表
     */
    public DataStoreTableDO getTable(String sourceId, String tableName) {
        return tableDao.findBySourceIdAndTableName(sourceId, tableName);
    }

    /**
     * 检查表是否存在
     */
    public boolean existsTable(String sourceId, String tableName) {
        DataStoreTableDO tableDO = tableDao.findBySourceIdAndTableName(sourceId, tableName);
        return tableDO != null;
    }

    /**
     * 表列表
     */
    public List<DataStoreTableDO> list(String sourceId) {
        if (StringUtils.hasText(sourceId)) {
            return tableDao.findBySourceIdOrderByCreateTimeAsc(sourceId);
        }
        return tableDao.findAll();
    }

    /**
     * 表列表-分页
     */
    public PageData<DataStoreTableDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<DataStoreTableDO> page = tableDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    /**
     * 数据表-按照名称或编号模糊匹配分页
     */
    public PageData<DataStoreTableDO> pageFuzzy(PageSearch pageSearch, String codeOrName) {
        JCondition searchParam = JCondition.ofPageParam(pageSearch.getSearchParams());
        JConditionL fuzzyParam = JConditionL.or(JCondition.contains("tableName", codeOrName, true),
                JCondition.contains("name", codeOrName, true));

        JCondition condition = JCondition.and(searchParam, fuzzyParam);

        Page<DataStoreTableDO> page = tableDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    /**
     * 新增数据库表
     */
    @Transactional
    public DataStoreTableDO add(DataStoreTableDO param) {
        DataStoreTableDO table = tableDao.findBySourceIdAndTableName(param.getSourceId(), param.getTableName());
        if (table != null) {
            throw RunException.optReject("表名已被使用，创建失败");
        }
        DataStoreEntityType entityType = DataStoreEntityType.valueOf(param.getType());
        boolean selfTableCreate = false;
        switch (entityType) {
            case SELF_BUILD -> {
                boolean success = dbService.createTable(param.getSourceId(), param.getTableName());
                if (!success) {
                    throw RunException.error("创建数据表失败");
                }
                selfTableCreate = true;
            }
            case SQL_BUILD -> {
                if (dbService.existsTable(param.getSourceId(), param.getTableName())) {
                    throw RunException.optReject("自定义SQL表名不能与数据库已有表重名");
                }
            }
        }
        table = tableDao.save(param);
        if (selfTableCreate) {
            addSelfTableIdField(table);
        }
        return table;
    }

    /**
     * 为自建表生成主键ID字段
     *
     * @param table
     */
    private void addSelfTableIdField(DataStoreTableDO table) {
        DataStoreTableFieldDO idField = new DataStoreTableFieldDO();
        idField.setTableId(table.getId());
        idField.setCode(DataStoreSelf.ID_SQL_PARAMETER);
        idField.setName("ID");
        idField.setColumnName(DataStoreSelf.ID_SQL_PARAMETER);
        idField.setDataType(SimpleDataType.TEXT.name());
        idField.setLength(32);
        idField.setIdKey(BoolHelper.INT_TRUE);
        idField.setDbGenerated(BoolHelper.INT_FALSE);
        idField.setCreateTime(LocalDateTime.now());
        tableFieldDao.save(idField);
    }

    /**
     * 修改数据库表,只能修改模块、中文名、构建SQL
     */
    @Transactional
    public DataStoreTableDO modify(DataStoreTableDO param) {
        DataStoreTableDO tableDO = tableDao.getById(param.getId());
        if (tableDO == null) {
            throw RunException.optReject("没有找到要修改的表");
        }
        tableDO.setName(param.getName());
        tableDO.setBuildSql(param.getBuildSql());
        return tableDao.save(param);
    }

    /**
     * 修改数据表所属文件夹
     */
    @Transactional
    public void modifyFolder(String id, String folderId) {
        DataStoreTableDO tableDO = tableDao.getById(id);
        if (tableDO == null) {
            throw RunException.optReject("没有找到要修改的表");
        }
        // 修改所属文件夹
        tableDO.setFolderId(folderId);
        tableDao.save(tableDO);
    }

    /**
     * 删除表索引
     */
    @Transactional
    public void delete(String tableId) {
        DataStoreTableDO tableDO = tableDao.getById(tableId);
        if (tableDO == null) {
            return;
        }
        // 删除数据模型
        dataStoreEntityService.deleteByTable(tableId);
        // 删除表字段
        tableFieldDao.deleteByTableId(tableId);
        // 删除表
        if (DataStoreEntityType.SELF_BUILD.name().equals(tableDO.getType())) {
            dbService.dropTable(tableDO.getSourceId(), tableDO.getTableName());
        }
        tableDao.deleteById(tableId);
    }

    /**
     * 根据ID获取字段
     *
     * @param id
     * @return
     */
    public DataStoreTableFieldDO getFieldById(String id) {
        return tableFieldDao.getById(id);
    }

    /**
     * 表字段列表
     */
    public List<DataStoreTableFieldDO> listField(String tableId) {
        return tableFieldDao.findByTableIdOrderByCreateTimeAsc(tableId);
    }

    /**
     * 添加表字段
     */
    @Transactional
    public DataStoreTableFieldDO addField(DataStoreTableFieldDO fieldDO) {
        DataStoreTableDO tableDO = tableDao.getById(fieldDO.getTableId());
        if (!DataStoreEntityType.SELF_BUILD.name().equals(tableDO.getType())) {
            throw ReqException.paramValidate("只允许自建表添加表字段");
        }
        // 唯一性检查
        boolean existsCode = tableFieldDao.existsByTableIdAndCode(fieldDO.getTableId(), fieldDO.getCode());
        if (existsCode) {
            throw ReqException.paramValidate("添加表字段失败，字段编号已经存在");
        }
        boolean existsName = tableFieldDao.existsByTableIdAndName(fieldDO.getTableId(), fieldDO.getName());
        if (existsName) {
            throw ReqException.paramValidate("添加表字段失败，字段名称已经存在");
        }
        // 添加表字段虚列
        boolean success = dbService.createSelfTableColumn(tableDO, fieldDO);
        if (!success) {
            throw RunException.optReject("添加表字段虚列失败，请检查重试");
        }
        // 创建表字段记录
        fieldDO = tableFieldDao.save(fieldDO);
        // 推送字段到数据模型
        dataStoreEntityService.extendsTableField(fieldDO, true);

        return fieldDO;
    }

    /**
     * 载入字段，包括已有字段和数据库表物理字段
     *
     * @param tableId
     * @return
     */
    @Transactional
    public List<DataStoreTableFieldDO> loadField(String tableId) {
        DataStoreTableDO tableDO = tableDao.getById(tableId);
        DataStoreEntityType entityType = DataStoreEntityType.valueOf(tableDO.getType());
        List<DBColumn> dbColumns = switch (entityType) {
            case LOAD_BUILD -> dbService.tableColumns(tableDO.getSourceId(), tableDO.getTableName());
            case SQL_BUILD -> dbService.sqlColumns(tableDO.getSourceId(), tableDO.getBuildSql());
            default -> new ArrayList<>();
        };
        List<DataStoreTableFieldDO> fieldList = tableFieldDao.findByTableIdOrderByCreateTimeAsc(tableId);
        List<String> fieldColumns = fieldList.stream().map(DataStoreTableFieldDO::getColumnName).toList();
        for (DBColumn column : dbColumns) {
            if (!fieldColumns.contains(column.getName())) {
                DataStoreTableFieldDO fieldDO = DataStoreTableFieldConvert.buildDO(tableDO.getId(), column);
                // 检查名称是否存在
                String fieldName = fieldDO.getName();
                boolean existsName = tableFieldDao.existsByTableIdAndName(fieldDO.getTableId(), fieldName);
                if (existsName) {
                    // 随机补几位数字
                    fieldDO.setName(fieldName + CodeHelper.createSmsCode4());
                }
                fieldDO = tableFieldDao.save(fieldDO);
                fieldList.add(fieldDO);
                // 推送字段到数据模型
                dataStoreEntityService.extendsTableField(fieldDO, true);
            }
        }
        return fieldList;
    }

    /**
     * 修改表字段
     */
    @Transactional
    public DataStoreTableFieldDO modifyField(DataStoreTableFieldDO fieldDO) {
        // 表字段原数据
        DataStoreTableFieldDO oldFieldDO = tableFieldDao.getById(fieldDO.getId());
        // 逻辑检查
        DataStoreTableDO tableDO = tableDao.getById(fieldDO.getTableId());
        // 字段唯一变更校验,将一个非唯一字段变更为唯一字段
        if (!BoolHelper.intToBool(oldFieldDO.getUnique()) && BoolHelper.intToBool(fieldDO.getUnique())) {
            boolean canUnique = dbService.checkColumnUnique(tableDO, fieldDO.getColumnName());
            if (!canUnique) {
                throw RunException.optReject("列[" + fieldDO.getColumnName() + "]的值存在不唯一情况，不能将字段设置为唯一");
            }
        }
        // 名称唯一性检查
        if (!Objects.equals(oldFieldDO.getName(), fieldDO.getName())) {
            boolean existsName = tableFieldDao.existsByTableIdAndName(fieldDO.getTableId(), fieldDO.getName());
            if (existsName) {
                throw ReqException.paramValidate("修改表字段失败，字段名称已经存在");
            }
        }
        if (DataStoreEntityType.SELF_BUILD.name().equals(tableDO.getType())) {
            // 修改表字段虚列
            boolean success = dbService.modifySelfTableColumn(tableDO, fieldDO);
            if (!success) {
                throw RunException.optReject("修改表字段虚列失败，请检查重试");
            }
        }
        // 修改表字段记录
        fieldDO = tableFieldDao.save(fieldDO);
        // 推送字段到数据模型
        dataStoreEntityService.extendsTableField(fieldDO, false);
        return fieldDO;
    }

    /**
     * 删除表字段
     */
    @Transactional
    public void deleteField(String fieldId) {
        DataStoreTableFieldDO fieldDO = tableFieldDao.getById(fieldId);
        DataStoreTableDO tableDO = tableDao.getById(fieldDO.getTableId());
        if (DataStoreEntityType.SELF_BUILD.name().equals(tableDO.getType())) {
            // 删除虚列
            boolean success = dbService.deleteSelfTableColumn(tableDO, fieldDO);
            if (!success) {
                throw RunException.optReject("删除表字段虚列失败，请检查重试");
            }
        }
        // 删除数据模型字段
        dataStoreEntityService.deleteFieldByTable(fieldDO.getTableId(), fieldDO.getCode());
        // 删除表字段记录
        tableFieldDao.deleteById(fieldId);
    }

    /**
     * 获取字段备选项配置信息
     */
    public DataStoreTableFieldOptionDO getFieldOption(String fieldId) {
        return tableFieldOptionDao.findByFieldId(fieldId);
    }

    /**
     * 保存字段备选项配置信息
     */
    @Transactional
    public DataStoreTableFieldOptionDO saveFieldOption(DataStoreTableFieldOptionDO param) {
        DataStoreTableFieldOptionDO tableFieldOptionDO = tableFieldOptionDao.save(param);
        // 将表字段备选配置信息推送到模型字段
        dataStoreEntityService.extendsTableFieldOption(tableFieldOptionDO);
        return tableFieldOptionDO;
    }

    /**
     * 删除字段备选项配置信息
     */
    @Transactional
    public void deleteFieldOption(String fieldOptionId) {
        tableFieldOptionDao.deleteById(fieldOptionId);
    }

    /**
     * 表索引列表
     *
     * @param tableId 索引ID
     * @return 索引列表
     */
    public List<DataStoreTableIndexDO> listIndex(String tableId) {
        return tableIndexDao.findByTableId(tableId);
    }

    /**
     * 保存表索引
     */
    @Transactional
    public DataStoreTableIndexDO saveIndex(DataStoreTableDO tableDO, DataStoreTableIndexDO param) {
        if (param.getId() != null) {
            // 删除索引
            boolean success = dbService.deleteTableIndex(tableDO.getSourceId(), tableDO.getTableName(), param.getCode());
            if (!success) {
                throw RunException.optReject("删除表索引失败，请检查重试");
            }
        }
        // 检查本表的索引个数
        long tableIndexCount = tableIndexDao.countByTableId(param.getTableId());
        if (tableIndexCount > 16) {
            throw RunException.optReject("索引个数已经达到数据库上限");
        }
        List<DataStoreTableFieldDO> fieldDOList = (List<DataStoreTableFieldDO>) tableFieldDao.findAllById(param.getFieldIds());
        if (fieldDOList.isEmpty()) {
            throw RunException.optReject("索引字段不允许为空");
        }
        List<String> fieldColumnList = fieldDOList.stream().map(DataStoreTableFieldDO::getColumnName).toList();
        boolean success = dbService.createTableIndex(tableDO.getSourceId(), tableDO.getTableName(), param.getCode(), param.getType(), fieldColumnList);
        if (!success) {
            throw RunException.optReject("添加表索引失败，请检查重试");
        }
        return tableIndexDao.save(param);
    }

    /**
     * 删除表索引
     */
    @Transactional
    public void deleteIndex(String indexId) {
        DataStoreTableIndexDO tableIndexDO = tableIndexDao.getById(indexId);
        if (tableIndexDO == null) {
            return;
        }
        DataStoreTableDO tableDO = tableDao.getById(tableIndexDO.getTableId());
        if (tableDO == null) {
            return;
        }
        // 删除索引
        boolean success = dbService.deleteTableIndex(tableDO.getSourceId(), tableDO.getTableName(), tableIndexDO.getCode());
        if (!success) {
            throw RunException.optReject("删除表索引失败，请检查重试");
        }
        // 删除索引记录
        tableIndexDao.delete(tableIndexDO);
    }

    public DataStoreTableIndexDO getIndexById(String id) {
        return tableIndexDao.getById(id);
    }


    @Transactional
    public void sort(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        int sortNo = 1;
        for (String id : ids) {
            DataStoreTableDO tableDO = tableDao.getById(id);
            if (tableDO == null) {
                continue;
            }
            tableDO.setSortNo(sortNo++);
            tableDao.save(tableDO);
        }
    }

    public void checkEntityField(String tableId, String fieldCode) {
        List<DataStoreEntityDO> entityDOS = dataStoreEntityService.findByTableId(tableId);
        if (entityDOS != null) {
            for (DataStoreEntityDO entityDO : entityDOS) {
                boolean exist = dataStoreEntityService.existFieldByEntityCodeAndCode(entityDO.getCode(), fieldCode);
                if (exist) {
                    throw ReqException.paramValidate("数据模型[" + entityDO.getName() + "(" + entityDO.getCode() + ")]已存在此字段，请修改主表字段编号后重试");
                }
            }
        }
    }
}
