package com.deeppaas.flow.biz.service;

import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.AssertHelper;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.flow.biz.FlowAction;
import com.deeppaas.flow.biz.FlowConst;
import com.deeppaas.flow.biz.dao.FlowTaskDao;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import com.deeppaas.flow.biz.element.FlowNode;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class FlowTaskServiceImpl implements FlowTaskService{
	private FlowTaskDao flowTaskDao;

	public FlowTaskServiceImpl(FlowTaskDao flowTaskDao) {
		this.flowTaskDao = flowTaskDao;
	}

	@Override
	public FlowTaskDO getTask(Long taskId) {
		return flowTaskDao.getById(taskId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FlowTaskDO open(FlowTaskDO task) {
		flowTaskDao.updateTaskOpen(task.getId());
		task.setState(FlowConst.TASK_STATE_OPEN);
		return task;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FlowTaskDO close(FlowTaskDO task, String userId, FlowAction action, String comment) {
		LocalDateTime closeTime = LocalDateTime.now();
		int updated = flowTaskDao.updateTaskClose(task.getId(), action.name(), userId, closeTime);
		AssertHelper.isTrue(updated!=0, "任务已关闭");
		AssertHelper.notNull(task, "指定的任务不存在");
		task.setAction(action.name());
		task.setOperator(userId);
		task.setEndTime(closeTime);
		task.setState(FlowConst.TASK_STATE_CLOSE);
		// 如果是正常流转的，打开等待任务
		if(FlowAction.execute==action){
			FlowTaskDO waitTask = flowTaskDao.findByWaitTaskId(task.getId());
			if(waitTask!=null && FlowConst.TASK_STATE_WAIT==waitTask.getState()){
				flowTaskDao.updateTaskOpen(waitTask.getId());
			}
		}
		return task;
	}

	@Override
	public List<FlowTaskDO> findUnclosedTaskByInstanceId(Long instanceId) {
		return flowTaskDao.findByInstanceIdAndStateNot(instanceId, FlowConst.TASK_STATE_CLOSE);
	}

	@Override
	public Long findOpenTaskIdByInstanceIdForUser(Long instanceId, String userId) {
		if(instanceId==null|| !StringUtils.hasText(userId)){
			return null;
		}
		return flowTaskDao.findOpenTaskIdByInstanceIdForUser(instanceId, userId);
	}

	@Override
	public FlowTaskDO createTask(Long flowInstanceId, Long tokenId, FlowNode flowNode, LocalDateTime assignLimitedTime,
                                 List<String> todoUserIds, int taskState, int sortNo, long waitTaskId,int priority) {
		FlowTaskDO task = new FlowTaskDO();
		task.setInstanceId(flowInstanceId);
		task.setTokenId(tokenId);
		task.setNodeCode(flowNode.getCode());
		task.setNodeName(flowNode.getName());
		task.setStartTime(LocalDateTime.now());
		task.setEndTime(null);
		task.setType(0);
		task.setState(taskState);
		task.setPriority(priority);
		task.setAction("");
		task.setActors(todoUserIds);
		task.setLimitedTime(assignLimitedTime);
		task.setSortNo(sortNo);
		task.setWaitTaskId(waitTaskId);
		return flowTaskDao.save(task);
	}

	@Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByInstance(Long instanceId) {
        flowTaskDao.deleteByInstanceId(instanceId);
    }

	@Override
	public List<FlowTaskDO> findByInstanceIdAndNodeCode(Long instanceId, String nodeCode){
		return flowTaskDao.findByInstanceIdAndNodeCode(instanceId, nodeCode);
	}

	@Override
	public List<FlowTaskDO> findOpenTaskByInstanceIdForUser(Long instanceId, String userCode) {
		return flowTaskDao.findByInstanceIdAndActorAndState(instanceId, userCode, FlowConst.TASK_STATE_OPEN);
	}

	@Override
	public List<FlowTaskDO> findUnclosedTaskByTokenIds(List<Long> tokenIds) {
		if(CollectionUtils.isEmpty(tokenIds)){
			return new ArrayList<>();
		}
		return flowTaskDao.findByTokenIdIn(tokenIds);
	}

	@Override
	public List<FlowTaskDO> findOpenTaskByInstanceId(Long instanceId) {
		return flowTaskDao.findByInstanceIdAndState(instanceId, FlowConst.TASK_STATE_OPEN);
	}

	@Override
	public List<FlowTaskDO> findByInstanceId(Long instanceId) {
		return flowTaskDao.findByInstanceId(instanceId);
	}

	@Override
	public Page<FlowTaskDO> findForPage(PageSearch pageSearch) {
		JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
		return flowTaskDao.queryForPage(condition, pageSearch.getPageable());
	}
}
