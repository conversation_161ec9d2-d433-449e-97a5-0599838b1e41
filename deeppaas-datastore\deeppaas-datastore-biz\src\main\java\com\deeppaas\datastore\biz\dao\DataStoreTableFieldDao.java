package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreTableFieldDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/19
 */
public interface DataStoreTableFieldDao extends BaseJdbcDao<DataStoreTableFieldDO, String> {

    List<DataStoreTableFieldDO> findByTableIdOrderByCreateTimeAsc(String tableId);

    boolean existsByTableIdAndCode(String tableId, String code);

    boolean existsByTableIdAndName(String tableId, String name);

    @Modifying
    @Query("delete from datastore_table_field where table_id=:tableId")
    void deleteByTableId(@Param("tableId") String tableId);
}
