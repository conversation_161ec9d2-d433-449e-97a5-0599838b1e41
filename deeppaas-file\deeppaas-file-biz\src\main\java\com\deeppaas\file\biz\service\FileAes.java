package com.deeppaas.file.biz.service;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.security.Key;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
public class FileAes {
    /** 算法名称 */
    public static final String KEY_ALGORITHM = "AES";
    /** 算法名称/加密模式/填充方式 */
    public static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    /** 私钥 AES固定格式为128/192/256 bits.即：16/24/32bytes */
    private static final byte[] KEY = "xuxDRBwdSK0gR8iQ".getBytes(Charset.forName("UTF-8"));
    /** 初始化向量参数，AES 为16bytes */
    private static final byte[] IV = "pfV6rEIyYgcOvCh7".getBytes(Charset.forName("UTF-8"));

    /**
     * 加密
     * @param text
     * @return
     * <AUTHOR>
     * @date 2017年12月12日
     */
    public static String encrypt(String text){
        String result = null;
        try {
            Key keySpec = new SecretKeySpec(KEY, KEY_ALGORITHM);    	//两个参数，第一个为私钥字节数组， 第二个为加密方式 AES或者DES
            IvParameterSpec ivSpec = new IvParameterSpec(IV);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);		//实例化加密类，参数为加密方式，要写全
            cipher.init(Cipher.ENCRYPT_MODE,  keySpec, ivSpec);		//初始化，此方法可以采用三种方式，按服务器要求来添加。（1）无第三个参数（2）第三个参数为SecureRandom random = new SecureRandom();中random对象，随机数。(AES不可采用这种方法)（3）采用此代码中的IVParameterSpec
            byte [] b = cipher.doFinal(text.getBytes(Charset.forName("UTF-8")));				//加密操作,返回加密后的字节数组，然后需要编码。主要编解码方式有Base64, HEX, UUE, 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　7bit等等。此处看服务器需要什么编码方式
            result = Base64.getUrlEncoder().encodeToString(b);		//Base64、HEX等编解码
        }catch(Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 解密
     * @param textDeCipher
     * @return
     * <AUTHOR>
     * @date 2017年12月12日
     */
    public static String decrypt(String textDeCipher){
        String result = null;
        try {
            byte[] bytes = Base64.getUrlDecoder().decode(textDeCipher);	//先用Base64解码
            IvParameterSpec ivSpec = new IvParameterSpec(IV);
            Key key = new SecretKeySpec(KEY, KEY_ALGORITHM);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, key, ivSpec);               //与加密时不同MODE:Cipher.DECRYPT_MODE
            byte[] ret = cipher.doFinal(bytes);
            result = new String(ret, "utf-8");
        }catch(Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}
