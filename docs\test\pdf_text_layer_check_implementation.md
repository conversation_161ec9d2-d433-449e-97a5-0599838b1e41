# PDF文字层检查功能实现文档

## 概述
本文档记录了在RUYI项目中实现PDF文字层检查功能的详细过程，包括Java后端和前端的修改。

## 实现目标
在PDF文件质量审查项中，在"PDF与图片一致性审查"和"OFD一致性检查"之间添加"PDF文字层检查"选项，用户选中后参数值传递到Java端进行相应的检查。

## 修改内容

### 1. Java后端修改

#### 1.1 ImageChechRule.java 主要修改
- **文件路径**: `kpass-exactness-check/kpass-exactness-soa/src/main/java/com/deeppaas/rule/factory/impl/image/ImageChechRule.java`

**主要改动**:
1. **分离PDF检查逻辑**: 将原来的`checkPdf`方法拆分为两个独立的方法：
   - `checkPdfImageUniformity()`: 专门处理PDF图像一致性检查
   - `checkPdfTextLayer()`: 专门处理PDF文字层检查

2. **修改原有checkPdf方法**: 增加参数控制是否执行特定检查：
   ```java
   private TaskErrorResultDO[] checkPdf(ProjectTaskPdfDataDTO taskPdfData, 
                                       ProjectTaskImageDataDTO imageDataDO, 
                                       boolean checkImageUniformity, 
                                       boolean checkTextLayer)
   ```

3. **更新调用逻辑**: 在`aiCheck`方法中分别处理两种检查：
   ```java
   if (taskPdfData != null) {
       if (BoolHelper.boolToB(param.getPdfImageUniformity()) || BoolHelper.boolToB(param.getPdfHasTextCheck())) {
           checkPdf(taskPdfData, imageDataDO, 
                   BoolHelper.boolToB(param.getPdfImageUniformity()), 
                   BoolHelper.boolToB(param.getPdfHasTextCheck()));
           taskPdfData.setMateIs(BoolHelper.INT_TRUE);
           pdfDataService.save(taskPdfData);
       }
   }
   ```

#### 1.2 CheckImageHtmlResultParam.java 参数类
- **文件路径**: `kpass-exactness-check/kpass-exactness-api/src/main/java/com/deeppaas/rule/param/CheckImageHtmlResultParam.java`
- **修改**: 已包含`pdfHasTextCheck`字段，无需修改

### 2. 前端修改

#### 2.1 类型定义修改
- **文件路径**: `../SuperDG-web/src/typings/ruleMould.d.ts`

**添加字段**:
```typescript
declare interface FilesDisposeType {
  // ... 其他字段
  // PDF与图片一致性审查
  pdfImageUniformity: boolean
  // PDF文字层检查
  pdfHasTextCheck: boolean
  // ... 其他字段
}
```

#### 2.2 FilesDispose.tsx 组件修改
- **文件路径**: `../SuperDG-web/src/components/ruleModule/FilesDispose.tsx`

**主要修改**:
1. **defaultValue对象**: 添加新字段默认值
   ```typescript
   const defaultValue: FilesDisposeType = {
     // ... 其他字段
     pdfImageUniformity: false,
     pdfHasTextCheck: false,
     // ... 其他字段
   }
   ```

2. **allValue对象**: 添加全选时的值
   ```typescript
   const allValue = {
     // ... 其他字段
     pdfImageUniformity: true,
     pdfHasTextCheck: true,
     // ... 其他字段
   }
   ```

3. **useEffect处理**: 添加向后兼容性处理
   ```typescript
   json.pdfHasTextCheck = json.pdfHasTextCheck === undefined? 
                         defaultValue.pdfHasTextCheck : json.pdfHasTextCheck
   ```

4. **UI组件**: 在PDF文件质量审查部分添加新选项
   ```tsx
   <div className=" text-[#323233] font-bold mt-6 mb-2">PDF文件质量审查</div>
   <div className="flex">
     <FormItem name="pdfImageUniformity" className="" valuePropName="checked">
       <Checkbox>PDF与图片一致性审查</Checkbox>
     </FormItem>
     <FormItem name="pdfHasTextCheck" className="" valuePropName="checked">
       <Checkbox>PDF文字层检查</Checkbox>
     </FormItem>
     <FormItem name="ofdUniformity" className="" valuePropName="checked">
       <Checkbox>OFD一致性检查</Checkbox>
     </FormItem>
   </div>
   ```

## 功能说明

### PDF文字层检查逻辑
1. **检查条件**: 用户在前端选中"PDF文字层检查"选项
2. **检查过程**: 
   - 遍历PDF文档的每一页
   - 使用PDFTextStripper提取文本内容
   - 检查文本内容是否为空
3. **错误记录**: 如果某页没有文字层或内容为空，记录错误信息

### 参数传递流程
1. **前端**: 用户选择选项 → 表单数据收集
2. **传输**: JSON格式传递到后端
3. **后端**: 解析参数 → 执行相应检查逻辑

## 测试建议

### 前端测试
1. 验证"PDF文字层检查"选项是否正确显示
2. 测试选中/取消选中功能
3. 验证全选功能是否包含新选项
4. 测试表单提交时参数是否正确传递

### 后端测试
1. 测试只选择PDF图像一致性检查
2. 测试只选择PDF文字层检查
3. 测试同时选择两个选项
4. 测试都不选择的情况
5. 验证错误信息是否正确记录

### 集成测试
1. 准备包含文字层的PDF文件
2. 准备不包含文字层的PDF文件
3. 验证检查结果的准确性

## 注意事项
1. 保持向后兼容性，旧数据中没有pdfHasTextCheck字段时使用默认值
2. 确保UI布局在添加新选项后仍然美观
3. 错误信息应该清晰明确，便于用户理解

## 完成状态
- [x] Java后端逻辑修改
- [x] 前端类型定义更新
- [x] 前端UI组件添加
- [x] 参数传递逻辑完善
- [ ] 功能测试验证
- [ ] 集成测试验证
