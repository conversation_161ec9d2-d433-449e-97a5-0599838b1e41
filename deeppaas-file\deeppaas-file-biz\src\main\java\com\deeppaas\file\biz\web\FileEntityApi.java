package com.deeppaas.file.biz.web;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.file.FileConstant;
import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.api.model.UploadSign;
import com.deeppaas.file.desk.model.DeskFileSign;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
@RestController
@RequestMapping("/api/file")
@RequestAuth
public class FileEntityApi {
    private final FileClient fileClient;

    public FileEntityApi(FileClient fileClient) {
        this.fileClient = fileClient;
    }

    /**
     * 获取文件静态访问地址
     *
     * @param: fileGroup 文件组名
     * @param: fileKey  文件key
     * @param: fileAlias    文件别名
     * @param: process  附加处理
     * @return: com.lh.deeppaas.common.model.RestModel<java.lang.String>
     * <AUTHOR> @date
     **/
    @GetMapping(value = "/getUrl")
    RestModel<String> getUrl(@RequestParam String fileGroup, @RequestParam String fileKey,
                             @RequestParam(required = false) String fileAlias,
                             @RequestParam(required = false) String process) {
        String tempUrl = fileClient.getTempUrl(fileGroup, fileKey, fileAlias, process);
        return RestModel.data(tempUrl);
    }

    /**
     * 载入文件，直接重定向到文件地址
     *
     * @param: fileGroup 文件组
     * @param: fileKey  文件key
     * @param: fileAlias    别名
     * @param: process  附加处理
     * <AUTHOR>
     * @date 2020/8/20 10:13 上午
     **/
    @GetMapping(value = "/load")
    ModelAndView load(@RequestParam String fileGroup, @RequestParam String fileKey,
                      @RequestParam(required = false) String fileAlias,
                      @RequestParam(required = false) String process) {
        String tempUrl = fileClient.getTempUrl(fileGroup, fileKey, fileAlias, process);
        return new ModelAndView("redirect:" + tempUrl);
    }

    /**
     * 获取临时上传授权签名
     *
     * @param: fileGroup
     * @param: dir
     * @return: com.lh.deeppaas.common.model.RestModel<com.lh.deeppaas.file.model.UploadSign>
     * <AUTHOR> @date
     **/
    @GetMapping(value = "/getUploadSign")
    RestModel<UploadSign> getUploadSign(@RequestParam String fileGroup,
                                        @RequestParam(defaultValue = "") String dir) {
        UploadSign uploadSign = fileClient.getUploadSign(fileGroup, dir);
        return RestModel.data(uploadSign);
    }

    /**
     * 根据签名授权上传文件
     *
     * <AUTHOR>
     * @date 2020/7/16 10:41 上午
     **/
    @PostMapping(value = "/signUpload")
    RestModel<String> uploadFile(@RequestParam String fileGroup,
                                 @RequestParam(defaultValue = "") String dir,
                                 @RequestParam String expireTime,
                                 @RequestParam("file") MultipartFile file,
                                 @RequestParam String sign) {
        FileGroup fileGroupDO = fileClient.getFileGroup(fileGroup);
        if(FileConstant.MODE_OSS.equalsIgnoreCase(fileGroupDO.getMode())){
            throw RunException.error("OSS文件组请直接使用OSS提供的API");
        }else if(FileConstant.MODE_DESK.equalsIgnoreCase(fileGroupDO.getMode())){
            boolean signCheck = DeskFileSign.checkUploadSign(fileGroup, dir, expireTime, sign);
            if (signCheck) {
                String fileKey = dir + file.getOriginalFilename();
                try {
                    InputStream inputStream = file.getInputStream();
                    fileClient.uploadFile(fileGroup, fileKey, inputStream);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return RestModel.success("上传成功");
    }

//    /**
//     *
//     * @param fileGroup 文件组
//     * @param fileKey   文件KEY
//     * @param fileAlias 文件别名
//     * @param expireTime    过期时间
//     * @param sign  签名
//     */
//    public void get(@RequestParam String fileGroup, @RequestParam String fileKey,
//                    @RequestParam(required = false) String fileAlias,
//                    @RequestParam(required = false) String process,
//                    @RequestParam Long expireTime,
//                    @RequestParam String sign, HttpServletResponse response){
//        DeskTempUrlReq tempUrlReq = new DeskTempUrlReq(fileGroup, fileKey, fileAlias, process, expireTime, sign);
//        FileEntity file = fileClient.getFile(fileGroup, fileKey);
//        FileGroup group = fileClient.getFileGroup(fileGroup);
//        if(group.isPublic()||DeskFileSign.checkTempUrlSign(tempUrlReq)){
//            InputStream input = file.getContent();
//            OutputStream out = null;
//            try{
//                out = response.getOutputStream();
//                byte[] buff =new byte[1024];
//                int index=0;
//                while((index= input.read(buff))!= -1){
//                    out.write(buff, 0, index);
//                    out.flush();
//                }
//            }catch (IOException ex){
//                LogerHelper.errorLog(logger, ex);
//            }finally {
//                try {
//                    if(input!=null){
//                        input.close();
//                    }
//                    out.close();
//                } catch (IOException e) {
//                    LogerHelper.errorLog(logger, e);
//                }
//            }
//        }
//    }
}
