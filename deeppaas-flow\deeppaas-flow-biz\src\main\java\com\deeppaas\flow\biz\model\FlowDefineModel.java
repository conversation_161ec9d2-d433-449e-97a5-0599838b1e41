package com.deeppaas.flow.biz.model;

import java.util.*;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.flow.biz.element.EndNode;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.element.FlowEventRule;
import com.deeppaas.flow.biz.element.StartNode;
import com.deeppaas.flow.biz.entity.FlowDefineDO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @ClassName: FlowDefineModel 
 * @Description: 流程定义模型
 * <AUTHOR>
 */
@Data
public class FlowDefineModel {
	private static final long serialVersionUID = -6622662717227484748L;
	private String id;			//主键ID
	private String code;		//关键字（所有版本编号相同）
	private String name;		//名称
	private String entityCode;	//数据模型
	private Integer version;	//版本
	private String defJson;		//定义JSON
	private String attachment;	//附件
	private String flowMessage;	//通知消息设置
	private Map<String, FlowNode> nodes = new HashMap<String, FlowNode>();	//流程定义中的节点信息

	protected List<FlowEventRule> eventRules = new ArrayList<FlowEventRule>();
	public FlowDefineModel(FlowDefineDO define) {
		this.id = define.getId();
		this.code = define.getCode();
		this.name = define.getName();
		this.entityCode = define.getEntityCode();
		this.version = define.getVersion();
		this.defJson = define.getDefJson();
		this.attachment = define.getAttachment();
		this.flowMessage = define.getFlowMessage();
	}

	@JsonIgnore
	//保存跳转后转json异常，前台未用到此方法属性，取消转化
	public StartNode getStartNode(String code) {
		for(FlowNode node : nodes.values()){
			if(node instanceof StartNode startNode && Objects.equals(code, node.getCode())){
				return startNode;
			}
		}
		throw RunException.dataNotFound("指定的流程定义[pdid=" + id  + "]开始环节没有找到");
	}

	public FlowNode getNode(String code) {
		for(FlowNode node : nodes.values()){
			if(code.equals(node.getCode())){
				return node;
			}
		}
		return null;
	}

	public List<String> check() {
		List<String> errors = new ArrayList<>();
		boolean startExist = false;
		boolean endExist = false;
		for(FlowNode node : nodes.values()){
			errors.addAll(node.check());
			if(node instanceof StartNode) {
				startExist = true;
			}
			if(node instanceof EndNode){
				endExist = true;
			}
		}
		if(!startExist){
			errors.add("没有开始环节");
		}
		if(!endExist){
			errors.add("没有结束环节");
		}
		return errors;
	}
}
