package com.deeppaas.process.biz.web;

import com.deeppaas.process.biz.entity.AidosProcessTaskDO;
import com.deeppaas.process.biz.service.AidosProcessTaskService;
import com.deeppaas.template.biz.entity.AidosImageTemplateDO;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/processTask")
public class AidosProcessTaskApi {


    @Autowired
    private AidosProcessTaskService aidosProcessTaskService;

    @PostMapping("/page")
    public RestModel<PageData<AidosProcessTaskDO>> page(@RequestBody PageSearch pageSearch) {
        PageData<AidosProcessTaskDO> list = aidosProcessTaskService.page(pageSearch);
        return RestModel.data(list);
    }

    @GetMapping("/getAll")
    public RestModel<List<AidosProcessTaskDO>> list() {
        List<AidosProcessTaskDO> list = aidosProcessTaskService.getAll();
        return RestModel.data(list);
    }

    @PostMapping("/save")
    public RestModel list(@RequestBody AidosProcessTaskDO aidosImageTemplateDO) {
        String message = aidosProcessTaskService.save(aidosImageTemplateDO);
        if (StringHelper.isNotEmpty(message)) {
            return RestModel.error(500, message);
        }
        return RestModel.success("保存成功");
    }

    @GetMapping("/del")
    public RestModel list(@RequestParam String id) {
        aidosProcessTaskService.del(id);
        return RestModel.success("删除成功");
    }

    @GetMapping("/getById")
    public RestModel<AidosProcessTaskDO> getById(@RequestParam String id) {
        AidosProcessTaskDO model = aidosProcessTaskService.get(id);
        return RestModel.data(model);
    }

    @GetMapping("/getTemplate")
    public RestModel getTemplate(@RequestParam String processTaskId, @RequestParam(value = "type",required = false) String type) {
        List<AidosImageTemplateDO> lists = aidosProcessTaskService.getTemplate(processTaskId,type);
        return RestModel.data(lists);
    }

}
