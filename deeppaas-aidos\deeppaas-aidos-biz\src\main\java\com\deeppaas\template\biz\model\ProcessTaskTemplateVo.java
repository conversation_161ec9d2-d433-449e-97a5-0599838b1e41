package com.deeppaas.template.biz.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Data
public class ProcessTaskTemplateVo {

    private String id;
    private String imageBase;
    private String name;
    private String label;
    private List<ProcessTaskTemplateType> types;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ProcessTaskTemplateType{
        private String typeId;
        private String typeName;
    }

    public void setProcessTaskTemplateType(String id,String type){
        if(CollectionUtils.isEmpty(types)){
            types= Lists.newArrayList();
        }
        ProcessTaskTemplateType processTaskTemplateType=new ProcessTaskTemplateType();
        processTaskTemplateType.setTypeId(id);
        processTaskTemplateType.setTypeName(type);
        types.add(processTaskTemplateType);
    }
}
