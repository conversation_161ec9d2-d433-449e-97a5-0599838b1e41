package com.deeppaas.common.helper;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 布尔工具
 * @date 2019-01-17
 */
public class BoolHelper {
    /**
     * 数字类型TRUE
     */
    public static final int INT_TRUE = 1;
    /**
     * 数字类型FALSE
     */
    public static final int INT_FALSE = 0;

    /**
     * @param intBool
     * @return boolean
     * @Description: 判断数字是否
     * <AUTHOR>
     * @date 2019-01-17
     */
    public static boolean intToBool(Integer intBool) {
        return (Objects.equals(INT_TRUE, intBool));
    }

    /**
     * 检查是否为真
     *
     * @param value 布尔值
     * @return 布尔值
     */
    public static boolean isTrue(Boolean value) {
        return null == value ? false : value;
    }

    /**
     * 返回是非数字
     * @param value
     * @return
     * <AUTHOR>
     * @date 2020-05-21
     */
    public static int boolToInt(Boolean value){
        if(value != null && value){
            return INT_TRUE;
        }
        return INT_FALSE;
    }

    public static Boolean boolToB(Boolean value){
        if(value != null && value){
            return true;
        }
        return false;
    }
    /**
     * 判断 是否为假值
     * 返回真值
     *
     */

    public static boolean isFalse(Boolean value){
        if(value != null && !value){
            return true;
        }
        return false;
    }

}
