package com.deeppaas.flow.biz.api;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.flow.biz.FlowConst;
import com.deeppaas.flow.biz.entity.FlowDefineDO;
import com.deeppaas.flow.biz.model.param.FlowDefineParam;
import com.deeppaas.flow.biz.service.FlowDefineService;
import com.deeppaas.flow.biz.service.FlowPageService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程定义接口
 *
 * <AUTHOR>
 * @date 2022/5/5
 */
@RestController
@RequestMapping("/api/flow/define")
public class FlowDefineApi {
    private final FlowDefineService flowDefineService;

    public FlowDefineApi(FlowDefineService flowDefineService) {
        this.flowDefineService = flowDefineService;
    }

    /**
     * 获取指定ID
     *
     * @param id
     * @return
     */
    @GetMapping(path = "/get", params = "id")
    public RestModel<FlowDefineDO> getById(String id) {
        FlowDefineDO flowDefineDO = flowDefineService.get(id);
        return RestModel.data(flowDefineDO);
    }

    /**
     * 获取指定编号流程定义
     *
     * @param code
     * @return
     */
    @GetMapping(path = "/get", params = "code")
    public RestModel<FlowDefineDO> get(String code) {
        FlowDefineDO flowDefineDO = flowDefineService.findByCodeAndState(code, FlowConst.DEFINE_STATE_EDIT);
        return RestModel.data(flowDefineDO);
    }

    /**
     * 获取指定编号流程定义
     *
     * @return
     */
    @GetMapping("/list")
    public RestModel<List<FlowDefineDO>> list(String code) {
        List<FlowDefineDO> list = flowDefineService.listByCode(code);
        return RestModel.data(list);
    }

    /**
     * 根据模型获取流程定义
     *
     * @param entity
     * @return
     */
    @GetMapping("/listByEntity")
    public RestModel<List<FlowDefineDO>> listByEntity(String entity) {
        List<FlowDefineDO> list = flowDefineService.listByEntity(entity);
        return RestModel.data(list);
    }

    /**
     * 保存流程定义
     *
     * @param flowDefineParam
     * @return
     */
    @PostMapping("/save")
    public RestModel<FlowDefineDO> save(@RequestBody FlowDefineParam flowDefineParam) {
        FlowDefineDO flowDefineDO = flowDefineService.save(flowDefineParam);
        return RestModel.data(flowDefineDO);
    }

    /**
     * 发布
     *
     * @param flowDefineParam
     * @return
     */
    @PostMapping("/issue")
    public RestModel<FlowDefineDO> issue(@RequestBody FlowDefineParam flowDefineParam) {
        FlowDefineDO flowDefineDO = flowDefineService.issue(flowDefineParam);
        return RestModel.data(flowDefineDO);
    }

    /**
     * 删除
     *
     * @param id 主键ID
     * @return
     */
    @PostMapping("/delete")
    public RestModel<Boolean> delete(@BodyParam String id) {
        flowDefineService.delete(id);
        return RestModel.data(true);
    }

    /**
     * 复制流程定义
     *
     * @param id 主键ID
     */
    @PostMapping("/copy")
    public RestModel<FlowDefineDO> copy(@BodyParam String id) {
        FlowDefineDO flowDefineDO = flowDefineService.copy(id);
        return RestModel.data(flowDefineDO);
    }
}
