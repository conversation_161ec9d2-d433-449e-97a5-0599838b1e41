package com.deeppaas.flow.biz;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.element.ForkNode;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowTokenDO;
import com.deeppaas.flow.biz.model.FlowDefineModel;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
public class MockExecuteItem extends ExecuteItem{

    public MockExecuteItem(FlowEngine engine, FlowDefineModel define, FlowInstanceDO instance, FlowTokenDO token, FlowNode node, FlowAction action, String operator, String comment) {
        super(engine, define, instance, token, node, action, operator, comment);
    }

    public MockExecuteItem subExecuteItem(ForkNode forkNode) {
        FlowTokenDO childToken = new FlowTokenDO();
        childToken.setInstanceId(this.getInstance().getId());
        childToken.setNodeCode(this.getNode().getCode());
        childToken.setUsed(BoolHelper.INT_TRUE);
        childToken.setParentId(this.getToken().getId());
        return new MockExecuteItem(
                this.getEngine(), this.getDefine(),
                this.getInstance(),childToken,
                forkNode, this.getAction(),
                null,null);
    }
}
