package com.deeppaas.extapi.biz.web;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.HashHelper;
import com.deeppaas.common.helper.IPHelper;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.entity.ExtApiUserDO;
import com.deeppaas.extapi.biz.enums.ApiAuthType;
import com.deeppaas.extapi.biz.service.*;
import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.rule.api.client.RuleClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 供第三方调用的公开接口
 * <AUTHOR>
 * @date 2022/6/22
 */
@RequestAuth(auth = false)
@RestController
@RequestMapping("/openApi")
public class ExtApiOpenApi {
    private final ExtApiService extApiService;

    private final ExtApiParamResolver extApiParamResolver;
    private final ExtApiUserService extApiUserService;

    private final RuleClient ruleClient;

    private final FileClient fileClient;
    private final SelfApiDispatcher selfApiDispatcher;

    public ExtApiOpenApi(ExtApiService extApiService, ExtApiParamResolver extApiParamResolver, ExtApiUserService extApiUserService, RuleClient ruleClient, FileClient fileClient, SelfApiDispatcher selfApiDispatcher) {
        this.extApiService = extApiService;
        this.extApiParamResolver = extApiParamResolver;
        this.extApiUserService = extApiUserService;
        this.ruleClient = ruleClient;
        this.fileClient = fileClient;
        this.selfApiDispatcher = selfApiDispatcher;
    }

    /**
     * 对外调用接口
     * @param apiId
     * @param userId
     * @param request
     * @return
     */
    @RequestMapping("/{apiId}/{userId}")
    public Object execute(@PathVariable String apiId, @PathVariable String userId,
                          HttpServletRequest request, HttpServletResponse response) {
        ExtApiDO extApiDO = extApiService.get(apiId);
        List<ExtApiParamDO> apiParams = extApiService.listParam(apiId);
        Map<String, Object> reqParams = extApiParamResolver.resolveOpenApiParams(extApiDO, apiParams, request);
        // 检查授权
        boolean authCheck = authCheck(apiId, apiParams, reqParams, userId, request);
        // todo 检查授权
        if(extApiDO.isSelfApi()){
            if(BoolHelper.intToBool(extApiDO.getSelfApi())){
                selfApiDispatcher.forward(extApiDO, request, response);
                return null;
            }else {
                return ruleClient.executeById(extApiDO.getRuleId(), reqParams);
            }
        }else {
            ThirdApiClient thirdApiClient = new ThirdApiClient(extApiDO, apiParams, fileClient);
            Object result = thirdApiClient.execute(reqParams);
            return result;
        }
    }

    private boolean authCheck(String apiId, List<ExtApiParamDO> apiParams, Map<String, Object> reqParams, String userId, HttpServletRequest request) {
        boolean hasRight = extApiUserService.existByApiIdAndUserId(apiId, userId);
        if(hasRight==false){
            return false;
        }
        ExtApiUserDO apiUser = extApiUserService.get(userId);
        ApiAuthType apiAuthType = ApiAuthType.valueOf(apiUser.getCallAuthType());
        if(apiAuthType==null){
            return false;
        }
        switch (apiAuthType){
            case IP -> {
                String reqIp = IPHelper.getIpAddr(request);
                String ips = apiUser.getCallAuthInfo();
                return IPHelper.match(ips, reqIp);
            }
            case SECRET -> {
                String sign = request.getHeader("sign");
                List<ExtApiParamDO> signParams = apiParams.stream()
                        .filter(apiParam -> BoolHelper.intToBool(apiParam.getRight()))
                        .sorted(Comparator.comparing(ExtApiParamDO::getCode)).toList();
                StringBuilder signBuilder = new StringBuilder();
                for(ExtApiParamDO signParam : signParams){
                    signBuilder.append(signParam.getCode()).append("=").append(reqParams.get(signParam.getCode())).append("&");
                }
                signBuilder.append(apiUser.getCallAuthInfo());
                String encodedSign = HashHelper.sha256(signBuilder.toString());
                return Objects.equals(encodedSign, sign);
            }
            case OAUTH2 -> {
                throw RunException.error("暂未支持OAUTH2方式授权");
            }
        }
        return false;
    }

}
