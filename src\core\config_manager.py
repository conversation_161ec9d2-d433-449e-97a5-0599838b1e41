"""
配置管理器 - 管理系统配置
"""
import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None, **kwargs):
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = self._get_default_config()
        
        # 加载配置文件
        if config_path:
            self.config = self._load_config_file(config_path)
        else:
            self.config = self.default_config.copy()
        
        # 应用额外参数
        if kwargs:
            self._update_config_recursive(self.config, kwargs)
        
        self.logger.info("配置管理器初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 设备配置 - RTX 3060 (12GB)
            'device': {
                'force_mode': 'single',  # RTX 3060单卡模式
                'min_memory_per_gpu': 12288,  # RTX 3060 12GB显存
                'gpu_name': 'RTX 3060',
                'architecture': 'Ampere',
                'compute_capability': '8.6'
            },
            
            # 模型配置
            'models': {
                'load_strategy': 'lazy',  # lazy, eager, dynamic
                
                'ocr': {
                    'engine_type': 'rapidocr',  # 使用RapidOCR引擎
                    'model_name': 'RapidOCR',
                    'memory_usage': 600,  # MB - RapidOCR内存使用更少
                    'device_id': 0,
                    'config': {
                        'use_angle_cls': True,
                        'lang': 'ch',
                        'det_limit_side_len': 960,
                        'rec_batch_num': 6,
                        'force_cpu': False,  # 启用GPU加速
                        # PP-OCRv5配置 - 默认使用Mobile版本（46%性能提升）
                        'ocr_version': 'ppocrv5',  # ppocrv4 | ppocrv5
                        'model_type': 'mobile'     # mobile | server
                    }
                },
                
                'structure': {
                    'model_name': 'PP-Structure',
                    'memory_usage': 500,  # MB
                    'config': {
                        'layout_model_dir': None,
                        'table_model_dir': None
                    }
                },
                
                'table': {
                    'model_name': 'PP-Table',
                    'memory_usage': 400,  # MB
                    'config': {
                        'table_model_dir': None
                    }
                },
                
                'embedding': {
                    # 'model_name': 'dengcao/qwen3-embedding-4b:q4_k_m',  # 实际使用的Qwen3 4B embedding
                    # 'memory_usage': 5100,  # MB (4B模型量化后实际占用)
                    'model_name': 'dengcao/qwen3-embedding-0.6b:f16',  # 实际使用的Qwen3 4B embedding
                    'memory_usage': 3000,  # MB (0.6B模型实际占用3GB)
                    'config': {
                        'model_path': None,
                        'max_length': 32768,  # 32K context length
                        'batch_size': 4,  # 减少批量大小以节省内存
                        'embedding_dim': 2560  # Qwen 4B embedding维度
                    }
                },
                
                'llm': {
                    # 'model_name': 'qwen3:4b',  
                    # 'memory_usage': 5200,
                    'model_name': 'qwen3:1.7b', # 使用轻量级Qwen3 1.7B模型
                    'memory_usage': 2500,  # MB (1.7B模型内存需求更小)
                    'config': {
                        'model_path': None,
                        'max_length': 2048,
                        'temperature': 0.1,
                        'top_p': 0.9,
                        'do_sample': True
                    }
                }
            },
            
            # 图像预处理配置
            'image_preprocessing': {
                'enabled': True,                    # 是否启用图像预处理
                'target_long_side': 800,           # 目标长边像素（通用文档优化）
                'jpeg_quality': 90,                # JPEG压缩质量
                'enhance_contrast': 1.15,          # 对比度增强倍数
                'enhance_sharpness': 1.05,         # 锐度增强倍数
                'remove_exif': True,               # 是否移除EXIF数据
                'enable_denoising': False,         # 是否启用去噪（扫描文档通常不需要）
                'min_resize_threshold': 800,       # 最小缩放阈值 - 超过800就缩放
                'memory_limit_mb': 500,            # 内存使用限制(MB)
                'output_format': 'JPEG',           # 输出格式
                'enable_stats': True,              # 是否启用性能统计

                # 印章处理配置
                'stamp_processing': {
                    'enabled': True,                            # 启用印章处理功能
                    'model_name': 'PP-OCRv4_mobile_seal_det',   # 使用PP-OCRv4_mobile_seal_det
                    'confidence_threshold': 0.5,               # 印章检测置信度阈值（较低阈值）
                    'enhancement_methods': ['redblue_removal'], # 处理方法：红蓝色移除
                    'create_multiple_versions': True,          # 是否创建多版本图像供OCR选择
                    'enable_stats': True,                      # 是否启用印章处理统计
                },
            },

            # 流水线配置
            'pipeline': {
                'max_workers': 2,
                'timeout': 300,  # 秒
                'enable_cache': True,
                'cache_size': 100
            },
            
            # 提取配置
            'extraction': {
                'default_options': {
                    'confidence_threshold': 0.7,
                    'max_retries': 2,
                    'enable_multi_strategy': True
                },
                
                # 档案专用配置
                'archive': {
                    'fusion_rules': {
                        '题名': {
                            'preferred_regions': ['title', 'header'],
                            'position_weights': {'top': 0.8, 'center': 0.6},
                            'semantic_keywords': ['题名', '标题', '名称']
                        },
                        '责任者': {
                            'preferred_regions': ['text', 'signature'],
                            'position_weights': {'bottom': 0.8, 'right': 0.6},
                            'semantic_keywords': ['单位', '机构', '责任者', '发文']
                        },
                        '文号': {
                            'preferred_regions': ['text', 'header'],
                            'pattern_matching': r'[\w\d]+号|[\w\d]+字',
                            'semantic_keywords': ['文号', '编号', '字号']
                        },
                        '成文日期': {
                            'preferred_regions': ['text', 'footer'],
                            'pattern_matching': r'\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?',
                            'semantic_keywords': ['日期', '时间', '成文日期']
                        }
                    }
                }
            },
            
            # 系统配置
            'system': {
                'auto_initialize': True,
                'max_file_size': 50 * 1024 * 1024,  # 50MB
                'supported_formats': [
                    '.jpg', '.jpeg', '.png', '.bmp', '.tiff',
                    '.pdf', '.doc', '.docx'
                ],
                'log_level': 'INFO',
                'enable_performance_monitoring': True
            }
        }
    
    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            self.logger.warning(f"配置文件不存在: {config_path}, 使用默认配置")
            return self.default_config.copy()
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    file_config = yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            # 合并配置
            merged_config = self.default_config.copy()
            self._update_config_recursive(merged_config, file_config)
            
            self.logger.info(f"配置文件加载成功: {config_path}")
            return merged_config
            
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            self.logger.warning("使用默认配置")
            return self.default_config.copy()
    
    def _update_config_recursive(self, base_config: Dict[str, Any], update_config: Dict[str, Any]):
        """递归更新配置"""
        for key, value in update_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config_recursive(base_config[key], value)
            else:
                base_config[key] = value
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.config.copy()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置节"""
        return self.config.get(section, {}).copy()
    
    def get_value(self, key_path: str, default: Any = None) -> Any:
        """获取配置值（支持点分隔的路径）"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def update_config(self, update_dict: Dict[str, Any]):
        """更新配置"""
        self._update_config_recursive(self.config, update_dict)
        self.logger.info("配置已更新")
    
    def set_value(self, key_path: str, value: Any):
        """设置配置值（支持点分隔的路径）"""
        keys = key_path.split('.')
        config = self.config
        
        # 导航到最后一级的父节点
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        self.logger.info(f"配置值已设置: {key_path} = {value}")
    
    def save_config(self, output_path: str):
        """保存配置到文件"""
        output_path = Path(output_path)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"配置已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"配置保存失败: {e}")
            raise
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        issues = []
        
        # 验证模型配置
        models_config = self.config.get('models', {})
        
        # 检查必需的模型
        required_models = ['ocr', 'embedding', 'llm']
        for model_type in required_models:
            if model_type not in models_config:
                issues.append(f"缺少必需的模型配置: {model_type}")
        
        # 验证显存配置
        total_memory_usage = 0
        for model_type, model_config in models_config.items():
            if isinstance(model_config, dict):
                memory_usage = model_config.get('memory_usage', 0)
                total_memory_usage += memory_usage
        
        min_memory = self.config.get('device', {}).get('min_memory_per_gpu', 4096)
        if total_memory_usage > min_memory:
            issues.append(f"模型总显存需求 ({total_memory_usage}MB) 超过最小显存 ({min_memory}MB)")
        
        # 验证文件路径
        system_config = self.config.get('system', {})
        max_file_size = system_config.get('max_file_size', 0)
        if max_file_size <= 0:
            issues.append("max_file_size必须大于0")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'total_memory_usage': total_memory_usage
        }
    
    def get_gpu_optimized_config(self, gpu_memory_mb: int) -> Dict[str, Any]:
        """根据GPU显存获取优化配置"""
        optimized_config = self.config.copy()
        
        if gpu_memory_mb <= 12288: #6144:  # 6GB
            # 6GB显存不足以运行4B embedding，使用轻量级LLM模型
            optimized_config['models']['embedding']['model_name'] = 'dengcao/qwen3-embedding-0.6b:f16'
            optimized_config['models']['embedding']['memory_usage'] = 2500
            optimized_config['models']['llm']['model_name'] = 'qwen3:4b'# 'qwen3:1.7b'
            optimized_config['models']['llm']['memory_usage'] = 3000
            
        elif gpu_memory_mb <= 16448: #12288:  # RTX 3060 12GB
            # RTX 3060优化配置 - 充分利用12GB显存
            optimized_config['models']['embedding']['model_name'] = 'dengcao/qwen3-embedding-4b:q4_k_m'
            optimized_config['models']['embedding']['memory_usage'] = 5100  # 4B embedding模型实际占用
            optimized_config['models']['llm']['model_name'] = 'qwen3:4b'# 'qwen3:1.7b'
            optimized_config['models']['llm']['memory_usage'] = 5200  # 1.7B LLM模型实际占用
            # OCR和印章检测也可以使用GPU
            optimized_config['models']['ocr'] = {
                'device': 'gpu:0',
                'memory_usage': 2000
            }
            optimized_config['models']['seal_detection'] = {
                'device': 'gpu:0',
                'memory_usage': 1500
            }
            
        else:  # >12GB
            # 使用大模型配置
            optimized_config['models']['embedding']['model_name'] = 'dengcao/qwen3-embedding-4b:q4_k_m'
            optimized_config['models']['embedding']['memory_usage'] = 5100
            optimized_config['models']['llm']['model_name'] = 'qwen3:4b'# 'qwen3:1.7b'  # 统一使用qwen3:1.7b
            optimized_config['models']['llm']['memory_usage'] = 3000
        
        return optimized_config
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ConfigManager(sections={list(self.config.keys())})"
