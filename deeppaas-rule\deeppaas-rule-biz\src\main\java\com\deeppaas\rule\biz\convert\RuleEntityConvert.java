package com.deeppaas.rule.biz.convert;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.datastore.api.client.model.DataStoreEntityCO;
import com.deeppaas.rule.api.model.RuleEntityPropertyPO;
import com.deeppaas.rule.biz.entity.RuleEntityDO;
import com.deeppaas.rule.biz.entity.RuleEntityPropertyDO;
import com.deeppaas.rule.biz.web.vo.RuleEntityPropertyVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计算模型转换器
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RuleEntityConvert {

    public static RuleEntityPropertyVO entityToPropertyVO(RuleEntityPropertyDO propertyDO,
                                                          DataStoreEntityCO storeEntity,
                                                          RuleEntityDO ruleEntity) {
        RuleEntityPropertyVO vo = new RuleEntityPropertyVO();
        vo.setId(propertyDO.getId());
        vo.setEntityId(propertyDO.getEntityId());
        vo.setName(propertyDO.getName());
        vo.setRemark(propertyDO.getRemark());
        vo.setType(propertyDO.getType());
        vo.setEntity(storeEntity);
        vo.setRuleEntity(ruleEntity);
        vo.setCreateTime(propertyDO.getCreateTime());
        return vo;
    }

    public static List<RuleEntityPropertyVO> entityToPropertyVOS(List<RuleEntityPropertyDO> propertyDOList,
                                                                 List<DataStoreEntityCO> storeEntityList,
                                                                 List<RuleEntityDO> ruleEntityList) {

        Map<String, DataStoreEntityCO> storeEntityMap = storeEntityList.stream().collect(Collectors.toMap(DataStoreEntityCO::getCode, Function.identity()));
        Map<String, RuleEntityDO> ruleEntityMap = ruleEntityList.stream().collect(Collectors.toMap(RuleEntityDO::getId, Function.identity()));

        List<RuleEntityPropertyVO> voList = new ArrayList<>();
        for (RuleEntityPropertyDO propertyDO : propertyDOList) {
            DataStoreEntityCO storeEntity = null;
            RuleEntityDO ruleEntity = null;
            if (SimpleDataType.isDataEntity(propertyDO.getType())) {
                storeEntity = storeEntityMap.get(propertyDO.getTypeEntity());
            }
            if (SimpleDataType.isRuleEntity(propertyDO.getType())) {
                ruleEntity = ruleEntityMap.get(propertyDO.getTypeEntity());
            }
            voList.add(entityToPropertyVO(propertyDO, storeEntity, ruleEntity));
        }
        return voList;
    }

    public static List<RuleEntityPropertyPO> entityToPropertyPOS(List<RuleEntityPropertyDO> propertyDOList) {
        if (CollectionUtils.isEmpty(propertyDOList)) {
            return Collections.emptyList();
        }
        List<RuleEntityPropertyPO> propertyPOList = new ArrayList<>();
        for (RuleEntityPropertyDO propertyDO : propertyDOList) {
            RuleEntityPropertyPO propertyPO = new RuleEntityPropertyPO();
            propertyPO.setId(propertyDO.getId());
            propertyPO.setEntityId(propertyDO.getEntityId());
            propertyPO.setName(propertyDO.getName());
            propertyPO.setRemark(propertyDO.getRemark());
            propertyPO.setType(propertyDO.getType());
            propertyPO.setTypeEntity(propertyDO.getTypeEntity());
            propertyPOList.add(propertyPO);
        }

        return propertyPOList;
    }
}
