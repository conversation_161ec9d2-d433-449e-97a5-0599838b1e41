package com.deeppaas.datastore.common;

import com.deeppaas.datastore.common.emnus.DataStoreOperator;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/1/19
 */
public class DataStoreConditionS extends DataStoreCondition{
    /**
     * entity对象属性名
     * 如果属性为嵌套对象，如createUser，可使用createUser.userName的方式查询其嵌套对象属性
     */
    private final String propertyName;
    /**
     * 查询属性匹配值
     */
    private final Object value;

    private int markNum = 0;

    private DataStoreConditionS(String propertyName, Object value, DataStoreOperator operator) {
        super(operator);
        this.propertyName = propertyName;
        this.value = value;
    }

    public static DataStoreConditionS of(String propertyName, Object value, DataStoreOperator operator){
        if(operator==null){
            return null;
        }
        switch (operator){
            case IN,NOTIN:{
                if(value==null||"[]".equals(value.toString().trim())){
                    return null;
                }
                if(value instanceof Collection<?> collection){
                    if(collection.isEmpty()){
                        return null;
                    }
                }
                if(value.getClass().isArray() && Arrays.asList(value).isEmpty()){
                    return null;
                }
            }
        }
        return new DataStoreConditionS(propertyName, value, operator);
    }
    public String getFieldName() {
        return propertyName;
    }

    public String getBindMarker() {
        return ":" + (markNum==0 ? propertyName : propertyName+"_"+markNum);
    }

    @Override
    public Map<String, Object> getBindParam() {
        Map<String, Object> sqlMap = new HashMap<>();
        String bindMarker = (markNum==0 ? propertyName : propertyName+"_"+markNum);
        switch (getOperator()) {
            case CONTAINS: {
                sqlMap.put(bindMarker, "%"+value+"%");
                return sqlMap;
            }
            case STARTWITH: {
                sqlMap.put(bindMarker, ""+value+"%");
                return sqlMap;
            }
            case ENDWITH: {
                sqlMap.put(bindMarker, "%"+value+"");
                return sqlMap;
            }
            default: {
                sqlMap.put(bindMarker, value);
                return sqlMap;
            }
        }
    }

    @Override
    void sortMarkNum(AtomicInteger markNum) {
        this.markNum = markNum.addAndGet(1);
    }
}
