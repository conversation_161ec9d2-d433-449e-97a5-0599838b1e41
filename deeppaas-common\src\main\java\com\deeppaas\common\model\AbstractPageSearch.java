package com.deeppaas.common.model;

import com.deeppaas.common.helper.StringHelper;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-11-22
 */
public abstract class AbstractPageSearch implements BaseModel {
    private int pageNo = 1;
    private int pageSize;
    /**
     * 排序（ASC_id,DESC_createTime）
     */
    private String sortInfo;

    public PageRequest getPageable() {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        if (StringHelper.isEmpty(sortInfo)) {
            return PageRequest.of(pageNo - 1, pageSize);
        } else {
            List<String> sortList = StringHelper.splitToList(sortInfo, ",");
            Sort mainSort = null;
            for (String sortStr : sortList) {
                Sort sort = null;
                if (sortStr.startsWith("ASC_")) {
                    sort = Sort.by(Sort.Direction.ASC, sortStr.substring(4));
                } else if (sortStr.startsWith("DESC_")) {
                    sort = Sort.by(Sort.Direction.DESC, sortStr.substring(5));
                }
                if (sort != null) {
                    if (mainSort == null) {
                        mainSort = sort;
                    } else {
                        mainSort = mainSort.and(sort);
                    }
                }
            }
            if (mainSort != null) {
                return PageRequest.of(pageNo - 1, pageSize, mainSort);
            } else {
                return PageRequest.of(pageNo - 1, pageSize);
            }
        }
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getSortInfo() {
        return sortInfo;
    }

    public void setSortInfo(String sortInfo) {
        this.sortInfo = sortInfo;
    }
}
