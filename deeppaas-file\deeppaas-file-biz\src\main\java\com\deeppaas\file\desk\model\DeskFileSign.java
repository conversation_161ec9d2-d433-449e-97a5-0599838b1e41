package com.deeppaas.file.desk.model;

import com.deeppaas.common.helper.HashHelper;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/2/21
 */
public class DeskFileSign {
    private static final String SECRET_KEY = "this is a cipher text used to obfuscate encryption";

    /**
     * 获取临时授权地址签名
     * @param: req
     * @return: java.lang.String
     * <AUTHOR> @date
     **/
    public static String getTempUrlSign(DeskTempUrlReq req) {
        String timeStr = String.valueOf(req.getExpireTime());
        StringBuffer buffer = new StringBuffer(SECRET_KEY);
        buffer.append(req.getFileGroup());
        buffer.append(req.getFileKey());
        buffer.append(req.getExpireTime());
        buffer.append(req.getFileAlias());
        buffer.append(req.getProcess());
        int key1 = Integer.valueOf(timeStr.substring(timeStr.length()-1));
        int key2 = Integer.valueOf(timeStr.substring(timeStr.length()-2,timeStr.length()-1));
        int timeLength = timeStr.length();
        for(int i=0;i<timeLength;i++){
            char num = timeStr.charAt(i);
            buffer.append((char)(num + key1 + key2 + 17));
        }
        return HashHelper.sha256(buffer.toString());
    }

    /**
     * 校验临时授权地址签名
     * @param: tempUrlReq
     * @param: signature
     * @return: boolean
     * <AUTHOR> @date
     **/
    public static boolean checkTempUrlSign(DeskTempUrlReq tempUrlReq) {
        if(tempUrlReq==null || tempUrlReq.getSign()==null){
            return false;
        }
        if(tempUrlReq.getExpireTime()==null || Long.valueOf(tempUrlReq.getExpireTime())<System.currentTimeMillis()){
            return false;
        }
        String sign = getTempUrlSign(tempUrlReq);
        return Objects.equals(tempUrlReq.getSign(), sign);
    }

    /**
     * 获取临时授权上传签名
     * @param: fileGroup
     * @param: dir
     * @param: expireTime
     * @return: java.lang.String
     * <AUTHOR> @date
     **/
    public static String getUploadSign(String fileGroup, String dir, String expireTime) {
        String timeStr = expireTime;
        StringBuffer buffer = new StringBuffer(SECRET_KEY);
        buffer.append(fileGroup).append(dir).append(expireTime);
        int key1 = Integer.valueOf(timeStr.substring(timeStr.length()-1));
        int key2 = Integer.valueOf(timeStr.substring(timeStr.length()-2,timeStr.length()-1));
        int timeLength = timeStr.length();
        for(int i=0;i<timeLength;i++){
            char num = timeStr.charAt(i);
            buffer.append((char)(num + key1 + key2 + 17));
        }
        return HashHelper.sha256(buffer.toString());
    }

    /**
     * 校验临时授权上传签名
     * @param: fileGroup
     * @param: dir
     * @param: expireTime
     * @param: signature
     * @return: boolean
     * <AUTHOR> @date
     **/
    public static boolean checkUploadSign(String fileGroup, String dir, String expireTime, String signature) {
        if(fileGroup==null || !StringUtils.hasText(signature)){
            return false;
        }
        if(expireTime==null || Long.valueOf(expireTime)<System.currentTimeMillis()){
            return false;
        }
        String sign = getUploadSign(fileGroup, dir, expireTime);
        return Objects.equals(signature, sign);
    }
}
