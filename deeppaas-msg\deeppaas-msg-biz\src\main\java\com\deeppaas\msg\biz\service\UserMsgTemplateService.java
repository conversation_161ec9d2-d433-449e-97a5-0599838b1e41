package com.deeppaas.msg.biz.service;

import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.msg.biz.dao.UserMsgTemplateDao;
import com.deeppaas.msg.biz.dao.UserMsgTemplateParamDao;
import com.deeppaas.msg.biz.entity.UserMsgTemplateDO;
import com.deeppaas.msg.biz.entity.UserMsgTemplateParamDO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
@Service
public class UserMsgTemplateService {
    private final UserMsgTemplateDao templateDao;
    private final UserMsgTemplateParamDao templateParamDao;

    public UserMsgTemplateService(UserMsgTemplateDao templateDao, UserMsgTemplateParamDao templateParamDao) {
        this.templateDao = templateDao;
        this.templateParamDao = templateParamDao;
    }

    public UserMsgTemplateDO getById(String id) {
        return templateDao.getById(id);
    }

    public List<UserMsgTemplateDO> findAll() {
        return templateDao.findAll();
    }

    public Page<UserMsgTemplateDO> findByPage(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        return templateDao.queryForPage(condition, pageSearch.getPageable());
    }

    @Transactional
    public UserMsgTemplateDO save(UserMsgTemplateDO templateDO) {
        return templateDao.save(templateDO);
    }

    @Transactional
    public void delete(String id) {
        templateDao.deleteById(id);
        templateParamDao.deleteByTemplateId(id);
    }

    public UserMsgTemplateParamDO getParamById(String paramId) {
        return templateParamDao.getById(paramId);
    }

    public List<UserMsgTemplateParamDO> findParamByTemplateId(String templateId) {
        return templateParamDao.findByTemplateId(templateId);
    }

    @Transactional
    public UserMsgTemplateParamDO saveParam(UserMsgTemplateParamDO paramDO) {
        return templateParamDao.save(paramDO);
    }

    public void deleteParam(String paramId) {
        templateParamDao.deleteById(paramId);
    }


}
