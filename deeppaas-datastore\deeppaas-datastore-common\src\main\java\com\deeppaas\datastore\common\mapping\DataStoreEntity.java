package com.deeppaas.datastore.common.mapping;

import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/1/5
 */
@Data
public class DataStoreEntity {

    /**
     * 别名
     */
    protected String alias;
    /**
     * 类型
     */
    protected DataStoreEntityType type;
    /**
     * 对应主表名或者SQL
     */
    private String table;

    /**
     * 主键属性
     */
    private DataStoreProperty idProperty;
    /**
     * 属性
     */
    protected List<DataStoreProperty> properties = new ArrayList<>();
    /**
     * 连接查询
     */
    private Set<DataStoreTableJoin> joins = new HashSet<>();

    public DataStoreProperty getProperty(String fieldName) {
        return properties.stream().filter(property -> property.getName().equals(fieldName)).findFirst().orElse(null);
    }

    public List<DataStoreProperty> getUniqueProperties(){
        return properties.stream().filter(property -> property.isUnique()).toList();
    }
}
