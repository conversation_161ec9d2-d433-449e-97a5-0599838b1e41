package com.deeppaas.datastore.biz.web.req;

import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.BaseModel;
import com.deeppaas.datastore.biz.entity.DataStoreFolderDO;
import lombok.Data;

/**
 * 数据表文件夹参数
 *
 * <AUTHOR>
 * @date 2022/07/30
 */
@Data
public class DataStoreFolderParam implements BaseModel {
    /**
     * 父文件夹ID
     * <p>
     * 一级文件夹=null
     * 一级文件夹=ROOT
     */
    private String parentId;

    /**
     * 文件夹名称
     */
    private String name;

    /**
     * 排序号
     */
    private Integer sortNo;

    public void check() {
        if (StringHelper.isEmpty(parentId)) {
            parentId = DataStoreFolderDO.TOP_PARENT_ID;
        }
        if (StringHelper.isEmpty(name)) {
            throw ReqException.paramNotFound("名称不允许为空");
        }
        if (sortNo == null) {
            sortNo = 0;
        }
    }
}
