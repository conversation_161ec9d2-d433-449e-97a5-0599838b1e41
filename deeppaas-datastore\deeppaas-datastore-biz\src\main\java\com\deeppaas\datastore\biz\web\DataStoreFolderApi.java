package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.datastore.biz.entity.DataStoreFolderDO;
import com.deeppaas.datastore.biz.entity.DataStoreTableDO;
import com.deeppaas.datastore.biz.service.DataStoreFolderService;
import com.deeppaas.datastore.biz.service.DataStoreTableService;
import com.deeppaas.datastore.biz.web.req.DataStoreFolderParam;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderBaseVO;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderNode;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderVO;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 数据表文件夹API
 *
 * <AUTHOR>
 * @date 2022/07/30
 */
@RestController
@RequestMapping("/api/datastore/folder")
public class DataStoreFolderApi {
    private DataStoreFolderService dataStoreFolderService;
    private DataStoreTableService dataStoreTableService;

    public DataStoreFolderApi(DataStoreFolderService dataStoreFolderService, DataStoreTableService dataStoreTableService) {
        this.dataStoreFolderService = dataStoreFolderService;
        this.dataStoreTableService = dataStoreTableService;
    }

    /**
     * 获取
     *
     * @param id 主键ID
     * @return 文件夹列表
     */
    @GetMapping("/get")
    public RestModel<DataStoreFolderDO> get(@RequestParam String id) {
        DataStoreFolderDO folderDO = dataStoreFolderService.get(id);
        return RestModel.data(folderDO);
    }

    /**
     * 批量获取
     *
     * @param ids 主键ID列表
     * @return 文件夹列表
     */
    @GetMapping("/gets")
    public RestModel<List<DataStoreFolderDO>> gets(@RequestParam String[] ids) {
        List<DataStoreFolderDO> folderDOList = dataStoreFolderService.gets(Set.of(ids));
        return RestModel.data(folderDOList);
    }

    /**
     * 获取后代节点列表
     *
     * @param parentId 父编号ID
     * @return 组织结构子节点
     */
    @GetMapping("/descendants")
    public RestModel<List<DataStoreFolderNode>> descendants(@RequestParam String parentId) {
        List<DataStoreFolderNode> folderNodes = dataStoreFolderService.getDescendants(parentId);
        return RestModel.data(folderNodes);
    }

    /**
     * 获取子节点列表
     *
     * @param parentId 父编号ID
     * @return 组织结构子节点
     */
    @GetMapping("/children")
    public RestModel<List<DataStoreFolderNode>> children(@RequestParam String parentId) {
        List<DataStoreFolderNode> folderNodes = dataStoreFolderService.getChildren(parentId);
        return RestModel.data(folderNodes);
    }

    /**
     * 新增
     *
     * @param param 参数信息
     * @return 文件夹DO
     */
    @PostMapping("/add")
    public RestModel<DataStoreFolderDO> add(@RequestBody DataStoreFolderParam param) {
        // 参数校验
        param.check();
        // 业务处理
        DataStoreFolderDO folderDO = dataStoreFolderService.add(param);
        return RestModel.data(folderDO);
    }

    /**
     * 重命名
     *
     * @param id   主键ID
     * @param name 新名称
     * @return 操作结果
     */
    @PostMapping("/rename")
    public RestModel<Boolean> rename(@BodyParam String id, @BodyParam String name) {
        // 业务处理
        dataStoreFolderService.rename(id, name);
        return RestModel.data(true);
    }

    /**
     * 换爹
     *
     * @param id       主键ID
     * @param parentId 父文件夹ID
     * @return 操作结果
     */
    @PostMapping("/modifyParent")
    public RestModel<Boolean> modifyParent(@BodyParam String id, @BodyParam String parentId) {
        // 业务处理
        dataStoreFolderService.modifyParent(id, parentId);
        return RestModel.data(true);
    }

    /**
     * 逻辑删除
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/remove")
    public RestModel<Boolean> remove(@BodyParam String id) {
        dataStoreFolderService.remove(id);
        return RestModel.data(true);
    }

    /**
     * 修改排序
     *
     * @param ids 主键ID数组
     * @return 操作结果
     */
    @PostMapping("/sort")
    public RestModel<Boolean> sort(@BodyParam String[] ids) {
        dataStoreFolderService.sort(List.of(ids));
        return RestModel.data(true);
    }

    /**
     * 回收站列表
     */
    @GetMapping("/recycles")
    public RestModel<List<DataStoreFolderVO>> recycles() {
        List<DataStoreFolderVO> folderVOList = dataStoreFolderService.recycles();
        return RestModel.data(folderVOList);
    }

    /**
     * 恢复
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/regain")
    public RestModel<Boolean> regain(@BodyParam String id) {
        dataStoreFolderService.regain(id);
        return RestModel.data(true);
    }

    /**
     * 彻底删除
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    public RestModel<Boolean> delete(@BodyParam String id) {
        DataStoreFolderDO folderDO = dataStoreFolderService.get(id);
        if (folderDO == null) {
            throw RunException.dataNotFound("操作失败，未找到有效数据");
        }
        if (BoolHelper.intToBool(folderDO.getUsable())) {
            throw RunException.optReject("操作失败，无法彻底删除有效状态文件夹");
        }
        // 业务处理
        dataStoreFolderService.delete(id);
        return RestModel.data(true);
    }

    /**
     * 获取主表所属文件夹列表（按照根到叶子顺序排列）
     *
     * @param tableId 数据表主键ID
     * @return 主表所属文件夹列表
     */
    @GetMapping("/listTableFolder")
    public RestModel<List<DataStoreFolderBaseVO>> listTableFolder(@RequestParam String tableId) {
        // 业务处理
        DataStoreTableDO tableDO = dataStoreTableService.get(tableId);
        if (tableDO == null) {
            return RestModel.data(Collections.emptyList());
        }
        // 按照ROOT到叶子节点顺序
        List<DataStoreFolderBaseVO> tableFolders = dataStoreFolderService.getParents(tableDO.getFolderId());
        return RestModel.data(tableFolders);
    }
}
