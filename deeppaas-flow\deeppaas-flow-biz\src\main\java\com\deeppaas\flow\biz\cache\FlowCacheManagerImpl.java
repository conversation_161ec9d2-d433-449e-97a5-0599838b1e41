package com.deeppaas.flow.biz.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/3/3
 */
@Component
public class FlowCacheManagerImpl extends FlowCacheManager {
    private static Cache flowDefineCache = new CaffeineCache(
            "flowDefineCache", Caffeine.from("maximumSize=10000").build(), true);
    @Override
    public Cache getDefineCache() {
        return flowDefineCache;
    }
}
