//package com.deeppaas.flow.biz.handler;
//
//import com.deeppaas.flow.biz.ExecuteItem;
//
///**
// * @ClassName: DecisionHandler
// * @Description: 判断处理器，流程线规则执行器顶级接口
// * <AUTHOR>
// */
//public interface DecideHandler {
//
//	/**
//	 * 根据流程信息，返回是否按处理器所属流程流转线进行流转
//	 * @param execution
//	 * @param condition
//	 * @return
//	 * <AUTHOR>
//	 * @date 2017年8月1日
//	 */
//	boolean execute(ExecuteItem execution, String condition);
//}
