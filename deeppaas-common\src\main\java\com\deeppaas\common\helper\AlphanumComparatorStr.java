package com.deeppaas.common.helper;

import org.apache.commons.compress.utils.Lists;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class AlphanumComparatorStr implements Comparator<String> {
    private final boolean isDigit(char ch) {
        return ((ch >= 48) && (ch <= 57));
    }

    private final String getChunk(String s, int slength, int marker) {
        StringBuilder chunk = new StringBuilder();
        char c = s.charAt(marker);
        chunk.append(c);
        marker++;
        if (isDigit(c)) {
            while (marker < slength) {
                c = s.charAt(marker);
                if (!isDigit(c)) {
                    break;
                }
                chunk.append(c);
                marker++;
            }
        } else {
            while (marker < slength) {
                c = s.charAt(marker);
                if (isDigit(c)) {
                    break;
                }
                chunk.append(c);
                marker++;
            }
        }
        return chunk.toString();
    }

    @Override
    public int compare(String ss1, String ss2) {
        if ((ss1 == null) || (ss2 == null)) {
            return 0;
        }
        String s1 = ss1.replaceAll("-", "_");
        String s2 = ss2.replaceAll("-", "_");
        int thisMarker = 0;
        int thatMarker = 0;
        int s1Length = s1.length();
        int s2Length = s2.length();

        while (thisMarker < s1Length && thatMarker < s2Length) {
            String thisChunk = getChunk(s1, s1Length, thisMarker);
            thisMarker += thisChunk.length();

            String thatChunk = getChunk(s2, s2Length, thatMarker);
            thatMarker += thatChunk.length();

            // If both chunks contain numeric characters, sort them numerically
            int result = 0;
            if (isDigit(thisChunk.charAt(0)) && isDigit(thatChunk.charAt(0))) {
                // Simple chunk comparison by length.
                int thisChunkLength = thisChunk.length();
                result = thisChunkLength - thatChunk.length();
                // If equal, the first different number counts
                if (result == 0) {
                    for (int i = 0; i < thisChunkLength; i++) {
                        result = thisChunk.charAt(i) - thatChunk.charAt(i);
                        if (result != 0) {
                            return result;
                        }
                    }
                }
            } else {
                result = thisChunk.compareTo(thatChunk);
            }

            if (result != 0) {
                return result;
            }
        }

        return s1Length - s2Length;
    }

    public static void main(String[] args) {
        List<String> list = Lists.newArrayList();
        list.add("001-002-003-003");
        list.add("001-002-003-004");
        list.add("001-002-003-005");
        list.add("001-002-003-001");
        list.add("001-002-003-002");
        list.add("001-002-003-010");
        list.add("001-002-003-020");
        list.add("001-002-003-032");
        list = list.stream().sorted(new AlphanumComparatorStr()).collect(Collectors.toList());
        list.forEach(item -> {
            System.out.println(item);
        });

    }
}
