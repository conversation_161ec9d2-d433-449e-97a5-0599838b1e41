package com.deeppaas.flow.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowInstanceUserDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

/**
 * 流程实例相关用户
 * <AUTHOR>
 * @date 2022/9/7
 */
public interface FlowInstanceUserDao extends BaseJdbcDao<FlowInstanceUserDO, Long> {
    @Modifying
    @Query("delete from flow_instance_user where instance_id=:instanceId")
    void deleteByInstance(Long instanceId);
}
