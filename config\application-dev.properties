dp.datasource.db-type=MYSQL
spring.datasource.jdbc-url=************************************************************************************************
spring.datasource.username=root
spring.datasource.password=kubao@666
spring.datasource.hikari.connectionInitSql=SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
kubao.ai.server=http://127.0.0.1:7999
#???????-???? ?????????
kubao.ex.customized=jfj
#?????  ????????????????????
kubao.ex.datakeyrule=(?<=aa).*(?=bb)
#?????  ??????????????
kubao.ex.partrule=(?<=dd).*(?=ee)
#?????  ?????????????
kubao.ex.pagerule=(?<=ff).*(?=gg)

#\u6587\u4EF6\u7CFB\u7EDF
dp.file.groups=
##flyway
spring.flyway.locations= db/commondb,db/mysqldbg
