package com.deeppaas.common.exception;

/**
 * @Description 配置异常
 * <AUTHOR>
 * @Date 2019-03-22
 */
public class ConfigException extends BaseException{
    /** 配置信息未找到异常码 */
    private static final int NOT_FOUNE_CODE = 6001;
    /** 配置错误异常码 */
    private static final int CONFIG_ERROR_CODE = 6002;

    private ConfigException(int code, String message) {
        super(code, message);
    }

    /**
     * @Description: 没有找到配置信息
     * @param message
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static ConfigException notFound(String message) {
        return new ConfigException(NOT_FOUNE_CODE, message);
    }

    /**
     * @Description: 配置信息错误
     * @param message
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static ConfigException error(String message) {
        return new ConfigException(CONFIG_ERROR_CODE, message);
    }

}
