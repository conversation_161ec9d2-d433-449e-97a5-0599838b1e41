package com.deeppaas.file.biz.web;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.biz.convert.FileGroupConvert;
import com.deeppaas.file.biz.entity.FileGroupDO;
import com.deeppaas.file.biz.service.FileGroupService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
@RestController
@RequestMapping("/api/fileGroup")
@RequestAuth
public class FileGroupApi {
    private final FileGroupService fileGroupService;

    public FileGroupApi(FileGroupService fileGroupService) {
        this.fileGroupService = fileGroupService;
    }

    @GetMapping("/list")
    public RestModel<List<FileGroup>> list(){
        List<FileGroupDO> list = fileGroupService.findAll();
        List<FileGroup> groups = list.stream().map(FileGroupConvert::buildFileGroup).toList();
        return RestModel.data(groups);
    }

    @GetMapping("/get")
    public RestModel<FileGroup> get(String name){
        FileGroupDO fileGroupDO = fileGroupService.getGroupDO(name);
        return RestModel.data(FileGroupConvert.buildFileGroup(fileGroupDO));
    }

    @PostMapping("/save")
    public RestModel<String> save(@RequestBody FileGroup fileGroup){
        fileGroupService.saveGroup(fileGroup);
        return RestModel.success("success");
    }

    @PostMapping("/delete")
    public RestModel<String> delete(@BodyParam String id){
        fileGroupService.deleteById(id);
        return RestModel.success("success");
    }

    @GetMapping("/getProperties")
    public RestModel<Map<String, Object>> getProperties(String name){
        Map<String, Object> properties = fileGroupService.getGroupProperties(name);
        return RestModel.data(properties);
    }

    @PostMapping("/saveProperties")
    public RestModel<String> saveProperties(@BodyParam String name, @BodyParam Map<String, String> properties){
        fileGroupService.saveProperties(name, properties);
        return RestModel.success("success");
    }

}
