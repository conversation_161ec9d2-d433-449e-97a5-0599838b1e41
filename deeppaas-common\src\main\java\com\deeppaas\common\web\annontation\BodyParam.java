package com.deeppaas.common.web.annontation;

import java.lang.annotation.*;

/**
 * 用于处理restful body传值时的单个参数的单独解析
 * <AUTHOR>
 * @date 2016年7月29日
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BodyParam {
	/**
	 * 以下以传入body请求为：{"userName":"lee","age":33}为例
	 */
	/**
	 * 参数名，非必需，默认为方法参数名
	 * 如果接收参数为userName,而方法参数名为user，则使用该属性
	 * 如(@BodyParam(name="userName") String user)
	 */
	String name() default "";
	
	/**
	 * 是否为必须字段，非必需，默认true
	 * 如果未发现参数则提示参数异常
	 */
	boolean required() default true;

}
