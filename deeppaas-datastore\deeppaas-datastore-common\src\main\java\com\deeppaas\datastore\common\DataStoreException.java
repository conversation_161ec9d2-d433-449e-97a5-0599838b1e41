package com.deeppaas.datastore.common;

import com.deeppaas.common.exception.BaseException;

/**
 * <AUTHOR>
 * @date 2022/1/25
 */
public class DataStoreException extends BaseException {
    /**
     * 数据仓库异常
     */
    public static final int DATA_STORE_ERROR = 10000;
    /**
     * sql构建的数据实体不能保存
     */
    public static final int SQL_BUILD_STORE_ERROR = 10001;

    public DataStoreException(int code, String message) {
        super(code, message);
    }

    public static DataStoreException error(String msg) {
        return new DataStoreException(DATA_STORE_ERROR, msg);
    }

    /**
     * @Description: 未登录异常
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static DataStoreException sqlBuildStoreError() {
        return new DataStoreException(SQL_BUILD_STORE_ERROR, "sql构建的数据实体不能保存");
    }

}
