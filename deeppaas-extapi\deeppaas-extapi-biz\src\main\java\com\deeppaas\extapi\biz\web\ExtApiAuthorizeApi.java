package com.deeppaas.extapi.biz.web;

import com.deeppaas.account.api.client.UserClient;
import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.account.api.client.model.User;
import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.helper.HashHelper;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.extapi.api.ExtApiConstant;
import com.deeppaas.extapi.biz.cache.ExtApiCache;
import com.deeppaas.extapi.biz.entity.ExtApiUserDO;
import com.deeppaas.extapi.biz.service.ExtApiUserService;
import org.springframework.cache.Cache;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
@RequestAuth(auth = false)
@RestController
@RequestMapping("/openApi/authorize")
public class ExtApiAuthorizeApi {
    private final ExtApiUserService extApiUserService;
    private final ExtApiCache extApiCache;
    private final UserClient userClient;

    public ExtApiAuthorizeApi(ExtApiUserService extApiUserService, ExtApiCache extApiCache, UserClient userClient) {
        this.extApiUserService = extApiUserService;
        this.extApiCache = extApiCache;
        this.userClient = userClient;
    }

    /**
     * 授权跳转
     * @param appId
     * @param redirectUrl
     * @return
     */
    @GetMapping("/redirect")
    public ModelAndView authorize(String appId, String redirectUrl, RedirectAttributes redirectAttributes){
        String curUserCode = LocalUserContext.getCurUserCode();
        ExtApiUserDO apiUser = extApiUserService.get(appId);
        if(apiUser==null){
            return new ModelAndView("没有登陆用户");
        }
        String authCode = UUID.randomUUID().toString().replaceAll("-","");
        Cache authCodeCache = extApiCache.getAuthCodeCache();
        String cacheKey = appId+"_"+authCode;
        authCodeCache.put(cacheKey, curUserCode);
        redirectAttributes.addAttribute(ExtApiConstant.OAUTH2_AUTH_CODE, authCode);
        RedirectView redirectView = new RedirectView(redirectUrl);
        return new ModelAndView(redirectView);
    }
    /**
     *
     * @param appId
     * @param authCode
     * @param sign
     * @return
     */
    @RequestAuth(auth = false)
    @RequestMapping("/getUserInfo")
    @ResponseBody
    public RestModel getUserInfo(String appId, String authCode, String sign){
        ExtApiUserDO apiUser = extApiUserService.get(appId);
        if(apiUser==null){
            return RestModel.error(ReqException.paramValidate("appId无效"));
        }
        String checkSign = HashHelper.sha256(authCode+"&"+apiUser.getCallAuthInfo());
        if(Objects.equals(checkSign, sign)){
            Cache authCodeCache = extApiCache.getAuthCodeCache();
            String cacheKey = appId+"_"+authCode;
            String userCode = authCodeCache.get(cacheKey, String.class);
            if(userCode==null){
                return RestModel.error(ReqException.paramValidate("authCode无效"));
            }
            User user = userClient.getUserByCode(userCode);
            return RestModel.data(user);
        }
        return RestModel.error(ReqException.paramValidate("sign无效"));
    }

//    public static void main(String[] args){
//        String authCode = "1bdf86eaa80e4a4aaf374b95253f1d26";
//        String sign = HashHelper.md5(authCode+"&192.168.124.2,192.168.124.3");
//        System.out.println(sign);
//    }
}
