package com.deeppaas.common.data.ddl;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.sql.JDBCType;

/**
 * 列生成注解
 * <AUTHOR>
 * @date 2022/7/28
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ColumnDDL {
    /**
     * 数据库类型
     * @return
     */
    JDBCType type();

    /**
     * 特殊生成定义，比如json
     * @return
     */
    String definition() default "";

    /**
     * 注释
     * @return
     */
    String comment() default "";

    /**
     * 长度
     * @return
     */
    int length() default 255;

    /**
     * 小数位数
     * @return
     */
    int digits() default 0;

    /**
     * 唯一索引
     * @return
     */
    boolean unique() default false;

    /**
     * 是否可为空
     * @return
     */
    boolean nullable() default true;
}
