package com.deeppaas.common.web.annontation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description 小程序系统级权限拦截，处理角色接口
 * <AUTHOR>
 * @Date 2019-06-13
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequestAuth {
    /**
     * 是否不需要登录安全
     * true为需要登录,false不需要登录
     */
    boolean auth() default true;
}
