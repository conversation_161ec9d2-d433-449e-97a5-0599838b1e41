package com.deeppaas.handler;

import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.common.exception.AuthException;
import com.deeppaas.common.exception.BaseException;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.WebHelper;
import com.deeppaas.common.web.context.BodyParamContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
public class GlobalExceptionResolver implements HandlerExceptionResolver {
    private static final Logger logger = LoggerFactory.getLogger(HandlerExceptionResolver.class);
    private static final String X_REQ_HEADER = "X-Requested-With";
    private static final String X_REQ_IDX = "XMLHttpRequest";
    private static final String ACCEPT_HEADER = "accept";
    private static final String ACCEPT_JSON = "application/json";
    private static final String CHARSET = "utf-8";
    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        /** 统一对使用ThreadLocal的线程信息进行管理 */
        LocalUserContext.remove();
        BodyParamContext.remove();
        logError(ex, request);
        boolean isAjaxReq = WebHelper.isAjax(request);
        ModelAndView mv = new ModelAndView();
        if (ex instanceof BaseException) {
            // 无权限访问
            boolean isAuthError = Objects.equals(AuthException.UNAUTHORIZED_CODE, ((BaseException) ex).getCode());
            if (!isAjaxReq && isAuthError) {
                String redirectUrl = getAuthErrorUrl(request);
                logger.info("无权限访问|{}", redirectUrl);
                mv.setView(new RedirectView(redirectUrl, false));
                return mv;
            }
            mv.addObject("code", ((BaseException) ex).getCode());
            mv.addObject("message", ex.getMessage());
        } else {
            mv.addObject("code", RunException.RUN_ERROR_CODE);
            mv.addObject("message", ex.getMessage());
        }
        if (isAjaxReq) {
            mv.setView(new MappingJackson2JsonView());
        } else {
            mv.setViewName("/index");
        }
        return mv;
    }

    private String getAuthErrorUrl(HttpServletRequest request) {
        String redirectUrl = "/403";
        // 其他端
        try {
            String originalUrl = WebHelper.getUrl(request);
            redirectUrl += "?originalUrl=" + URLEncoder.encode(originalUrl, CHARSET);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return redirectUrl;
    }

    private void logError(Exception ex, HttpServletRequest request) {
        logger.error("***********GlobalErrorHandler异常开始************");
        logger.error("异常信息:{} {}", ex.getClass().getSimpleName(), ex.getMessage());
        logger.error("请求地址:{}", request.getRequestURI());
        StackTraceElement[] errorElements = ex.getStackTrace();
        for (StackTraceElement errorElement : errorElements) {
            String p = errorElement.toString();
            if (p.contains("com.deeppaas")) {
                logger.error("位置:{}", p);
            }
        }
        if (ex.getCause() != null) {
            StackTraceElement[] causeElements = ex.getCause().getStackTrace();
            for (StackTraceElement causeElement : causeElements) {
                String p = causeElement.toString();
                if (p.contains("com.deeppaas")) {
                    logger.error("cause by:{}", p);
                }
            }
        }
        ex.printStackTrace();
        logger.error("***********AppletGlobalErrorHandler异常结束************");
    }

    private boolean isAjax(HttpServletRequest request){
        String accept = request.getHeader(ACCEPT_HEADER);
        if (accept != null && accept.indexOf(ACCEPT_JSON) != -1){
            return true;
        }
        return X_REQ_IDX.equals(request.getHeader(X_REQ_HEADER));
    }
}
