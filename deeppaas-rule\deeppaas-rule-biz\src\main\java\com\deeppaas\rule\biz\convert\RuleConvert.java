package com.deeppaas.rule.biz.convert;

import com.deeppaas.datastore.api.client.model.DataStoreEntityCO;
import com.deeppaas.rule.api.enums.RuleOwnerType;
import com.deeppaas.rule.api.model.RulePO;
import com.deeppaas.rule.biz.entity.EventRuleDO;
import com.deeppaas.rule.biz.entity.RuleDO;
import com.deeppaas.rule.biz.web.vo.EventRuleVO;
import com.deeppaas.rule.biz.web.vo.RuleVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (事件)规则转换器
 *
 * <AUTHOR>
 * @date 2022/4/25
 */
public class RuleConvert {


    public static EventRuleVO eventRuleToVO(EventRuleDO eventRuleDO, RuleDO ruleDO) {
        EventRuleVO vo = new EventRuleVO();
        vo.setId(eventRuleDO.getId());
        vo.setOwnerType(eventRuleDO.getOwnerType());
        vo.setOwnerId(eventRuleDO.getOwnerId());
        vo.setEvent(eventRuleDO.getEvent());
        vo.setRule(ruleDO);
        vo.setSortNo(eventRuleDO.getSortNo());
        vo.setCreateTime(eventRuleDO.getCreateTime());
        return vo;
    }

    public static List<EventRuleVO> eventRuleToVOS(List<EventRuleDO> eventRuleDOList, Map<String, RuleDO> ruleDOMap) {
        return eventRuleDOList.stream().map(eventRuleDO -> eventRuleToVO(eventRuleDO, ruleDOMap.get(eventRuleDO.getRuleId()))).collect(Collectors.toList());
    }

    public static RulePO ruleDOToPO(RuleDO ruleDO) {
        if (ruleDO == null) {
            return null;
        }
        RulePO rulePO = new RulePO();
        rulePO.setId(ruleDO.getId());
        rulePO.setName(ruleDO.getName());
        rulePO.setParamType(ruleDO.getParamType());
        rulePO.setParamEntity(ruleDO.getParamEntity());
        rulePO.setReturnType(ruleDO.getReturnType());
        rulePO.setReturnEntity(ruleDO.getReturnEntity());
        rulePO.setBackEnd(ruleDO.getBackEnd());
        rulePO.setOwnerType(ruleDO.getOwnerType());
        rulePO.setOwnerId(ruleDO.getOwnerId());
        rulePO.setContent(ruleDO.getContent());
        return rulePO;
    }

    private static RuleVO entityToVO(RuleDO ruleDO) {
        RuleVO vo = new RuleVO();
        vo.setId(ruleDO.getId());
        vo.setName(ruleDO.getName());
        vo.setParamType(ruleDO.getParamType());
        vo.setParamAppCode(ruleDO.getParamAppCode());
        vo.setParamEntity(ruleDO.getParamEntity());
        vo.setParamRemark(ruleDO.getParamRemark());
        vo.setReturnType(ruleDO.getReturnType());
        vo.setReturnEntity(ruleDO.getReturnEntity());
        vo.setReturnAppCode(ruleDO.getReturnAppCode());
        vo.setBackEnd(ruleDO.getBackEnd());
        vo.setOwnerType(ruleDO.getOwnerType());
        vo.setOwnerId(ruleDO.getOwnerId());
        vo.setContent(ruleDO.getContent());
        return vo;
    }

    public static List<RuleVO> entityToVOS(List<RuleDO> ruleDOList, List<DataStoreEntityCO> storeEntityCOList) {
        Map<String, DataStoreEntityCO> storeEntityCOMap = storeEntityCOList.stream().collect(Collectors.toMap(DataStoreEntityCO::getCode, Function.identity()));
        List<RuleVO> voList = new ArrayList<>(ruleDOList.size());
        for (RuleDO ruleDO : ruleDOList) {
            RuleVO vo = entityToVO(ruleDO);
            if (RuleOwnerType.DATASTORE_ENTITY.name().equals(ruleDO.getOwnerType())) {
                vo.setEntity(storeEntityCOMap.get(ruleDO.getOwnerId()));
            }
            voList.add(vo);
        }
        return voList;
    }
}
