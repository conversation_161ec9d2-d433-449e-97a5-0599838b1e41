package com.deeppaas.flow.biz.element;

import com.deeppaas.flow.biz.enums.NodeType;

import java.util.List;

/**
 * 带页面的节点
 * <AUTHOR>
 * @date 2022/5/8
 */
public abstract class FlowPageNode extends FlowNode{
    /**
     * 页面组件编号
     */
    private String page;

    public FlowPageNode(String code, String name, NodeType type, String remark, String page, List<FlowEventRule> eventRules) {
        super(code, name, type, remark, eventRules);
        this.page = page;
    }

    public String getPage() {
        return page;
    }
}
