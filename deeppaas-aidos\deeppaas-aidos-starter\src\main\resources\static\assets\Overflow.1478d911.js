import{r,F,f as I,b as ne,E as b,W as He,_ as x,P as he}from"./index.689bcdcb.js";import{R as Ne}from"./index.71d34973.js";import{u as Ye}from"./index.1f7b2316.js";var qe=["prefixCls","invalidate","item","renderItem","responsive","registerSize","itemKey","className","style","children","display","order","component"],w=void 0;function Je(e,u){var v=e.prefixCls,i=e.invalidate,l=e.item,n=e.renderItem,f=e.responsive,S=e.registerSize,o=e.itemKey,y=e.className,R=e.style,h=e.children,G=e.display,X=e.order,c=e.component,B=c===void 0?"div":c,z=F(e,qe),d=f&&!G;function W(p){S(o,p)}r.exports.useEffect(function(){return function(){W(null)}},[]);var H=n&&l!==w?n(l):h,A;i||(A={opacity:d?0:1,height:d?0:w,overflowY:d?"hidden":w,order:f?X:w,pointerEvents:d?"none":w,position:d?"absolute":w});var g={};d&&(g["aria-hidden"]=!0);var P=r.exports.createElement(B,I({className:ne(!i&&v,y),style:b(b({},A),R)},g,z,{ref:u}),H);return f&&(P=r.exports.createElement(Ne,{onResize:function(K){var Y=K.offsetWidth;W(Y)}},P)),P}var k=r.exports.forwardRef(Je);k.displayName="Item";function Qe(){var e=He({}),u=x(e,2),v=u[1],i=r.exports.useRef([]),l=0,n=0;function f(S){var o=l;l+=1,i.current.length<o+1&&(i.current[o]=S);var y=i.current[o];function R(h){i.current[o]=typeof h=="function"?h(i.current[o]):h,he.cancel(n),n=he(function(){v({},!0)})}return[y,R]}return f}var Ze=["component"],et=["className"],tt=["className"],rt=function(u,v){var i=r.exports.useContext(L);if(!i){var l=u.component,n=l===void 0?"div":l,f=F(u,Ze);return r.exports.createElement(n,I({},f,{ref:v}))}var S=i.className,o=F(i,et),y=u.className,R=F(u,tt);return r.exports.createElement(L.Provider,{value:null},r.exports.createElement(k,I({ref:v,className:ne(S,y)},o,R)))},Ce=r.exports.forwardRef(rt);Ce.displayName="RawItem";var at=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],L=r.exports.createContext(null),Ie="responsive",ge="invalidate";function nt(e){return"+ ".concat(e.length," ...")}function st(e,u){var v=e.prefixCls,i=v===void 0?"rc-overflow":v,l=e.data,n=l===void 0?[]:l,f=e.renderItem,S=e.renderRawItem,o=e.itemKey,y=e.itemWidth,R=y===void 0?10:y,h=e.ssr,G=e.style,X=e.className,c=e.maxCount,B=e.renderRest,z=e.renderRawRest,d=e.suffix,W=e.component,H=W===void 0?"div":W,A=e.itemComponent,g=e.onVisibleChange,P=F(e,at),p=Qe(),K=h==="full",Y=p(null),se=x(Y,2),M=se[0],Ee=se[1],N=M||0,we=p(new Map),ie=x(we,2),oe=ie[0],be=ie[1],ze=p(0),le=x(ze,2),We=le[0],Pe=le[1],Ue=p(0),ue=x(Ue,2),O=ue[0],Fe=ue[1],ke=p(0),fe=x(ke,2),D=fe[0],Ae=fe[1],Ke=r.exports.useState(null),ce=x(Ke,2),de=ce[0],V=ce[1],Me=r.exports.useState(null),me=x(Me,2),q=me[0],Oe=me[1],E=r.exports.useMemo(function(){return q===null&&K?Number.MAX_SAFE_INTEGER:q||0},[q,M]),De=r.exports.useState(!1),ve=x(De,2),Ve=ve[0],$e=ve[1],J="".concat(i,"-item"),xe=Math.max(We,O),_=n.length&&c===Ie,Se=c===ge,Te=_||typeof c=="number"&&n.length>c,C=r.exports.useMemo(function(){var t=n;return _?M===null&&K?t=n:t=n.slice(0,Math.min(n.length,N/R)):typeof c=="number"&&(t=n.slice(0,c)),t},[n,R,M,c,_]),Q=r.exports.useMemo(function(){return _?n.slice(E+1):n.slice(C.length)},[n,C,_,E]),$=r.exports.useCallback(function(t,a){var s;return typeof o=="function"?o(t):(s=o&&(t==null?void 0:t[o]))!==null&&s!==void 0?s:a},[o]),Le=r.exports.useCallback(f||function(t){return t},[f]);function T(t,a){Oe(t),a||($e(t<n.length-1),g==null||g(t))}function je(t,a){Ee(a.clientWidth)}function ye(t,a){be(function(s){var m=new Map(s);return a===null?m.delete(t):m.set(t,a),m})}function Ge(t,a){Fe(a),Pe(O)}function Xe(t,a){Ae(a)}function Z(t){return oe.get($(C[t],t))}Ye(function(){if(N&&xe&&C){var t=D,a=C.length,s=a-1;if(!a){T(0),V(null);return}for(var m=0;m<a;m+=1){var ae=Z(m);if(ae===void 0){T(m-1,!0);break}if(t+=ae,s===0&&t<=N||m===s-1&&t+Z(s)<=N){T(s),V(null);break}else if(t+xe>N){T(m-1),V(t-ae-D+O);break}}d&&Z(0)+D>N&&V(null)}},[N,oe,O,D,$,C]);var Re=Ve&&!!Q.length,pe={};de!==null&&_&&(pe={position:"absolute",left:de,top:0});var U={prefixCls:J,responsive:_,component:A,invalidate:Se},Be=S?function(t,a){var s=$(t,a);return r.exports.createElement(L.Provider,{key:s,value:b(b({},U),{},{order:a,item:t,itemKey:s,registerSize:ye,display:a<=E})},S(t,a))}:function(t,a){var s=$(t,a);return r.exports.createElement(k,I({},U,{order:a,key:s,item:t,renderItem:Le,itemKey:s,registerSize:ye,display:a<=E}))},ee,_e={order:Re?E:Number.MAX_SAFE_INTEGER,className:"".concat(J,"-rest"),registerSize:Ge,display:Re};if(z)z&&(ee=r.exports.createElement(L.Provider,{value:b(b({},U),_e)},z(Q)));else{var te=B||nt;ee=r.exports.createElement(k,I({},U,_e),typeof te=="function"?te(Q):te)}var re=r.exports.createElement(H,I({className:ne(!Se&&i,X),style:G,ref:u},P),C.map(Be),Te?ee:null,d&&r.exports.createElement(k,I({},U,{order:E,className:"".concat(J,"-suffix"),registerSize:Xe,display:!0,style:pe}),d));return _&&(re=r.exports.createElement(Ne,{onResize:je},re)),re}var j=r.exports.forwardRef(st);j.displayName="Overflow";j.Item=Ce;j.RESPONSIVE=Ie;j.INVALIDATE=ge;export{j as F};
