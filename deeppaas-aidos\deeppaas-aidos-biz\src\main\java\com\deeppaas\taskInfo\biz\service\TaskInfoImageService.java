package com.deeppaas.taskInfo.biz.service;

import com.deeppaas.taskInfo.biz.dao.TaskInfoImageDao;
import com.deeppaas.taskInfo.biz.entity.TaskInfoImageDo;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Service
public class TaskInfoImageService {

    @Autowired
    private TaskInfoImageDao taskInfoImageDao;

    public List<TaskInfoImageDo> getAll() {
        return taskInfoImageDao.findAll();
    }

    public void insert(TaskInfoImageDo taskInfoImageDo) {
        String id = taskInfoImageDo.getId();
        if (StringHelper.isEmpty(id)) {
            taskInfoImageDo.setCreateTime(LocalDateTime.now());
            taskInfoImageDao.insert(taskInfoImageDo);
        } else {
            taskInfoImageDao.save(taskInfoImageDo);
        }
    }
    public void save(List<TaskInfoImageDo> taskInfoImageDo) {
        taskInfoImageDao.saveAll(taskInfoImageDo);
    }


    public void insert(List<TaskInfoImageDo> taskInfoImageDos) {
        taskInfoImageDao.insertAll(taskInfoImageDos);
    }

    public void del(String id) {
        taskInfoImageDao.deleteById(id);
    }

    public PageData<TaskInfoImageDo> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<TaskInfoImageDo> page = taskInfoImageDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    public List<TaskInfoImageDo> findAllById(String... id) {
        return (List<TaskInfoImageDo>) taskInfoImageDao.findAllById(Arrays.asList(id));
    }
    public List<TaskInfoImageDo> findAllBycallBackImageId(String... id) {
        return taskInfoImageDao.findAllBycallBackImageIdIn(Arrays.asList(id));
    }

    public TaskInfoImageDo get(String taskId) {
        return taskInfoImageDao.getById(taskId);
    }
    public List<TaskInfoImageDo> getByTaskInfoId(String taskId) {
        return taskInfoImageDao.findByTaskInfoId(taskId);
    }
}
