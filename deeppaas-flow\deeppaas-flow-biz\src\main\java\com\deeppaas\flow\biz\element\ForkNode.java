package com.deeppaas.flow.biz.element;

import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.MockExecuteItem;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.enums.NodeType;
import com.deeppaas.flow.biz.model.AssignInfo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 分支环节
 * <AUTHOR>
 * @date 2021/12/27
 */
public class ForkNode extends FlowNode {

    public ForkNode(String code, String name, NodeType type, String remark, List<FlowEventRule> eventRules) {
        super(code, name, type, remark, eventRules);
    }

    /**
     * 向前流转，根据流程线流转
     * @param executeItem
     */
    protected void runOutTransition(ExecuteItem executeItem) {
        for (FlowLine line : this.getOutLines()) {
            ExecuteItem childItem = executeItem.subExecuteItem(this);
            line.execute(childItem);
        }
    }

    //模拟进入
    @Override
    public List<AssignInfo> mockEnter(MockExecuteItem execution){
        FlowInstanceDO instance = execution.getInstance();
        execution.setInstance(instance);
        execution.setNode(this);
        return this.mockExecute(execution);
    }
    //模拟执行，传递令牌
    public List<AssignInfo> mockExecute(MockExecuteItem execution) {
        FlowNode jumpNode = execution.getJumpNode();
        if(jumpNode!=null){	//跳转
            return jumpNode.mockEnter(execution);
        }else{
            return mockRunOutTransition(execution);
        }
    }
    //模拟流转
    protected List<AssignInfo> mockRunOutTransition(MockExecuteItem execution) {
        List<AssignInfo> assignInfoList = new ArrayList<>();
        for (FlowLine tm : this.getOutLines()) {
            MockExecuteItem childItem = execution.subExecuteItem(this);
            List<AssignInfo> lineAssignInfoList = tm.mockExecute(childItem);
            if(!CollectionUtils.isEmpty(lineAssignInfoList)){
                assignInfoList.addAll(lineAssignInfoList);
            }
        }
        return assignInfoList;
    }

    @Override
    public List<String> check() {
        List<String> errors = new ArrayList<>();
        if(inLines.isEmpty()){
            errors.add("环节["+getCode()+"]没有进入连接线");
        }
        if(outLines.isEmpty()){
            errors.add("环节["+getCode()+"]没有流转连接线");
        }
        return errors;
    }
}
