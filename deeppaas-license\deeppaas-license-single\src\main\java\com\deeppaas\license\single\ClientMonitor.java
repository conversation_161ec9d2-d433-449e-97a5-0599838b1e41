package com.deeppaas.license.single;

import com.deeppaas.common.helper.HashHelper;
import oshi.SystemInfo;
import oshi.hardware.ComputerSystem;
import oshi.hardware.HardwareAbstractionLayer;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
public class ClientMonitor {
    private final static String computerSn;
    private final static String hardwareUUID;
    private final static String baseboardSn;

    private final static String clientSource;

    static {
        SystemInfo systemInfo = new SystemInfo();
        HardwareAbstractionLayer hardware = systemInfo.getHardware();
        ComputerSystem computerSystem = hardware.getComputerSystem();
        computerSn = computerSystem.getSerialNumber();
        hardwareUUID = computerSystem.getHardwareUUID();
        baseboardSn = computerSystem.getBaseboard().getSerialNumber();
        clientSource = HashHelper.sha256(computerSn+hardwareUUID+baseboardSn).toUpperCase();

    }

    public static String getClientSource() {
        return clientSource;
    }
}
