package com.deeppaas.rule.biz.model;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.action.RAction;

import java.util.List;
import java.util.Map;

/**
 * 规则模型
 * <AUTHOR>
 * @date 2022/6/16
 */
public class RuleModel {
    private final String id;
    /**
     * 规则动作
     */
    private final Map<String, RAction> actionMap;
    /**
     * 规则参数类型
     */
    private final SimpleDataType paramDataType;

    public RuleModel(String id, Map<String, RAction> actionMap, SimpleDataType paramDataType) {
        this.id = id;
        this.actionMap = actionMap;
        this.paramDataType = paramDataType;
    }

    public Map<String, RAction> getActionMap() {
        return actionMap;
    }

    public SimpleDataType getParamDataType() {
        return paramDataType;
    }

    public RAction getStartAction() {
        List<RAction> startActions = actionMap.values().stream().filter(rAction -> rAction.isStart()).toList();
        if (startActions.size() != 1) {
            throw RunException.error("规则首节点异常,请检查规则");
        }
        return startActions.get(0);
    }
}
