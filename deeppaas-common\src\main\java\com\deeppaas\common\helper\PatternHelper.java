package com.deeppaas.common.helper;

import com.deeppaas.common.exception.RunException;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PatternHelper {

    private final static String NUM = "[^0-9]";
    private final static String IS_NUM = "-?\\\\d+(\\\\.\\\\d+)?\"";

    //正则取字符串中所有数字
    public static List<String> strToNums(String str) {
        Pattern p = Pattern.compile("[^0-9]");
        Matcher m = p.matcher(str);
        String string = m.replaceAll(" ").trim();
        String[] strArr = string.split(" ");
        return Arrays.stream(strArr).toList();
    }

    //正则取字符串中最后一块数字
    public static Long strToNum(String str) {
        Pattern p = Pattern.compile("[^0-9]");
        Matcher m = p.matcher(str);
        String string = m.replaceAll(" ").trim();
        String[] strArr = string.split(" ");
        String lastStr = strArr[strArr.length - 1];
        if (!lastStr.matches("-?\\d+(\\.\\d+)?")) {
            return null;
        }

        return Long.parseLong(lastStr);
    }

    public static String get(String str, String regex) {
        try {
            if (StringHelper.isEmpty(regex)) {
                return null;
            }
            String s="";
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(str);
            while (m.find()){
                s=s+m.group();
            }
          //  String ddss = m.replaceAll(" ").trim();
            return s.replaceAll(" ", "");
        } catch (Exception e) {
            throw RunException.error("正则【" + regex + "】无效！");
        }


    }

}
