package com.deeppaas.file.oss.model;

import com.deeppaas.file.FileConstant;
import com.deeppaas.file.api.model.UploadSign;

/**
 * 阿里云OSS签名
 * <AUTHOR>
 */
public class OssUploadSign extends UploadSign {
    private String accessId;
    private String policy;
    private String signature;
    private String dir;
    private String host;
    private String expire;

    public OssUploadSign() {
        super(FileConstant.MODE_OSS);
    }

    public String getAccessId() {
        return accessId;
    }

    public void setAccessId(String accessId) {
        this.accessId = accessId;
    }

    public String getPolicy() {
        return policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getExpire() {
        return expire;
    }

    public void setExpire(String expire) {
        this.expire = expire;
    }
}
