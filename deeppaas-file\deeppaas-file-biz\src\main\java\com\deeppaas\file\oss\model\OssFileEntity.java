package com.deeppaas.file.oss.model;

import com.aliyun.oss.model.OSSObject;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.file.api.model.FileEntity;

import java.io.IOException;
import java.io.InputStream;

/**
 * 阿里云OSS文件对象
 * <AUTHOR>
 * @date 2020/7/16
 */
public class OssFileEntity extends FileEntity {
    private OSSObject ossObject;

    public OssFileEntity(String group, String fileKey, OSSObject ossObject) {
        super(group, fileKey);
        this.ossObject = ossObject;
    }

    @Override
    public InputStream getContent() {
        return ossObject.getObjectContent();
    }

    @Override
    public void close() {
        try {
            this.ossObject.close();
        } catch (IOException e) {
            e.printStackTrace();
            throw RunException.error("oss object close error");
        }
    }
}
