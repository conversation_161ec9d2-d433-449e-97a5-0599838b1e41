package com.deeppaas.common.data.jdbc;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.jdbc.core.JdbcAggregateTemplate;
import org.springframework.data.jdbc.core.convert.DataAccessStrategy;
import org.springframework.data.jdbc.core.convert.EntityRowMapper;
import org.springframework.data.jdbc.core.convert.JdbcConverter;
import org.springframework.data.jdbc.repository.support.JdbcRepositoryFactory;
import org.springframework.data.mapping.callback.EntityCallbacks;
import org.springframework.data.relational.core.dialect.Dialect;
import org.springframework.data.relational.core.mapping.RelationalMappingContext;
import org.springframework.data.relational.core.mapping.RelationalPersistentEntity;
import org.springframework.data.repository.core.EntityInformation;
import org.springframework.data.repository.core.RepositoryInformation;
import org.springframework.data.repository.core.RepositoryMetadata;
import org.springframework.data.repository.core.support.PersistentEntityInformation;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */
public class JRepositoryFactory extends JdbcRepositoryFactory {
    private final RelationalMappingContext context;
    private final JdbcConverter converter;
    private final ApplicationEventPublisher publisher;
    private final DataAccessStrategy accessStrategy;
    private final NamedParameterJdbcOperations operations;
    private final Dialect dialect;

    private EntityCallbacks entityCallbacks;

    public JRepositoryFactory(DataAccessStrategy dataAccessStrategy, RelationalMappingContext context,
                              JdbcConverter converter, Dialect dialect, ApplicationEventPublisher publisher,
                              NamedParameterJdbcOperations operations) {
        super(dataAccessStrategy, context, converter, dialect, publisher, operations);
        this.publisher = publisher;
        this.context = context;
        this.converter = converter;
        this.dialect = dialect;
        this.accessStrategy = dataAccessStrategy;
        this.operations = operations;
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T, ID> EntityInformation<T, ID> getEntityInformation(Class<T> aClass) {

        RelationalPersistentEntity<?> entity = context.getRequiredPersistentEntity(aClass);

        return (EntityInformation<T, ID>) new PersistentEntityInformation<>(entity);
    }

    @Override
    protected Object getTargetRepository(RepositoryInformation repositoryInformation) {

        JdbcAggregateTemplate template = new JdbcAggregateTemplate(publisher, context, converter, accessStrategy);

        if (entityCallbacks != null) {
            template.setEntityCallbacks(entityCallbacks);
        }

        RelationalPersistentEntity<?> persistentEntity = context
                .getRequiredPersistentEntity(repositoryInformation.getDomainType());
        SqlGenerator sqlGenerator = new SqlGenerator(context, converter, persistentEntity, dialect);
        EntityRowMapper entityRowMapper = new EntityRowMapper<>(persistentEntity, converter);
        return instantiateClass(repositoryInformation.getRepositoryBaseClass(),
                template,
                persistentEntity,
                sqlGenerator,
                entityRowMapper,
                operations);
    }

    @Override
    protected Class<?> getRepositoryBaseClass(RepositoryMetadata repositoryMetadata) {
        return JSimpleJdbcRepository.class;
    }

    public void setEntityCallbacks(EntityCallbacks entityCallbacks) {
        this.entityCallbacks = entityCallbacks;
    }

}
