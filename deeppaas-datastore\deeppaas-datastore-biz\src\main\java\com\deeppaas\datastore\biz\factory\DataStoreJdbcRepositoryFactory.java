package com.deeppaas.datastore.biz.factory;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.datastore.biz.entity.*;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.biz.service.DataStoreEntityJoinService;
import com.deeppaas.datastore.biz.service.DataStoreEntityService;
import com.deeppaas.datastore.biz.service.DataStoreSourceService;
import com.deeppaas.datastore.common.DataStoreRepository;
import com.deeppaas.datastore.common.DataStoreRepositoryCache;
import com.deeppaas.datastore.common.DataStoreRepositoryFactory;
import com.deeppaas.datastore.common.emnus.DBDialect;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import com.deeppaas.datastore.common.jdbc.DataStoreJdbcRepository;
import com.deeppaas.datastore.common.mapping.DataStoreEntity;
import com.deeppaas.datastore.common.mapping.DataStoreProperty;
import com.deeppaas.datastore.common.mapping.DataStoreTableJoin;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/26
 */
@Component
public class DataStoreJdbcRepositoryFactory implements DataStoreRepositoryFactory {
    private final DataStoreSourceService dataStoreSourceService;
    private final DataStoreEntityService dataStoreEntityService;
    private final DataStoreEntityJoinService dataStoreEntityJoinService;
    private final NamedParameterJdbcTemplate defaultTemplate;
    private final DataStoreRepositoryCache dataStoreRepositoryCache;

    private static Map<String, NamedParameterJdbcTemplate> templateMap = new ConcurrentHashMap<>();

    public DataStoreJdbcRepositoryFactory(DataStoreSourceService dataStoreSourceService,
                                          DataStoreEntityService dataStoreEntityService,
                                          DataStoreEntityJoinService dataStoreEntityJoinService,
                                          NamedParameterJdbcTemplate defaultTemplate,
                                          DataStoreRepositoryCache dataStoreRepositoryCache) {
        this.dataStoreSourceService = dataStoreSourceService;
        this.dataStoreEntityService = dataStoreEntityService;
        this.dataStoreEntityJoinService = dataStoreEntityJoinService;
        this.defaultTemplate = defaultTemplate;
        this.dataStoreRepositoryCache = dataStoreRepositoryCache;
    }

    @Override
    public DataStoreRepository getRepository(String entityIndex) {
        DataStoreRepository repository = dataStoreRepositoryCache.get(entityIndex);
        if (repository == null) {
            DataStoreEntityDO dataStoreEntityDO = dataStoreEntityService.findByCode(entityIndex);
            DataStoreEntity entity = getEntity(dataStoreEntityDO);
            NamedParameterJdbcTemplate npJdbcTemplate = getJdbcTemplate(dataStoreEntityDO.getSourceId());
            DBDialect dbDialect = getDBDialect(dataStoreEntityDO.getSourceId());
            repository = new DataStoreJdbcRepository(dbDialect, entity, npJdbcTemplate);
            dataStoreRepositoryCache.put(entityIndex, repository);
        }
        return repository;
    }

    private DataStoreEntity getEntity(DataStoreEntityDO dataStoreEntityDO) {
        List<DataStoreEntityJoinDO> joinList = dataStoreEntityJoinService.findByEntityCode(dataStoreEntityDO.getCode());
        List<DataStoreEntityJoinMatchDO> matchList = dataStoreEntityJoinService.findMatchByEntityCode(dataStoreEntityDO.getCode());
        List<DataStoreEntityFieldDTO> fieldList = dataStoreEntityService.findFieldDTOByEntityCode(dataStoreEntityDO.getCode());
        DataStoreEntity entity = new DataStoreEntity();
        entity.setAlias(dataStoreEntityDO.getCode());
        entity.setType(DataStoreEntityType.valueOf(dataStoreEntityDO.getType()));
        entity.setTable(dataStoreEntityDO.getTableName());
        Map<String, List<DataStoreEntityJoinMatchDO>> joinMatches = matchList.stream().collect(Collectors.groupingBy(DataStoreEntityJoinMatchDO::getJoinCode));
        Map<String, DataStoreTableJoin> joinMap = joinList.stream().collect(
                Collectors.toMap(DataStoreEntityJoinDO::getCode, joinDO -> {
                    DataStoreTableDO tableDO = dataStoreEntityService.getTable(joinDO.getTableId());
                    DataStoreTableJoin join = new DataStoreTableJoin();
                    join.setAlias(joinDO.getCode());
                    if(DataStoreEntityType.SQL_BUILD.name().equals(tableDO.getType())){
                        join.setRealTable(false);
                        join.setJoinTable(tableDO.getBuildSql());
                    }else {
                        join.setRealTable(true);
                        join.setJoinTable(joinDO.getTableName());
                    }
                    List<DataStoreEntityJoinMatchDO> matchDOS = joinMatches.get(joinDO.getCode());
                    if(matchDOS!=null){
                        Map<String, String> matchColumns = matchDOS.stream().collect(Collectors.toMap(DataStoreEntityJoinMatchDO::getLeftColumnName, DataStoreEntityJoinMatchDO::getRightColumnName));
                        join.setMatchColumns(matchColumns);
                    }
                    join.setJoins(new HashSet<>());
                    return join;
                })
        );
        Set<DataStoreTableJoin> joins = new HashSet<>();
        joinList.forEach(joinDO -> {
            DataStoreTableJoin join = joinMap.get(joinDO.getCode());
            if (StringUtils.hasText(joinDO.getParentJoinCode()) && !DataStoreEntityJoinDO.PARENT_JOIN_CODE_NULL.equals(joinDO.getParentJoinCode())) {
                joinMap.get(joinDO.getParentJoinCode()).getJoins().add(join);
            } else {
                joins.add(join);
            }
        });
        entity.setJoins(joins);

        List<DataStoreProperty> properties = new ArrayList<>();
        fieldList.forEach(field -> {
            DataStoreProperty property = new DataStoreProperty();
            property.setName(field.getCode());
            property.setColumnName(field.getColumnName());
            property.setDataType(field.getDataType());
            property.setPrimary(BoolHelper.intToBool(field.getIdKey()));
            property.setVirtual(BoolHelper.intToBool(field.getReadOnly()));
            property.setDiscriminator(BoolHelper.intToBool(field.getDiscriminator()));
            property.setDiscriminatorValue(field.getDiscriminatorValue());
            property.setTableJoin(joinMap.get(field.getJoinCode()));
            property.setUnique(BoolHelper.intToBool(field.getUnique()));
            properties.add(property);
            if (property.isPrimary()) {
                entity.setIdProperty(property);
            }
        });
        entity.setProperties(properties);
        return entity;
    }

    /**
     * 获取NamedParameterJdbcTemplate实例
     *
     * @param dataSourceId 数据源ID
     * @return JdbcTemplate实例
     */
    public NamedParameterJdbcTemplate getJdbcTemplate(String dataSourceId) {
        if (DataStoreSourceDO.ID_DEFAULT.equals(dataSourceId)) {
            return defaultTemplate;
        } else {
            NamedParameterJdbcTemplate jdbcTemplate = templateMap.get(dataSourceId);
            if (jdbcTemplate == null) {
                DataStoreSourceDO sourceDO = dataStoreSourceService.findById(dataSourceId);
                HikariConfig hikariConfig = new HikariConfig();
                hikariConfig.setJdbcUrl(sourceDO.getJdbcUrl());
                hikariConfig.setUsername(sourceDO.getUser());
                hikariConfig.setPassword(sourceDO.getPwd());
                hikariConfig.setDriverClassName(sourceDO.getDriverClass());
                HikariDataSource dataSource = new HikariDataSource(hikariConfig);
                jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
                templateMap.put(dataSourceId, jdbcTemplate);
            }
            return jdbcTemplate;
        }
    }

    public DBDialect getDBDialect(String dataSourceId) {
        if (DataStoreSourceDO.ID_DEFAULT.equals(dataSourceId)) {
            return DBDialect.valueOf(dataStoreSourceService.getDefaultDbType());
        }
        DataStoreSourceDO sourceDO = dataStoreSourceService.findById(dataSourceId);
        if(sourceDO==null || !StringUtils.hasText(sourceDO.getDbType())){
            return null;
        }
        return DBDialect.valueOf(sourceDO.getDbType());
    }
    public String getDBSchema(String dataSourceId){
        DataStoreSourceDO sourceDO = dataStoreSourceService.findById(dataSourceId);
        return sourceDO.getDefaultSchema();
    }

    public String getOwner(String dataSourceId) {
        DataStoreSourceDO sourceDO = dataStoreSourceService.findById(dataSourceId);
        return sourceDO.getUser();
    }

}
