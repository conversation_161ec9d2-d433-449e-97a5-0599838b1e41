package com.deeppaas.msg.biz.web.param;

import com.deeppaas.account.api.client.model.UserScope;
import lombok.Data;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/5
 */
@Data
public class UserTemplateMsgSendParam {
    /**
     * 所属消息模块
     */
    private String boxId;

    /**
     * 消息标题
     */
    private String templateId;

    /**
     * 参数
     */
    private Map<String, Object> params;

    /**
     * 接收人范围
     */
    private Collection<UserScope> toUsers;


}
