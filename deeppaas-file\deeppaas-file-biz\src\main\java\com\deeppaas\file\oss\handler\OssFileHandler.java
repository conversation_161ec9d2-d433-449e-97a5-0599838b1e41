package com.deeppaas.file.oss.handler;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.*;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.LogerHelper;
import com.deeppaas.file.FileConstant;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.api.model.FileEntity;
import com.deeppaas.file.api.model.UploadSign;
import com.deeppaas.file.oss.model.OSSFileGroup;
import com.deeppaas.file.oss.model.OssFileEntity;
import com.deeppaas.file.oss.model.OssUploadSign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
@Component
public class OssFileHandler {
    private static Logger logger = LoggerFactory.getLogger(OssFileHandler.class);

    public UploadSign getUploadSign(FileGroup fileGroup, String fileDir, int expireSeconds) {
        if(fileGroup instanceof OSSFileGroup ossFileGroup){
            try {
                OSSClient ossClient = getOssClient(fileGroup);
                long expireEndTime = System.currentTimeMillis() + expireSeconds * 1000;
                Date expiration = new Date(expireEndTime);
                PolicyConditions policyConds = new PolicyConditions();
                policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
                policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, fileDir);

                String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
                byte[] binaryData = postPolicy.getBytes("utf-8");
                String encodedPolicy = BinaryUtil.toBase64String(binaryData);
                String postSignature = ossClient.calculatePostSignature(postPolicy);

                String host = "https://" + fileGroup.getBucket() + "." + ossFileGroup.getEndPoint().replaceFirst("-internal", "");
                OssUploadSign ossSign = new OssUploadSign();
                ossSign.setAccessId(ossFileGroup.getAccessKeyId());
                ossSign.setPolicy(encodedPolicy);
                ossSign.setSignature(postSignature);
                ossSign.setDir(fileDir);
                ossSign.setHost(host);
                ossSign.setExpire(String.valueOf(expireEndTime / 1000));
                return ossSign;
            } catch (Exception ex) {
                LogerHelper.errorLog(logger, ex);
            }
        }else {
            throw RunException.error("文件组[]不是OSS文件模式");
        }

        return null;
    }

    public void uploadFile(FileGroup fileGroup, String fileKey, File file) {
        getOssClient(fileGroup).putObject(fileGroup.getBucket(), fileKey, file);
    }

    public void uploadFile(FileGroup fileGroup, String fileKey, InputStream input) {
        getOssClient(fileGroup).putObject(fileGroup.getBucket(), fileKey, input);
    }

    public FileEntity getFile(FileGroup fileGroup, String fileKey) {
        OSSObject ossObject = getOssClient(fileGroup).getObject(fileGroup.getBucket(), fileKey);
        OssFileEntity fileEntity = new OssFileEntity(fileGroup.getName(), fileKey, ossObject);
        return fileEntity;
    }

    public String getTempUrl(FileGroup fileGroup, String fileKey, String fileAlias, String process, int expireSeconds) {
        OSSClient ossClient = getOssClient(fileGroup);
        if(fileGroup instanceof OSSFileGroup ossFileGroup){
            if (FileConstant.AUTH_PUBLIC.equalsIgnoreCase(fileGroup.getType())) {
                String publicUlr = fileGroup.getDomain() + fileKey;
                if (StringUtils.hasText(process)) {
                    publicUlr += "?process=" + process;
                }
                return publicUlr;
            }
            try {
                long expireEndTime = System.currentTimeMillis() + expireSeconds * 1000;
                Date expiration = new Date(expireEndTime);
                GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(fileGroup.getBucket(), fileKey);
                request.setExpiration(expiration);
                if (StringUtils.hasText(process)) {
                    request.setProcess(process);
                }
                if (fileAlias != null && !"".equals(fileAlias.trim())) {
                    ResponseHeaderOverrides responseHeaderOverrides = new ResponseHeaderOverrides();
                    try {
                        responseHeaderOverrides.setContentDisposition("attachment; fileName=" + fileAlias
                                + ";fileName*=utf-8''" + URLEncoder.encode(fileAlias, "UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        throw RunException.optReject("文件名称转码异常：" + e.getMessage());
                    }
                    request.setResponseHeaders(responseHeaderOverrides);
                }
                URL url = ossClient.generatePresignedUrl(request);
                ossClient.shutdown();
                String tempUrl = url.toString().replaceFirst("http://", "https://").replaceFirst("-internal", "");
                if (StringUtils.hasText(fileGroup.getDomain())) {
                    String ossDomain = "https://" + fileGroup.getBucket() + "." + ossFileGroup.getEndPoint();
                    tempUrl = tempUrl.replaceAll(ossDomain, fileGroup.getDomain());
                }
                return tempUrl;
            } catch (Exception ex) {
                LogerHelper.errorLog(logger, ex);
            }
        }

        return null;
    }

    public void deleteFile(FileGroup fileGroup, String fileKey) {
        getOssClient(fileGroup).deleteObject(fileGroup.getBucket(), fileKey);
    }

    private OSSClient getOssClient(FileGroup fileGroup){
        if(fileGroup instanceof OSSFileGroup ossFileGroup){
            return new OSSClient(ossFileGroup.getEndPoint(),
                    new DefaultCredentialProvider(ossFileGroup.getAccessKeyId(), ossFileGroup.getAccessKeySecret()), null);
        }else {
            throw RunException.error("文件组[]不是OSS文件模式");
        }
    }
}
