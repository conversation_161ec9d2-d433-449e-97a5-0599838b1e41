package com.deeppaas.processConfiguration.domain.factory;

import com.deeppaas.configuration.biz.entity.AidosProcessConfigDO;
import com.deeppaas.process.biz.entity.AidosProcessDO;
import com.deeppaas.process.biz.entity.AidosProcessTaskDO;
import com.deeppaas.processConfiguration.domain.model.dto.TaskConfigDto;
import com.deeppaas.processConfiguration.domain.model.vo.ProcessConfigVO;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.StringHelper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ProcessTaskConfigFactory {

    public List<ProcessConfigVO> tobuilds(List<AidosProcessDO> aidosProcessDOs, List<AidosProcessTaskDO> aidosProcessTaskDOs, List<AidosProcessConfigDO> aidosProcessConfigDOs) {
        Map<String, AidosProcessDO> aidosProcessMap = aidosProcessDOs.stream().collect(Collectors.toMap(AidosProcessDO::getId, Function.identity()));


        Map<String, List<AidosProcessConfigDO>> configMap = aidosProcessConfigDOs.stream().collect(Collectors.groupingBy(AidosProcessConfigDO::getProcessTaskId));

        List<ProcessConfigVO> lists = new ArrayList<>();
        aidosProcessTaskDOs.forEach(item -> {
            AidosProcessDO aidosProcessDO = aidosProcessMap.get(item.getProcessId());
            if (aidosProcessDO != null) {
                List<AidosProcessConfigDO> list = configMap.get(item.getId());
                lists.add(tobuild(aidosProcessDO, item, list));
            }

        });


        return lists;
    }


    public ProcessConfigVO tobuild(AidosProcessDO aidosProcessDO, AidosProcessTaskDO aidosProcessTaskDO, List<AidosProcessConfigDO> list) {
        if (aidosProcessTaskDO == null)
            aidosProcessTaskDO = new AidosProcessTaskDO();


        ProcessConfigVO processConfigVO = new ProcessConfigVO();
        processConfigVO.setProcessId(aidosProcessDO.getId());
        processConfigVO.setProcessName(aidosProcessDO.getName());
        processConfigVO.setProcessTaskId(aidosProcessTaskDO.getId());
        processConfigVO.setProcessTaskName(aidosProcessTaskDO.getName());
        String templates=aidosProcessTaskDO.getTemplates();
        processConfigVO.setTemplates(templates);
        if(StringHelper.isNotEmpty(templates)){
            processConfigVO.setTemplateNo(templates.split(",").length);
        }

        tobuild(processConfigVO, list);
        return processConfigVO;
    }

    public ProcessConfigVO tobuild(ProcessConfigVO processConfigVO, List<AidosProcessConfigDO> list) {
        if (list == null)
            list = Lists.newArrayList();
        List<TaskConfigDto> taskConfigDtos = Lists.newArrayList();
        list.forEach(item -> {
            TaskConfigDto taskConfigDto = new TaskConfigDto();
            taskConfigDto.setConfigId(item.getId());
            taskConfigDto.setConfigName(item.getName());
            taskConfigDto.setConfigAilas(item.getAlias());
            if (item.getConfigs() != null)
                taskConfigDto.setConfig(JsonHelper.json2map(item.getConfigs()));
            taskConfigDtos.add(taskConfigDto);

        });
        processConfigVO.setConfigs(taskConfigDtos);
        return processConfigVO;
    }
}
