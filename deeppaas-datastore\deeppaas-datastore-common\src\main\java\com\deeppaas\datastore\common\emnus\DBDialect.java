package com.deeppaas.datastore.common.emnus;

import com.deeppaas.datastore.common.dialect.AbstractDialect;
import com.deeppaas.datastore.common.dialect.KingbaseDialect;
import com.deeppaas.datastore.common.dialect.MySqlDialect;
import com.deeppaas.datastore.common.dialect.PostgresqlDialect;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 数据库类型
 * <AUTHOR>
 * @date 2022/3/5
 */
public enum DBDialect {
    MYSQL(new MySqlDialect()),

    KINGBASE(new KingbaseDialect()),

    POSTGRESQL(new PostgresqlDialect());

    private AbstractDialect dialect;

    DBDialect(AbstractDialect dialect) {
        this.dialect = dialect;
    }

    public String limitOffset(Pageable pageable) {
        return dialect.limitOffset(pageable);
    }

    public String backtick(String name){
        return dialect.backtick(name);
    }

    public String createSelfTableSql(String schema, String tableName, String owner) {
        return dialect.createSelfTableSql(schema, tableName, owner);
    }

    public String createSelfTableColumnSql(String schema, String tableName, String fieldCode, String columnName, String dataType, Integer length, Integer decimalDigits) {
        return dialect.createSelfTableColumnSql(schema, tableName, fieldCode, columnName, dataType, length, decimalDigits);
    }

    public String modifySelfTableColumnSql(String schema, String tableName, String fieldCode, String columnName, String dataType, Integer length, Integer decimalDigits) {
        return dialect.modifySelfTableColumnSql(schema, tableName, fieldCode, columnName, dataType, length, decimalDigits);
    }

    public String createTableIndexSql(String schema, String tableName, String indexName, TableIndexType indexType, List<String> indexColumns) {
        return dialect.createTableIndexSql(schema, tableName, indexName, indexType, indexColumns);
    }

    public String deleteTableIndexSql(String schema, String tableName, String indexName) {
        return dialect.deleteTableIndexSql(schema, tableName, indexName);
    }
}
