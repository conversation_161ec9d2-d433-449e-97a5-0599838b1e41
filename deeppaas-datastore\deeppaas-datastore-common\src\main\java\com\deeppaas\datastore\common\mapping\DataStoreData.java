package com.deeppaas.datastore.common.mapping;

import com.deeppaas.common.exception.RunException;

import java.util.HashMap;
import java.util.Map;

/**
 * 一条数据
 * <AUTHOR>
 * @date 2022/1/6
 */
public class DataStoreData extends HashMap<String, Object>{
    /**
     * 唯一主键值
     */
    public static final String KEY_ID = "@ID";
    /**
     * 对应模型
     */
    public static final String KEY_ENTITY = "@ENTITY";

    public static DataStoreData instance(String entity, Map<String, Object> dataMap) {
        return instance(entity, null, dataMap);
    }

    public static DataStoreData instance(String entity, Object id, Map<String, Object> dataMap) {
        if(entity==null){
            throw RunException.optReject("entity不能为空");
        }
        DataStoreData dataStoreData = new DataStoreData();
        if(dataMap!=null){
            dataStoreData.putAll(dataMap);
        }
        dataStoreData.put(KEY_ID, id);
        dataStoreData.put(KEY_ENTITY, entity);
        return dataStoreData;
    }

    private DataStoreData(){

    }

    public Object getID(){
        return this.get(KEY_ID);
    }

    public String getEntity() {
        Object entity = this.get(KEY_ENTITY);
        if(entity != null){
            return entity.toString();
        }
        return null;
    }

}
