[{"stepId": "step1", "type": "VARIABLE", "name": "用户所在部门编号列表", "varName": "userDeptCodes", "target": {"type": "FUN", "value": {"name": "GetCurUserDeptCodes", "params": []}}, "next": "step2"}, {"stepId": "step2", "type": "DECISION", "name": "当前人所属部门是否包含IT", "target": {"type": "FUN", "value": {"name": "ArrayContains", "params": [{"type": "VAR", "value": "userDeptCodes"}, {"type": "CONST", "value": "IT"}]}}, "next": {"tureNext": "step3", "falseNext": "step4"}}, {"stepId": "step3", "type": "DATA_QUERY", "name": "可数据查看范围,只能看自己部门", "auth": true, "condition": {"operator": "AND", "conditions": [{"field": "userDept", "operator": "IN", "target": {"type": "VAR", "value": "userDeptCodes"}}]}}, {"stepId": "step4", "type": "DATA_QUERY", "name": "返回", "auth": false}]