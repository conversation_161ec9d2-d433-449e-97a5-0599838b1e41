package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.entity.FlowAssignDO;
import com.deeppaas.flow.biz.model.AssignInfo;
import com.deeppaas.flow.biz.element.TaskNode;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @ClassName: FlowAssignService 
 * @Description: 流程流转环节处理人指派服务接口
 * <AUTHOR>
 */
public interface FlowAssignService {

	/**
	 * @Title: assign 
	 * @Description: 向指定流程实例环节指派处理人
	 * @param instanceId
	 * @param nodeCode
	 * @param actors
	 * @param priority
	 * @param limitedTime
	 * <AUTHOR>
	 */
	void assign(Long instanceId, String nodeCode, List<String> actors, Integer priority, LocalDateTime limitedTime);

	/**
	 * 根据流程实例ID环节ID获取
	 * @param instanceId
	 * @param nodeCode
	 * @return
	 */
	FlowAssignDO findByInstanceIdAndNodeCode(Long instanceId, String nodeCode);

    void init(FlowAssignDO assign);

	/**
	 * 指派生成任务
	 * @param executeItem
	 * @param node
	 * @return
	 */
	boolean doAssign(ExecuteItem executeItem, TaskNode node);

	/**
	 * 模拟获取审批人
	 * @param executeItem
	 * @param node
	 * @return
	 */
	Collection<String> mockAssignUser(ExecuteItem executeItem, TaskNode node);

	/**
	 * 模拟指派
	 * @param executeItem
	 * @param node	流程环节
	 * @param optionActors 备选参与人
	 * @return
	 */
	AssignInfo mockAssign(ExecuteItem executeItem, TaskNode node , Collection<String> optionActors);

}
