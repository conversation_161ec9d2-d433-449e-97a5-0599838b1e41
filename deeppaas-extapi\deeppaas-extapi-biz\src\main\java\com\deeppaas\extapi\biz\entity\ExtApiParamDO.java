package com.deeppaas.extapi.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 请求参数
 *
 * <AUTHOR>
 * @date 2022/2/25
 */
@Data
@Table("ext_api_param")
@EqualsAndHashCode(callSuper = true)
public class ExtApiParamDO extends BaseUuidDO {
    /**
     * 所属API
     */
    @Column("api_id")
    private String apiId;

    /**
     * 参数code
     */
    @Column("code")
    private String code;

    /**
     * 参数中文名
     */
    @Column("name")
    private String name;

    /**
     * 参数类型
     * <p>
     * 文本、数字等
     * @com.deeppaas.common.data.enums.SimpleDataType
     */
    @Column("type")
    private String type;

    /**
     * 如果类型是模型，对应模型ID或code
     */
    @Column("entity")
    private String entity;

    /**
     * 传输位置
     * <p>
     * 查询参数、Headers..
     */
    @Column("position")
    private String position;

    /**
     * 是否必填
     * <p>
     * 1=是
     * 0=否
     */
    @Column("required")
    private Integer required;

    /**
     * 是否参与鉴权
     * <p>
     * 1=是
     * 0=否
     */
    @Column("right")
    private Integer right;

    /**
     * 参数描述
     */
    @Column("remark")
    private String remark;
}
