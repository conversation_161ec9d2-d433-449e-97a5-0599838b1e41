package com.deeppaas.common.helper;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串处理工具
 *
 * <AUTHOR>
 */
public class StringHelper {
    /**
     * 格式={xx}
     */
    private static final Pattern format_pattern = Pattern.compile("\\{(.*?)\\}");
    private static Matcher format_matcher;

    /**
     * 判断是否为null或空串
     *
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        if (str == null) {
            return true;
        }
        if ("".equals(str.trim())) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否不为null或空串
     *
     * @param str
     * @return
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 按分隔符将字符串组装为字符串数组
     * 如果传入参数为null或空串，直接放回空数组
     *
     * @param toSplit
     * @param delimiter
     * @return
     */
    public static String[] split(String toSplit, String delimiter) {
        if (isNotEmpty(toSplit)) {
            return toSplit.split(delimiter);
        }
        return new String[]{};
    }

    /**
     * 按分隔符将字符串组装为字符串List集合
     * 如果传入参数为null或空串，直接放回空List
     *
     * @param toSplit
     * @param delimiter
     * @return
     */
    public static List<String> splitToList(String toSplit, String delimiter) {
        return Arrays.asList(split(toSplit, delimiter));
    }

    /**
     * 将html转为存文本
     *
     * @param htmlStr
     * @return
     */
    public static String html2Text(String htmlStr) {
        if (isEmpty(htmlStr)) {
            return "";
        }
        String textStr = "";
        Pattern pScript;
        Matcher mScript;
        Pattern pStyle;
        Matcher mStyle;
        Pattern pHtml;
        Matcher mHtml;

        try {
            String regexScript = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>"; // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
            // }
            String regexStyle = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style>
            // }
            String regexHtml = "<[^>]+>"; // 定义HTML标签的正则表达式


            pScript = Pattern.compile(regexScript, Pattern.CASE_INSENSITIVE);
            mScript = pScript.matcher(htmlStr);
            htmlStr = mScript.replaceAll(""); // 过滤script标签

            pStyle = Pattern.compile(regexStyle, Pattern.CASE_INSENSITIVE);
            mStyle = pStyle.matcher(htmlStr);
            htmlStr = mStyle.replaceAll(""); // 过滤style标签

            pHtml = Pattern.compile(regexHtml, Pattern.CASE_INSENSITIVE);
            mHtml = pHtml.matcher(htmlStr);
            htmlStr = mHtml.replaceAll(""); // 过滤html标签


            textStr = htmlStr.replaceAll("\r\n", "").replaceAll("\n", "").replaceAll("&nbsp;", "").replaceAll("\t", "").replaceAll("\\[b\\]", "").replaceAll("\\[/b\\]", "").replaceAll("\\[/img\\]", "").replaceAll("\\[img\\]", "").trim();
            textStr = textStr.replaceAll("&amp;ldquo;", "").replaceAll("&amp;rdquo;", "").trim();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return textStr;// 返回文本字符串
    }

    /**
     * 将html文本转为指定字符数量的文本
     *
     * @param htmlStr
     * @param limitNum
     * @return
     */
    public static String html2Text(String htmlStr, int limitNum) {
        String textStr = html2Text(htmlStr);
        if (textStr.length() > limitNum) {
            textStr = textStr.trim();
            textStr = textStr.substring(0, limitNum) + "...";
        }
        return textStr;// 返回文本字符串
    }

    /**
     * 连接返回字符串
     *
     * @param coll
     * @param separator
     * @return
     */
    public static String join(Collection<?> coll, String separator) {
        return join(coll, separator, "", "");
    }

    /**
     * 连接返回字符串并添加前缀和后缀
     *
     * @param coll
     * @param separator
     * @param prefix
     * @param suffix
     * @return
     */
    public static String join(Collection<?> coll, String separator, String prefix, String suffix) {
        if (coll == null || coll.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        Iterator<?> it = coll.iterator();
        while (it.hasNext()) {
            sb.append(prefix).append(it.next()).append(suffix);
            if (it.hasNext()) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }


    public static Number strToNum(String numStr) {
        if (isEmpty(numStr)) {
            return null;
        }
        if (numStr.indexOf(".") > -1) {
            return Double.valueOf(numStr);
        } else {
            return Long.valueOf(numStr);
        }
    }

    public static String toString(Object obj) {
        if (obj == null)
            return "";
        return obj.toString();
    }

    /**
     * 是否包含
     *
     * @param content
     * @param keyword
     * @return
     */
    public boolean contain(String content, String keyword) {
        if (isEmpty(content)) {
            return false;
        }
        return content.indexOf(keyword) > -1;
    }

    /**
     * @param text
     * @return
     */
    public static String text2html(String text) {
        String result = "";
        if (isNotEmpty(text)) {
            result = text
                    .replaceAll("\"", "&quot;")
                    .replaceAll("\'", "&apos;")
                    .replaceAll("<", "&lt;")
                    .replaceAll(">", "&gt;")
                    .replaceAll("&'", "&amp;");
        }
        return result;
    }

    public static String removeScript(String htmlStr) {
        if (isEmpty(htmlStr)) {
            return "";
        }
        String textStr = "";
        Pattern pScript;
        Matcher mScript;

        try {
            // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
            String regexScript = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>";
            pScript = Pattern.compile(regexScript, Pattern.CASE_INSENSITIVE);
            mScript = pScript.matcher(htmlStr);
            // 过滤script标签
            htmlStr = mScript.replaceAll("");

            textStr = htmlStr;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return textStr;// 返回文本字符串
    }

    public static Boolean has(List<Long> list, Long id) {
        if (null == list) {
            return false;
        }
        return list.contains(id);
    }

    public static boolean equals(String str1, String str2) {
        return str1 == null ? str2 == null : str1.equals(str2);
    }

    public static boolean equalsIgnoreCase(String str1, String str2) {
        return str1 == null ? str2 == null : str1.equalsIgnoreCase(str2);
    }

    /**
     * 整理字符串，如果为空返回空串，如果不为空清除空格
     *
     * @param str
     * @return
     * <AUTHOR>
     * @date 2016年7月4日
     */
    public static String trim(String str) {
        if (null == str) {
            return "";
        } else {
            return str.trim();
        }
    }

    /**
     * 判断字符窜是否是正整数类型
     *
     * @param @return
     * @return boolean
     * @Title: isInteger
     */
    public static boolean isInteger(String str) {
        if (str != null && str.matches("^[0-9]+$")) {
            return true;
        }
        return false;
    }

    /**
     * 验证是否为手机号
     *
     * @param numStr
     * @return
     * <AUTHOR>
     * @date 2016年7月7日
     */
    public static boolean isMobile(String numStr) {
        if (numStr == null || numStr.length() != 11) {
            return false;
        }
        String mobileRegex = "^1[0-9]{10}$";
        if (numStr.matches(mobileRegex)) {
            return true;
        }
        return false;
    }

    /**
     * @param str
     * @return
     * @Title: isNum
     * @Description: 判断字符串是否为数字
     * <AUTHOR>
     * @date 2017年9月15日
     */
    public static boolean isNum(String str) {
        return str.matches("-?\\d+(\\.\\d+)?");
    }


    /**
     * 格式化字符串 (替换所有) 字符串中使⽤{key}表⽰占位符
     *
     * @param sourceStr 需要匹配的字符串）
     * @param params    参数集
     * @return 替换后的字符串
     */
    public static String formatAll(String sourceStr, Map<String, Object> params) {
        if (isEmpty(sourceStr) || params == null || params.isEmpty()) {
            return sourceStr;
        }
        String targetStr = sourceStr;
        format_matcher = format_pattern.matcher(targetStr);
        while (format_matcher.find()) {
            String groupKey = format_matcher.group();
            String replaceCode = groupKey.substring(1, groupKey.length() - 1).trim();
            if (params.containsKey(replaceCode)) {
                Object value = params.get(replaceCode);
                targetStr = targetStr.replace(groupKey, value.toString());
            }
        }
        return targetStr;
    }

    /***
     * <AUTHOR>
     * @Description: 取字符串中的符号
     * @date 2022/3/14 10:15
     * @Param msg: "scan/老文件展示/ad.min/001-001-001/";
     * @return: java.lang.String
     */
    public static String regularSymbol(String msg) {
        String str = "";
        //Pattern p = Pattern.compile("[\\s\\p{P}\\n\\r=+$￥<>^`~|]");
        Pattern p = Pattern.compile("[\\/\\-\\·]");
        Matcher m = p.matcher(msg);
        while (m.find()) {
            str = str + m.group();
        }
        return str;
    }


}
