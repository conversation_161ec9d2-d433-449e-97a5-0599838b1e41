package com.deeppaas.msg.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.msg.biz.entity.UserMsgDO;
import com.deeppaas.msg.biz.web.vo.UserMsgCountVO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public interface UserMsgDao extends BaseJdbcDao<UserMsgDO, String> {

    @Modifying
    @Query("update deeppaas_user_msg set read_flag = 1, read_time = :readTime where id in :ids and to_user = :userCode")
    void updateReadByIds(List<String> ids, String userCode, LocalDateTime readTime);

    @Modifying
    @Query("update deeppaas_user_msg set read_flag = 1, read_time = :readTime where to_user = :userCode and box_id=:boxId and read_flag!=1")
    void updateReadByBox(String boxId, String userCode, LocalDateTime readTime);

    @Modifying
    @Query("update deeppaas_user_msg set read_flag = 1, read_time = :readTime where to_user = :userCode and read_flag!=1")
    void updateReadByUser(String userCode, LocalDateTime readTime);

    @Modifying
    @Query("update deeppaas_user_msg set del=1, del_time=:delTime where id in :ids and to_user=:userCode and del!=1")
    void updateDelByIdsO(List<String> ids, String userCode, LocalDateTime delTime);

    @Query(value = "select box_id as boxId, count(*) as msgCount from deeppaas_user_msg " +
            "WHERE to_user=:userCode and del=0 and read_flag=0 GROUP BY box_id")
    List<UserMsgCountVO> countForBoxUnRead(String userCode);
}
