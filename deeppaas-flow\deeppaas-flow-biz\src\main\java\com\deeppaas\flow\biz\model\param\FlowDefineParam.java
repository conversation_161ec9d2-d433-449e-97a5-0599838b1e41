package com.deeppaas.flow.biz.model.param;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
@Data
public class FlowDefineParam {
    /**
     * ID
     */
    private String id;
    /**
     * 编号（所有版本编号相同）
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 数据模型
     */
    private String entityCode;
    /**
     * 流程定义内容
     */
    private String defJson;
    /**
     * 附件
     */
    private String attachment;
    /**
     * 流程描述
     */
    private String flowMessage;
    /**
     * 流程备注
     */
    private String remark;

}
