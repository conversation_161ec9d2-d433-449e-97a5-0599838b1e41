package com.deeppaas.common.data.converter;

import com.deeppaas.common.helper.JsonHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;

import java.util.Collections;
import java.util.List;

/**
 * Json转List转换器
 * <AUTHOR>
 * @date 2021/12/24
 */
@ReadingConverter
public class JsonToListConverter<E> implements Converter<String, List<E>> {

    @Override
    public List<E> convert(String source) {
        if(source==null){
            return Collections.emptyList();
        }
        try {
            TypeReference<List<E>> listType = new TypeReference<>(){};
            List<E> list = JsonHelper.readValue(source, listType);
            return list;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }
}
