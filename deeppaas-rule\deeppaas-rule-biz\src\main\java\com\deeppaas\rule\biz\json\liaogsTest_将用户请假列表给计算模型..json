[{"stepId": "step1", "type": "DATA_LIST", "returned": false, "returnVar": true, "varName": "varApplyOrder", "varType": "DATA_ENTITIES", "varEntity": "userAbsenceApply", "name": "获取员工请假申请单", "dataEntity": "ET_UserAbsenceApply", "permits": [], "defaultCondition": {"field": "applyType", "operator": "EQ", "dataBind": {"targetType": "CONST", "value": "年假", "dataType": "TEXT"}}, "next": "step2"}, {"stepId": "step2", "type": "VARIABLE", "returned": true, "returnVar": false, "varName": "resultData", "varType": "DATA_ENTITIES", "varEntity": "06p22er400217hyf87", "name": "员工请假信息列表", "dataBind": {"targetType": "VARS", "value": "varApplyOrder", "dataType": "DATA_ENTITIES", "fields": [{"code": "empCode", "dataBind": {"targetType": "VARS", "dataType": "TEXT", "value": "varApplyOrder.applyUserCode"}}, {"code": "empName", "dataBind": {"targetType": "VARS", "dataType": "TEXT", "value": "varApplyOrder.applyUserName"}}, {"code": "empDays", "dataBind": {"targetType": "EXPR", "dataType": "NUMBER", "value": "vars.varApplyOrder.applyDays + 20"}}]}}]