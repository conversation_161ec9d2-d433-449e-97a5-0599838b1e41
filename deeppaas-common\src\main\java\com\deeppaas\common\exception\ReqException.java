package com.deeppaas.common.exception;

/**
 * @Description 请求异常
 * <AUTHOR>
 * @Date 2019-03-21
 */
public class ReqException extends BaseException {
    /**
     * 请求参数未找到异常码
     */
    private static final int PARAM_NOT_FOUND_CODE = 4001;
    /**
     * 请求参数校验错误异常码
     */
    private static final int PARAM_VALIDATE_CODE = 4002;

    private ReqException(int code, String message) {
        super(code, message);
    }

    /**
     * @Description: 请求参数为空
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static ReqException paramNotFound(String message) {
        return new ReqException(PARAM_NOT_FOUND_CODE, message);
    }

    /**
     * @Description: 请求参数校验失败
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static ReqException paramValidate(String message) {
        return new ReqException(PARAM_VALIDATE_CODE, message);
    }

}
