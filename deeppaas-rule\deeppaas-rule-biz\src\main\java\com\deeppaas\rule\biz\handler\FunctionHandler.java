package com.deeppaas.rule.biz.handler;

import com.deeppaas.account.api.client.DeptClient;
import com.deeppaas.account.api.client.UserClient;
import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.account.api.client.model.OrgNode;
import com.deeppaas.account.api.client.model.User;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BeanHelper;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.UidGenerator;
import com.deeppaas.datastore.common.mapping.DataStoreFile;
import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.file.api.model.FileEntity;
import com.deeppaas.rule.biz.function.RFunctions;
import com.deeppaas.rule.biz.function.SimpleFunction;
import com.deeppaas.rule.biz.model.RFunction;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 方法处理器
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@Component
public class FunctionHandler {
    private final UserClient userClient;
    private final DeptClient deptClient;
    private final FileClient fileClient;

    public FunctionHandler(UserClient userClient, DeptClient deptClient, FileClient fileClient) {
        this.userClient = userClient;
        this.deptClient = deptClient;
        this.fileClient = fileClient;
    }

    /**
     * 执行方法
     *
     * @param function
     * @param arguments
     * @return
     */
    public Object execute(RFunction function, Object[] arguments) {
        RFunctions rFunctions = RFunctions.valueOf(function.getName());
        if (rFunctions == null) {
            throw RunException.error("没有找到方法" + function.getName());
        }
        Class[] paramTypes = rFunctions.getParamTypes();
        if (paramTypes.length != arguments.length) {
            List<String> paramTypeNames = Arrays.stream(paramTypes).map(Class::getSimpleName).collect(Collectors.toList());
            throw RunException.error("方法" + function.getName() + "传参异常,参数需求" + JsonHelper.toJson(paramTypeNames) + ",配置参数" + JsonHelper.toJson(arguments));
        }
        return execute(rFunctions, arguments);
    }


    private Object execute(RFunctions rFunctions, Object[] arguments) {
        switch (rFunctions) {
            case ArrayContains -> {
                return SimpleFunction.arrayContains(arguments);
            }
            case ArrayJoin -> {
                return SimpleFunction.arrayJoin(arguments);
            }
            case GetCurUserProperty -> {
                String curUserCode = LocalUserContext.getCurUserCode();
                User user = userClient.getUserByCode(curUserCode);
                Map<String, Object> userPropertyMap = BeanHelper.objectToMap(user);
                return userPropertyMap.get(arguments[0]);
            }
            case GetCurUserDeptCodes -> {
                String curUserCode = LocalUserContext.getCurUserCode();
                List<OrgNode> userDeptList = deptClient.listUserDept(curUserCode);
                return userDeptList.stream().map(OrgNode::getCode).toList();
            }
            case GetCurDate -> {
                String dateFormat = String.valueOf(arguments[0]);
                return DateHelper.date2str(new Date(), dateFormat);
            }
            case Equals -> {
                return Objects.equals(arguments[0], arguments[1]);
            }
            case IMAGE_BASE64 -> {
                if(arguments[0] instanceof File file){
                    return SimpleFunction.toBase64(file);
                }
                if(arguments[0] instanceof DataStoreFile dataStoreFile){
                    FileEntity fileEntity = fileClient.getFile(dataStoreFile.getGroup(), dataStoreFile.getKey());
                    return SimpleFunction.toBase64(fileEntity.getContent());
                }
                throw RunException.error("只能文件将转换为Base64");
            }
            case NextDPUUID -> {
                return UidGenerator.nextId();
            }
            default -> throw RunException.error("暂不支持方法" + rFunctions.name());
        }
    }
}
