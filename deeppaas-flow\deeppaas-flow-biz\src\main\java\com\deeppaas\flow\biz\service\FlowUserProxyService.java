package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.dao.FlowUserProxyDao;
import com.deeppaas.flow.biz.entity.FlowUserProxyDO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class FlowUserProxyService {
    private final FlowUserProxyDao flowUserProxyDao;

    public FlowUserProxyService(FlowUserProxyDao flowUserProxyDao) {
        this.flowUserProxyDao = flowUserProxyDao;
    }

    /**
     * 获取可用的代理用户
     * @param: flowUserId
     * @return: com.lh.oa.org.entity.User
     * <AUTHOR>
     **/
    public String getProxyUser(String flowUser) {
        FlowUserProxyDO userProxy = flowUserProxyDao.findByFlowUser(flowUser);
        if(userProxy==null){
            return null;
        }else{
            FlowUserProxyDO childProxy = flowUserProxyDao.findByFlowUser(userProxy.getProxyUser());
            if(childProxy==null){
                return userProxy.getProxyUser();
            }
            return getProxyUser(userProxy.getProxyUser());
        }
    }

//    @Transactional
//    public void add(Long flowUserId, Long proxyUserId) {
//        FlowUserProxy userProxy = flowUserProxyDao.findByFlowUserId(flowUserId);
//        if(userProxy==null){
//            userProxy = new FlowUserProxy();
//            userProxy.setFlowUserId(flowUserId);
//            userProxy.setProxyUserId(proxyUserId);
//        }else {
//            if(!Objects.equals(proxyUserId, userProxy.getProxyUserId())){
//                userProxy.setProxyUserId(proxyUserId);
//            }
//        }
//        flowUserProxyDao.save(userProxy);
//    }
//
//    @Transactional
//    public void proxyTodoUser(Long flowUserId, Long proxyUserId){
//        String flowInstanceSql = "select * from flow_instance where status="+FlowInstance.STATUS_OPEN+" and JSON_CONTAINS(todo_users, '"+flowUserId+"')";
//        List<FlowInstance> flowInstanceList = entityManager.createNativeQuery(flowInstanceSql, FlowInstance.class).getResultList();
//        for(FlowInstance flowInstance : flowInstanceList){
//            List<Long> todoUserIds = flowInstance.getTodoUserIds();
//            todoUserIds.remove(flowUserId);
//            if(!todoUserIds.contains(proxyUserId)){
//                todoUserIds.add(proxyUserId);
//            }
//            flowInstance.setTodoUsers(JsonHelper.toJson(todoUserIds));
//            flowInstanceDao.save(flowInstance);
//        }
//        String flowTaskSql = "select * from flow_task where state!=0 and JSON_CONTAINS(todo_users, '"+flowUserId+"')";
//        List<FlowTask> flowTaskList = entityManager.createNativeQuery(flowTaskSql, FlowTask.class).getResultList();
//        for(FlowTask flowTask : flowTaskList){
//            List<Long> todoUserIds = Lists.newArrayList(flowTask.getTodoUsers());
//            todoUserIds.remove(flowUserId);
//            if(!todoUserIds.contains(proxyUserId)){
//                todoUserIds.add(proxyUserId);
//            }
//            flowTask.setTodoUsers(todoUserIds);
//            flowTaskDao.save(flowTask);
//        }
//        String flowOrderSql = "select * from flow_order where status="+OaOrder.STATUS_AUDIT+" and JSON_CONTAINS(todo_users, '"+flowUserId+"')";
//        List<FlowOrder> flowOrderList = entityManager.createNativeQuery(flowOrderSql, FlowOrder.class).getResultList();
//        for(FlowOrder flowOrder : flowOrderList){
//            List<Long> todoUserIds = flowOrder.getTodoUserIds();
//            todoUserIds.remove(flowUserId);
//            if(!todoUserIds.contains(proxyUserId)){
//                todoUserIds.add(proxyUserId);
//            }
//            flowOrder.setTodoUsers(JsonHelper.toJson(todoUserIds));
//            flowOrderDao.save(flowOrder);
//        }
//        List<OaOrder> oaOrderList = oaOrderDao.findByStatusAndTodoUser(OaOrder.STATUS_AUDIT, flowUserId);
//        for(OaOrder oaOrder : oaOrderList){
//            List<Long> todoUserIds = JsonHelper.json2LongList(oaOrder.getTodoUsers());
//            todoUserIds.remove(flowUserId);
//            if(!todoUserIds.contains(proxyUserId)){
//                todoUserIds.add(proxyUserId);
//            }
//            oaOrder.setTodoUsers(JsonHelper.toJson(todoUserIds));
//            oaOrderDao.save(oaOrder);
//        }
//    }

}
