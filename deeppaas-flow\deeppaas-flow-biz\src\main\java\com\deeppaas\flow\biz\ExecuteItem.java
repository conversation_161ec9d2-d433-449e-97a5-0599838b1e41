package com.deeppaas.flow.biz;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.flow.biz.element.ForkNode;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowTokenDO;
import com.deeppaas.flow.biz.model.FlowDefineModel;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * @ClassName: ExecuteItem 
 * @Description: 流程引擎执行器
 * <AUTHOR>
 */
public class ExecuteItem implements Serializable {
	private final FlowEngine engine;
	/** 流程定义对象 */
	private final FlowDefineModel define;
	/** 流程实例对象 */
	private FlowInstanceDO instance;
	/** 流程令牌对象 */
	private FlowTokenDO token;
	/** 当前环节 */
	private FlowNode node;
	/** 发起源环节 */
	private FlowNode sourceNode;
	/** 跳转节点 */
	private FlowNode jumpNode;
	/** 动作 */
	private FlowAction action;
	/** 操作人 */
	private String operator;
	/** 意见 */
	private String comment;
	/**
	 * 处理任务
	 */
	private FlowTaskDO task;
	/**
	 * 流程申请单数据
	 */
	private DataStoreData orderData;

	public ExecuteItem(FlowEngine engine, FlowDefineModel define, FlowInstanceDO instance, FlowTokenDO token,
					   FlowNode node, FlowAction action, String operator, String comment) {
		this.engine = engine;
		this.define = define;
		this.instance = instance;
		this.token = token;
		this.node = node;
		this.sourceNode = node;
		this.action = action;
		this.operator = operator;
		this.comment = comment;
	}

	public FlowEngine getEngine() {
		return engine;
	}

	public FlowDefineModel getDefine() {
		return define;
	}

	public FlowInstanceDO getInstance() {
		return instance;
	}

	public FlowTokenDO getToken() {
		return token;
	}

	public FlowNode getNode() {
		return node;
	}

	public FlowNode getSourceNode() {
		return sourceNode;
	}

	public FlowNode getJumpNode() {
		return jumpNode;
	}

	public FlowAction getAction() {
		return action;
	}

	public String getOperator() {
		return operator;
	}

	public String getComment() {
		return comment;
	}

	public FlowTaskDO getTask() {
		return task;
	}

	public void setInstance(FlowInstanceDO instance) {
		this.instance = instance;
	}

	public void setToken(FlowTokenDO token) {
		this.token = token;
	}

	public void setNode(FlowNode node) {
		this.node = node;
	}

	public void setSourceNode(FlowNode sourceNode) {
		this.sourceNode = sourceNode;
	}

	public void setJumpNode(FlowNode jumpNode) {
		this.jumpNode = jumpNode;
	}

	public void setAction(FlowAction action) {
		this.action = action;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public void setTask(FlowTaskDO task) {
		this.task = task;
	}

	public void setOrderData(DataStoreData orderData) {
		this.orderData = orderData;
	}

	public ExecuteItem subExecuteItem(FlowNode flowNode) {
		FlowTokenDO childToken = new FlowTokenDO();
		childToken.setInstanceId(this.instance.getId());
		childToken.setNodeCode(flowNode.getCode());
		childToken.setUsed(BoolHelper.INT_TRUE);
		childToken.setParentId(token.getId());
		return new ExecuteItem(engine, define,
				instance,childToken,
				flowNode, action,
				null,null);
	}


	public DataStoreData getOrderData() {
		if(orderData==null){
			orderData = engine.getOrderData(define.getEntityCode(), instance.getOrderId());
		}
		return orderData;
	}

	/**
	 * 模拟一个执行器，为模拟流转使用
	 * @return
	 */
    public MockExecuteItem mock() {
		FlowInstanceDO mockInstance = new FlowInstanceDO();
		BeanUtils.copyProperties(this.instance, mockInstance);
		MockExecuteItem mockItem = new MockExecuteItem(
				this.engine, this.define, mockInstance, this.token,
				this.node, this.action, this.operator, this.comment
		);
		return mockItem;
    }
}
