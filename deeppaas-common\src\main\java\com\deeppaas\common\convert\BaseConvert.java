package com.deeppaas.common.convert;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2018-12-27
 */
public abstract class BaseConvert<Entity,Param,VO> {
    /**
     * @Description: 将Param转换为Entity
     * @param param 参数实体
     * @return Entity 存储实体
     * <AUTHOR>
     * @date 2018-12-27
     */
    public abstract Entity paramToEntity(Param param);

    /**
     * @Description: 将Entity转换为VO
     * @param entity
     * @return VO
     * <AUTHOR>
     * @date 2018-12-27
     */
    public abstract VO entityToVO(Entity entity);

    /**
     * @Description: 将Param集合转换为Entity集合
     * @param params
     * @return java.util.List<Entity>
     * <AUTHOR>
     * @date 2018-12-27
     */
    public List<Entity> paramToEntitys(List<Param> params){
        if(CollectionUtils.isEmpty(params)){
            return Collections.EMPTY_LIST;
        }else{
            List<Entity> entitys = params.stream().map(param -> paramToEntity(param)).collect(Collectors.toList());
            return entitys;
        }
    }

    /**
     * @Description: 将entity集合转换为VO集合
     * @param entitys
     * @return java.util.List<VO>
     * <AUTHOR>
     * @date 2018-12-27
     */
    public List<VO> entityToVOS(List<Entity> entitys){
        if(CollectionUtils.isEmpty(entitys)){
            return Collections.EMPTY_LIST;
        }else{
            List<VO> vos = entitys.stream().map(entity -> entityToVO(entity)).collect(Collectors.toList());
            return vos;
        }
    }


}
