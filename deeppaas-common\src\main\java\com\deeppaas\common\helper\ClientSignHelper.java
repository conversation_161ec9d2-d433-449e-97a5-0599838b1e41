package com.deeppaas.common.helper;

import com.deeppaas.common.exception.AuthException;

import java.util.Objects;

/**
 * 97~121,65~90
 * <AUTHOR>
 * @date 2020/7/8
 */
public class ClientSignHelper {
    public static final String SIGN_KEY = "sign";
    /**
     * 生成一个客户端连接签名
     * @return: java.lang.String
     * <AUTHOR>
     * @date 2020/7/8 8:37 下午
     **/
    public static String initSign(){
        String timeStr = String.valueOf(System.currentTimeMillis());
        int key1 = Integer.valueOf(timeStr.substring(timeStr.length()-1));
        int key2 = Integer.valueOf(timeStr.substring(timeStr.length()-2,timeStr.length()-1));
        int timeLength = timeStr.length();
        StringBuffer buffer = new StringBuffer();
        for(int i=0;i<timeLength;i++){
            char num = timeStr.charAt(i);
            buffer.append(timeStr.charAt(i));
            buffer.append((char)(num + key1 + key2 + 17));
        }
        return AesHelper.encrypt(buffer.toString());
    }

    /**
     * 检验签名是否合法
     * @param: sign
     * @return: boolean
     * <AUTHOR>
     * @date 2020/7/8 8:38 下午
     **/
    public static boolean checkSign(String sign){
        if(sign==null){
            return false;
        }
        String signInfo = AesHelper.decrypt(sign);
        char[] singChars = signInfo.toCharArray();
        StringBuffer timeBuffer = new StringBuffer();
        StringBuffer wordBuffer = new StringBuffer();
        for(int i=0;i<singChars.length;i++){
            if(Character.isDigit(singChars[i])){
                timeBuffer.append(singChars[i]);
            }else{
                wordBuffer.append(singChars[i]);
            }
        }
        String timeStr = timeBuffer.toString();
        int key1 = Integer.valueOf(timeStr.substring(timeStr.length()-1));
        int key2 = Integer.valueOf(timeStr.substring(timeStr.length()-2,timeStr.length()-1));
        int timeLength = timeStr.length();
        StringBuffer realWordBuffer = new StringBuffer();
        for(int i=0;i<timeLength;i++){
            char num = timeStr.charAt(i);
            realWordBuffer.append((char)(num + key1 + key2 + 17));
        }
        return Objects.equals(realWordBuffer.toString(), wordBuffer.toString());
    }

    public static void assertSign(String sign) {
        if (!checkSign(sign)) {
            throw AuthException.unAuthorized("deeppaas client sign error!");
        }
    }
//    public static void main(String... args) throws InterruptedException {
//        long time1 = System.currentTimeMillis();
//        checkSign(initSign());
//        long time2 = System.currentTimeMillis();
//        System.out.println(time2-time1);
//        checkSign(initSign());
//        long time3 = System.currentTimeMillis();
//        System.out.println(time3-time2);
//    }
}
