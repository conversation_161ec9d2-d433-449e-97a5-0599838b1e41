package com.deeppaas.msg.biz.web;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.msg.biz.entity.UserMsgBoxDO;
import com.deeppaas.msg.biz.service.UserMsgBoxService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@RestController
@RequestMapping("/api/userMsgBox")
public class UserMsgBoxApi {
    private final UserMsgBoxService userMsgBoxService;

    public UserMsgBoxApi(UserMsgBoxService userMsgBoxService) {
        this.userMsgBoxService = userMsgBoxService;
    }

    /**
     * 获取消息模块
     * @return
     */
    @GetMapping("/get")
    public RestModel<UserMsgBoxDO> get(String id) {
        UserMsgBoxDO boxDO = userMsgBoxService.getById(id);
        return RestModel.data(boxDO);
    }
    /**
     * 获取所有消息模块
     * @return
     */
    @GetMapping("/list")
    public RestModel<List<UserMsgBoxDO>> list() {
        List<UserMsgBoxDO> list = userMsgBoxService.findAllBoxes();
        return RestModel.data(list);
    }

    /**
     * 保存消息模块
     * @return
     */
    @PostMapping("/save")
    public RestModel<String> save(@RequestBody UserMsgBoxDO boxDO) {
        boxDO = userMsgBoxService.saveBox(boxDO);
        return RestModel.data(boxDO.getId());
    }

    /**
     * 删除消息模块
     * @return
     */
    @PostMapping("/delete")
    public RestModel<Boolean> delete(@BodyParam String id) {
        userMsgBoxService.deleteBox(id);
        return RestModel.data(true);
    }
}
