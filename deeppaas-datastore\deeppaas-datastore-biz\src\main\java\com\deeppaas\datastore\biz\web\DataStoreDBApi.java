package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.datastore.biz.service.DataStoreDBService;
import com.deeppaas.datastore.common.mapping.DBColumn;
import com.deeppaas.datastore.common.mapping.DBTable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据库API
 *
 * <AUTHOR>
 * @date 2022/3/4
 */
@RestController
@RequestMapping("/api/datastore/db")
public class DataStoreDBApi {
    private DataStoreDBService dataStoreDBService;

    public DataStoreDBApi(DataStoreDBService dataStoreDBService) {
        this.dataStoreDBService = dataStoreDBService;
    }

    /**
     * 数据源表列表
     */
    @GetMapping("/tables")
    public RestModel<List<DBTable>> tables(@RequestParam String sourceId) {
        List<DBTable> tables = dataStoreDBService.tables(sourceId);
        return RestModel.data(tables);
    }

    /**
     * 数据源表字段列表
     */
    @GetMapping("/table/columns")
    public RestModel<List<DBColumn>> tableColumns(@RequestParam String sourceId, String tableName) {
        List<DBColumn> tables = dataStoreDBService.tableColumns(sourceId, tableName);
        return RestModel.data(tables);
    }
}
