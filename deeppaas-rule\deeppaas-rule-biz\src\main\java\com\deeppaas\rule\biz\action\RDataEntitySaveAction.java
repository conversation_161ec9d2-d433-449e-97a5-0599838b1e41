package com.deeppaas.rule.biz.action;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.datastore.api.client.DataStoreDataClient;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;

import java.util.*;

/**
 * 数据模型保存动作
 * <AUTHOR>
 * @date 2022/4/27
 */
@Data
public class RDataEntitySaveAction  extends RAction{
    /**
     * 更新数据模型编号
     */
    private String dataEntity;
    /**
     * 赋值绑定
     */
    private RDataBind dataBind;

    @Override
    public Object execute(RuleContext ruleContext) {
        DataStoreDataClient dataStoreDataClient = ruleContext.getRuleEngine().dataStoreDataClient();
        Object dataInfo = dataBind.getBindData(ruleContext);
        Iterable<Map<String, Object>> dataMaps = null;
        if(dataInfo.getClass().isArray()){
            Map<String, Object>[] dataArray = (Map<String, Object>[]) dataInfo;
            dataMaps = Arrays.stream(dataArray).toList();
        }
        if(dataInfo instanceof Iterable<?> dataList){
            dataMaps = (Iterable<Map<String, Object>>) dataList;
        }
        if(dataMaps!=null){
            Iterator<Map<String, Object>> iterator = dataMaps.iterator();
            List<Map<String, Object>> result = new ArrayList<>();
            while (iterator.hasNext()){
                Map<String, Object> dataMap = iterator.next();
                DataStoreData dataStoreData = DataStoreData.instance(dataEntity, dataMap);
                result.add(dataStoreDataClient.saveData(dataStoreData));
            }
            return super.returnOrNext(result, ruleContext);
        }
        if(dataInfo instanceof Map<?,?> dataMap){
            DataStoreData dataStoreData = DataStoreData.instance(dataEntity, (Map<String, Object>) dataMap);
            Map<String, Object> result = dataStoreDataClient.saveData(dataStoreData);
            return super.returnOrNext(result, ruleContext);
        }
        throw RunException.error("类型识别异常，请检查动作["+super.getId()+"/"+super.getName()+"]");
    }

}
