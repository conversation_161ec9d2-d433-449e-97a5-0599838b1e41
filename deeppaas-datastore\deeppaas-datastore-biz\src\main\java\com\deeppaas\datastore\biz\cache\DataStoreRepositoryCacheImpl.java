package com.deeppaas.datastore.biz.cache;

import com.deeppaas.datastore.common.DataStoreRepository;
import com.deeppaas.datastore.common.DataStoreRepositoryCache;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@Component
public class DataStoreRepositoryCacheImpl implements DataStoreRepositoryCache {
    /**
     * repository缓存
     */
    private static Map<String, DataStoreRepository> repositoryMap = new ConcurrentHashMap<>();


    @Override
    public DataStoreRepository get(String entityCode) {
        return repositoryMap.get(entityCode);
    }

    @Override
    public void remove(String entityCode) {
        repositoryMap.remove(entityCode);
    }

    @Override
    public void put(String entityIndex, DataStoreRepository repository) {
        repositoryMap.put(entityIndex, repository);
    }
}
