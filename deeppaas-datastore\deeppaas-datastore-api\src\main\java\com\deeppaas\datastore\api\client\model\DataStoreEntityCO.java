package com.deeppaas.datastore.api.client.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据模型客户端实体
 * <AUTHOR>
 * @date 2022/4/20
 */
@Data
public class DataStoreEntityCO {
    /**
     * 数据源
     **/
    private String sourceId;

    /**
     * 数据模型编号手动，全局唯一
     **/
    private String code;

    /**
     * 数据模型名称
     **/
    private String name;

    /**
     * 类型 @DataStoreEntityType
     */
    private String type;

    /**
     * 所属应用编号
     */
    private String appCode;

    /**
     * 所属应用名称
     */
    private String appName;

    /**
     * 数据模型对应主表ID
     **/
    private String tableId;

    /**
     * 数据模型对应主表
     **/
    private String tableName;

    /**
     * 更新时间
     **/
    private LocalDateTime updateTime;
}
