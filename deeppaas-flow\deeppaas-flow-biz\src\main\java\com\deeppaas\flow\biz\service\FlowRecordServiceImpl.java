package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.FlowAction;
import com.deeppaas.flow.biz.dao.FlowRecordDao;
import com.deeppaas.flow.biz.entity.FlowRecordDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional(readOnly = true)
public class FlowRecordServiceImpl implements FlowRecordService {
    private FlowRecordDao flowRecordDao;

    public FlowRecordServiceImpl(FlowRecordDao flowRecordDao) {
        this.flowRecordDao = flowRecordDao;
    }

    @Override
    @Transactional
    public void saveRecord(Long instanceId, String nodeName, FlowAction action, String userId, String comment) {
        FlowRecordDO record = new FlowRecordDO();
        record.setInstanceId(instanceId);
        record.setNodeName(nodeName);
        record.setAction(action.name());
        record.setExecuteUser(userId);
        record.setExecuteTime(LocalDateTime.now());
        record.setComment(comment);
        flowRecordDao.save(record);
    }

    @Override
    public List<FlowRecordDO> findRecord(Long instanceId) {
        return flowRecordDao.findByInstanceIdOrderByExecuteTime(instanceId);
    }

    @Override
    public void updateFlowId(Long newFlowId, Long oldFlowId) {
        flowRecordDao.updateFlowId(newFlowId, oldFlowId);
    }

}
