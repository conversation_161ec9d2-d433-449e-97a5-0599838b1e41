import{u as h,r as c,g as t,j as r,ak as u,ai as b}from"./index.689bcdcb.js";import{u as F}from"./useQuery.d623ee6b.js";import{a as f}from"./dealFile.a8bf2005.js";import{e as N}from"./util.bf0e86fd.js";import{s as l}from"./side.10d2df17.js";import{T as i}from"./index.1f7b2316.js";import{I as o}from"./index.0524b9fa.js";import{L as g}from"./LeftOutlined.6bce5536.js";import"./reactNode.50ea6579.js";import"./EyeOutlined.70f6da72.js";import"./css.032fc6ca.js";import"./DialogWrap.9951837c.js";import"./getScrollBarSize.001053fa.js";import"./RightOutlined.ed31d8e3.js";import"./pickAttrs.04174e9b.js";function D(){const m=h(),[n,p]=c.exports.useState([]),[d,x]=c.exports.useState([]),s=m.search.split("?")[1].split("=")[1];return F(["getTaskInfo",s],()=>N(()=>f(s))(),{onSuccess(e){console.log(e.checkImageVo),e.checkImageVo.successImage&&x(e.checkImageVo.successImage),e.checkImageVo.errorImage&&p(e.checkImageVo.errorImage)}}),t("div",{children:[t("div",{className:"bg-white h-12 py-3 pl-6 font-bold",children:[r(g,{style:{marginRight:"8px"},className:" cursor-pointer",onClick:()=>{window.history.back()}}),"\u57FA\u672C\u4FE1\u606F"]}),t("div",{className:"mt-6 w-[1200px] min-h-[804px] bg-white mx-auto",children:[r("div",{className:"h-57 leading-[57px] border-b pl-6 text-base",children:"\u57FA\u672C\u4FE1\u606F"}),t("div",{className:"px-10",children:[r("div",{className:"mt-7  flex flex-wrap"}),t("div",{className:"mt-10 flex flex-wrap",children:[n.map(e=>t("div",{className:"mr-[170px] mb-8 cursor-pointer text-center",children:[r("div",{className:"flex items-center mt-2",children:r(u,{style:{color:"red"}})}),r(i,{title:()=>t("div",{className:"max-w-[222px]",children:[r("div",{className:" text-primary-black-65",children:e.name}),r("div",{className:"flex flex-wrap",children:e.category&&JSON.parse(e.category).map(a=>r("span",{className:"border px-2 bg-[#FFFBE6] text-[#FAAD14] border-[#FFE58F] mt-3 inline-block mr-2",children:a}))})]}),color:"white",placement:"right",children:r("div",{className:"mt-2 w-[200px] h-[150px] border flex items-center justify-center",style:{borderStyle:"rgba(0, 0, 0, 0.06)"},children:r(o,{src:"/api/imagetemplate/"+e.path,className:"w-full max-h-[150px]",alt:"",fallback:l})})}),r("div",{className:" text-primary-black-85 text-center truncate w-[168px] mx-auto mt-2",children:e.name}),e.category&&JSON.parse(e.category)[0]?r("span",{className:"border px-2 bg-[#FFFBE6] text-[#FAAD14] border-[#FFE58F] mt-3 inline-block max-w-[136px] truncate",children:e.category&&JSON.parse(e.category)[0]}):null]},e.id)),d.map(e=>t("div",{className:"mr-[170px] mb-8 cursor-pointer text-center",children:[t("div",{className:"flex items-center mt-2",children:["\xA0 ",r(b,{style:{color:"green"}})]}),r(i,{title:()=>t("div",{className:"max-w-[222px]",children:[r("div",{className:" text-primary-black-65",children:e.name}),r("div",{className:"flex flex-wrap",children:e.category&&JSON.parse(e.category).map(a=>r("span",{className:"border px-2 bg-[#FFFBE6] text-[#FAAD14] border-[#FFE58F] mt-3 inline-block mr-2 ",children:a}))})]}),color:"white",placement:"right",children:r("div",{className:"mt-2 w-[200px] h-[150px] border flex items-center justify-center",style:{borderStyle:"rgba(0, 0, 0, 0.06)"},children:r(o,{src:"/api/imagetemplate/"+e.path,className:"w-full max-h-[150px]",alt:"",fallback:l})})}),r("div",{className:" text-primary-black-85 text-center truncate w-[168px] mx-auto mt-2",children:e.name}),r("span",{className:"border px-2 bg-[#FFFBE6] text-[#FAAD14] border-[#FFE58F] mt-3 inline-block max-w-[136px] truncate",children:e.category&&JSON.parse(e.category)[0]})]},e.id))]})]})]})]})}export{D as default};
