CREATE TABLE aidos_task_info (
                                 id VARCHAR(36) PRIMARY KEY,
                                 handler_user VARCHAR(255),
                                 check_remarks TEXT,
                                 ai_tips TEXT,
                                 task_id VARCHAR(255),
                                 task_name VARCHAR(255),
                                 pro_examine INT,
                                 call_back_task_info TEXT,
                                 call_back_id VARCHAR(255),
                                 create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                                 update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE TABLE aidos_call_back_image (
                                       id VARCHAR(36) PRIMARY KEY,
                                       name VARCHA<PERSON>(255),
                                       task_info_id VARCHAR(36),
                                       path VARCHAR(255),
                                       python_path VARCHAR(255),
                                       category VARCHAR(255),
                                       call_back_image_id VARCHAR(255),
                                       call_back_id VARCHAR(255),
                                       tag VARCHAR(255),
                                       create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                                       update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE TABLE aidos_process (
                               id VARCHAR(36) PRIMARY KEY,
                               name VA<PERSON>HAR(255),
                               alias VARCHAR(255),
                               upload_user_id INT,
                               upload_user_name VARCHAR(255),
                               create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                               update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE aidos_process_task (
                                    id VARCHAR(36) PRIMARY KEY,
                                    process_id VARCHAR(36),
                                    name VARCHAR(255),
                                    alias VARCHAR(255),
                                    user_id VARCHAR(511),
                                    upload_user_id INT,
                                    upload_user_name VARCHAR(255),
                                    templates TEXT,
                                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE aidos_image_template (
                                      id VARCHAR(36) PRIMARY KEY,
                                      name VARCHAR(255),
                                      initials VARCHAR(255),
                                      alias VARCHAR(255),
                                      label VARCHAR(255),
                                      path VARCHAR(255),
                                      upload_user_id INT,
                                      upload_user_name VARCHAR(255),
                                      create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                                      update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE TABLE aidos_image_template_type (
                                           id VARCHAR(36) PRIMARY KEY,
                                           process_id VARCHAR(36),
                                           process_task_id VARCHAR(36),
                                           name VARCHAR(255),
                                           alias VARCHAR(255),
                                           create_user_id INT,
                                           create_user_name VARCHAR(255),
                                           create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                                           update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE TABLE aidos_intermediate_template_type (
                                                  id VARCHAR(36) PRIMARY KEY,
                                                  process_id VARCHAR(36),
                                                  process_task_id VARCHAR(36),
                                                  image_template_type_id VARCHAR(36),
                                                  image_template_id VARCHAR(36),
                                                  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                                                  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE aidos_config_variable (
                                       id VARCHAR(36) PRIMARY KEY,
                                       process_id VARCHAR(255),
                                       process_task_id VARCHAR(255),
                                       aidos_process_config_id VARCHAR(255),
                                       name VARCHAR(255),
                                       `key` VARCHAR(255),  -- 使用反引号包裹 key
                                       value VARCHAR(255),
                                       create_time datetime NOT NULL COMMENT '创建时间’
);

CREATE TABLE aidos_process_config (
    id VARCHAR(36) PRIMARY KEY,
    process_id VARCHAR(255),
    process_task_id VARCHAR(255),
    name VARCHAR(255),
    alias VARCHAR(255),
    configs TEXT,  -- 使用 TEXT 类型存储较长的配置值对象
    create_time datetime NOT NULL COMMENT '创建时间’,
                                       sort INT DEFAULT 0  -- 设置默认值为 0
);