package com.deeppaas.rule.biz.action;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.RuleContext;
import lombok.Data;

/**
 * 错误抛出动作
 * <AUTHOR>
 * @date 2022/4/27
 */
@Data
public class RErrorAction extends RAction{
    /**
     * 返回消息
     */
    private String message;

    @Override
    public Object execute(RuleContext ruleContext) {
        throw RunException.error(message);
    }
}
