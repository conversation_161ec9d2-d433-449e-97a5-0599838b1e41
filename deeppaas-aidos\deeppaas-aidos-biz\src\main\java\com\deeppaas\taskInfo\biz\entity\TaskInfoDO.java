package com.deeppaas.taskInfo.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Data
@Table("aidos_task_info")
public class TaskInfoDO extends BaseUuidDO {
    /**
     *
     */
    @Column("handler_user")
    private String handlerUser;
    /**
     *
     */
    @Column("check_remarks")
    private String checkRemarks;

    /**
     *
     */
    @Column("ai_tips")
    private String aiTips;

    /**
     * 办件类型id
     * */
    @Column("task_id")
    private String taskId;

    /**
     *
     * */
    @Column("task_name")
    private String taskName;

    /**
     * 预审状态
     * */
    @Column("pro_examine")
    private Integer proExamineStae;


    /**
     * 预审状态
     * */
    @Column("call_back_task_info")
    private String  callBackTaskInfo;
    /**
     * 预审状态
     * */
    @Column("call_back_id")
    private String  callBackId;

}
