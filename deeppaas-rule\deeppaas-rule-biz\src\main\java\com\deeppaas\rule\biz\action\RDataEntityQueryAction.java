package com.deeppaas.rule.biz.action;

import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.DataStoreConditionL;
import com.deeppaas.datastore.common.DataStoreConditionS;
import com.deeppaas.datastore.common.emnus.DataStoreOperator;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.deeppaas.rule.biz.model.RDataQueryPermit;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据模型查询动作
 * <AUTHOR>
 * @date 2022/6/14
 */
@Data
public abstract class RDataEntityQueryAction extends RAction{
    /**
     * 查询数据模型编号
     */
    protected final String dataEntity;
    /**
     * 查询附加授权
     */
    protected final RDataQueryPermit[] permits;
    /**
     * 默认查询条件
     */
    protected final RConditionModel defaultCondition;


    protected DataStoreCondition getQueryCondition(RuleContext ruleContext){
        List<DataStoreCondition> conditions = new ArrayList<>();
        DataStoreCondition defaultC = transformCondition(defaultCondition, ruleContext);
        if(defaultC!=null){
            conditions.add(defaultC);
        }
        for(RDataQueryPermit permit : permits){
            Boolean permitted = (Boolean) permit.getAuthBind().getBindData(ruleContext);
            if(permitted){
                DataStoreCondition permitC = transformCondition(permit.getCondition(), ruleContext);
                if(permitC!=null){
                    conditions.add(permitC);
                }
            }
        }
        if(conditions.isEmpty()){
            return null;
        }else if(conditions.size()==1){
            return conditions.get(0);
        }else {
            return DataStoreCondition.and(conditions.toArray(new DataStoreCondition[conditions.size()]));
        }
    }
    /**
     * 将配置条件中的绑定数据计算后转换为数据仓库查询条件
     * @param rConditionModel
     * @param ruleContext
     * @return
     */
    private DataStoreCondition transformCondition(RConditionModel rConditionModel, RuleContext ruleContext) {
        if(rConditionModel instanceof RConditionModel.Simple cs){
            Object value = cs.getDataBind().getBindData(ruleContext);
            DataStoreOperator dataStoreOperator = DataStoreOperator.valueOf(cs.getOperator());
            return DataStoreConditionS.of(cs.getFieldName(), value, dataStoreOperator);
        }
        if(rConditionModel instanceof RConditionModel.Logic cl){
            DataStoreOperator dataStoreOperator = DataStoreOperator.valueOf(cl.getOperator());
            List<DataStoreCondition> children = cl.getConditions().stream().map(c -> transformCondition(c, ruleContext)).toList();
            return new DataStoreConditionL(children.toArray(new DataStoreCondition[children.size()]), dataStoreOperator);
        }
        return null;
    }

}
