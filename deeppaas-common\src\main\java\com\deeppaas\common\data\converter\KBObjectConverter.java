//package com.deeppaas.common.data.converter;
//
//import com.kingbase8.util.KBobject;
//import org.springframework.core.convert.converter.Converter;
//import org.springframework.data.convert.ReadingConverter;
//
///**
// * 数据转换-金仓对象类型转字符串
// * <AUTHOR>
// * @date 2022/7/21
// */
//@ReadingConverter
//public class KBObjectConverter implements Converter<KBobject, String> {
//
//    @Override
//    public String convert(KBobject source) {
//        if(source==null){
//            return null;
//        }
//        String objType = source.getType();
//        return source.getValue();
//    }
//}
