package com.deeppaas.quartz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * 定时任务管理
 *
 * <AUTHOR>
 * @date 2020/9/24
 */
@Data
@Table("quartz_job")
@EqualsAndHashCode(callSuper = true)
public class QuartzJobDO extends BaseUuidDO {
    /**
     * 所属应用
     */
    @Column("app_code")
    private String appCode;

    /**
     * 任务名称
     */
    @Column("job_name")
    private String jobName;

    /**
     * 任务类型@JobType
     */
    @Column("job_type")
    private String jobType;

    /**
     * 执行任务实例（实例类名-包含包）
     * 内置三种不同类型的执行器
     */
    @Column("job_class")
    private String jobClass;

    /**
     * 执行任务详情
     * <p>
     * 根据类型存放对应的内容
     */
    @Column("job_content")
    private String jobContent;

    /**
     * 是否启用状态
     * <p>
     * 1=启用
     * 0=停用
     */
    @Column("used")
    private Integer used;

    /**
     * quartz表达式
     */
    @Column("cron")
    private String cron;

    /**
     * 配置更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 上次执行时间
     */
    @Column("prev_fire_time")
    private LocalDateTime prevFireTime;

    /**
     * 上次执行结果
     */
    @Column("prev_fire_result")
    private String prevFireResult;

    /**
     * 下次执行时间
     */
    @Column("next_fire_time")
    private LocalDateTime nextFireTime;

    @Override
    public String toString() {
        return "QuartzJobDO{" +
                "appCode='" + appCode + '\'' +
                ", jobName='" + jobName + '\'' +
                ", jobType='" + jobType + '\'' +
                ", jobClass='" + jobClass + '\'' +
                ", jobContent='" + jobContent + '\'' +
                '}';
    }
}
