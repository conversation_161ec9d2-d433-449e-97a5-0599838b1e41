package com.deeppaas.configuration.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.sql.Date;
import java.time.LocalDateTime;

@Data
@Table("aidos_config_variable")
public class AidosConfigVariableDO extends BaseUuidDO {

    /**
     * 事项名称
     */
    @Column("process_id")
    private String processId;
    /**
     * 事项名称
     */
    @Column("process_task_id")
    private String processTaskId;

    /**
     * 事项名称
     */
    @Column("aidos_process_config_id")
    private String aidosProcessConfigId;
    /**
     * 配置名称
     */
    @Column("name")
    private String name;

    @Column("key")
    private String key;

    @Column("value")
    private String value;

    /**
     * 创建时间
     * add by xby 2024.9.19
     */
    @Column("create_time")
    private LocalDateTime createTime;
}
