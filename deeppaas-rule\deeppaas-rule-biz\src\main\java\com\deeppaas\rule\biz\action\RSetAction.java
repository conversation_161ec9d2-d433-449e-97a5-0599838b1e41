package com.deeppaas.rule.biz.action;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 更新动作
 * 如：将某字段修改为某个值
 * <AUTHOR>
 * @date 2022/4/22
 */
@Data
public class RSetAction extends RAction{
    /**
     * 被更新信息
     */
    private RDataBind source;

    /**
     * 更新指向
     */
    private RDataBind target;

    @Override
    public Object execute(RuleContext ruleContext) {
        Object originalValue = source.getBindData(ruleContext);
        Object updateValue = target.updateBind(originalValue, ruleContext);
        switch (source.getType()){
            case PARAM -> ruleContext.setParamByPath(source.getValue()==null?null:source.getValue().toString(), updateValue);
            case VARS -> ruleContext.addVar(source.getValueTarget(), updateValue);
            default -> throw RunException.optReject("只能对变量和参数进行修改");
        }
        return super.returnOrNext(null, ruleContext);
    }

}
