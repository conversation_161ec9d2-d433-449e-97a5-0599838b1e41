package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 流程令牌
 * <AUTHOR>
 * @date 2022/3/22
 */
@Data
@Table("flow_token")
public class FlowTokenDO implements BaseModel {
    @Id
    @Column("id")
    private Long id;
    /**
     * 流程实例ID
     */
    @Column("instance_id")
    private Long instanceId;
    /**
     * 当前环节
     */
    @Column("node_code")
    private String nodeCode;
    /**
     * 令牌最新处理人
     */
    @Column("operator")
    private String operator;
    /**
     * 令牌最新处理任务
     */
    @Column("task_id")
    private Long taskId;
    /**
     * 令牌是否在用，使用中为1，令牌已用完但未被回收前为0
     */
    @Column("used")
    private Integer used;
    /**
     * 父令牌ID
     */
    @Column("parent_id")
    private Long parentId;
}
