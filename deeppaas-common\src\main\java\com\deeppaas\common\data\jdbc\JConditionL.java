package com.deeppaas.common.data.jdbc;


import com.deeppaas.common.data.Operator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */
public class JConditionL extends JCondition {
    private List<JCondition> conditions;

    JConditionL(Operator operator){
        super(operator);
        List<JCondition> conditionList = new ArrayList<>();
        this.conditions = conditionList;
    }

    JConditionL(JCondition[] conditions, Operator operator){
        super(operator);
        List<JCondition> conditionList = new ArrayList<>();
        for(JCondition jCondition : conditions){
            if(null != jCondition){
                conditionList.add(jCondition) ;
            }
        }
        this.conditions = conditionList;
    }

    public List<JCondition> getConditions() {
        return conditions;
    }

    public void add(JCondition jCondition){
        conditions.add(jCondition);
    }
    @Override
    public Map<String, Object> getBindParam() {
        Map<String, Object> paramMap = new HashMap<>();
        for(JCondition condition : conditions){
            if(null != condition) {
                paramMap.putAll(condition.getBindParam());
            }
        }
        return paramMap;
    }

    @Override
    void sortMarkNum(AtomicInteger markNum){
        for(JCondition condition : conditions){
            if(null != condition) {
                condition.sortMarkNum(markNum);
            }
        }
    }

    public void addAll(JCondition[] subConditions) {
        for(JCondition jCondition : subConditions){
            conditions.add(jCondition);
        }
    }
}
