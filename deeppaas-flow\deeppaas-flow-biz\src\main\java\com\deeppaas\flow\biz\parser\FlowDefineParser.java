package com.deeppaas.flow.biz.parser;

import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.flow.biz.element.FlowEventRule;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.deeppaas.flow.biz.FlowException;
import com.deeppaas.flow.biz.entity.FlowDefineDO;
import com.deeppaas.flow.biz.model.FlowDefineModel;
import com.deeppaas.flow.biz.element.FlowNode;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
public class FlowDefineParser {
    private static final String DEFINE_KEY_NODES = "nodes";
    private static final String DEFINE_KEY_LINES = "lines";
    private static final String DEFINE_KEY_EVENTS = "events";

    public static FlowDefineModel parse(FlowDefineDO defineDO, String jsonStr){
        JsonNode root = null;
        try {
            root = JsonHelper.readTree(jsonStr);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        FlowDefineModel flowDefine = new FlowDefineModel(defineDO);
        Map<String, FlowNode> nodeMap = new HashMap<>();
        JsonNode nodes = root.get(DEFINE_KEY_NODES);
        if(nodes instanceof ArrayNode nodeArray){
            nodeArray.forEach(node -> {
                FlowNode nodeModel = FlowNodeParser.parse(node);
                nodeMap.put(nodeModel.getCode(), nodeModel);
            });
        }else {
            throw new FlowException("流程定义nodes定义错误");
        }
        JsonNode lines = root.get(DEFINE_KEY_LINES);
        if(lines instanceof ArrayNode lineArray){
            Iterator<JsonNode> iterator = lineArray.elements();
            while (iterator.hasNext()){
                JsonNode line = iterator.next();
                FlowLineParser.loadLine(line, nodeMap);
            }
        }else {
            throw new FlowException("流程定义lines定义错误");
        }
        flowDefine.setNodes(nodeMap);
        List<FlowEventRule> eventRules = FlowEventParser.parse(root.get(DEFINE_KEY_EVENTS));

        flowDefine.setNodes(nodeMap);
        return flowDefine;
    }

}
