package com.deeppaas.common.captcha;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.caffeine.CaffeineCache;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-06-18
 */
public class CaptchaCache {
    private static CaffeineCache cache = new CaffeineCache(
            "captcha_image", Caffeine.from("maximumSize=10000,expireAfterWrite=10m").build(), true);

    public static void set(String key, Integer value){
        cache.put(key,value);
    }

    public static Integer get(String key){
        Integer value = null;
        try {
            value = cache.get(key, Integer.class);
        } catch (Exception e) {
            value = null;
        }
        return value;
    }

}
