package com.deeppaas.datastore.common.jdbc;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.datastore.common.mapping.DataStoreEntity;
import com.deeppaas.datastore.common.mapping.DataStoreProperty;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.LinkedCaseInsensitiveMap;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/26
 */
public class DataStoreDataRowMapper implements RowMapper<DataStoreData> {
    private final DataStoreEntity entity;

    DataStoreDataRowMapper(DataStoreEntity entity){
        this.entity = entity;
    }

    public static DataStoreDataRowMapper of(DataStoreEntity entity){
        return new DataStoreDataRowMapper(entity);
    }

    @Override
    public DataStoreData mapRow(ResultSet rs, int rowNum) throws SQLException {
        ResultSetMetaData resultSetMetaData = rs.getMetaData();
        int columnCount = resultSetMetaData.getColumnCount();
        Map<String, Object> mapOfColumnValues = createColumnMap(columnCount);
        for (int i = 1; i <= columnCount; i++) {
            String column = JdbcUtils.lookupColumnName(resultSetMetaData, i);
            DataStoreProperty property = entity.getProperty(column);
            mapOfColumnValues.putIfAbsent(column, getColumnValue(rs, i, property));
        }
        Object idValue = null;
        if(entity.getIdProperty()!=null){
            idValue = mapOfColumnValues.get(entity.getIdProperty().getName());
        }
        return DataStoreData.instance(entity.getAlias(), idValue, mapOfColumnValues);
    }

    protected Map<String, Object> createColumnMap(int columnCount) {
        return new LinkedCaseInsensitiveMap<>(columnCount);
    }

    @Nullable
    protected Object getColumnValue(ResultSet rs, int index, DataStoreProperty property) throws SQLException {
        Object columnValue = JdbcUtils.getResultSetValue(rs, index);
        if(property==null || columnValue==null){
            return columnValue;
        }
        SimpleDataType dataType = SimpleDataType.valueOf(property.getDataType());
        return switch (dataType) {
            case NUMBERS -> JsonHelper.fromJson(columnValue.toString(), BigDecimal[].class);
            case TEXTS -> JsonHelper.fromJson(columnValue.toString(), String[].class);
            default -> columnValue;
        };
    }
}
