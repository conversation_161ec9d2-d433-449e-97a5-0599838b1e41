package com.deeppaas.datastore.common;

import com.deeppaas.datastore.common.emnus.DataStoreOperator;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/1/19
 */
public abstract class DataStoreCondition {
    private final DataStoreOperator operator;		//计算符

    DataStoreCondition(DataStoreOperator operator){
        this.operator = operator;
    }

    public DataStoreOperator getOperator() {
        return operator;
    }

    /**
     * 等于
     */
    public static DataStoreCondition eq(String fieldName, Object value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.EQ);
    }

    /**
     * 不等于
     */
    public static DataStoreCondition ne(String fieldName, Object value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.NE);
    }

    /**
     * 大于
     */
    public static DataStoreConditionS gt(String fieldName, Object value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.GT);
    }

    /**
     * 小于
     */
    public static DataStoreConditionS lt(String fieldName, Object value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.LT);
    }

    /**
     * 小于等于
     */
    public static DataStoreConditionS lte(String fieldName, Object value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.LTE);
    }

    /**
     * 大于等于
     */
    public static DataStoreConditionS gte(String fieldName, Object value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.GTE);
    }

    /**
     * 并且
     */
    public static DataStoreCondition and(DataStoreCondition... conditions){
        List<DataStoreCondition> notNullConditions = Arrays.stream(conditions).filter(condition -> condition!=null).toList();
        if(notNullConditions.isEmpty()){
            return null;
        }
        if(notNullConditions.size()==1){
            return notNullConditions.get(0);
        }
        return new DataStoreConditionL(conditions, DataStoreOperator.AND);
    }
    /**
     * 或者
     */
    public static DataStoreCondition or(DataStoreCondition... conditions){
        List<DataStoreCondition> notNullConditions = Arrays.stream(conditions).filter(condition -> condition!=null).toList();
        if(notNullConditions.isEmpty()){
            return null;
        }
        if(notNullConditions.size()==1){
            return notNullConditions.get(0);
        }
        return new DataStoreConditionL(conditions, DataStoreOperator.OR);
    }
    /**
     * 包含于
     */
    public static DataStoreCondition in(String fieldName, Collection value, boolean ignoreNull) {
        if(CollectionUtils.isEmpty(value)){
            if(ignoreNull){
                return null;
            }else{
                return DataStoreConditionS.of("1", 1, DataStoreOperator.NE);
            }
        }else{
            return DataStoreConditionS.of(fieldName, value, DataStoreOperator.IN);
        }
    }
    /**
     * 不包含
     */
    public static DataStoreCondition notIn(String fieldName, Collection value, boolean ignoreNull) {
        if(ignoreNull&&(value==null||value.isEmpty())){
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.NOTIN);
    }

    /**
     * 字符串结束
     */
    public static DataStoreConditionS contains(String fieldName, String value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.CONTAINS);
    }

    /**
     * 字符串开始
     */
    public static DataStoreConditionS startWith(String fieldName, String value, boolean ignoreNull) {
        if(ignoreNull && isEmpty(value)) {
            return null;
        }
        return DataStoreConditionS.of(fieldName, value, DataStoreOperator.STARTWITH);
    }

    public static DataStoreCondition isNull(String fieldName) {
        return DataStoreConditionS.of(fieldName, null, DataStoreOperator.ISNULL);
    }

    public abstract Map<String, Object> getBindParam();

    public void sortMarkNum(){
        AtomicInteger markNum = new AtomicInteger();
        sortMarkNum(markNum);
    }

    abstract void sortMarkNum(AtomicInteger markNum);

    private static boolean isEmpty(Object value){
        if(value==null){
            return true;
        }
        if(value instanceof String str){
            return !StringUtils.hasText(str);
        }
        return false;
    }
}
