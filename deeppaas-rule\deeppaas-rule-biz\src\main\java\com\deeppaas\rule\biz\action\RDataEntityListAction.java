package com.deeppaas.rule.biz.action;

import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.deeppaas.rule.biz.model.RDataQuerySortOrder;
import com.deeppaas.rule.biz.model.RDataQueryPermit;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;

/**
*<AUTHOR>
*@date 2022/6/18
*/
public class RDataEntityListAction  extends RDataEntityQueryAction{
    /**
     * 分页查询页码
     */
    protected final RDataBind pageNoBind;
    /**
     * 分页查询每页条数
     */
    protected final RDataBind pageSizeBind;
    /**
     * 排序信息
     */
    protected final RDataQuerySortOrder[] sortOrders;

    public RDataEntityListAction(String dataEntity, RDataQueryPermit[] permits, RConditionModel defaultCondition,
                                 RDataBind pageNoBind, RDataBind pageSizeBind, RDataQuerySortOrder[] sortOrders) {
        super(dataEntity, permits, defaultCondition);
        this.pageNoBind = pageNoBind;
        this.pageSizeBind = pageSizeBind;
        this.sortOrders = sortOrders;
    }

    @Override
    public Object execute(RuleContext ruleContext) {
        DataStoreCondition queryCondition = getQueryCondition(ruleContext);
        List<DataStoreData> dataList;
        if(pageSizeBind==null){
            dataList = ruleContext.getRuleEngine().dataStoreDataClient().query(dataEntity, queryCondition, getSort());
        }else {
            dataList = ruleContext.getRuleEngine().dataStoreDataClient().query(dataEntity, queryCondition, getQueryPageParam(ruleContext));
        }
        return super.returnOrNext(dataList, ruleContext);
    }

    protected Sort getSort() {
        List<Sort.Order> orders = new ArrayList<>();
        for(RDataQuerySortOrder sortOrder : sortOrders){
            Sort.Direction direction = Sort.Direction.valueOf(sortOrder.getType());
            orders.add(new Sort.Order(direction, sortOrder.getField()));
        }
        if(orders.isEmpty()){
            return null;
        }
        return Sort.by(orders);
    }

    private Pageable getQueryPageParam(RuleContext ruleContext) {
        Integer pageNo = (Integer) pageNoBind.getBindData(ruleContext);
        Integer pageSize = (Integer) pageSizeBind.getBindData(ruleContext);
        if (pageNo == null || pageNo == 0) {
            pageNo = 1;
        }
        if (pageNo == null || pageSize == 0) {
            pageSize = 10;
        }
        Sort sort = this.getSort();
        Pageable pageParam = PageRequest.of(pageNo, pageSize, sort);
        return pageParam;
    }
}
