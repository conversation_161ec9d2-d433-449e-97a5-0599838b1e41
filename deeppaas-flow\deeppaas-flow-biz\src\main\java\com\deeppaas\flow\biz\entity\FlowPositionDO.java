package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * @ClassName: FlowPosition 
 * @Description: 流程位置
 * 该实体主要负责对于流程节点轨迹的记录，并非完整轨迹，而是在流程中的相对位置记录
 * 当流程回退时位置之前的记录被删除，只保留历史该环节位置，保证该环节时可以继续按流程回退
 * <AUTHOR>
 */
@Data
@Table("flow_position")
public class FlowPositionDO implements BaseModel {
	private static final long serialVersionUID = 8676605730537170589L;
	@Id
	@Column("id")
	private Long id;
	/**
	 *
	 */
	@Column("prev_id")
	private Long prevId;
	/**
	 * 流程实例ID
	 */
	@Column("instance_id")
	private Long instanceId;
	/**
	 * 环节编号
	 */
	@Column("node_code")
	private String nodeCode;

}
