<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
	<include resource="org/springframework/boot/logging/logback/base.xml" />
    <property name="LOG_PATH" value="../log/" />
	<!-- 按照每天生成日志文件 -->
	<appender name="FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 服务器存放日志位置 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- 日志文件输出的文件名 -->
			<FileNamePattern>${LOG_PATH}/kubao.%d{yyyy-MM-dd}.log</FileNamePattern>
			<!-- 日志文件保留天数 -->
			<MaxHistory>30</MaxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
		</encoder>
	</appender>
	<!-- 日志输出级别 -->
	<root level="INFO">
		<appender-ref ref="FILE" />
	</root>
</configuration>

