package com.deeppaas.common.data.enums;

/**
 * 数据类型
 * <AUTHOR>
 * @date 2022/6/15
 */
public enum SimpleDataType {
    /**
     * 无类型，没有
     */
    VOID(false),
    /**
     * 文本
     */
    TEXT(false),

    /**
     * 数字
     */
    NUMBER(false),

    /**
     * 是否
     */
    BOOLEAN(false),
    /**
     * 日期时间，暂缓支持
     */
    DATE_TIME(false),

    /**
     * 文件
     */
    FILE(false),

    /**
     * 数据模型
     */
    DATA_ENTITY(true),

    /**
     * 计算模型
     */
    RULE_ENTITY(false),

    /**
     * 文本数组
     */
    TEXTS(false),

    /**
     * 数字数组
     */
    NUMBERS(false),

    /**
     * 文件数组
     */
    FILES(false),

    /**
     * 数据模型数组
     */
    DATA_ENTITIES(false),

    /**
     * 计算模型数组
     */
    RULE_ENTITIES(false),

    /**
     * 数据模型分页信息
     */
    DATA_ENTITY_PAGE(false);

    /**
     * 是否是模型类型
     */
    private boolean entity;

    SimpleDataType(boolean isEntity) {
        this.entity = isEntity;
    }

    public boolean isEntity() {
        return entity;
    }
    /**
     * 是否数据模型
     */
    public static boolean isDataEntity(String type) {
        return DATA_ENTITY.name().equals(type)
                || DATA_ENTITIES.name().equals(type)
                || DATA_ENTITY_PAGE.name().equals(type);
    }

    /**
     * 是否计算模型
     */
    public static boolean isRuleEntity(String type) {
        return RULE_ENTITY.name().equals(type) || RULE_ENTITIES.name().equals(type);
    }

    /**
     * 是否文件类型
     */
    public static boolean isFile(String type) {
        return FILE.name().equals(type) || FILES.name().equals(type);
    }
}
