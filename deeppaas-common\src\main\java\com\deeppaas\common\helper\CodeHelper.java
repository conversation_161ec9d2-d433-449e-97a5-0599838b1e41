package com.deeppaas.common.helper;

import java.math.BigInteger;
import java.util.Objects;
import java.util.Random;

/**
 * 编码生成器
 *
 * <AUTHOR>
 * @date 2019-04-02
 */
public class CodeHelper {
    private static final String[] CODE_NUM_ARR = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
    private static final String[] CODE_CHAR_ARR = {"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
    private static final Integer INIT_CODE_LENGTH = 6;

    /**
     * 随机生成指定长度的编码
     *
     * @param length 编码的长度
     * @return 编码
     */
    public static String createCode(Integer length, String suffix) {
        if (Objects.isNull(length)) {
            length = INIT_CODE_LENGTH;
        }
        String uniqueCode = "";
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            uniqueCode = uniqueCode.concat(CODE_CHAR_ARR[random.nextInt(CODE_CHAR_ARR.length)]);
        }
        return uniqueCode + suffix;
    }

    /**
     * 随机生成6位的编码
     *
     * @return 编码
     */
    public static String createCode(String suffix) {
        return createCode(INIT_CODE_LENGTH, suffix);
    }

    /**
     * 随机生成4位短信验证码
     * @return: java.lang.String
     * <AUTHOR>
     * @date 2020/6/10 2:25 下午
     **/
    public static String createSmsCode4() {
        String uniqueCode = "";
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            uniqueCode = uniqueCode.concat(CODE_NUM_ARR[random.nextInt(CODE_NUM_ARR.length)]);
        }
        return uniqueCode;
    }

    /**
     * 生成10位字符串ID
     * @return
     */
    public static String id10(){
        return Base62.encodeToString(BigInteger.valueOf(SnowflakeHelper.nextId()).toByteArray());
    }
}
