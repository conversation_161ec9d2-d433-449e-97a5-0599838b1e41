package com.deeppaas.flow.biz.entity;

import java.time.LocalDateTime;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * @ClassName: FlowInstance
 * @Description: 流程实例
 * <AUTHOR>
 */
@Data
@Table("flow_instance")
public class FlowInstanceDO implements BaseModel {
	private static final long serialVersionUID = 7632013716358715601L;

	@Id
	@Column("id")
	private Long id;

	/**
	 * 流程定义
	 */
	@Column("define_code")
	private String defineCode;

	/**
	 * 流程版本
	 */
	@Column("version")
	private Integer version;

	/**
	 * 开始环节
	 */
	@Column("start_node")
	private String startNode;

	/**
	 * 工单ID
	 */
	@Column("order_id")
	private String orderId;

	/**
	 * 当前优先级
	 */
	@Column("priority")
	private Integer priority;

	/**
	 * 流程实例状态@FlowInstanceState
	 */
	@Column("state")
	private Integer state;
	/**
	 * 开始时间
	 */
	@Column("start_time")
	private LocalDateTime startTime;
	/**
	 * 结束时间
	 */
	@Column("end_time")
	private LocalDateTime endTime;
	/**
	 * 父流程ID
	 */
	@Column("parent_Id")
	private String parentId;
	/**
	 * 父流程依赖的节点编号
	 */
	@Column("parent_node_code")
	private String parentNodeCode;
	/**
	 * 创建人
	 */
	@Column("creator")
	private String creator;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private LocalDateTime createTime;

}
