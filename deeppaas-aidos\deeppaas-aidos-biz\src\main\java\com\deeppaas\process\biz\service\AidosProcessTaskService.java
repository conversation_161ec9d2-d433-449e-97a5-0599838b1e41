package com.deeppaas.process.biz.service;

import com.deeppaas.configuration.biz.enums.ProcessConfigEnum;
import com.deeppaas.process.biz.dao.AidosProcessTaskDao;
import com.deeppaas.process.biz.entity.AidosProcessTaskDO;
import com.deeppaas.processConfiguration.domain.model.dto.TaskConfigDto;
import com.deeppaas.processConfiguration.domain.model.vo.ProcessConfigVO;
import com.deeppaas.taskInfo.biz.entity.TaskInfoDO;
import com.deeppaas.taskInfo.biz.entity.TaskInfoImageDo;
import com.deeppaas.taskInfo.biz.service.TaskInfoImageService;
import com.deeppaas.taskInfo.biz.service.TaskInfoService;
import com.deeppaas.template.biz.dao.AidosIntermediateTemplateTypeDao;
import com.deeppaas.template.biz.entity.AidosImageTemplateDO;
import com.deeppaas.template.biz.entity.AidosImageTemplateTypeDO;
import com.deeppaas.template.biz.entity.AidosIntermediateTemplateTypeDO;
import com.deeppaas.template.biz.model.AidosImageTemplateVo;
import com.deeppaas.template.biz.model.ProcessTaskTemplateVo;
import com.deeppaas.template.biz.service.AidosImageTemplateService;
import com.deeppaas.template.biz.service.AidosImageTemplateTypeService;
import com.deeppaas.common.captcha.CaptchaBuilder;
import com.deeppaas.common.data.Operator;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AidosProcessTaskService {

    @Autowired
    private AidosProcessTaskDao aidosProcessTaskDao;
    @Autowired
    private AidosImageTemplateService aidosImageTemplateService;
    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private TaskInfoImageService taskInfoImageService;
    @Autowired
    private AidosImageTemplateTypeService aidosImageTemplateTypeService;
    @Autowired
    private AidosIntermediateTemplateTypeDao aidosIntermediateTemplateTypeDao;


    public List<AidosProcessTaskDO> getAll() {
        return aidosProcessTaskDao.findAll();
    }

    public List<AidosProcessTaskDO> like(String taskUserId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(Operator.CONTAINS + "_userId", taskUserId);
        JCondition condition = JCondition.ofPageParam(paramMap);
        return aidosProcessTaskDao.queryForList(condition);
    }

    public String save(AidosProcessTaskDO aidosImageTemplateDO) {
        String id = aidosImageTemplateDO.getId();
        if (isIdenticalName(aidosImageTemplateDO))
            return "已存在相同名称【" + aidosImageTemplateDO.getName() + "】";
        if (StringHelper.isEmpty(id)) {
            aidosImageTemplateDO.setCreateTime(LocalDateTime.now());
            aidosProcessTaskDao.insert(aidosImageTemplateDO);
        } else {
            String tems = aidosImageTemplateDO.getTemplates();
            aidosProcessTaskDao.save(aidosImageTemplateDO);
        }
        return null;
    }

    private boolean isIdenticalName(AidosProcessTaskDO source) {
        String id = source.getId();
        AidosProcessTaskDO name = aidosProcessTaskDao.findByNameAndProcessId(source.getName(), source.getProcessId());
        if (name == null)
            return false;
        if (Objects.equals(name.getId(), id)) {
            return false;
        }
        return true;
    }

    public void insert(List<AidosProcessTaskDO> aidosImageTemplateDO) {
        aidosProcessTaskDao.insertAll(aidosImageTemplateDO);
    }

    public void del(String id) {
        aidosProcessTaskDao.deleteById(id);
    }

    public PageData<AidosProcessTaskDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<AidosProcessTaskDO> page = aidosProcessTaskDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    public List<AidosImageTemplateDO> getTemplate(String processTaskId, String type) {
        AidosProcessTaskDO aidosProcessTaskDO = aidosProcessTaskDao.getById(processTaskId);
        String templates = aidosProcessTaskDO.getTemplates();
        List<AidosImageTemplateDO> aidosImageTemplateDO = new ArrayList<>();
        List<AidosIntermediateTemplateTypeDO> list = Lists.newArrayList();
        if (StringHelper.isEmpty(type)) {
            list = aidosImageTemplateTypeService.getInterByProcessId(processTaskId);

        }
        List<String> ids = new ArrayList<>();
        if (StringHelper.isNotEmpty(templates)) {
            String[] temps = templates.split(",");
            ids = Arrays.stream(temps).toList();
        }
        if (!CollectionUtils.isEmpty(list))
            ids.addAll(list.stream().map(AidosIntermediateTemplateTypeDO::getImageTemplateId).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(ids))
            aidosImageTemplateDO = aidosImageTemplateService.findAllById(ids);
        return aidosImageTemplateDO;
    }


    public List<ProcessTaskTemplateVo> getTemplateVo(String processTaskId, String templateId) {

        AidosProcessTaskDO aidosProcessTaskDO = aidosProcessTaskDao.getById(processTaskId);
        if (aidosProcessTaskDO == null)
            return Lists.newArrayList();
        String templates = aidosProcessTaskDO.getTemplates();
        if (StringHelper.isEmpty(templates)) {
            return new ArrayList<>();
        }

        List<AidosImageTemplateDO> aidosImageTemplateDOS = aidosImageTemplateService.findAllById(templateId);
        List<ProcessTaskTemplateVo> lists = new ArrayList<>();
        if (!CollectionUtils.isEmpty(aidosImageTemplateDOS)) {
            AidosImageTemplateDO aidosImageTemplateDO = aidosImageTemplateDOS.get(0);
            String url = aidosImageTemplateDO.getPath();
            ProcessTaskTemplateVo processTaskTemplateVo = new ProcessTaskTemplateVo();
            url = new String(Base64Utils.decodeFromUrlSafeString(url), StandardCharsets.UTF_8);
            //判断图片是否存在模板类型
            List<AidosIntermediateTemplateTypeDO> intermediates = aidosIntermediateTemplateTypeDao.findByProcessTaskIdAndImageTemplateId(processTaskId, templateId);
            if (!CollectionUtils.isEmpty(intermediates)) {
                for (AidosIntermediateTemplateTypeDO intermediate : intermediates) {
                    AidosImageTemplateTypeDO aidosImageTemplateTypeDO = aidosImageTemplateTypeService.get(intermediate.getImageTemplateTypeId());

                    processTaskTemplateVo.setProcessTaskTemplateType(aidosImageTemplateTypeDO.getId(), aidosImageTemplateTypeDO.getName());
                }
            }

            File imageFile = new File(url);
            if (imageFile.isDirectory()) {
                throw new RuntimeException("图片未找到！");
            }

            BufferedImage tempImg = null;
            try {
                tempImg = ImageIO.read(imageFile);
                String imageBase64 = ";base64," + CaptchaBuilder.imageToBase64(tempImg);
                processTaskTemplateVo.setImageBase(imageBase64);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            processTaskTemplateVo.setId(aidosImageTemplateDO.getId());
            processTaskTemplateVo.setName(aidosImageTemplateDO.getName());
            processTaskTemplateVo.setLabel(aidosImageTemplateDO.getLabel());
            lists.add(processTaskTemplateVo);
        }
        return lists;
    }

    public AidosProcessTaskDO get(String id) {
        return aidosProcessTaskDao.getById(id);
    }

    public void saveTaskCallBack(ProcessConfigVO processConfigVO) {

        List<TaskConfigDto> listTaskConfig = processConfigVO.getConfigs();
        Map<String, Map<String, Object>> configMap = listTaskConfig.stream().filter(item -> item.getResult() != null).collect(Collectors.toMap(TaskConfigDto::getConfigName, TaskConfigDto::getResult));
        Map<String, Map<String, Object>> cMap = listTaskConfig.stream().filter(item -> item.getResult() != null).collect(Collectors.toMap(TaskConfigDto::getConfigName, TaskConfigDto::getConfig));
        List<String> userName = Lists.newArrayList();
        List<String> checkRemarks = Lists.newArrayList();
        List<String> errorMsgs = Lists.newArrayList();
        List<Map<String, Object>> imgSuccesss = Lists.newArrayList();
        List<Map<String, Object>> imgErrors = Lists.newArrayList();
        ProcessConfigEnum[] processConfigEnums = ProcessConfigEnum.values();
        for (ProcessConfigEnum config : processConfigEnums) {
            TaskConfigDto taskConfigDto = listTaskConfig.stream().filter(item -> Objects.equals(item.getConfigName(), config.getCode())).findFirst().orElse(null);
            if (taskConfigDto != null) {
                Map<String, Object> result = taskConfigDto.getResult();
                Map<String, Object> con = taskConfigDto.getConfig();
                if (result != null) {
                    result.forEach((k, y) -> {
                        if (Objects.equals(config.getCode(), ProcessConfigEnum.MESSAGE.getCode())) {
                            String resultVarname = StringHelper.toString(con.get("result_varname"));
                            if (StringHelper.isNotEmpty(resultVarname) && k.equals(resultVarname))
                                checkRemarks.add(y.toString());
                        }
                        if (Objects.equals(config.getCode(), ProcessConfigEnum.IMAGEIDENTIFICATION.getCode())) {
                            if (y instanceof Map<?, ?>) {
                                Map<String, Object> configs = JsonHelper.json2map(JsonHelper.toJson(y));
                                // 取错误信息
                                String errorMsg = StringHelper.toString(configs.get("error_msg"));
                                if (StringHelper.isNotEmpty(errorMsg)) {
                                    errorMsgs.add(errorMsg);
                                }
                                //取名字
                                String name = StringHelper.toString(configs.get("username"));
                                if (CollectionUtils.isEmpty(userName) && StringHelper.isNotEmpty(name)) {
                                    userName.add(name);
                                }
                                //取正确照片
                                Object imgSuccess = configs.get("img_success");
                                if (imgSuccess != null && imgSuccess instanceof List) {
                                    List<Map<String, Object>> success = (List<Map<String, Object>>) imgSuccess;
                                    imgSuccesss.addAll(success);
                                }

                                //取错误照片
                                //取正确照片
                                Object imgError = configs.get("img_error");
                                if (imgError != null && imgError instanceof List) {
                                    List<Map<String, Object>> error = (List<Map<String, Object>>) imgError;
                                    imgErrors.addAll(error);
                                }
                            }
                        }

                    });
                }

            }
        }

        /*********    存任务信息    ***********/
        TaskInfoDO taskInfoDO = new TaskInfoDO();
        if (!CollectionUtils.isEmpty(userName)) {
            taskInfoDO.setHandlerUser(userName.get(0));
        }
        if (!CollectionUtils.isEmpty(checkRemarks)) {
            taskInfoDO.setCheckRemarks(checkRemarks.get(0));
        }
        taskInfoDO.setCallBackId(processConfigVO.getCallbackId());
        taskInfoDO.setAiTips(StringHelper.join(errorMsgs, ","));
        taskInfoDO.setTaskId(processConfigVO.getProcessTaskId());
        taskInfoDO.setTaskName(processConfigVO.getProcessTaskName());
        taskInfoDO.setCallBackTaskInfo(JsonHelper.toJson(processConfigVO).trim());
        Integer i = CollectionUtils.isEmpty(errorMsgs) ? BoolHelper.INT_TRUE : BoolHelper.INT_FALSE;
        taskInfoDO.setProExamineStae(i);
        taskInfoDO.setCreateTime(LocalDateTime.now());
        taskInfoService.insert(taskInfoDO);
        processConfigVO.setTaskInfoId(taskInfoDO.getId());
        //存图片信息
        if (!CollectionUtils.isEmpty(imgSuccesss))
            saveImage(imgSuccesss, processConfigVO, "success");

        if (!CollectionUtils.isEmpty(imgErrors))
            saveImage(imgErrors, processConfigVO, "error");
    }

    private void saveImage(List<Map<String, Object>> source, ProcessConfigVO processConfigVO, String tag) {
        List<TaskInfoImageDo> lists = Lists.newArrayList();
        for (int i = 0; i < source.size(); i++) {
            Map<String, Object> map = source.get(i);
            TaskInfoImageDo taskInfoImageDo = new TaskInfoImageDo();
            taskInfoImageDo.setName(StringHelper.toString(map.get("file_name")));
            taskInfoImageDo.setCallBackImageId(StringHelper.toString(map.get("file_key")));
            taskInfoImageDo.setCategory(JsonHelper.toJson(map.get("category")));
            taskInfoImageDo.setCreateTime(LocalDateTime.now());
            taskInfoImageDo.setPythonPath(StringHelper.toString(map.get("file_path")));
            taskInfoImageDo.setCallBackId(processConfigVO.getCallbackId());
            taskInfoImageDo.setTag(tag);
            taskInfoImageDo.setTaskInfoId(processConfigVO.getTaskInfoId());
            lists.add(taskInfoImageDo);
        }
        if (!CollectionUtils.isEmpty(lists))
            taskInfoImageService.insert(lists);
    }


    public List<AidosImageTemplateVo> getTemplateByProcessTaskId(String processTaskId) {
        List<AidosImageTemplateVo> aidosImageTemplateVos = Lists.newArrayList();
        List<AidosImageTemplateTypeDO> types = aidosImageTemplateTypeService.getByProcessId(processTaskId);
        List<AidosIntermediateTemplateTypeDO> inters = aidosImageTemplateTypeService.getInterByProcessId(processTaskId);
        List<AidosImageTemplateDO> images = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(inters))
            images = aidosImageTemplateService.findAllById(inters.stream().map(AidosIntermediateTemplateTypeDO::getImageTemplateId).collect(Collectors.toList()));

        for (AidosImageTemplateTypeDO item : types) {
            AidosImageTemplateVo aidosImageTemplateVo = new AidosImageTemplateVo();
            Set<String> setInter = inters.stream().filter(it -> Objects.equals(it.getImageTemplateTypeId(), item.getId()))
                    .map(AidosIntermediateTemplateTypeDO::getImageTemplateId).collect(Collectors.toSet());
            aidosImageTemplateVo.setName(item.getName());
            aidosImageTemplateVo.setTemplate(images.stream().filter(image -> setInter.contains(image.getId())).collect(Collectors.toList()));
            aidosImageTemplateVo.setId(item.getId());
            aidosImageTemplateVos.add(aidosImageTemplateVo);
        }
        ;
        return aidosImageTemplateVos;
    }


}
