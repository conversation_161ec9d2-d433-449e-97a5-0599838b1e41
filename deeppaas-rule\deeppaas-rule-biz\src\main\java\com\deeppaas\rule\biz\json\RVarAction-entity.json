{"stepId": "step1", "type": "VARIABLE", "name": "用户订单集合", "varName": "userOrders", "varType": "RULE_ENTITIES", "varEntity": "06ewk7f700d01gjiuk", "databind": {"targetType": "VARS", "value": "varX", "fields": [{"code": "fieldA", "databind": {"targetType": "VARS", "value": "varX.id"}}, {"code": "fieldB", "dataBind": {"targetType": "VARS", "value": "varX.user", "fields": [{"code": "fieldB_a", "databind": {"targetType": "VARS", "value": "varX.user.code"}}]}}]}, "next": "step2", "定义规则": "如果做变量赋值且指定一个赋值同为模型数组的变量，但内部数组字段变量赋值非映射变量的字段，那么新的变量该字段将全部相同"}