package com.deeppaas.common.data;

import com.deeppaas.common.data.enums.SimpleDataType;
import lombok.Data;

import java.util.Map;

/**
 * 数据类型
 * <AUTHOR>
 * @date 2022/6/16
 */
@Data
public class DataClass {
    private final SimpleDataType type;
    private final String entity;

    private final Map<String, DataClass> fields;

    public DataClass(SimpleDataType type, String entity, Map<String, DataClass> fields) {
        this.type = type;
        this.entity = entity;
        this.fields = fields;
    }
}
