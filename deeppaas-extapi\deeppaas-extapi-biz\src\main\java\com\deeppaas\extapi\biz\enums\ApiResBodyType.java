package com.deeppaas.extapi.biz.enums;

import com.deeppaas.common.data.enums.SimpleDataType;

/**
 * 回应消息体类型
 * <AUTHOR>
 * @date 2022/6/28
 */
public enum ApiResBodyType {
    /**
     * 文本
     */
    TEXT_TEXT(SimpleDataType.TEXT),
    TEXT_NUMBER(SimpleDataType.NUMBER),
    TEXT_BOOLEAN(SimpleDataType.BOOLEAN),

    /**
     * HTML
     */
    HTML_TEXT(SimpleDataType.TEXT),

    /**
     * JSON
     */
    JSON_TEXTS(SimpleDataType.TEXTS),
    J<PERSON><PERSON>_NUMBERS(SimpleDataType.NUMBERS),
    JSON_DATA_ENTITY(SimpleDataType.DATA_ENTITY),
    JSON_DATA_ENTITIES(SimpleDataType.DATA_ENTITIES),
    JSON_VO_ENTITY(SimpleDataType.RULE_ENTITY),
    J<PERSON><PERSON>_RULE_ENTITY(SimpleDataType.RULE_ENTITY),
    JSO<PERSON>_RULE_ENTITIES(SimpleDataType.RULE_ENTITIES),

    /**
     * 流
     */
    STREAM_FILE(SimpleDataType.FILE);

    /**
     * 对应数据类型
     */
    private SimpleDataType dataType;

    ApiResBodyType(SimpleDataType dataType) {
        this.dataType = dataType;
    }

    public SimpleDataType getDataType() {
        return dataType;
    }
}
