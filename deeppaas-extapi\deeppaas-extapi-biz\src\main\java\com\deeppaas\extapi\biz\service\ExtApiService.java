package com.deeppaas.extapi.biz.service;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.datastore.api.client.DataStoreEntityClient;
import com.deeppaas.datastore.api.client.model.DataStoreEntityFieldCO;
import com.deeppaas.extapi.biz.convert.ExtApiConvert;
import com.deeppaas.extapi.biz.dao.ExtApiDao;
import com.deeppaas.extapi.biz.dao.ExtApiParamDao;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.model.*;
import com.deeppaas.rule.api.client.RuleClient;
import com.deeppaas.rule.api.model.RuleEntityPropertyPO;
import com.deeppaas.rule.api.model.RulePO;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/2/25
 */
@Service
public class ExtApiService {
    private final ExtApiDao apiDao;
    private final ExtApiParamDao apiParamDao;
    private final RuleClient ruleClient;
    private final DataStoreEntityClient entityClient;

    public ExtApiService(ExtApiDao apiDao, ExtApiParamDao apiParamDao,
                         @Lazy RuleClient ruleClient,
                         @Lazy DataStoreEntityClient entityClient) {
        this.apiDao = apiDao;
        this.apiParamDao = apiParamDao;
        this.ruleClient = ruleClient;
        this.entityClient = entityClient;
    }

    /**
     * 接口-获取
     */
    public ExtApiDO get(String id) {
        return apiDao.getById(id);
    }

    /**
     * 接口-获取
     */
    public ExtApiVO getVO(String id) {
        ExtApiDO apiDO = apiDao.getById(id);
        return ExtApiConvert.entityToVO(apiDO);
    }

    /**
     * 接口-批量获取
     */
    public List<ExtApiSimpleVO> listByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<ExtApiDO> apiDOList = apiDao.findByIdIn(ids);
        return ExtApiConvert.entityToSimpleVOS(apiDOList);
    }

    /**
     * 接口-分页查询
     */
    public PageData<ExtApiVO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<ExtApiDO> page = apiDao.queryForPage(condition, pageSearch.getPageable());
        List<ExtApiVO> voList = ExtApiConvert.entityToVOS(page.getContent());
        return PageData.init(page, voList);
    }

    /**
     * 接口-保存
     */
    @Transactional
    public ExtApiDO save(ExtApiParam param) {
        if (StringUtils.hasText(param.getId())) {
            ExtApiDO apiDO = apiDao.getById(param.getId());
            ExtApiConvert.paramToEntity(apiDO, param);
            return apiDao.save(apiDO);
        }
        ExtApiDO apiDO = ExtApiConvert.paramToEntity(param);
        return apiDao.save(apiDO);
    }

    /**
     * 接口-删除
     */
    @Transactional
    public void delete(String id) {
        // 删除接口参数
        apiParamDao.deleteByApiId(id);
        // 删除接口
        apiDao.deleteById(id);
    }

    /**
     * 接口-禁用|启用
     */
    @Transactional
    public void usable(String id, Integer usable) {
        ExtApiDO apiDO = apiDao.getById(id);
        if (Objects.equals(apiDO.getUsable(), usable)) {
            return;
        }
        // 修改可用标识
        apiDO.setUsable(usable);
        apiDao.save(apiDO);
    }

    /**
     * 接口参数-保存
     */
    @Transactional
    public ExtApiParamDO saveParam(ExtApiParamDO param) {
        return apiParamDao.save(param);
    }

    /**
     * 接口参数-删除
     */
    @Transactional
    public void deleteParam(String id) {
        // 删除接口参数
        apiParamDao.deleteById(id);
    }

    /**
     * 接口参数-列表
     */
    public List<ExtApiParamDO> listParam(String apiId) {
        return apiParamDao.findByApiIdOrderByIdAsc(apiId);
    }

    /**
     * 接口参数-生成
     */
    @Transactional
    public void convertParam(ExtApiParamConvert param) {
        String apiId = param.getId();
        String position = param.getPosition();
        // 先清除原有参数
        apiParamDao.deleteByApiId(apiId);
        // 生成新的参数
        List<ExtApiParamDO> paramDOList = null;
        if (SimpleDataType.isDataEntity(param.getType())) {
            List<DataStoreEntityFieldCO> fieldList = entityClient.listEntityField(param.getEntity());
            paramDOList = ExtApiConvert.storeEntityFieldToParams(apiId, position, fieldList);
        } else if (SimpleDataType.isRuleEntity(param.getType())) {
            List<RuleEntityPropertyPO> fieldList = ruleClient.listEntityProperty(param.getEntity());
            paramDOList = ExtApiConvert.ruleEntityFieldToParams(apiId, position, fieldList);
        }
        if (!CollectionUtils.isEmpty(paramDOList)) {
            // 保存
            apiParamDao.saveAll(paramDOList);
        }
    }

    /**
     * 接口规则-保存
     */
    @Transactional
    public RulePO saveRule(ExtApiRuleParam param) {
        ExtApiDO apiDO = apiDao.getById(param.getApiId());
        if (!apiDO.isSelfApi()) {
            throw RunException.optReject("暂不支持第三方API保存执行规则");
        }

        RulePO rulePO = ExtApiConvert.ruleParamToPO(apiDO, param);
        // 组装RulePO
        rulePO = ruleClient.saveOwnerUniqueRule(rulePO);
        if (StringHelper.isEmpty(apiDO.getRuleId())) {
            apiDO.setRuleId(rulePO.getId());
            apiDao.save(apiDO);
        }
        return rulePO;
    }

    /**
     * 接口规则-获取
     */

    public RulePO getApiRule(String apiId) {
        ExtApiDO apiDO = apiDao.getById(apiId);
        if (apiDO == null || !StringUtils.hasText(apiDO.getRuleId())) {
            return null;
        }
        return ruleClient.getRuleById(apiDO.getRuleId());
    }
}
