package com.deeppaas.extapi.biz.web;

import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.common.web.context.BodyParamContext;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.service.*;
import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.rule.api.client.RuleClient;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 前端动作调用接口
 *
 * <AUTHOR>
 * @date 2022/6/21
 */
@RequestAuth(auth = false)
@RestController
@RequestMapping("/api/extApiAction")
public class ExtApiActionApi {
    private final ExtApiService extApiService;
    private final ExtApiParamResolver extApiParamResolver;
    private final RuleClient ruleClient;
    private final FileClient fileClient;
    private final SelfApiDispatcher selfApiDispatcher;

    public ExtApiActionApi(ExtApiService extApiService, ExtApiParamResolver extApiParamResolver, RuleClient ruleClient, FileClient fileClient, SelfApiDispatcher selfApiDispatcher) {
        this.extApiService = extApiService;
        this.extApiParamResolver = extApiParamResolver;
        this.ruleClient = ruleClient;
        this.fileClient = fileClient;
        this.selfApiDispatcher = selfApiDispatcher;
    }

    @PostMapping("/execute/json/{apiId}")
    public Object executeJson(@PathVariable String apiId, HttpServletRequest request,
                              HttpServletResponse response) {
        ExtApiDO extApiDO = extApiService.get(apiId);
        if(extApiDO==null){
            return RestModel.error(ReqException.paramValidate("没有找到ID="+apiId+"的接口"));
        }
        List<ExtApiParamDO> apiParams = extApiService.listParam(apiId);
        BodyParamContext.loadJsonParam(request);
        Map<String, Object> paramMap = BodyParamContext.getBodyParameterMap();

        if (extApiDO.isSelfApi()) {
            if(BoolHelper.intToBool(extApiDO.getSelfApi())){
                selfApiDispatcher.restActionForward(extApiDO, apiParams, paramMap, request, response);
                return null;
            }else {
                return RestModel.data(ruleClient.executeById(extApiDO.getRuleId(), paramMap));
            }
        } else {
            return ThirdApiClient.of(extApiDO, apiParams, fileClient).execute(paramMap);
        }
    }

    @PostMapping("/execute/form/{apiId}")
    public Object executeForm(@PathVariable String apiId, MultipartHttpServletRequest request,
                                    HttpServletResponse response) {
        ExtApiDO extApiDO = extApiService.get(apiId);
        if(extApiDO==null){
            return RestModel.error(ReqException.paramValidate("没有找到ID="+apiId+"的接口"));
        }
        List<ExtApiParamDO> apiParams = extApiService.listParam(apiId);

        // 调用
        if (extApiDO.isSelfApi()) {
            if(BoolHelper.intToBool(extApiDO.getSelfApi())){
                selfApiDispatcher.formActionForward(extApiDO, apiParams, request, response);
                return null;
            }else {
                // 加载参数数据
                Map<String, Object> reqParams = extApiParamResolver.resolveApiActionParams(extApiDO, apiParams, request);
                return ruleClient.executeById(extApiDO.getRuleId(), reqParams);
            }
        } else {
            // 加载参数数据
            Map<String, Object> reqParams = extApiParamResolver.resolveApiActionParams(extApiDO, apiParams, request);
            return ThirdApiClient.of(extApiDO, apiParams, fileClient).execute(reqParams);
        }
    }

}
