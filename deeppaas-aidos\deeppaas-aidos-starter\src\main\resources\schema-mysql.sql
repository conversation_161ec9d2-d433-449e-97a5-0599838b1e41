SET NAMES utf8mb4;

-- 组织结构相关表结构-----begin
CREATE TABLE IF NOT EXISTS `org_attribute` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `usable` int(11) NOT NULL COMMENT '是否有效（1=有效、其他=无效）',
  `source` int(11) DEFAULT NULL COMMENT '来源（1=手动输入、2=业务表）',
  `form_type` varchar(50) DEFAULT NULL COMMENT '表单属性',
  `org_type` json DEFAULT NULL COMMENT '应用于组织结构类型',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_attribute_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `org_attribute_relation` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `org_type` varchar(50) NOT NULL COMMENT '组织结构类型',
  `org_code` varchar(100) NOT NULL COMMENT '组织结构编号',
  `attribute_code` varchar(100) NOT NULL COMMENT '属性编号',
  `attribute_value` tinytext DEFAULT NULL COMMENT '属性值',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_org_attribute_relation_typeCode` (`org_type`,`org_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `org_dept` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `parent_code` varchar(100) NOT NULL COMMENT '上级部门编号',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `dept_level` int(11) NOT NULL COMMENT '部门级别（0-9（0为默认、1最大、9最小））',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `usable` int(11) NOT NULL COMMENT '是否有效（1=有效、其他=无效）',
  `type` varchar(50) DEFAULT NULL COMMENT '部门类型（部门=DEPT、岗位=POST）',
  `post_code` varchar(100) DEFAULT NULL COMMENT '岗位编号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_dept_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `org_dept_manager` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `dept_code` varchar(100) NOT NULL COMMENT '部门编号',
  `user_code` varchar(100) NOT NULL COMMENT '用户编号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_dept_manager_code` (`dept_code`,`user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `org_dept_user` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `dept_code` varchar(100) NOT NULL COMMENT '部门编号',
  `user_code` varchar(100) NOT NULL COMMENT '用户编号',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `usable` int(11) NOT NULL COMMENT '是否有效（1=有效、其他=无效）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_dept_user_code` (`code`),
  KEY `idx_org_dept_user_deptCode` (`dept_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `org_post` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `usable` int(11) NOT NULL COMMENT '是否有效（1=有效、其他=无效）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_post_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `org_post_scope` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `post_code` varchar(100) NOT NULL COMMENT '岗位编号',
  `user_code` varchar(100) NOT NULL COMMENT '用户编号',
  `dept_code` varchar(100) NOT NULL COMMENT '管理部门编号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_org_post_scope_postUser` (`post_code`,`user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_permission` (
      `id` varchar(50) NOT NULL COMMENT '主键ID',
      `code` varchar(100) NOT NULL COMMENT '权限编号',
      `name` varchar(100) NOT NULL COMMENT '权限名称',
      `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
      `summary` varchar(200) DEFAULT NULL COMMENT '权限说明',
      `create_time` datetime NOT NULL COMMENT '创建时间',
      `group_code` varchar(100) DEFAULT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_account_permission_code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_permission_group` (
                                                        `id` varchar(50) NOT NULL COMMENT '主键ID',
                                                        `name` varchar(100) NOT NULL COMMENT '权限名称',
                                                        `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
                                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                                        PRIMARY KEY (`id`)
                                                      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_role` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '权限编号',
  `group_code` varchar(100) NOT NULL COMMENT '分组编号',
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `summary` varchar(200) DEFAULT NULL COMMENT '权限说明',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_role_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_role_user` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `role_code` varchar(100) NOT NULL COMMENT '角色编号',
  `user_code` varchar(100) NOT NULL COMMENT '用户编号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_role_user_code` (`role_code`,`user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_role_group` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_role_group_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_role_permission` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `role_code` varchar(100) NOT NULL COMMENT '角色编号',
  `permission_code` varchar(100) NOT NULL COMMENT '权限编号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_role_permission_code` (`role_code`,`permission_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_user` (
    `id` varchar(32) NOT NULL COMMENT '主键ID',
    `code` varchar(32) NOT NULL COMMENT '编号',
    `name` varchar(50) NOT NULL COMMENT '名称',
    `password` varchar(64) NOT NULL COMMENT '用户密码',
    `usable` tinyint(11) NOT NULL COMMENT '是否有效（1=有效、其他=无效）',
    `avatar` varchar(200) DEFAULT NULL COMMENT '用户头像',
    `phone_number` varchar(20) DEFAULT NULL COMMENT '手机号码',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `pwd_flag` int(11) DEFAULT NULL COMMENT '是否修改过密码',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `login_time` datetime DEFAULT NULL COMMENT '最近登录时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_org_dept_code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `account_user_proxy` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `user_code` varchar(100) NOT NULL COMMENT '用户编号',
  `proxy_user_code` varchar(100) NOT NULL COMMENT '用户编号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_user_proxy_code` (`user_code`,`proxy_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- 组织结构相关表结构-----end

CREATE TABLE IF NOT EXISTS `tenant_app` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `icon` varchar(200) DEFAULT NULL COMMENT '图标',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `usable` int(11) NOT NULL COMMENT '是否有效（1=有效、其他=无效）',
  `remark` varchar(200) DEFAULT NULL COMMENT '说明',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_app_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `tenant_app_menu` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `app_code` varchar(100) NOT NULL COMMENT '应用编号',
  `group_id` varchar(100) NOT NULL COMMENT '所属菜单组',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `icon` varchar(200) DEFAULT NULL COMMENT '图标',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `usable` int(11) NOT NULL COMMENT '是否有效（1=有效、其他=无效）',
  `remark` varchar(200) DEFAULT NULL COMMENT '说明',
  `component` varchar(100) DEFAULT NULL COMMENT '关联模型下入口组件',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `tenant_app_menu_group` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `app_code` varchar(100) NOT NULL COMMENT '应用编号',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `tenant_setting` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '编号',
  `value` text COMMENT '值',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `deeppaas_page_release` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `page` varchar(100) NOT NULL,
  `title` varchar(100) DEFAULT NULL,
  `urls` longtext,
  `active` int(11) NOT NULL DEFAULT '0',
  `remark` varchar(200) DEFAULT NULL,
  `create_time` datetime NOT NULL,
  `head` text,
  `body` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
