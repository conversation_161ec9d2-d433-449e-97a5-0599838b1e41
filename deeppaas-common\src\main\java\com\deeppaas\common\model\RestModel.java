package com.deeppaas.common.model;

import com.deeppaas.common.exception.BaseException;
import org.springframework.data.domain.Page;

/**
 * @param <T>
 * <AUTHOR>
 * @ClassName: RestModel
 * @Description: Rest结果返回包装类
 * @date 2017年7月31日
 */
public class RestModel<T> {
    public static final int CODE_OK = 0;
    /**
     * 消息编码
     */
    private int code;
    /**
     * 消息编码(兼容老OA)
     */
    private int errorCode;
    /**
     * 消息信息
     */
    private String message;
    /**
     * 传输数据
     */
    private T data;

    public RestModel() {
        this.code = CODE_OK;
        this.errorCode = CODE_OK;
        this.message = "success";
    }

    public static <T> RestModel<T> data(T t) {
        RestModel<T> rd = new RestModel<T>();
        rd.setData(t);
        return rd;
    }

    public static <T> RestModel<PageData<T>> page(Page<T> page) {
        RestModel<PageData<T>> rd = new RestModel<PageData<T>>();
        rd.setData(PageData.init(page));
        return rd;
    }

    public static RestModel<String> success(String message) {
        RestModel<String> rd = new RestModel<String>();
        rd.setCode(CODE_OK);
        rd.setErrorCode(CODE_OK);
        rd.setMessage(message);
        return rd;
    }

    public static RestModel<?> error(int errorCode, String message) {
        RestModel<?> rd = new RestModel<String>();
        rd.setCode(errorCode);
        rd.setErrorCode(errorCode);
        rd.setMessage(message);
        return rd;
    }

    public static <T> RestModel<T> error(BaseException bex){
        RestModel<T> rd = new RestModel<>();
        rd.setCode(bex.getCode());
        rd.setErrorCode(bex.getCode());
        rd.setMessage(bex.getMessage());
        return rd;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }
}
