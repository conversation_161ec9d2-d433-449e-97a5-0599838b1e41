package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityRelationDO;
import org.springframework.data.jdbc.repository.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
public interface DataStoreEntityRelationDao extends BaseJdbcDao<DataStoreEntityRelationDO, String> {

    @Query("select * from datastore_entity_relation where " +
            "(entity1_code=:entity1_code and entity2_code=:entity2_code) or " +
            "(entity1_code=:entity2_code and entity2_code=:entity1_code)")
    DataStoreEntityRelationDO findRelation(String entity1Code, String entity2Code);

    @Query("select * from datastore_entity_relation where entity1_code=:entityCode or entity2_code=:entityCode")
    List<DataStoreEntityRelationDO> findByEntityCode(String entityCode);
}
