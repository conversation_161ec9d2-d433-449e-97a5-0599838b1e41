package com.deeppaas.flow.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowTokenDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/22
 */
public interface FlowTokenDao extends BaseJdbcDao<FlowTokenDO, Long> {

    List<FlowTokenDO> findByParentId(Long parentId);

    void deleteByInstanceId(Long instanceId);

    List<FlowTokenDO> findByInstanceId(Long instanceId);

    @Modifying
    @Query("update flow_token set operator=:operator, task_id=:taskId where id=:id")
    void updateToken(Long id, String operator, Long taskId);
}
