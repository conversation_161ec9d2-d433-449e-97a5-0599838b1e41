package com.deeppaas.file.biz.convert;

import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.biz.entity.FileGroupDO;
import com.deeppaas.file.biz.service.FileAes;
import com.deeppaas.file.oss.model.OSSFileGroup;
import com.deeppaas.file.oss.model.OssUploadSign;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
public class FileGroupConvert {

    public static FileGroup buildFileGroup(FileGroupDO fileGroupDO){
        if(fileGroupDO==null){
            return null;
        }
        FileGroup fileGroup = new FileGroup();
        fileGroup.setName(fileGroupDO.getId());
        fileGroup.setMode(fileGroupDO.getMode());
        fileGroup.setType(fileGroupDO.getType());
        fileGroup.setBucket(fileGroupDO.getBucket());
        fileGroup.setDomain(fileGroupDO.getDomain());
        return fileGroup;
    }

    public static OSSFileGroup buildOssFileGroup(FileGroupDO fileGroupDO){
        if(fileGroupDO==null){
            return null;
        }
        OSSFileGroup fileGroup = new OSSFileGroup();
        fileGroup.setName(fileGroupDO.getId());
        fileGroup.setMode(fileGroupDO.getMode());
        fileGroup.setType(fileGroupDO.getType());
        fileGroup.setBucket(fileGroupDO.getBucket());
        fileGroup.setDomain(fileGroupDO.getDomain());
        String propertiesSecret = fileGroupDO.getProperties();
        if(StringUtils.hasText(propertiesSecret)){
            String propertiesJson = FileAes.decrypt(propertiesSecret);
            Map<String, Object> propertiesMap = JsonHelper.json2map(propertiesJson);
            fileGroup.setAccessKeyId(propertiesMap.getOrDefault(OSSFileGroup.PROPERTY_ACCESS_KEY_ID, "").toString());
            fileGroup.setAccessKeySecret(propertiesMap.getOrDefault(OSSFileGroup.PROPERTY_ACCESS_KEY_SECRET, "").toString());
            fileGroup.setEndPoint(propertiesMap.getOrDefault(OSSFileGroup.PROPERTY_END_POINT, "").toString());
        }
        return fileGroup;
    }
}
