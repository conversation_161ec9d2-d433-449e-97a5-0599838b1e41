package com.deeppaas.rule.biz.cache;

import com.deeppaas.rule.biz.model.RuleModel;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 规则模型缓存
 * <AUTHOR>
 * @date 2022/6/17
 */
@Component
public class RuleModelCache {
    /**
     * repository缓存
     */
    private static Map<String, RuleModel> ruleModelMap = new ConcurrentHashMap<>();


    public RuleModel get(String ruleId) {
        return ruleModelMap.get(ruleId);
    }

    public void remove(String ruleId) {
        ruleModelMap.remove(ruleId);
    }

    public void put(String ruleId, RuleModel ruleModel) {
        ruleModelMap.put(ruleId, ruleModel);
    }

}
