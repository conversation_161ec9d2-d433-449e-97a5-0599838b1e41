import{P as $,I as Z,a as ee,r as u,C as A,f as y,J as _,K as S,L as b,M as R,b as k,e as w,a0 as te,ao as ne,a1 as re}from"./index.689bcdcb.js";import{e as E}from"./reactNode.50ea6579.js";import{o as ie,R as z}from"./index.71d34973.js";import{g as oe,s as ae}from"./scrollTo.edb0bd08.js";function se(s){var a,r=function(n){return function(){a=null,s.apply(void 0,Z(n))}},e=function(){if(a==null){for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];a=$(r(i))}};return e.cancel=function(){return $.cancel(a)},e}function V(){return function(a,r,e){var t=e.value,n=!1;return{configurable:!0,get:function(){if(n||this===a.prototype||this.hasOwnProperty(r))return t;var o=se(t.bind(this));return n=!0,Object.defineProperty(this,r,{value:o,configurable:!0,writable:!0}),n=!1,o}}}}function P(s){return s!==window?s.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function B(s,a,r){if(r!==void 0&&a.top>s.top-r)return r+a.top}function D(s,a,r){if(r!==void 0&&a.bottom<s.bottom+r){var e=window.innerHeight-a.bottom;return r+e}}var j=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"],T=[];function U(s,a){if(!!s){var r=T.find(function(e){return e.target===s});r?r.affixList.push(a):(r={target:s,affixList:[a],eventHandlers:{}},T.push(r),j.forEach(function(e){r.eventHandlers[e]=E(s,e,function(){r.affixList.forEach(function(t){t.lazyUpdatePosition()})})}))}}function M(s){var a=T.find(function(r){var e=r.affixList.some(function(t){return t===s});return e&&(r.affixList=r.affixList.filter(function(t){return t!==s})),e});a&&a.affixList.length===0&&(T=T.filter(function(r){return r!==a}),j.forEach(function(r){var e=a.eventHandlers[r];e&&e.remove&&e.remove()}))}var G=globalThis&&globalThis.__decorate||function(s,a,r,e){var t=arguments.length,n=t<3?a:e===null?e=Object.getOwnPropertyDescriptor(a,r):e,i;if((typeof Reflect=="undefined"?"undefined":ee(Reflect))==="object"&&typeof Reflect.decorate=="function")n=Reflect.decorate(s,a,r,e);else for(var o=s.length-1;o>=0;o--)(i=s[o])&&(n=(t<3?i(n):t>3?i(a,r,n):i(a,r))||n);return t>3&&n&&Object.defineProperty(a,r,n),n};function fe(){return typeof window!="undefined"?window:null}var C;(function(s){s[s.None=0]="None",s[s.Prepare=1]="Prepare"})(C||(C={}));var L=function(s){_(r,s);var a=S(r);function r(){var e;return b(this,r),e=a.apply(this,arguments),e.state={status:C.None,lastAffix:!1,prevTarget:null},e.getOffsetTop=function(){var t=e.props,n=t.offsetBottom,i=t.offsetTop;return n===void 0&&i===void 0?0:i},e.getOffsetBottom=function(){return e.props.offsetBottom},e.savePlaceholderNode=function(t){e.placeholderNode=t},e.saveFixedNode=function(t){e.fixedNode=t},e.measure=function(){var t=e.state,n=t.status,i=t.lastAffix,o=e.props.onChange,f=e.getTargetFunc();if(!(n!==C.Prepare||!e.fixedNode||!e.placeholderNode||!f)){var c=e.getOffsetTop(),p=e.getOffsetBottom(),h=f();if(!!h){var l={status:C.None},v=P(h),d=P(e.placeholderNode),m=B(d,v,c),x=D(d,v,p);m!==void 0?(l.affixStyle={position:"fixed",top:m,width:d.width,height:d.height},l.placeholderStyle={width:d.width,height:d.height}):x!==void 0&&(l.affixStyle={position:"fixed",bottom:x,width:d.width,height:d.height},l.placeholderStyle={width:d.width,height:d.height}),l.lastAffix=!!l.affixStyle,o&&i!==l.lastAffix&&o(l.lastAffix),e.setState(l)}}},e.prepareMeasure=function(){e.setState({status:C.Prepare,affixStyle:void 0,placeholderStyle:void 0})},e}return R(r,[{key:"getTargetFunc",value:function(){var t=this.context.getTargetContainer,n=this.props.target;return n!==void 0?n:t||fe}},{key:"componentDidMount",value:function(){var t=this,n=this.getTargetFunc();n&&(this.timeout=setTimeout(function(){U(n(),t),t.updatePosition()}))}},{key:"componentDidUpdate",value:function(t){var n=this.state.prevTarget,i=this.getTargetFunc(),o=(i==null?void 0:i())||null;n!==o&&(M(this),o&&(U(o,this),this.updatePosition()),this.setState({prevTarget:o})),(t.offsetTop!==this.props.offsetTop||t.offsetBottom!==this.props.offsetBottom)&&this.updatePosition(),this.measure()}},{key:"componentWillUnmount",value:function(){clearTimeout(this.timeout),M(this),this.updatePosition.cancel(),this.lazyUpdatePosition.cancel()}},{key:"updatePosition",value:function(){this.prepareMeasure()}},{key:"lazyUpdatePosition",value:function(){var t=this.getTargetFunc(),n=this.state.affixStyle;if(t&&n){var i=this.getOffsetTop(),o=this.getOffsetBottom(),f=t();if(f&&this.placeholderNode){var c=P(f),p=P(this.placeholderNode),h=B(p,c,i),l=D(p,c,o);if(h!==void 0&&n.top===h||l!==void 0&&n.bottom===l)return}}this.prepareMeasure()}},{key:"render",value:function(){var t=this,n=this.state,i=n.affixStyle,o=n.placeholderStyle,f=this.props,c=f.affixPrefixCls,p=f.children,h=k(w({},c,!!i)),l=ie(this.props,["prefixCls","offsetTop","offsetBottom","target","onChange","affixPrefixCls"]);return u.exports.createElement(z,{onResize:function(){t.updatePosition()}},u.exports.createElement("div",y({},l,{ref:this.savePlaceholderNode}),i&&u.exports.createElement("div",{style:o,"aria-hidden":"true"}),u.exports.createElement("div",{className:h,ref:this.saveFixedNode,style:i},u.exports.createElement(z,{onResize:function(){t.updatePosition()}},p))))}}]),r}(u.exports.Component);L.contextType=A;G([V()],L.prototype,"updatePosition",null);G([V()],L.prototype,"lazyUpdatePosition",null);var le=u.exports.forwardRef(function(s,a){var r=s.prefixCls,e=u.exports.useContext(A),t=e.getPrefixCls,n=t("affix",r),i=y(y({},s),{affixPrefixCls:n});return u.exports.createElement(L,y({},i,{ref:a}))}),ce=le,ue=u.exports.createContext(null),J=ue;function pe(){return window}function H(s,a){if(!s.getClientRects().length)return 0;var r=s.getBoundingClientRect();return r.width||r.height?a===window?(a=s.ownerDocument.documentElement,r.top-a.clientTop):r.top-a.getBoundingClientRect().top:r.top}var W=/#([\S ]+)$/,N=function(s){_(r,s);var a=S(r);function r(){var e;return b(this,r),e=a.apply(this,arguments),e.state={activeLink:null},e.wrapperRef=u.exports.createRef(),e.links=[],e.registerLink=function(t){e.links.includes(t)||e.links.push(t)},e.unregisterLink=function(t){var n=e.links.indexOf(t);n!==-1&&e.links.splice(n,1)},e.getContainer=function(){var t=e.context.getTargetContainer,n=e.props.getContainer,i=n||t||pe;return i()},e.handleScrollTo=function(t){var n=e.props,i=n.offsetTop,o=n.targetOffset;e.setCurrentActiveLink(t);var f=e.getContainer(),c=oe(f,!0),p=W.exec(t);if(!!p){var h=document.getElementById(p[1]);if(!!h){var l=H(h,f),v=c+l;v-=o!==void 0?o:i||0,e.animating=!0,ae(v,{callback:function(){e.animating=!1},getContainer:e.getContainer})}}},e.saveInkNode=function(t){e.inkNode=t},e.setCurrentActiveLink=function(t){var n=e.state.activeLink,i=e.props,o=i.onChange,f=i.getCurrentAnchor;n!==t&&(e.setState({activeLink:typeof f=="function"?f():t}),o==null||o(t))},e.handleScroll=function(){if(!e.animating){var t=e.props,n=t.offsetTop,i=t.bounds,o=t.targetOffset,f=e.getCurrentAnchor(o!==void 0?o:n||0,i);e.setCurrentActiveLink(f)}},e.updateInk=function(){var t=te(e),n=t.prefixCls,i=t.wrapperRef,o=i.current,f=o==null?void 0:o.getElementsByClassName("".concat(n,"-link-title-active"))[0];f&&(e.inkNode.style.top="".concat(f.offsetTop+f.clientHeight/2-4.5,"px"))},e.getMemoizedContextValue=ne(function(t,n){return{registerLink:e.registerLink,unregisterLink:e.unregisterLink,scrollTo:e.handleScrollTo,activeLink:t,onClick:n}}),e}return R(r,[{key:"componentDidMount",value:function(){this.scrollContainer=this.getContainer(),this.scrollEvent=E(this.scrollContainer,"scroll",this.handleScroll),this.handleScroll()}},{key:"componentDidUpdate",value:function(){if(this.scrollEvent){var t=this.getContainer();this.scrollContainer!==t&&(this.scrollContainer=t,this.scrollEvent.remove(),this.scrollEvent=E(this.scrollContainer,"scroll",this.handleScroll),this.handleScroll())}this.updateInk()}},{key:"componentWillUnmount",value:function(){this.scrollEvent&&this.scrollEvent.remove()}},{key:"getCurrentAnchor",value:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:5,i=[],o=this.getContainer();if(this.links.forEach(function(c){var p=W.exec(c.toString());if(!!p){var h=document.getElementById(p[1]);if(h){var l=H(h,o);l<t+n&&i.push({link:c,top:l})}}}),i.length){var f=i.reduce(function(c,p){return p.top>c.top?p:c});return f.link}return""}},{key:"render",value:function(){var t=this.context,n=t.getPrefixCls,i=t.direction,o=this.props,f=o.prefixCls,c=o.className,p=c===void 0?"":c,h=o.style,l=o.offsetTop,v=o.affix,d=o.showInkInFixed,m=o.children,x=o.onClick,O=this.state.activeLink,g=n("anchor",f);this.prefixCls=g;var K=k("".concat(g,"-ink-ball"),{visible:O}),q=k("".concat(g,"-wrapper"),w({},"".concat(g,"-rtl"),i==="rtl"),p),Q=k(g,w({},"".concat(g,"-fixed"),!v&&!d)),X=y({maxHeight:l?"calc(100vh - ".concat(l,"px)"):"100vh"},h),I=u.exports.createElement("div",{ref:this.wrapperRef,className:q,style:X},u.exports.createElement("div",{className:Q},u.exports.createElement("div",{className:"".concat(g,"-ink")},u.exports.createElement("span",{className:K,ref:this.saveInkNode})),m)),Y=this.getMemoizedContextValue(O,x);return u.exports.createElement(J.Provider,{value:Y},v?u.exports.createElement(ce,{offsetTop:l,target:this.getContainer},I):I)}}]),r}(u.exports.Component);N.defaultProps={affix:!0,showInkInFixed:!1};N.contextType=A;var F=function(s){_(r,s);var a=S(r);function r(){var e;return b(this,r),e=a.apply(this,arguments),e.handleClick=function(t){var n=e.context,i=n.scrollTo,o=n.onClick,f=e.props,c=f.href,p=f.title;o==null||o(t,{title:p,href:c}),i(c)},e.renderAnchorLink=function(t){var n=t.getPrefixCls,i=e.props,o=i.prefixCls,f=i.href,c=i.title,p=i.children,h=i.className,l=i.target,v=n("anchor",o),d=e.context.activeLink===f,m=k("".concat(v,"-link"),w({},"".concat(v,"-link-active"),d),h),x=k("".concat(v,"-link-title"),w({},"".concat(v,"-link-title-active"),d));return u.exports.createElement("div",{className:m},u.exports.createElement("a",{className:x,href:f,title:typeof c=="string"?c:"",target:l,onClick:e.handleClick},c),p)},e}return R(r,[{key:"componentDidMount",value:function(){this.context.registerLink(this.props.href)}},{key:"componentDidUpdate",value:function(t){var n=t.href,i=this.props.href;n!==i&&(this.context.unregisterLink(n),this.context.registerLink(i))}},{key:"componentWillUnmount",value:function(){this.context.unregisterLink(this.props.href)}},{key:"render",value:function(){return u.exports.createElement(re,null,this.renderAnchorLink)}}]),r}(u.exports.Component);F.defaultProps={href:"#"};F.contextType=J;var he=F;N.Link=he;var xe=N;const Ce=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];var ke="/static/assets/emptyImg.0ab1172a.png";export{xe as A,ke as e,Ce as w};
