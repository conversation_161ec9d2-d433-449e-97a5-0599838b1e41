package com.deeppaas.rule.biz.action;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 调用接口动作
 * <AUTHOR>
 * @date 2022/6/11
 */
@Data
public class RExtApiAction extends RAction{
    /**
     * 调用的接口
     */
    private String apiId;

    private Map<String, RDataBind> paramBinds;

    @Override
    public Object execute(RuleContext ruleContext) {
        Map<String, Object> params = new HashMap<>();
        if(paramBinds!=null){
            for(String paramName : paramBinds.keySet()){
                RDataBind paramDataBind = paramBinds.get(paramName);
                params.put(paramName, paramDataBind.getBindData(ruleContext));
            }
        }
        Object result = ruleContext.getRuleEngine().extApiClient().execute(apiId, params);
        return super.returnOrNext(result, ruleContext);
    }
}
