package com.deeppaas.quartz.api;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.quartz.entity.JobApiParamDO;
import com.deeppaas.quartz.service.QuartzJobParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 定时任务管理器接口层
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@RestController
@RequestMapping("/api/quartz/param")
public class QuartzJobParamApi {
    private final QuartzJobParamService jobParamService;

    @Autowired
    public QuartzJobParamApi(QuartzJobParamService jobParamService) {
        this.jobParamService = jobParamService;
    }


    /**
     * 获取API方式定时任务参数列表
     */
    @GetMapping("/api/list")
    public RestModel<List<JobApiParamDO>> listApiParam(@RequestParam String jobId) {
        List<JobApiParamDO> apiParamDOList = jobParamService.listApiParam(jobId);
        return RestModel.data(apiParamDOList);
    }

    /**
     * 保存API方式参数
     */
    @PostMapping("/api/save")
    public RestModel<Boolean> saveApiParam(@RequestBody JobApiParamDO param) {
        jobParamService.saveApiParam(param);
        return RestModel.data(true);
    }

    /**
     * 删除API方式参数
     */
    @PostMapping("/api/delete")
    public RestModel<Boolean> deleteApiParam(@BodyParam Long paramId) {
        jobParamService.deleteApiParam(paramId);
        return RestModel.data(true);
    }
}
