package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityDO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
public interface DataStoreEntityDao extends BaseJdbcDao<DataStoreEntityDO, String> {

    DataStoreEntityDO findByCode(String code);

    List<DataStoreEntityDO> findBySourceIdOrderByIdAsc(String sourceId);

    boolean existsByCode(String code);

    List<DataStoreEntityDO> findByAppCodeOrderByIdAsc(String appCode);

    List<DataStoreEntityDO> findByCodeIn(Collection<String> relationEntityCodes);

    List<DataStoreEntityDO> findByTableIdOrderByIdAsc(String tableId);
}
