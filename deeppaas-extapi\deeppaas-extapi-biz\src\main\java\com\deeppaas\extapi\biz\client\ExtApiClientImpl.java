package com.deeppaas.extapi.biz.client;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.extapi.api.client.ExtApiClient;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.service.ExtApiService;
import com.deeppaas.extapi.biz.service.ThirdApiClient;
import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.rule.api.client.RuleClient;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
@Component
public class ExtApiClientImpl implements ExtApiClient {
    private final ExtApiService apiService;
    private final RuleClient ruleClient;

    private final FileClient fileClient;

    public ExtApiClientImpl(ExtApiService apiService, RuleClient ruleClient, FileClient fileClient) {
        this.apiService = apiService;
        this.ruleClient = ruleClient;
        this.fileClient = fileClient;
    }

    @Override
    public Object execute(String apiId, Map<String, Object> params) {
        ExtApiDO apiDO = apiService.get(apiId);
        // 检查API状态
        if (apiDO == null || !BoolHelper.intToBool(apiDO.getUsable())) {
            return null;
        }
        // TODO 权限检查
        if (apiDO.isSelfApi()) {
            return ruleClient.executeById(apiDO.getRuleId(), params);
        }
        // 获取接口参数
        List<ExtApiParamDO> apiParams = apiService.listParam(apiId);
        return ThirdApiClient.of(apiDO, apiParams, fileClient).execute(params);

    }


    public static void main(String[] args) {
//        // ====================不需要登录即可访问的API=======================
//        String pathParams = "code=ABBREVIATION";
//        // 接口地址
//        String url = "https://v2.test.linghe.tv/api/tenantSetting/get" + "?" + pathParams;
//        // 组装api参数数据
//        Map<String, String> headerParams = new HashMap<>();
//        Map<String, Object> bodyParams = new HashMap<>();
//        // 请求体参数
//        System.out.println(url);
//        String returnData = HttpHelper.httpsRequest(url, HttpMethod.valueOf("GET"), BodyType.JSON, bodyParams, headerParams);
//        System.out.println("returnData=" + returnData);


        // ====================需要登录的API====================
//        String pathParams = "code=UserAbsenceApply";
//        String url = "https://v2.test.linghe.tv/api/app/get" + "?" + pathParams;
//        // 组装api参数数据
//        Map<String, String> headerParams = new HashMap<>();
//        Map<String, Object> bodyParams = new HashMap<>();
//        // 请求头参数
//        headerParams.put("dt", "YIT3Y3v1PMEq35HEQG8xTjZW18rGrDz9DqPAk_ZxjMPFlQ0l1s8lNY2JGpuF5tjigvvleM1jns6jyt6Tl1_cl30BPSNJCj-u4swwgljcQQq9usO3gXjvk0RaoZhUSD2Y5GFdQzAZCnTgllrujZzFRg**");
//        // 拼接URL参数
//        // 请求体参数
//        System.out.println(url);
//        String returnData = HttpHelper.httpsRequest(url, HttpMethod.valueOf("GET"), BodyType.JSON, bodyParams, headerParams);
//        System.out.println("returnData=" + returnData);


        // ====================需要登录的API-POST====================
//        String pathParams = "";
//        String url = "https://v2.test.linghe.tv/api/app/add";
//        // 组装api参数数据
//        Map<String, String> headerParams = new HashMap<>();
//        Map<String, Object> bodyParams = new HashMap<>();
//        // 请求体参数
//        bodyParams.put("code", "API_TEST");
//        bodyParams.put("name", "接口测试");
//        bodyParams.put("icon", "");
//        bodyParams.put("remark", "API接口调用保存测试");
//        // 请求头参数
//        headerParams.put("dt", "YIT3Y3v1PMEq35HEQG8xTjZW18rGrDz9DqPAk_ZxjMPFlQ0l1s8lNY2JGpuF5tjigvvleM1jns6jyt6Tl1_cl30BPSNJCj-u4swwgljcQQq9usO3gXjvk0RaoZhUSD2Y5GFdQzAZCnTgllrujZzFRg**");
//        // 接口地址
//        // 请求体参数
//        System.out.println(url);
//        String returnData = HttpHelper.httpsRequest(url, HttpMethod.valueOf("POST"), BodyType.JSON, bodyParams, headerParams);
//        System.out.println("returnData=" + returnData);
    }
}
