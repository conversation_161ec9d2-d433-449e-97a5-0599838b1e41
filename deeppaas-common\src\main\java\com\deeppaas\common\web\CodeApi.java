package com.deeppaas.common.web;

import com.deeppaas.common.helper.CodeHelper;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.RequestAuth;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 编号-API
 *
 * <AUTHOR>
 * @date 2022/02/16
 */
@RequestAuth
@RestController
@RequestMapping("/api/code")
public class CodeApi {

    @GetMapping("/get")
    public RestModel<String> getCode() {
        return RestModel.data(CodeHelper.id10());
    }
}
