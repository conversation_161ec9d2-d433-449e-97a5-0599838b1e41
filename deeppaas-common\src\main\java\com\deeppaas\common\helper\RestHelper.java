package com.deeppaas.common.helper;

import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2022/2/17
 */
public class RestHelper {
    private static RestTemplate restTemplate;
    static {
        restTemplate = new RestTemplate();
        // 设置restTemplate编码为utf-8
        restTemplate.getMessageConverters().forEach( httpMessageConverter -> {
            if(httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)){
                ((StringHttpMessageConverter) httpMessageConverter).setDefaultCharset(StandardCharsets.UTF_8);
            }
        });
    }

    public static <T> T getForObject(String url, Class<T> responseType){
        return restTemplate.getForObject(url, responseType);
    }

    public static <T> T postForObject(String url, Object request, Class<T> responseType) {
        return restTemplate.postForObject(url, request, responseType);
    }

    public static <T> T postForObject(String url, String json, Class<T> responseType) {
        HttpEntity<String> httpEntity = new HttpEntity(json);
        return restTemplate.postForObject(url, httpEntity, responseType);
    }

    public static <T> T exchange(String url, HttpMethod httpMethod, HttpEntity httpEntity, Class<T> responseType){
        ResponseEntity<T> responseEntity = restTemplate.exchange(url, httpMethod, httpEntity, responseType);
        return responseEntity.getBody();
    }

    public static <T> ResponseEntity<T>  getForEntity(String url, HttpHeaders httpHeaders, Class<T> responseType){
        RequestEntity requestEntity = new RequestEntity(null, httpHeaders, HttpMethod.GET, URI.create(url));
        return restTemplate.exchange(requestEntity, responseType);
    }

    public static <T> ResponseEntity<T>  postForEntity(String url, Object request, Class<T> responseType){
        return restTemplate.postForEntity(URI.create(url), request, responseType);
    }
}
