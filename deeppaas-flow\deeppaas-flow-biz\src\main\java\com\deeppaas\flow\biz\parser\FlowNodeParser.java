package com.deeppaas.flow.biz.parser;

import com.deeppaas.flow.biz.element.*;
import com.deeppaas.flow.biz.element.TaskNode.TaskActorGroup;
import com.deeppaas.flow.biz.enums.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
public class FlowNodeParser {
    private static final String KEY_CODE = "code";
    private static final String KEY_NAME = "name";
    private static final String KEY_TYPE = "type";
    private static final String KEY_REMARK = "remark";

    /**
     * 处理页面
     */
    private static final String KEY_PAGE = "page";
    /**
     * 环节事件规则
     */
    private static final String KEY_EVENTS = "events";

    /**
     * 审批模式
     **/
    private static final String KEY_TASK_TYPE = "taskType";
    /**
     * 人员是否需要手动指定
     **/
    private static final String KEY_ACTOR_ASSIGN = "actorAssign";
    /**
     * 无审批人自动跳过
     **/
    private static final String KEY_ACTOR_NULL_PASS = "actorNullPass";
    /**
     * 与上一环节相同审批人自动跳过
     **/
    private static final String KEY_ACTOR_SAME_PASS = "actorSamePass";
    /**
     * 限定时间(0为不限定)
     **/
    private static final String KEY_LIMITED_TIME = "limitedTime";
    /**
     * 允许动作（）
     **/
    private static final String KEY_ACTION = "action";
    /**
     * 是否必须审批意见
     **/
    private static final String KEY_COMMENT = "comment";
    /**
     * 驳回目标环节（START/SOURCE）
     **/
    private static final String KEY_BACK_TARGET = "backTarget";
    /**
     * 驳回是否发送给所有审批人消息
     **/
    private static final String KEY_BACK_MSG = "backMsg";
    /**
     * 处理组
     **/
    private static final String KEY_ACTOR_GROUPS = "actorGroups";
    /**
     * 处理组-使用条件
     **/
    private static final String KEY_ACTOR_GROUP_CONDITION = "condition";
    /**
     * 处理组-参与对象
     **/
    private static final String KEY_ACTOR_GROUP_ACTORS = "actors";
    /**
     * 处理组-处理对象-指定类型
     **/
    private static final String KEY_ACTOR_GROUP_ACTORS_ACTOR_TYPE = "type";
    /**
     * 处理组-处理对象-对象编号
     **/
    private static final String KEY_ACTOR_GROUP_ACTORS_ACTOR_CODE = "code";
    /**
     * 处理组-处理对象-通用岗位需要的部门级别
     **/
    private static final String KEY_ACTOR_GROUP_ACTORS_ACTOR_DEPT_LEVEL = "deptLevel";
    /**
     * 处理组-处理对象-通用岗位需要的部门字段编号
     **/
    private static final String KEY_ACTOR_GROUP_ACTORS_ACTOR_DEPT_FIELD_CODE = "deptFieldCode";


    public static FlowNode parse(JsonNode nodeJson) {
        JsonNode typeNode = nodeJson.get(KEY_TYPE);
        NodeType nodeType = NodeType.valueOf(typeNode.textValue());
        String code = nodeJson.get(KEY_CODE).textValue();
        String name = nodeJson.get(KEY_NAME).textValue();
        String remark = nodeJson.get(KEY_REMARK).textValue();
        String page = nodeJson.get(KEY_PAGE) == null ? null : nodeJson.get(KEY_PAGE).textValue();
        List<FlowEventRule> eventRules = FlowEventParser.parse(nodeJson.get(KEY_EVENTS));
        return switch (nodeType) {
            case START -> parseStart(nodeType, code, name, remark, page, eventRules);
            case DECISION -> parseDecision(nodeType, code, name, remark, eventRules);
            case TASK -> parseTask(nodeType, code, name, remark, page, nodeJson, eventRules);
            case FORK -> parseFork(nodeType, code, name, remark, eventRules);
            case JOIN -> parseJoin(nodeType, code, name, remark, eventRules);
            case SUB_FLOW -> parseSubFlow(nodeType, code, name, remark, eventRules);
            case END -> parseEnd(nodeType, code, name, remark, page, eventRules);
            default -> null;
        };
    }

    private static EndNode parseEnd(NodeType nodeType, String code, String name, String remark, String page, List<FlowEventRule> eventRules) {
        return new EndNode(code, name, nodeType, remark, page, eventRules);
    }

    private static SubFlowNode parseSubFlow(NodeType nodeType, String code, String name, String remark, List<FlowEventRule> eventRules) {
        return new SubFlowNode(code, name, nodeType, remark, eventRules);
    }

    private static JoinNode parseJoin(NodeType nodeType, String code, String name, String remark, List<FlowEventRule> eventRules) {
        return new JoinNode(code, name, nodeType, remark, eventRules);
    }

    private static ForkNode parseFork(NodeType nodeType, String code, String name, String remark, List<FlowEventRule> eventRules) {
        return new ForkNode(code, name, nodeType, remark, eventRules);
    }

    private static TaskNode parseTask(NodeType nodeType, String code, String name, String remark, String page, JsonNode nodeJson, List<FlowEventRule> eventRules) {
        TaskType taskType = TaskType.valueOf(nodeJson.get(KEY_TASK_TYPE).textValue());
        boolean actorAssign = nodeJson.get(KEY_ACTOR_ASSIGN).asBoolean();
        boolean actorNullPass = nodeJson.get(KEY_ACTOR_NULL_PASS).asBoolean();
        boolean actorSamePass = nodeJson.get(KEY_ACTOR_SAME_PASS).asBoolean();
        Long limitedTime = nodeJson.get(KEY_LIMITED_TIME).asLong();
        String action = nodeJson.get(KEY_ACTION).textValue();
        boolean comment = nodeJson.get(KEY_COMMENT).asBoolean();
        BackTarget backTarget = BackTarget.valueOf(nodeJson.get(KEY_BACK_TARGET).textValue());
        boolean backMsg = nodeJson.get(KEY_BACK_MSG).asBoolean();
        List<TaskActorGroup> actorGroups = new ArrayList<>();
        Iterator<JsonNode> actorGroupNodes = nodeJson.get(KEY_ACTOR_GROUPS).elements();
        while (actorGroupNodes.hasNext()) {
            TaskActorGroup actorGroup = new TaskActorGroup();
            JsonNode groupNode = actorGroupNodes.next();
            String groupCondition = groupNode.get(KEY_ACTOR_GROUP_CONDITION).textValue();
            Iterator<JsonNode> actorNodes = groupNode.get(KEY_ACTOR_GROUP_ACTORS).elements();
            List<TaskNode.TaskActor> taskActors = new ArrayList<>();
            if(actorNodes!=null){
                while (actorNodes.hasNext()){
                    JsonNode actorNode = actorNodes.next();
                    String actorType = actorNode.get(KEY_ACTOR_GROUP_ACTORS_ACTOR_TYPE).textValue();
                    String actorCode = actorNode.get(KEY_ACTOR_GROUP_ACTORS_ACTOR_CODE).textValue();
                    int deptLevel = 0;
                    if(actorNode.has(KEY_ACTOR_GROUP_ACTORS_ACTOR_DEPT_LEVEL)){
                        deptLevel = actorNode.get(KEY_ACTOR_GROUP_ACTORS_ACTOR_DEPT_LEVEL).asInt();
                    }
                    String deptFieldCode =  null;
                    if(actorNode.has(KEY_ACTOR_GROUP_ACTORS_ACTOR_DEPT_FIELD_CODE)){
                        deptFieldCode = actorNode.get(KEY_ACTOR_GROUP_ACTORS_ACTOR_DEPT_FIELD_CODE).textValue();
                    }
                    TaskNode.TaskActor taskActor = new TaskNode.TaskActor();
                    taskActor.setType(TaskActorType.valueOf(actorType));
                    taskActor.setCode(actorCode);
                    taskActor.setDeptLevel(deptLevel);
                    taskActor.setDeptFieldCode(deptFieldCode);
                    taskActors.add(taskActor);
                }
            }
            actorGroup.setCondition(groupCondition);
            actorGroup.setActors(taskActors);
            actorGroups.add(actorGroup);
        }
        TaskNode taskNode = new TaskNode(code, name, nodeType, remark, page, taskType, actorAssign, actorNullPass, actorSamePass,
                limitedTime, action, comment, backTarget, backMsg, actorGroups, eventRules);
        return taskNode;
    }

    private static StartNode parseStart(NodeType nodeType, String code, String name, String remark, String page, List<FlowEventRule> eventRules) {
        return new StartNode(code, name, nodeType, remark, page, eventRules);
    }

    private static DecisionNode parseDecision(NodeType nodeType, String code, String name, String remark, List<FlowEventRule> eventRules) {
        return new DecisionNode(code, name, nodeType, remark, eventRules);
    }


}
