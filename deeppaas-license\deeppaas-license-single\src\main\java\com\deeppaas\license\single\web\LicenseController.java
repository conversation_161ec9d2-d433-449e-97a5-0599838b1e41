package com.deeppaas.license.single.web;

import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.license.single.ClientMonitor;
import com.deeppaas.license.single.SingleLicenseHelper;
import com.deeppaas.license.single.model.SingleLicense;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
@RequestAuth(auth = false)
@Controller
@RequestMapping("/license")
public class LicenseController {

    @RequestMapping
    public String index(Model model){
        model.addAttribute("clientSource", ClientMonitor.getClientSource());
        SingleLicense licenseContext = SingleLicenseHelper.getLocalLicense();
        if(licenseContext==null){
            model.addAttribute("msg", "授权文件不存在或异常");
        }else {
            long expireTime = DateHelper.str2date(licenseContext.getExpiration(), DateHelper.DATE_PATTERN).getTime();
            if(expireTime<System.currentTimeMillis()){
                model.addAttribute("msg", "授权已超期，授权截止日期为"+licenseContext.getExpiration());
            }else {
                model.addAttribute("msg", "已完成授权，授权截止日期为"+licenseContext.getExpiration());
            }
        }
        return "license";
    }

    @PostMapping("/upload")
    public String upload(@RequestParam("file") MultipartFile file, Model model){
        try {
            byte[] licenseData = FileCopyUtils.copyToByteArray((file.getInputStream()));
            SingleLicense context = SingleLicenseHelper.getLicense(licenseData);
            if(context==null || !SingleLicenseHelper.check(context)){
                model.addAttribute("msg", "上传授权文件异常");
                return "license";
            }
            SingleLicenseHelper.updateLicense(licenseData);
            model.addAttribute("msg", "授权文件更新成功");
            return "redirect:/license";
        } catch (IOException e) {
            model.addAttribute("msg", "上传异常："+e.getMessage());
            return "license";
        }
    }

}
