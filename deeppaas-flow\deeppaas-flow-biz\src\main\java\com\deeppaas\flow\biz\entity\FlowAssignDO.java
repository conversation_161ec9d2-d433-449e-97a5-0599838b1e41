package com.deeppaas.flow.biz.entity;

import java.time.LocalDateTime;
import java.util.List;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * @ClassName: FlowAssign
 * @Description: 流程流转环节处理人指派结果记录实体
 * <AUTHOR>
 */
@Data
@Table("flow_assign")
public class FlowAssignDO implements BaseModel {
	private static final long serialVersionUID = 1872267529290653608L;
	@Id
	@Column("id")
	private Long id;
	/** 流程实例ID */
	@Column("instance_id")
	private Long instanceId;
	/** 流程环节编号 */
	@Column("node_code")
	private String nodeCode;
	/** 指定处理用户 */
	@Column("actors")
	private List<String> actors;
	/** 优先级 */
	@Column("priority")
	private Integer priority;
	/** 指派时间 */
	@Column("assign_time")
	private LocalDateTime assignTime;
	/** 限定处理时间 */
	@Column("limit_time")
	private LocalDateTime limitedTime;
	/** 状态是已经使用了 */
	@Column("used")
	private Integer used;

}
