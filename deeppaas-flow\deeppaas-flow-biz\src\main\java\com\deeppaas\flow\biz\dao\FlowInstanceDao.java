package com.deeppaas.flow.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

public interface FlowInstanceDao extends BaseJdbcDao<FlowInstanceDO, Long> {

	FlowInstanceDO findByOrderId(String orderId);

    FlowInstanceDO findByDefineCodeAndOrderIdAndState(String flowDefineCode, String orderId, int state);

    FlowInstanceDO findByDefineCodeAndOrderId(String defineCode, String orderId);

    @Modifying
    @Query("UPDATE flow_instance set priority=:priority where id=:id")
    void updatePriority(@Param("id") Long id, @Param("priority") Integer priority);
}
