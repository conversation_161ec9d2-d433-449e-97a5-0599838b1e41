package com.deeppaas.quartz.convert;

import com.deeppaas.quartz.entity.QuartzJobDO;
import com.deeppaas.quartz.enums.JobType;
import com.deeppaas.quartz.model.QuartzJobParam;

import java.time.LocalDateTime;

public class QuartzJobConvert {

    public static QuartzJobDO paramToEntity(QuartzJobParam param) {
        QuartzJobDO jobDO = new QuartzJobDO();
        jobDO.setAppCode(param.getAppCode());
        jobDO.setJobName(param.getJobName());
        jobDO.setJobType(param.getJobType());

        JobType jobType = JobType.valueOf(param.getJobType());
        jobDO.setJobClass(jobType.getJobClass());

        jobDO.setJobContent(param.getJobContent());
        jobDO.setUsed(param.getUsed());
        jobDO.setCron(param.getCron());
        jobDO.setCreateTime(LocalDateTime.now());
        return jobDO;
    }

    public static void paramToEntity(QuartzJobDO jobDO, QuartzJobParam param) {
        jobDO.setJobName(param.getJobName());
        jobDO.setJobType(param.getJobType());
        JobType jobType = JobType.valueOf(param.getJobType());
        jobDO.setJobClass(jobType.getJobClass());
        jobDO.setJobContent(param.getJobContent());
        jobDO.setUsed(param.getUsed());
        jobDO.setCron(param.getCron());
        jobDO.setUpdateTime(LocalDateTime.now());
    }
}
