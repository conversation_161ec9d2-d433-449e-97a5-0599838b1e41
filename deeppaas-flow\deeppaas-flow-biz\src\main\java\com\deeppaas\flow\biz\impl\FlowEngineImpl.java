package com.deeppaas.flow.biz.impl;

import com.deeppaas.common.helper.AssertHelper;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.datastore.api.client.DataStoreDataClient;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.flow.biz.*;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.element.StartNode;
import com.deeppaas.flow.biz.element.TaskNode;
import com.deeppaas.flow.biz.entity.*;
import com.deeppaas.flow.biz.enums.*;
import com.deeppaas.flow.biz.model.*;
import com.deeppaas.flow.biz.service.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class FlowEngineImpl implements FlowEngine {
    private final FlowDefineService flowDefineService;
    private final FlowInstanceService flowInstanceService;
    private final FlowTokenService flowTokenService;
    private final FlowTaskService flowTaskService;
    private final FlowAssignService flowAssignService;
    private final FlowRecordService flowRecordService;
    private final FlowEventService flowEventService;
    private final DataStoreDataClient dataStoreDataClient;

    public FlowEngineImpl(FlowDefineService flowDefineService,
                          FlowInstanceService flowInstanceService,
                          FlowTokenService flowTokenService,
                          FlowTaskService flowTaskService,
                          FlowAssignService flowAssignService,
                          FlowRecordService flowRecordService,
                          FlowEventService flowEventService,
                          DataStoreDataClient dataStoreDataClient) {
        this.flowDefineService = flowDefineService;
        this.flowInstanceService = flowInstanceService;
        this.flowTokenService = flowTokenService;
        this.flowTaskService = flowTaskService;
        this.flowAssignService = flowAssignService;
        this.flowRecordService = flowRecordService;
        this.flowEventService = flowEventService;
        this.dataStoreDataClient = dataStoreDataClient;
    }

    @Override
    public FlowDefineService define() {
        return flowDefineService;
    }

    @Override
    public FlowInstanceService instance() {
        return flowInstanceService;
    }

    public FlowTokenService token() {
        return flowTokenService;
    }

    @Override
    public FlowTaskService task() {
        return flowTaskService;
    }

    @Override
    public FlowAssignService assign() {
        return flowAssignService;
    }

    @Override
    public FlowEventService event() {
        return flowEventService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlowInstanceDO createInstance(String defineCode, String startNodeCode, String orderId, String userId) {
        FlowInstanceDO instance = flowInstanceService.getInstance(defineCode,orderId);
        FlowDefineModel defineModel = flowDefineService.getModelByCode(defineCode);
        StartNode startNode = defineModel.getStartNode(startNodeCode);
        AssertHelper.notNull(startNode, "流程定义未发现开始节点");
        if(instance == null){
            instance = new FlowInstanceDO();
        }else {
            if (FlowInstanceState.OPEN.intValueEquals(instance.getState())) {
                throw new FlowException("工单流转中，不能重复提交");
            }
            // 如果未变更流程定义且已经是开始环节的流程实例，直接返回
            if (Objects.equals(instance.getVersion(), defineModel.getVersion())) {
                return instance;
            }
            // 已经发布了新版本流程定义，清理掉原流程实例所有信息
            flowTaskService.deleteByInstance(instance.getId());
            // 清理令牌处理
            flowTokenService.deleteByInstance(instance.getId());
        }
        instance.setCreator(userId);
        instance.setDefineCode(defineModel.getCode());
        instance.setVersion(defineModel.getVersion());
        instance.setStartNode(startNodeCode);
        instance.setCreateTime(LocalDateTime.now());
        instance.setOrderId(orderId);
        instance.setState(FlowInstanceState.READY.getIntValue());
        instance = flowInstanceService.save(instance);

        FlowAssignDO assign = flowAssignService.findByInstanceIdAndNodeCode(instance.getId(), startNode.getCode());
        if(assign==null){
            assign = new FlowAssignDO();
            assign.setInstanceId(instance.getId());
            assign.setNodeCode(startNode.getCode());
            assign.setActors(List.of(userId));
            assign.setPriority(Priority.L1.getValue());
            assign.setUsed(0);
            flowAssignService.init(assign);
        }

        return instance;
    }

    @Override
    public ExecuteResult mockStartInstance(FlowInstanceDO instance, String userId) {
        FlowDefineModel define = flowDefineService.getModelByCode(instance.getDefineCode(), instance.getVersion());
        StartNode startNode = define.getStartNode(instance.getStartNode());
        FlowTokenDO flowTokenDO = new FlowTokenDO();
        flowTokenDO.setInstanceId(instance.getId());
        flowTokenDO.setNodeCode(startNode.getCode());
        flowTokenDO.setOperator(userId);
        flowTokenDO.setUsed(BoolHelper.INT_TRUE);

        ExecuteItem executeItem = new ExecuteItem(this, define, instance, flowTokenDO, startNode, FlowAction.execute, userId, "");
        List<AssignInfo> assignInfoList = startNode.mockExecute(executeItem.mock());
        return ExecuteResult.assignInstance(assignInfoList, instance.getOrderId(), instance.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult startInstance(FlowInstanceDO instance, String userId) {
        FlowDefineModel define = flowDefineService.getModelByCode(instance.getDefineCode(), instance.getVersion());
        StartNode startNode = define.getStartNode(instance.getStartNode());
        FlowTokenDO token = new FlowTokenDO();
        token.setInstanceId(instance.getId());
        token.setNodeCode(startNode.getCode());
        token.setOperator(userId);
        token.setUsed(BoolHelper.INT_TRUE);
        token = flowTokenService.save(token);

        ExecuteItem executeItem = new ExecuteItem(this, define, instance, token, startNode, FlowAction.execute, userId, "");
        //将执行对象交给该任务对应的节点模型执行
        List<AssignInfo> assignInfoList = startNode.mockExecute(executeItem.mock());
        if (!CollectionUtils.isEmpty(assignInfoList)) {
            boolean unAssigned = assignInfoList.stream().anyMatch(assignInfo -> !assignInfo.isAssigned());
            if(unAssigned){
                return ExecuteResult.assignInstance(assignInfoList, instance.getOrderId(), instance.getId());
            }
        }
        instance.setStartTime(LocalDateTime.now());
        instance.setState(FlowInstanceState.OPEN.getIntValue());
        flowInstanceService.save(instance);
        //将执行对象交给该任务对应的节点模型执行
        flowRecordService.saveRecord(instance.getId(), startNode.getName(), FlowAction.submit, userId, "");
        startNode.execute(executeItem);
        return ExecuteResult.successInstance(instance.getOrderId(), instance.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult forwardExecuteCheck(FlowTaskDO task, String userId) {
        ExecuteItem execution = this.taskExecute(task, userId, FlowAction.execute, "");
        // 2.检查是否是该流程目前最后一个任务，如果是先需要返回指派信息
        List<AssignInfo> assignInfoList = taskForwardAssign(execution, task);
        if (CollectionUtils.isEmpty(assignInfoList)) {
            return ExecuteResult.checkInstance(execution.getInstance().getOrderId(), execution.getInstance().getId());
        } else {
            return ExecuteResult.assignInstance(assignInfoList, execution.getInstance().getOrderId(), execution.getInstance().getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult forwardExecute(FlowTaskDO task, String userId, String comment) {
        // 1.创建执行器
        ExecuteItem execution = this.taskExecute(task, userId, FlowAction.execute, comment);
        // 2.检查是否是该流程目前最后一个任务，如果是先需要返回指派信息
        List<AssignInfo> assignInfoList = taskForwardAssign(execution, task);
        if (!CollectionUtils.isEmpty(assignInfoList)) {
            boolean unAssigned = assignInfoList.stream().anyMatch(assignInfo -> !assignInfo.isAssigned());
            if(unAssigned){
                return ExecuteResult.assignInstance(assignInfoList, execution.getInstance().getOrderId(), execution.getInstance().getId());
            }
        }
        // 3.关闭任务
        // 更新流程实例处理人
        task = flowTaskService.close(task, userId, FlowAction.execute, comment);

        execution.setTask(task);
        flowEventService.fireNodeEvent(FlowEvent.NODE_TASK_CLOSED, execution);
        flowRecordService.saveRecord(execution.getInstance().getId(), execution.getNode().getName(), FlowAction.execute, userId, comment);

        // 4.流转流程
        execution.getDefine().getNode(task.getNodeCode()).execute(execution);
        flowTokenService.updateOperator(task.getTokenId(), userId, task.getId());
        flowInstanceService.reloadInstanceUsers(task.getInstanceId());
        return ExecuteResult.successInstance(execution.getInstance().getOrderId(), execution.getInstance().getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult closeInstance(FlowInstanceDO instance, String userId, FlowAction action, String comment) {
        FlowDefineModel define = flowDefineService.getModelByCode(instance.getDefineCode(), instance.getVersion());
        ExecuteItem execution = new ExecuteItem(this, define, instance, null,null, action, userId, "");
        flowEventService.fireInsEvent(FlowEvent.INSTANCE_STOP_BEFORE, execution);
        List<FlowTaskDO> tasks = flowTaskService.findUnclosedTaskByInstanceId(instance.getId());
        for (FlowTaskDO task : tasks) {
            flowTaskService.close(task, userId, action, comment);
            execution.setTask(task);
            //flowEventService.fireNodeEvent(FlowEvent.NODE_TASK_CLOSED, execution);
        }
        flowInstanceService.close(instance, FlowInstanceState.closeState(action));
        flowRecordService.saveRecord(execution.getInstance().getId(), "", action, userId, comment);
        flowEventService.fireInsEvent(FlowEvent.INSTANCE_STOP_AFTER, execution);
        flowInstanceService.reloadInstanceUsers(instance.getId());
        return ExecuteResult.successInstance(execution.getInstance().getOrderId(), execution.getInstance().getId());
    }

    @Override
    public List<PastNodeInfo> getBackNodes(FlowTaskDO task, String userId) {
        // 1.创建执行器
        ExecuteItem execution = this.taskExecute(task, userId, FlowAction.back, null);
        FlowNode curNode = execution.getNode();
        if(curNode instanceof TaskNode taskNode){
            List<FlowNode> backNodes = new ArrayList<>();
            switch (taskNode.getBackTarget()){
                case START -> {
                    backNodes.add(execution.getDefine().getStartNode(execution.getInstance().getStartNode()));
                    break;
                }
                case SOURCE -> {
                    FlowPositionDO prevPosition = flowInstanceService.findPrevPosition(execution.getInstance(), taskNode.getCode());
                    FlowNode backNode = execution.getDefine().getNode(prevPosition.getNodeCode());
                    while (!(backNode instanceof TaskNode || backNode instanceof StartNode)) {
                        prevPosition = flowInstanceService.findPrevPosition(execution.getInstance(), backNode.getCode());
                        backNode = execution.getDefine().getNode(prevPosition.getNodeCode());
                    }
                    backNodes.add(backNode);
                    break;
                }
                case SELECT -> {
                    List<FlowPositionDO> positions = flowInstanceService.findAllPosition(execution.getInstance().getId());
                    for (FlowPositionDO position : positions) {
                        FlowNode nodeModel = execution.getDefine().getNode(position.getNodeCode());
                        if (nodeModel instanceof TaskNode || nodeModel instanceof StartNode) {
                            // 本环节排除
                            if (!Objects.equals(nodeModel.getCode(), task.getNodeCode())) {
                                backNodes.add(nodeModel);
                            }
                        }
                    }
                    break;
                }
            }
            List<PastNodeInfo> pastNodeInfos = new ArrayList<>();
            backNodes.forEach(flowNode -> {
                PastNodeInfo pastNodeInfo = new PastNodeInfo();
                pastNodeInfo.setCode(flowNode.getCode());
                pastNodeInfo.setName(flowNode.getName());
                pastNodeInfo.setOperators(new ArrayList<>());

                if (flowNode instanceof StartNode) {
                    pastNodeInfo.setOperators(List.of(execution.getInstance().getCreator()));
                    pastNodeInfos.add(pastNodeInfo);
                } else {
                    List<String> auditors = getNodeAuditors(task.getInstanceId(), flowNode.getCode());
                    // 排除自动跳过的环节
                    if (!CollectionUtils.isEmpty(auditors)) {
                        pastNodeInfo.setOperators(auditors);
                        pastNodeInfos.add(pastNodeInfo);
                    }
                }
            });
            return pastNodeInfos;
        }else {
            throw new FlowException("非人工任务环节不能驳回");
        }
    }

    /**
     * @param instanceId
     * @param nodeCode
     * @return java.util.List<com.lh.oa.org.entity.User>
     * @Description: 获取环节处理人
     * <AUTHOR>
     */
    private List<String> getNodeAuditors(Long instanceId, String nodeCode) {
        List<FlowTaskDO> oldTasks = flowTaskService.findByInstanceIdAndNodeCode(instanceId, nodeCode);
        FlowAssignDO flowAssign = flowAssignService.findByInstanceIdAndNodeCode(instanceId, nodeCode);
        List<String> oldAssignUserIds = new ArrayList<>();
        if (flowAssign != null) {
            oldAssignUserIds.addAll(flowAssign.getActors());
        }
        List<String> backTaskUsers = new ArrayList<>();
        for (FlowTaskDO oldTask : oldTasks) {
            String operator = oldTask.getOperator();
            if (operator != null) {
                if (!backTaskUsers.contains(operator) && oldAssignUserIds.contains(operator)) {
                    backTaskUsers.add(operator);
                }
            }
        }
        return backTaskUsers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult backwardExecute(FlowTaskDO task, String backNodeCode, String userId, String comment) {
        // 1.创建执行器
        ExecuteItem execution = this.taskExecute(task, userId, FlowAction.back, comment);
        FlowNode backNode = execution.getDefine().getNode(backNodeCode);
        if (backNode == null) {
            throw new FlowException("没有找到指定驳回环节");
        }
        // 2.指定跳转目标节点
        execution.setJumpNode(backNode);
        if (backNode instanceof StartNode) {
            flowAssignService.assign(task.getInstanceId(), backNodeCode,
                    List.of(execution.getInstance().getCreator()), 0, null);
        } else {
            // 3.重构节点指派信息
            List<FlowTaskDO> oldTasks = flowTaskService.findByInstanceIdAndNodeCode(task.getInstanceId(), backNodeCode);
            FlowAssignDO flowAssign = flowAssignService.findByInstanceIdAndNodeCode(task.getInstanceId(), backNodeCode);
            List<String> oldAssignUserIds = new ArrayList<>();
            if (flowAssign != null) {
                oldAssignUserIds.addAll(flowAssign.getActors());
            }
            List<String> backTaskUserIds = new ArrayList<>();
            for (FlowTaskDO oldTask : oldTasks) {
                if (oldTask.getOperator() != null) {
                    String auditUser = oldTask.getOperator();
                    if (!backTaskUserIds.contains(auditUser) && oldAssignUserIds.contains(auditUser)) {
                        backTaskUserIds.add(auditUser);
                    }
                }
            }
            flowAssignService.assign(task.getInstanceId(), backNodeCode, backTaskUserIds, flowAssign.getPriority(), flowAssign.getLimitedTime());
        }

        // 4.关闭任务
        // 更新流程实例处理人
        flowTaskService.close(task, userId, FlowAction.back, comment);
        flowRecordService.saveRecord(execution.getInstance().getId(), execution.getNode().getName(), FlowAction.back, userId, comment);
        execution.getDefine().getNode(task.getNodeCode()).execute(execution);
        flowTokenService.updateOperator(task.getTokenId(), userId, task.getId());
        flowInstanceService.reloadInstanceUsers(task.getInstanceId());
        return ExecuteResult.successInstance(execution.getInstance().getOrderId(), execution.getInstance().getId());
    }

    @Override
    public List<PastNodeInfo> taskTakeBackTarget(Long instanceId, String userId) {
        List<PastNodeInfo> takeBackTargetNodes = new ArrayList<>();
        List<FlowTokenDO> flowTokens = flowTokenService.findByInstanceId(instanceId);
        List<FlowTokenDO> operatorTokens = flowTokens.stream().filter(token -> userId.equals(token.getOperator())).toList();
        if(operatorTokens.isEmpty()) {
            return null;
        }
        FlowInstanceDO flowInstance = flowInstanceService.getInstance(instanceId);
        /** 流程实例结束不允许撤回 */
        if (!FlowInstanceState.OPEN.intValueEquals(flowInstance.getState())) {
            return null;
        }
        FlowDefineModel define = flowDefineService.getModelByCode(flowInstance.getDefineCode(), flowInstance.getVersion());
        for(FlowTokenDO operatorToken : operatorTokens){
            boolean noChildOperated = true;
            for(FlowTokenDO otherToken : flowTokens){
                if(operatorToken.getId().equals(otherToken.getParentId()) && StringUtils.hasText(otherToken.getOperator())){
                    noChildOperated = false;
                }
            }
            if(noChildOperated){
                FlowNode flowNode = define.getNode(operatorToken.getNodeCode());
                if(flowNode instanceof TaskNode taskNode){
                    // 当前任务为会签不能撤回
                    if(TaskType.ANY!=taskNode.getTaskType()){
                        continue;
                    }
                }

                if (operatorToken.getTaskId() == null) {
                    StartNode startNode = define.getStartNode(operatorToken.getNodeCode());
                    if (startNode!=null) {
                        PastNodeInfo pastNodeInfo = new PastNodeInfo();
                        pastNodeInfo.setCode(startNode.getCode());
                        pastNodeInfo.setName(startNode.getName());
                        pastNodeInfo.setTokenId(operatorToken.getId());
                        pastNodeInfo.setOperators(List.of(userId));
                        takeBackTargetNodes.add(pastNodeInfo);
                    }
                } else {
                    FlowTaskDO flowTask = flowTaskService.getTask(operatorToken.getTaskId());
                    if (flowTask != null) {
                        PastNodeInfo pastNodeInfo = new PastNodeInfo();
                        pastNodeInfo.setCode(define.getNode(flowTask.getNodeCode()).getCode());
                        pastNodeInfo.setName(define.getNode(flowTask.getNodeCode()).getName());
                        pastNodeInfo.setTokenId(operatorToken.getId());
                        pastNodeInfo.setOperators(List.of(userId));
                        takeBackTargetNodes.add(pastNodeInfo);
                    }
                }
            }
        }
        return takeBackTargetNodes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult taskTakeBackExecute(Long instanceId, String targetNodeCode, String userCode, String comment) {

        List<PastNodeInfo> backTargetNodes = this.taskTakeBackTarget(instanceId, userCode);
        PastNodeInfo targetNode = null;
        for(PastNodeInfo backNode : backTargetNodes){
            if(backNode.getCode().equals(targetNodeCode)){
                targetNode = backNode;
            }
        }
        if (targetNode == null) {
            throw new FlowException("流程已流转或撤回环节与多人审批有关，不能再撤回了");
        }
        FlowInstanceDO flowInstance = flowInstanceService.getInstance(instanceId);
        FlowDefineModel define = flowDefineService.getModelByCode(flowInstance.getDefineCode(), flowInstance.getVersion());
        FlowNode flowNode = define.getNode(targetNodeCode);
        FlowTokenDO token = flowTokenService.getTokenById(targetNode.getTokenId());

        ExecuteItem execution = new ExecuteItem(this, define, flowInstance, token, flowNode, FlowAction.takeBack, userCode, "");

        // 关掉令牌以及子令牌打开任务并关闭
        List<FlowTokenDO> childTokens = flowTokenService.findAllChildToken(token.getId());
        List<Long> childrenTokenIds = childTokens.stream().map(FlowTokenDO::getId).toList();
        List<FlowTaskDO> openTasks = flowTaskService.findUnclosedTaskByTokenIds(childrenTokenIds);
        openTasks.forEach(openTask -> flowTaskService.close(openTask, userCode, FlowAction.takeBack, comment));
        // 如果撤回令牌有子令牌，删除子令牌
        flowTokenService.deleteTokens(childTokens);

        FlowNode backTargetNode = define.getNode(targetNodeCode);
        // 对撤回环节进行判断
        if (backTargetNode instanceof StartNode startNode) {
            flowAssignService.assign(instanceId, startNode.getCode(),
                    List.of(execution.getInstance().getCreator()),
                    0, null);
        } else {
            // 3.重构节点指派信息
            FlowAssignDO flowAssign = flowAssignService.findByInstanceIdAndNodeCode(instanceId, backTargetNode.getCode());
            flowAssignService.assign(instanceId, backTargetNode.getCode(),List.of(userCode),
                    flowAssign.getPriority(), flowAssign.getLimitedTime());
        }
        // 记录操作
        flowRecordService.saveRecord(instanceId, backTargetNode.getName(), FlowAction.takeBack, userCode, "");
        // 重新进入撤回环节
        backTargetNode.enter(execution);
        flowInstanceService.reloadInstanceUsers(instanceId);
        return ExecuteResult.successInstance(flowInstance.getOrderId(), flowInstance.getId());
    }

    @Override
    public DataStoreData getOrderData(String entityCode, String orderId) {
        return dataStoreDataClient.getData(entityCode, orderId);
    }

    private List<AssignInfo> taskForwardAssign(ExecuteItem executeItem, FlowTaskDO task) {
        List<FlowTaskDO> tasks = flowTaskService.findUnclosedTaskByInstanceId(executeItem.getInstance().getId());
        if (tasks.size() == 1 && tasks.get(0).equals(task)) {
            FlowNode flowNode = executeItem.getDefine().getNode(task.getNodeCode());
            //将执行对象交给该任务对应的节点模型执行
            return flowNode.mockExecute(executeItem.mock());
        }
        return null;
    }

    private ExecuteItem taskExecute(FlowTaskDO task, String userId, FlowAction action, String comment) {
        AssertHelper.isTrue(FlowConst.TASK_STATE_OPEN == task.getState(), "任务[id=" + task.getId() + "]已关闭，请刷新重新查询申请状态");
        AssertHelper.notNull(task, "任务[id=" + task.getId() + "]无效，请刷新重新查询申请状态");
        FlowInstanceDO instance = flowInstanceService.getInstance(task.getInstanceId());
        AssertHelper.notNull(instance, "指定的流程实例[id=" + task.getNodeCode() + "]已完成或不存在");
        FlowDefineModel define = flowDefineService.getModelByCode(instance.getDefineCode(), instance.getVersion());
        FlowNode flowNode = define.getNode(task.getNodeCode());
        if(flowNode instanceof TaskNode taskNode){
            if(FlowAction.back.equals(action) && BackTarget.UNABLE==taskNode.getBackTarget()){
                throw new FlowException("该环节不允许驳回");
            }
        }else {
            throw new FlowException("任务节点异常");
        }
        FlowTokenDO token = flowTokenService.getTokenById(task.getTokenId());
        return new ExecuteItem(this, define, instance, token, flowNode, action, userId, comment);
    }
}
