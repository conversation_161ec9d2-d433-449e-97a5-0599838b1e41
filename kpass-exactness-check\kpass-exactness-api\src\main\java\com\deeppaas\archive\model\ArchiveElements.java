package com.deeppaas.archive.model;

import lombok.Data;
import java.util.HashMap;
import java.util.Map;

/**
 * 档案要素模型
 */
@Data
public class ArchiveElements {
    
    private String fileName;           // 文件名
    private String title;              // 题名
    private String responsibleParty;   // 责任者
    private String documentNumber;     // 文号
    private String issueDate;          // 成文日期
    
    private Map<String, Double> confidenceScores; // 置信度分数
    private String reasoningProcess;   // 推理过程
    private String extractionMethod;   // 提取方法
    
    public ArchiveElements() {
        this.confidenceScores = new HashMap<>();
    }
    
    /**
     * 获取指定要素的置信度
     */
    public Double getConfidenceScore(String elementName) {
        return confidenceScores.getOrDefault(elementName, 0.0);
    }
    
    /**
     * 更新指定要素的置信度
     */
    public void updateConfidenceScore(String elementName, Double score) {
        confidenceScores.put(elementName, score);
    }
    
    /**
     * 检查是否为空要素
     */
    public boolean isEmpty() {
        return isEmpty(title) && isEmpty(responsibleParty) && 
               isEmpty(documentNumber) && isEmpty(issueDate);
    }
    
    /**
     * 获取非空要素数量
     */
    public int getNonEmptyElementCount() {
        int count = 0;
        if (!isEmpty(title)) count++;
        if (!isEmpty(responsibleParty)) count++;
        if (!isEmpty(documentNumber)) count++;
        if (!isEmpty(issueDate)) count++;
        return count;
    }
    
    /**
     * 获取平均置信度
     */
    public double getAverageConfidence() {
        if (confidenceScores.isEmpty()) {
            return 0.0;
        }
        
        return confidenceScores.values().stream()
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);
    }
    
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return String.format("ArchiveElements{fileName='%s', title='%s', responsibleParty='%s', " +
                "documentNumber='%s', issueDate='%s', avgConfidence=%.2f}", 
                fileName, title, responsibleParty, documentNumber, issueDate, getAverageConfidence());
    }
}
