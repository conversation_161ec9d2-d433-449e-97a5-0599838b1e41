import{r as l,C as j,b as I,e as d,f as h,_ as q,ap as G,D as J,E as $}from"./index.689bcdcb.js";import{o as K}from"./index.71d34973.js";import{P as L,c as Q}from"./index.1f7b2316.js";import{W as U}from"./button.34f11f85.js";var X=globalThis&&globalThis.__rest||function(o,t){var a={};for(var e in o)Object.prototype.hasOwnProperty.call(o,e)&&t.indexOf(e)<0&&(a[e]=o[e]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,e=Object.getOwnPropertySymbols(o);r<e.length;r++)t.indexOf(e[r])<0&&Object.prototype.propertyIsEnumerable.call(o,e[r])&&(a[e[r]]=o[e[r]]);return a},Y=function(t){var a,e=t.prefixCls,r=t.className,u=t.checked,p=t.onChange,s=t.onClick,m=X(t,["prefixCls","className","checked","onChange","onClick"]),n=l.exports.useContext(j),v=n.getPrefixCls,C=function(g){p==null||p(!u),s==null||s(g)},f=v("tag",e),x=I(f,(a={},d(a,"".concat(f,"-checkable"),!0),d(a,"".concat(f,"-checkable-checked"),u),a),r);return l.exports.createElement("span",h({},m,{className:x,onClick:C}))},Z=Y,ee=globalThis&&globalThis.__rest||function(o,t){var a={};for(var e in o)Object.prototype.hasOwnProperty.call(o,e)&&t.indexOf(e)<0&&(a[e]=o[e]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,e=Object.getOwnPropertySymbols(o);r<e.length;r++)t.indexOf(e[r])<0&&Object.prototype.propertyIsEnumerable.call(o,e[r])&&(a[e[r]]=o[e[r]]);return a},te=new RegExp("^(".concat(L.join("|"),")(-inverse)?$")),oe=new RegExp("^(".concat(Q.join("|"),")$")),re=function(t,a){var e,r=t.prefixCls,u=t.className,p=t.style,s=t.children,m=t.icon,n=t.color,v=t.onClose,C=t.closeIcon,f=t.closable,x=f===void 0?!1:f,c=ee(t,["prefixCls","className","style","children","icon","color","onClose","closeIcon","closable"]),g=l.exports.useContext(j),D=g.getPrefixCls,_=g.direction,z=l.exports.useState(!0),O=q(z,2),V=O[0],P=O[1];l.exports.useEffect(function(){"visible"in c&&P(c.visible)},[c.visible]);var k=function(){return n?te.test(n)||oe.test(n):!1},W=h({backgroundColor:n&&!k()?n:void 0},p),N=k(),i=D("tag",r),A=I(i,(e={},d(e,"".concat(i,"-").concat(n),N),d(e,"".concat(i,"-has-color"),n&&!N),d(e,"".concat(i,"-hidden"),!V),d(e,"".concat(i,"-rtl"),_==="rtl"),e),u),w=function(b){b.stopPropagation(),v==null||v(b),!b.defaultPrevented&&("visible"in c||P(!1))},H=function(){return x?C?l.exports.createElement("span",{className:"".concat(i,"-close-icon"),onClick:w},C):l.exports.createElement(G,{className:"".concat(i,"-close-icon"),onClick:w}):null},M="onClick"in c||s&&s.type==="a",B=K(c,["visible"]),T=m||null,F=T?l.exports.createElement(l.exports.Fragment,null,T,l.exports.createElement("span",null,s)):s,E=l.exports.createElement("span",h({},B,{ref:a,className:A,style:W}),F,H());return M?l.exports.createElement(U,null,E):E},y=l.exports.forwardRef(re);y.displayName="Tag";y.CheckableTag=Z;var fe=y,ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},le=ae,R=function(t,a){return l.exports.createElement(J,$($({},t),{},{ref:a,icon:le}))};R.displayName="DownloadOutlined";var de=l.exports.forwardRef(R);export{de as D,fe as T};
