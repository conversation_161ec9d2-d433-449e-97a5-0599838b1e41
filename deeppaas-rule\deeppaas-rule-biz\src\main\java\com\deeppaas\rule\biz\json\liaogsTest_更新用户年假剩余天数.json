[{"stepId": "step1", "type": "DATA_GET", "returned": false, "returnVar": true, "varName": "varApplyOrder", "varType": "DATA_ENTITY", "varEntity": "userAbsenceApply", "name": "获取员工请假申请单", "dataEntity": "ET_UserAbsenceApply", "permits": [], "defaultCondition": {"field": "id", "operator": "EQ", "dataBind": {"targetType": "CONST", "value": "c2dd9f1d9c334f75a37843400278a954", "dataType": "TEXT"}}, "next": "step2"}, {"stepId": "step2", "type": "DATA_UPDATE", "name": "更新用户年假剩余天数", "returned": true, "returnVar": false, "varName": "userDays", "varType": "NUMBER", "varEntity": "", "dataEntity": "ET_UserAnnualLeave", "condition": {"operator": "AND", "conditions": [{"field": "userCode", "operator": "EQ", "dataBind": {"targetType": "VARS", "value": "varApplyOrder.applyUserCode", "dataType": "TEXT"}}, {"field": "year", "operator": "EQ", "dataBind": {"targetType": "CONST", "value": 2022, "dataType": "NUMBER"}}]}, "updateFields": [{"field": "remainDays", "dataBind": {"targetType": "EXPR", "value": "effect.remainDays - vars.varApplyOrder.applyDays", "dataType": "VOID"}}]}]