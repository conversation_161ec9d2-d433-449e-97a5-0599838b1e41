package com.deeppaas.flow.biz.element;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.entity.FlowTokenDO;
import com.deeppaas.flow.biz.enums.FlowEvent;
import com.deeppaas.flow.biz.enums.NodeType;
import com.deeppaas.flow.biz.service.FlowTokenService;

import java.util.ArrayList;
import java.util.List;

/**
 * 合并环节
 * <AUTHOR>
 * @date 2021/12/27
 */
public class JoinNode extends FlowNode {
    public JoinNode(String code, String name, NodeType type, String remark, List<FlowEventRule> eventRules) {
        super(code, name, type, remark, eventRules);
    }

    @Override
    public List<String> check() {
        List<String> errors = new ArrayList<>();
        if(inLines.isEmpty()){
            errors.add("环节["+getCode()+"]没有进入连接线");
        }
        if(outLines.isEmpty()){
            errors.add("环节["+getCode()+"]没有流转连接线");
        }
        return errors;
    }


    @Override
    public void enter(ExecuteItem executeItem) {
        FlowTokenService tokenService = executeItem.getEngine().token();
        FlowTokenDO currentToken = executeItem.getToken();
        currentToken.setUsed(BoolHelper.INT_FALSE);
        executeItem.setToken(tokenService.passTokenAndRecord(currentToken, this, executeItem.getOperator()));
        executeItem.setNode(this);
        executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_ENTER, executeItem);
        this.execute(executeItem);
    }

    @Override
    public void execute(ExecuteItem executeItem) {
        Long parentTokenId = executeItem.getToken().getParentId();
        List<FlowTokenDO> childTokens = executeItem.getEngine().token().findChildToken(parentTokenId);
        boolean haveUsedTokens = childTokens.stream().anyMatch(token -> BoolHelper.intToBool(token.getUsed()));
        if(!haveUsedTokens){
            FlowTokenService tokenService = executeItem.getEngine().token();
            FlowTokenDO parentToken = tokenService.getTokenById(executeItem.getToken().getParentId());
            executeItem.setToken(tokenService.passTokenNoRecord(parentToken, this, null));
            // 删除子令牌
            tokenService.deleteTokens(childTokens);
            executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_EXIT, executeItem);
            runOutTransition(executeItem);
        }
    }

}
