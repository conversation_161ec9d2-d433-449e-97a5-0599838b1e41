package com.deeppaas.flow.biz.service;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.flow.biz.FlowConst;
import com.deeppaas.flow.biz.cache.FlowCacheManager;
import com.deeppaas.flow.biz.dao.FlowDefineDao;
import com.deeppaas.flow.biz.model.FlowDefineModel;
import com.deeppaas.flow.biz.model.param.FlowDefineParam;
import com.deeppaas.flow.biz.parser.FlowDefineParser;
import com.deeppaas.common.helper.AssertHelper;
import com.deeppaas.flow.biz.entity.FlowDefineDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

@Component
@Transactional(readOnly = true)
public class FlowDefineServiceImpl implements FlowDefineService {
    private final FlowDefineDao flowDefineDao;
    private final FlowCacheManager flowCacheManager;
    private final FlowPageService flowPageService;

    public FlowDefineServiceImpl(FlowDefineDao flowDefineDao, FlowCacheManager flowCacheManager, FlowPageService flowPageService) {
        this.flowDefineDao = flowDefineDao;
        this.flowCacheManager = flowCacheManager;
        this.flowPageService = flowPageService;
    }

    @Override
    public FlowDefineDO get(String defineId) {
        return flowDefineDao.getById(defineId);
    }


    @Override
    @Transactional
    public FlowDefineDO copy(String defineId) {
        FlowDefineDO sourceDefine = flowDefineDao.getById(defineId);
        if (sourceDefine == null) {
            throw RunException.dataNotFound("未找到流程定义");
        }
        FlowDefineDO basicDefine = flowDefineDao.findByCodeAndState(sourceDefine.getCode(), FlowConst.DEFINE_STATE_EDIT);
        basicDefine.setDefJson(sourceDefine.getDefJson());
        basicDefine.setAttachment(sourceDefine.getAttachment());
        basicDefine.setFlowMessage(sourceDefine.getFlowMessage());
        basicDefine.setRemark(sourceDefine.getRemark());
        basicDefine.setUpdateTime(LocalDateTime.now());

//        FlowDefineDO newDefine = new FlowDefineDO();
//        newDefine.setCode(basicDefine.getCode());
//        newDefine.setName(basicDefine.getName());
//        newDefine.setEntityCode(basicDefine.getEntityCode());
//        newDefine.setVersion(basicDefine.getVersion() + 1);
//        newDefine.setDefJson(basicDefine.getDefJson());
//        newDefine.setCreateTime(LocalDateTime.now());
//        newDefine.setUpdateTime(LocalDateTime.now());
//        newDefine.setState(FlowConst.DEFINE_STATE_EDIT);
//        newDefine.setAttachment(basicDefine.getAttachment());
//        newDefine.setFlowMessage(basicDefine.getFlowMessage());
//        newDefine.setRemark(basicDefine.getRemark());

        flowPageService.updateFlowPage(basicDefine);
        return flowDefineDao.insert(basicDefine);
    }


    @Override
    public List<String> check(FlowDefineDO define) {
        FlowDefineModel defineModel = FlowDefineParser.parse(define, define.getDefJson());
        return defineModel.check();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyRemark(String defineId, String remark) {
        FlowDefineDO flowDefine = flowDefineDao.getById(defineId);
        AssertHelper.notNull(flowDefine, "修改备注失败，未找到流程定义");
        // 更新操作
        flowDefine.setRemark(remark);
        flowDefine.setUpdateTime(LocalDateTime.now());
        flowDefineDao.save(flowDefine);
    }

    @Override
    public FlowDefineDO findByCodeAndState(String code, int state) {
        FlowDefineDO flowDefine = flowDefineDao.findByCodeAndState(code, state);
        return flowDefine;
    }

    @Override
    public List<FlowDefineDO> listByCode(String code) {
        return flowDefineDao.findByCode(code);
    }

    @Override
    public List<FlowDefineDO> listByEntity(String entity) {
        return flowDefineDao.findByEntityCode(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        FlowDefineDO defineDO = flowDefineDao.getById(id);
        if(FlowConst.DEFINE_STATE_EDIT!=defineDO.getState()){
            throw RunException.optReject("不能删除非草稿状态流程");
        }
        // TODO 删除校验
        flowPageService.deleteFlowPage(id);
        flowDefineDao.deleteById(id);
    }

    @Override
    @Transactional
    public FlowDefineDO save(FlowDefineParam param) {
        if (param.getId() == null) {
            // 草稿唯一性检查
            FlowDefineDO editDefine = flowDefineDao.findByEntityCodeAndCodeAndState(
                    param.getEntityCode(), param.getCode(), FlowConst.DEFINE_STATE_EDIT);
            if (editDefine == null) {
                editDefine = new FlowDefineDO();
                editDefine.setId(param.getCode()+"_1");
                editDefine.setCode(param.getCode());
                editDefine.setName(param.getName());
                editDefine.setVersion(1);
                editDefine.setEntityCode(param.getEntityCode());
                editDefine.setDefJson(param.getDefJson());
                editDefine.setState(FlowConst.DEFINE_STATE_EDIT);
                editDefine.setAttachment(param.getAttachment());
                editDefine.setFlowMessage(param.getFlowMessage());
                editDefine.setRemark(param.getRemark());
                editDefine.setCreateTime(LocalDateTime.now());
                editDefine.setUpdateTime(LocalDateTime.now());
                flowPageService.updateFlowPage(editDefine);
                return flowDefineDao.insert(editDefine);
            }else {
                throw RunException.optReject("流程已经存在");
            }
        }else {
            FlowDefineDO updateDefine = flowDefineDao.getById(param.getId());
            if(updateDefine==null){
                throw RunException.optReject("流程ID="+param.getId()+"不存在");
            }
            updateDefine.setName(param.getName());
            updateDefine.setDefJson(param.getDefJson());
            updateDefine.setState(FlowConst.DEFINE_STATE_EDIT);
            updateDefine.setAttachment(param.getAttachment());
            updateDefine.setFlowMessage(param.getFlowMessage());
            updateDefine.setRemark(param.getRemark());
            updateDefine.setUpdateTime(LocalDateTime.now());
            flowPageService.updateFlowPage(updateDefine);
            return flowDefineDao.save(updateDefine);
        }
    }

    @Override
    @Transactional
    public FlowDefineDO issue(FlowDefineParam param) {
        FlowDefineDO flowDefineDO = save(param);
        if (FlowConst.DEFINE_STATE_EDIT!=flowDefineDO.getState()) {
            throw RunException.optReject("只能发布草稿状态流程");
        }
        // 1.将原来的发布范本设置为过期
        FlowDefineDO oldIssueDefine = flowDefineDao.findByCodeAndState(param.getCode(), FlowConst.DEFINE_STATE_ISSUE);
        if (null != oldIssueDefine) {
            // 已经存在发布过的版本，则修改为过期
            oldIssueDefine.setState(FlowConst.DEFINE_STATE_OVERDUE);
            oldIssueDefine.setUpdateTime(LocalDateTime.now());
            flowDefineDao.save(oldIssueDefine);
        }
        flowDefineDO.setState(FlowConst.DEFINE_STATE_ISSUE);
        flowDefineDO = flowDefineDao.save(flowDefineDO);
        // 3.生成发布版本新数据
        FlowDefineDO newEditDefine = new FlowDefineDO();
        newEditDefine.setCode(flowDefineDO.getCode());
        newEditDefine.setName(flowDefineDO.getName());
        newEditDefine.setVersion(flowDefineDO.getVersion()+1);
        newEditDefine.setId(newEditDefine.getCode()+"_"+newEditDefine.getVersion());
        newEditDefine.setEntityCode(flowDefineDO.getEntityCode());
        newEditDefine.setDefJson(flowDefineDO.getDefJson());
        newEditDefine.setState(FlowConst.DEFINE_STATE_EDIT);
        newEditDefine.setAttachment(flowDefineDO.getAttachment());
        newEditDefine.setFlowMessage(flowDefineDO.getFlowMessage());
        newEditDefine.setRemark(flowDefineDO.getRemark());
        newEditDefine.setCreateTime(flowDefineDO.getCreateTime());
        newEditDefine.setUpdateTime(LocalDateTime.now());
        flowPageService.updateFlowPage(newEditDefine);
        flowDefineDao.insert(newEditDefine);
        return flowDefineDO;
    }

    @Override
    public List<FlowDefineDO> findByIds(Collection<String> flowDefineIds) {
        if(CollectionUtils.isEmpty(flowDefineIds)){
            return new ArrayList<>();
        }
        return Lists.newArrayList(flowDefineDao.findAllById(flowDefineIds).iterator());
    }

    public FlowDefineModel getModelById(String defineId) {
        FlowDefineDO flowDefineDO = flowDefineDao.getById(defineId);
        return loadModel(flowDefineDO);
    }

    public FlowDefineModel getModelByCode(String code){
        FlowDefineDO flowDefineDO = flowDefineDao.findByCodeAndState(code, FlowConst.DEFINE_STATE_ISSUE);
        return loadModel(flowDefineDO);
    }

    public FlowDefineModel getModelByCode(String code, Integer version){
        FlowDefineDO flowDefineDO = flowDefineDao.findByCodeAndVersion(code, version);
        return loadModel(flowDefineDO);
    }

    public FlowDefineModel reloadModel(FlowDefineDO flowDefineDO){
        if(flowDefineDO!=null){
            FlowDefineModel defineModel = FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
            Cache cache = flowCacheManager.getDefineCache();
            if(cache!=null){
                String cacheKey = flowDefineDO.getCode()+"-"+flowDefineDO.getVersion();
                defineModel = FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
                cache.put(cacheKey, defineModel);
            }
            return defineModel;
        }
        return null;
    }

    public FlowDefineModel loadModel(FlowDefineDO flowDefineDO) {
        if(flowDefineDO==null){
            return null;
        }
        String defineId = flowDefineDO.getCode()+"_"+flowDefineDO.getVersion();
        FlowDefineModel defineModel = null;
        Cache cache = flowCacheManager.getDefineCache();
        if(cache!=null){
            defineModel = cache.get(defineId, FlowDefineModel.class);
            if(defineModel==null){
                defineModel = FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
                cache.put(defineId, defineModel);
            }
            return defineModel;
        }
        return FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
    }

}
