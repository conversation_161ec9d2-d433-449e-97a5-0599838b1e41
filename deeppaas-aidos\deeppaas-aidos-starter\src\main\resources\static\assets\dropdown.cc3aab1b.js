import{r as n,F as ie,_ as te,E as Z,b as W,e as X,T as le,C as re,f as ne,a as se,d as q}from"./index.689bcdcb.js";import{a as ce,g as ue}from"./index.1f7b2316.js";import{K as oe,R as de}from"./RightOutlined.ed31d8e3.js";import{E as fe}from"./index.41297259.js";import{B as J}from"./button.34f11f85.js";import{t as ve,c as ee}from"./reactNode.50ea6579.js";var S={adjustX:1,adjustY:1},L=[0,0],ge={topLeft:{points:["bl","tl"],overflow:S,offset:[0,-4],targetOffset:L},topCenter:{points:["bc","tc"],overflow:S,offset:[0,-4],targetOffset:L},topRight:{points:["br","tr"],overflow:S,offset:[0,-4],targetOffset:L},bottomLeft:{points:["tl","bl"],overflow:S,offset:[0,4],targetOffset:L},bottomCenter:{points:["tc","bc"],overflow:S,offset:[0,4],targetOffset:L},bottomRight:{points:["tr","br"],overflow:S,offset:[0,4],targetOffset:L}},me=oe.ESC,pe=oe.TAB;function ye(t){var e=t.visible,s=t.setTriggerVisible,o=t.triggerRef,a=t.menuRef,f=t.menuClassName,P=t.onVisibleChange,C=function(){e&&o.current&&(o.current.triggerRef.current&&o.current.triggerRef.current.focus(),s(!1),typeof P=="function"&&P(!1))},b=function(u){switch(u.keyCode){case me:C();break;case pe:C();break}},m=function(){if(a.current){var u,v,p,O=!((u=a.current.className)===null||u===void 0)&&u.includes(f)?a.current:(v=(p=a.current).querySelector)===null||v===void 0?void 0:v.call(p,f);O&&O.focus()}};n.exports.useEffect(function(){return e?(setTimeout(function(){m(),window.addEventListener("keydown",b)},100),function(){window.removeEventListener("keydown",b)}):function(){return null}},[e]);var E=function(){e&&o.current&&o.current.triggerRef.current&&setTimeout(function(){var u,v,p;(u=o.current)===null||u===void 0||(v=u.triggerRef)===null||v===void 0||(p=v.current)===null||p===void 0||p.focus()},100)};return{returnFocus:E}}var Ce=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger"];function be(t,e){var s=t.arrow,o=s===void 0?!1:s,a=t.prefixCls,f=a===void 0?"rc-dropdown":a,P=t.transitionName,C=t.animation,b=t.align,m=t.placement,E=m===void 0?"bottomLeft":m,y=t.placements,u=y===void 0?ge:y,v=t.getPopupContainer,p=t.showAction,O=t.hideAction,h=t.overlayClassName,_=t.overlayStyle,$=t.visible,A=t.trigger,N=A===void 0?["hover"]:A,V=ie(t,Ce),B=n.exports.useState(),x=te(B,2),l=x[0],d=x[1],i="visible"in t?$:l,g=n.exports.useRef(null);n.exports.useImperativeHandle(e,function(){return g.current});var R=n.exports.useRef(null),M="".concat(f,"-menu"),T=ye({visible:i,setTriggerVisible:d,triggerRef:g,menuRef:R,menuClassName:M,onVisibleChange:t.onVisibleChange}),j=T.returnFocus,k=function(){var r=t.overlay,c;return typeof r=="function"?c=r():c=r,c},K=function(r){var c=t.onOverlayClick,I=k().props;d(!1),c&&c(r),I.onClick&&I.onClick(r),j()},z=function(r){var c=t.onVisibleChange;d(r),typeof c=="function"&&c(r)},D=function(){var r=k(),c={prefixCls:M,onClick:K,ref:le(r)?R:void 0};return typeof r.type=="string"&&delete c.prefixCls,n.exports.createElement(n.exports.Fragment,null,o&&n.exports.createElement("div",{className:"".concat(f,"-arrow")}),n.exports.cloneElement(r,c))},G=function(){var r=t.overlay;return typeof r=="function"?D:D()},Y=function(){var r=t.minOverlayWidthMatchTrigger,c=t.alignPoint;return"minOverlayWidthMatchTrigger"in t?r:!c},U=function(){var r=t.openClassName;return r!==void 0?r:"".concat(f,"-open")},H=function(){var r=t.children,c=r.props?r.props:{},I=W(c.className,U());return i&&r?n.exports.cloneElement(r,{className:I}):r},F=O;return!F&&N.indexOf("contextMenu")!==-1&&(F=["click"]),n.exports.createElement(ce,Z(Z({builtinPlacements:u},V),{},{prefixCls:f,ref:g,popupClassName:W(h,X({},"".concat(f,"-show-arrow"),o)),popupStyle:_,action:N,showAction:p,hideAction:F||[],popupPlacement:E,popupAlign:b,popupTransitionName:P,popupAnimation:C,popupVisible:i,stretch:Y()?"minWidth":"",popup:G(),onPopupVisibleChange:z,getPopupContainer:v}),H())}var he=n.exports.forwardRef(be),xe=globalThis&&globalThis.__rest||function(t,e){var s={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(s[o]=t[o]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(t);a<o.length;a++)e.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(t,o[a])&&(s[o[a]]=t[o[a]]);return s},we=J.Group,ae=function(e){var s=n.exports.useContext(re),o=s.getPopupContainer,a=s.getPrefixCls,f=s.direction,P=e.prefixCls,C=e.type,b=C===void 0?"default":C,m=e.disabled,E=e.loading,y=e.onClick,u=e.htmlType,v=e.children,p=e.className,O=e.overlay,h=e.trigger,_=e.align,$=e.visible,A=e.onVisibleChange,N=e.placement,V=e.getPopupContainer,B=e.href,x=e.icon,l=x===void 0?n.exports.createElement(fe,null):x,d=e.title,i=e.buttonsRender,g=i===void 0?function(r){return r}:i,R=e.mouseEnterDelay,M=e.mouseLeaveDelay,T=e.overlayClassName,j=e.overlayStyle,k=e.destroyPopupOnHide,K=xe(e,["prefixCls","type","disabled","loading","onClick","htmlType","children","className","overlay","trigger","align","visible","onVisibleChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide"]),z=a("dropdown-button",P),D={align:_,overlay:O,disabled:m,trigger:m?[]:h,onVisibleChange:A,getPopupContainer:V||o,mouseEnterDelay:R,mouseLeaveDelay:M,overlayClassName:T,overlayStyle:j,destroyPopupOnHide:k};"visible"in e&&(D.visible=$),"placement"in e?D.placement=N:D.placement=f==="rtl"?"bottomLeft":"bottomRight";var G=n.exports.createElement(J,{type:b,disabled:m,loading:E,onClick:y,htmlType:u,href:B,title:d},v),Y=n.exports.createElement(J,{type:b,icon:l}),U=g([G,Y]),H=te(U,2),F=H[0],w=H[1];return n.exports.createElement(we,ne({},K,{className:W(z,p)}),F,n.exports.createElement(Oe,D,w))};ae.__ANT_BUTTON=!0;var Pe=ae;ve("topLeft","topCenter","topRight","bottomLeft","bottomCenter","bottomRight","top","bottom");var Q=function(e){var s=n.exports.useContext(re),o=s.getPopupContainer,a=s.getPrefixCls,f=s.direction,P=function(){var l=a(),d=e.placement,i=d===void 0?"":d,g=e.transitionName;return g!==void 0?g:i.indexOf("top")>=0?"".concat(l,"-slide-down"):"".concat(l,"-slide-up")},C=function(l){var d=e.overlay,i;typeof d=="function"?i=d():i=d,i=n.exports.Children.only(typeof i=="string"?n.exports.createElement("span",null,i):i);var g=i.props;q(!g.mode||g.mode==="vertical","Dropdown",'mode="'.concat(g.mode,`" is not supported for Dropdown's Menu.`));var R=g.selectable,M=R===void 0?!1:R,T=g.expandIcon,j=typeof T!="undefined"&&n.exports.isValidElement(T)?T:n.exports.createElement("span",{className:"".concat(l,"-menu-submenu-arrow")},n.exports.createElement(de,{className:"".concat(l,"-menu-submenu-arrow-icon")})),k=typeof i.type=="string"?i:ee(i,{mode:"vertical",selectable:M,expandIcon:j});return k},b=function(){var l=e.placement;if(!l)return f==="rtl"?"bottomRight":"bottomLeft";if(l.includes("Center")){var d=l.slice(0,l.indexOf("Center"));return q(!l.includes("Center"),"Dropdown","You are using '".concat(l,"' placement in Dropdown, which is deprecated. Try to use '").concat(d,"' instead.")),d}return l},m=e.arrow,E=e.prefixCls,y=e.children,u=e.trigger,v=e.disabled,p=e.getPopupContainer,O=e.overlayClassName,h=a("dropdown",E),_=n.exports.Children.only(y),$=ee(_,{className:W("".concat(h,"-trigger"),X({},"".concat(h,"-rtl"),f==="rtl"),_.props.className),disabled:v}),A=W(O,X({},"".concat(h,"-rtl"),f==="rtl")),N=v?[]:u,V;N&&N.indexOf("contextMenu")!==-1&&(V=!0);var B=ue({arrowPointAtCenter:se(m)==="object"&&m.pointAtCenter,autoAdjustOverflow:!0});return n.exports.createElement(he,ne({alignPoint:V},e,{builtinPlacements:B,arrow:!!m,overlayClassName:A,prefixCls:h,getPopupContainer:p||o,transitionName:P(),trigger:N,overlay:function(){return C(h)},placement:b()}),$)};Q.Button=Pe;Q.defaultProps={mouseEnterDelay:.15,mouseLeaveDelay:.1};var Oe=Q;export{Oe as D};
