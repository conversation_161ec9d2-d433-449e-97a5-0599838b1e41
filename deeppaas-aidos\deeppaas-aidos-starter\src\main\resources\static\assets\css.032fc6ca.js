function o(){var n=document.documentElement.clientWidth,e=window.innerHeight||document.documentElement.clientHeight;return{width:n,height:e}}function i(n){var e=n.getBoundingClientRect(),t=document.documentElement;return{left:e.left+(window.pageXOffset||t.scrollLeft)-(t.clientLeft||document.body.clientLeft||0),top:e.top+(window.pageYOffset||t.scrollTop)-(t.clientTop||document.body.clientTop||0)}}export{i as a,o as g};
