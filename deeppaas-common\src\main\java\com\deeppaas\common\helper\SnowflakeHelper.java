package com.deeppaas.common.helper;

import java.util.Random;

/**
 * 随机雪花算法ID生成
 * <AUTHOR>
 * @date 2022/2/16
 */
public class SnowflakeHelper {
    private static Random random = new Random();
    //初始时间戳2020-01-01
    private static final long startTime = 1577808000000L;

    /** 随机数所占的位数 */
    private static final long randomBits = 10L;

    /** 支持的最大机随机数 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数) */
    private static final long maxRandomValue = -1L ^ (-1L << randomBits);

    /** 序列在id中占的位数 */
    private static final long sequenceBits = 12L;

    /** 随机数向左移12位 */
    private static final long randomShift = sequenceBits;

    /** 时间截向左移22位(5+5+12) */
    private static final long timestampLeftShift = sequenceBits + randomBits;

    /** 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095) */
    private static final long sequenceMask = -1L ^ (-1L << sequenceBits);

    /** 毫秒内序列(0~4095) */
    private static long sequence = 0L;

    /** 上次生成ID的时间截 */
    private static long lastTimestamp = -1L;



    /**
     * 获得下一个ID (该方法是线程安全的)
     * @return SnowflakeId
     */
    public static synchronized long nextId() {
        long timestamp = timeGen();

        //如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                    String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }

        //如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            //毫秒内序列溢出
            if (sequence == 0) {
                //阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        }
        //时间戳改变，毫秒内序列重置
        else {
            sequence = 0L;
        }

        //上次生成ID的时间截
        lastTimestamp = timestamp;
        long randomNumber = random.nextLong(maxRandomValue);
        //移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - startTime) << timestampLeftShift) //
                | (randomNumber << randomShift) //
                | sequence;
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     * @param lastTimestamp 上次生成ID的时间截
     * @return 当前时间戳
     */
    protected static long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     * @return 当前时间(毫秒)
     */
    private static long timeGen() {
        return System.currentTimeMillis();
    }

//    public static void main(String[] args){
//        System.out.println(nextId());
//        Set<Long> set = new HashSet();
//        int cf = 0;
//        for(int i=0;i<10000000; i++){
//            if(!set.add(nextId())){
//                cf++;
//            }
//        }
//        System.out.println("重复次数="+cf);
//    }
}
