package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据模型连接
 *
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@Table("datastore_entity_join")
@EqualsAndHashCode(callSuper = true)
public class DataStoreEntityJoinDO extends BaseUuidDO {
    /**
     * 没有父级为ROOT
     */
    public static final String PARENT_JOIN_CODE_NULL = "ROOT";
    /**
     * 所属数据模型编号
     **/
    @Column("entity_code")
    private String entityCode;
    /**
     * 本级联别编号
     **/
    @Column("code")
    private String code;

    /**
     * 级联表名ID
     **/
    @Column("table_id")
    private String tableId;

    /**
     * 级联表名
     **/
    @Column("table_name")
    private String tableName;

    /**
     * 所属上级级联
     **/
    @Column("parent_join_code")
    private String parentJoinCode;
}
