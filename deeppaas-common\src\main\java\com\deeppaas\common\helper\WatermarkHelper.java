package com.deeppaas.common.helper;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Element;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * 文件水印工具类
 *
 * <AUTHOR>
 * @date 2020-10-09
 */
public class WatermarkHelper {

    private static final String FONT_NAME = "STSong-Light";
    private static final String FONT_ENCODING = "UniGB-UCS2-H";

    /**
     * 给PDF文件添加文本水印-单行水印
     *
     * @param inputStream 源文件流
     * @param watermark   文本水印
     * @return 水印文件流
     */
    public static FileInputStream markPdfForSingle(InputStream inputStream, String watermark) {
        // 坐标旋转角度
        final float rotation = 45F;
        // 设置透明度为0.2
        final float fillOpacity = 0.2F;
        // 水印文本长度
        final float watermarkLength = getCnWordNum(watermark);
        try {
            PdfReader reader = new PdfReader(inputStream);
            final int total = reader.getNumberOfPages() + 1;
            // 创建临时文件
            File tempPdfFile = File.createTempFile("temp", ".pdf");
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(tempPdfFile));
            PdfStamper stamper = new PdfStamper(reader, bos);
            // 这里的字体设置比较关键，这个设置是支持中文的写法
            BaseFont base = BaseFont.createFont(FONT_NAME, FONT_ENCODING, BaseFont.EMBEDDED);

            PdfContentByte content;
            for (int i = 1; i < total; i++) {
                // 水印X,Y坐标
                // 第一步，算出居中边长为短的一边的4/5的正方形，左下角坐标
                float width = reader.getPageSize(i).getWidth();
                float height = reader.getPageSize(i).getHeight();
                float x, y, b;
                if (width > height) {
                    b = height * 0.8f;
                    x = (width - height) / 2 + (height * 0.1f);
                    y = height * 0.1f;
                } else {
                    b = width * 0.8f;
                    x = width * 0.1f;
                    y = (height - width) / 2 + (width * 0.1f);
                }
                Double markLength = b / Math.sin(45);
                float wordSize = markLength.floatValue() / watermarkLength;
                Double realX = x + Math.sin(45) * wordSize * 0.5;
                // 在内容上方加水印
                content = stamper.getOverContent(i);
                content.saveState();
                // set Transparency
                PdfGState gs = new PdfGState();
                gs.setFillOpacity(fillOpacity);
                content.setGState(gs);
                content.restoreState();
                content.beginText();
                content.setFontAndSize(base, wordSize);
                content.setColorFill(new BaseColor(192, 192, 192, 100));
                // 水印文字成45度角倾斜
                content.showTextAligned(Element.ALIGN_LEFT, watermark, realX.floatValue(), y, rotation);
                // 添加水印文字
                content.endText();
                content.setLineWidth(1f);
                content.stroke();
            }

            FileInputStream fileInputStream = new FileInputStream(tempPdfFile);
            // 关闭&删除临时文件
            stamper.close();
            bos.close();
            tempPdfFile.deleteOnExit();
            return fileInputStream;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 给PDF文件添加文本水印-多行水印
     *
     * @param inputStream 源文件流
     * @param watermark   文本水印
     * @return 水印文件流
     */
    public static FileInputStream markPdfForMany(InputStream inputStream, String watermark) {
        // 坐标旋转角度
        final float rotation = 45F;
        // 设置透明度为0.2
        final float fillOpacity = 0.2F;
        // 间隔
        final int interval = 20;


        try {
            PdfReader reader = new PdfReader(inputStream);
            // 创建临时文件
            File tempPdfFile = File.createTempFile("temp", ".pdf");
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(tempPdfFile));
            PdfStamper stamper = new PdfStamper(reader, bos);
            // 这里的字体设置比较关键，这个设置是支持中文的写法
            BaseFont base = BaseFont.createFont(FONT_NAME, FONT_ENCODING, BaseFont.EMBEDDED);

            Rectangle pageRect = null;
            PdfGState gs = new PdfGState();
            gs.setFillOpacity(fillOpacity);
            // 总页数
            int total = reader.getNumberOfPages() + 1;
            JLabel label = new JLabel();
            FontMetrics metrics;
            label.setText(watermark);
            metrics = label.getFontMetrics(label.getFont());
            int textH = metrics.getHeight();
            int textW = metrics.stringWidth(label.getText());
            PdfContentByte under;

            for (int i = 1; i < total; i++) {
                pageRect = reader.getPageSizeWithRotation(i);
                under = stamper.getOverContent(i);
                under.saveState();
                under.setGState(gs);
                under.beginText();
                under.setFontAndSize(base, 20);

                for (int height = interval + textH; height < pageRect.getHeight(); height = height + textH * 3) {
                    for (int width = interval + textW; width < pageRect.getWidth() + textW; width = width + textW * 2) {
                        under.showTextAligned(Element.ALIGN_LEFT, watermark, width - textW, height - textH, rotation);
                    }
                }
                // 添加水印文字
                under.endText();
            }

            FileInputStream fileInputStream = new FileInputStream(tempPdfFile);
            // 关闭&删除临时文件
            stamper.close();
            bos.close();
            tempPdfFile.deleteOnExit();
            reader.close();
            return fileInputStream;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 给图片添加文本水印-多行水印
     */
    public static InputStream markImgForMany(InputStream sourceImgStream, String fileName, String watermark) {
        // 水印颜色
        final Color color = Color.red;
        // 设置水印文字的旋转角度
        final int degree = -35;
        // 设置水印透明度
        final float alpha = 0.2f;
        // 水印之间的间隔
        final int X_MOVE = 100;
        // 水印之间的间隔
        final int Y_MOVE = 200;
        // 图片后缀类型
        String imageType = getFileSuffix(fileName);
        try {
            // 文件转化为图片
            Image srcImg = ImageIO.read(sourceImgStream);
            // 获取图片的宽&高
            int srcImgWidth = srcImg.getWidth(null);
            int srcImgHeight = srcImg.getHeight(null);
            // 水印字体，大小
            int fontSize = srcImgWidth / 30;
            if (fontSize == 0) {
                fontSize = 10;
            }
            final Font font = new Font("宋体", Font.PLAIN, fontSize);

            // 图像缓冲区
            BufferedImage image = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
            // 画笔
            Graphics2D pen = image.createGraphics();
            pen.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
            // 设置水印文字颜色&字体
            pen.setColor(color);
            pen.setFont(font);
            // 设置水印文字透明度
            pen.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));
            // 设置水印旋转
            pen.rotate(Math.toRadians(degree), (double) image.getWidth() / 2, (double) image.getHeight() / 2);

            int x = -srcImgWidth / 2;
            int y = -srcImgHeight / 2;
            // 字体长度&高度
            float watermarkLength = getCnWordNum(watermark);
            float markWidth = fontSize * watermarkLength;
            int markHeight = fontSize;
            // 循环添加水印
            while (x < srcImgWidth * 1.5) {
                y = -srcImgHeight / 2;
                while (y < srcImgHeight * 1.5) {
                    pen.drawString(watermark, x, y);
                    y += markHeight * 2 + Y_MOVE;
                }
                x += markWidth * 1.2 + X_MOVE;
            }
            // 释放资源
            pen.dispose();
            // 创建新图片文件
            File tempFile = File.createTempFile("temp", "." + imageType);
            // 将处理好的图片数据写入到新图片文件中
            FileOutputStream fos = new FileOutputStream(tempFile);
            ImageIO.write(image, imageType, fos);
            // 得到新的文件流
            FileInputStream fileInputStream = new FileInputStream(tempFile);
            // 关闭&删除临时文件
            fos.close();
            tempFile.deleteOnExit();
            return fileInputStream;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static float getCnWordNum(String value) {
        float wordNum = 0;
        String chinese = "[\u4e00-\u9fa5]";
        for (int i = 0; i < value.length(); i++) {
            String temp = value.substring(i, i + 1);
            if (temp.matches(chinese)) {
                wordNum += 1;
            } else {
                wordNum += 0.5;
            }
        }
        return wordNum;
    }

//    public static void main(String[] args) throws IOException {
//        pdfTest();
//        pdfImg();
//    }
//
//    private static void pdfTest() {
//        String sourceFilepath = "/Users/<USER>/temp/pdf/aa.pdf";
//        String targetFilepath = "/Users/<USER>/temp/pdf/bb.pdf";
//        try {
//            File srcFile = new File(sourceFilepath);
//            FileInputStream srcInputStream = new FileInputStream(srcFile);
//            FileInputStream targetStream = markPdfForMany(srcInputStream, "liaogs廖高松");
//
//            int index;
//            byte[] bytes = new byte[1024];
//            FileOutputStream downloadFile = new FileOutputStream(targetFilepath);
//            while ((index = targetStream.read(bytes)) != -1) {
//                downloadFile.write(bytes, 0, index);
//                downloadFile.flush();
//            }
//            targetStream.close();
//            downloadFile.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    private static void pdfImg() {
//        String sourceFilepath = "/Users/<USER>/temp/img/source/p1.png";
//        String targetFilepath = "/Users/<USER>/temp/img/target/p1.png";
//        String sourceFilepath2 = "/Users/<USER>/temp/img/source/s1.jpeg";
//        String targetFilepath2 = "/Users/<USER>/temp/img/target/s1.jpeg";
//
//        String watermark = "liaogs2022-09-27 09:12:12廖高松";
//        try {
//            File srcFile = new File(sourceFilepath);
//            FileInputStream srcInputStream = new FileInputStream(srcFile);
//            InputStream targetStream = markImgForMany(srcInputStream, srcFile.getName(), watermark);
//
//            int index;
//            byte[] bytes = new byte[1024];
//            FileOutputStream downloadFile = new FileOutputStream(targetFilepath);
//            while ((index = targetStream.read(bytes)) != -1) {
//                downloadFile.write(bytes, 0, index);
//                downloadFile.flush();
//            }
//            targetStream.close();
//            downloadFile.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        try {
//            File srcFile = new File(sourceFilepath2);
//            FileInputStream srcInputStream = new FileInputStream(srcFile);
//            InputStream targetStream = markImgForMany(srcInputStream, srcFile.getName(), watermark);
//
//            int index;
//            byte[] bytes = new byte[1024];
//            FileOutputStream downloadFile = new FileOutputStream(targetFilepath2);
//            while ((index = targetStream.read(bytes)) != -1) {
//                downloadFile.write(bytes, 0, index);
//                downloadFile.flush();
//            }
//            targetStream.close();
//            downloadFile.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    private static String getFileSuffix(String fileName) {
        int suffixPlace = fileName.lastIndexOf(".");
        if (suffixPlace > -1 && suffixPlace != fileName.length() - 1) {
            return fileName.substring(suffixPlace + 1).toLowerCase();
        }
        return "";
    }
}
