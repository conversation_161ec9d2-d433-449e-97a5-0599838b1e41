package com.deeppaas.common.data.jdbc;

import com.deeppaas.common.data.Operator;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jdbc.core.convert.Identifier;
import org.springframework.data.jdbc.core.convert.JdbcConverter;
import org.springframework.data.mapping.PersistentPropertyPath;
import org.springframework.data.mapping.PropertyHandler;
import org.springframework.data.mapping.context.MappingContext;
import org.springframework.data.relational.core.dialect.Dialect;
import org.springframework.data.relational.core.dialect.RenderContextFactory;
import org.springframework.data.relational.core.mapping.PersistentPropertyPathExtension;
import org.springframework.data.relational.core.mapping.RelationalMappingContext;
import org.springframework.data.relational.core.mapping.RelationalPersistentEntity;
import org.springframework.data.relational.core.mapping.RelationalPersistentProperty;
import org.springframework.data.relational.core.sql.*;
import org.springframework.data.relational.core.sql.render.RenderContext;
import org.springframework.data.relational.core.sql.render.SqlRenderer;
import org.springframework.data.util.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */
public class SqlGenerator {
    static final SqlIdentifier ID_SQL_PARAMETER = SqlIdentifier.unquoted("id");
    static final SqlIdentifier IDS_SQL_PARAMETER = SqlIdentifier.unquoted("ids");

    private static final Pattern parameterPattern = Pattern.compile("\\W");
    private final RelationalPersistentEntity<?> entity;
    private final MappingContext<RelationalPersistentEntity<?>, RelationalPersistentProperty> mappingContext;
    private final RenderContext renderContext;

    private final SqlContext sqlContext;
    private final SqlRenderer sqlRenderer;
    private final Columns columns;

    private final Lazy<String> findOneSql = Lazy.of(this::createFindOneSql);
    private final Lazy<String> findAllSql = Lazy.of(this::createFindAllSql);
    private final Lazy<String> findAllInListSql = Lazy.of(this::createFindAllInListSql);

    private final Lazy<String> existsSql = Lazy.of(this::createExistsSql);
    private final Lazy<String> countSql = Lazy.of(this::createCountSql);

    /**
     * Create a new {@link SqlGenerator} given {@link RelationalMappingContext} and {@link RelationalPersistentEntity}.
     *
     * @param mappingContext must not be {@literal null}.
     * @param converter      must not be {@literal null}.
     * @param entity         must not be {@literal null}.
     * @param dialect        must not be {@literal null}.
     */
    SqlGenerator(RelationalMappingContext mappingContext, JdbcConverter converter, RelationalPersistentEntity<?> entity,
                 Dialect dialect) {

        this.mappingContext = mappingContext;
        this.entity = entity;
        this.sqlContext = new SqlContext(entity);
        this.sqlRenderer = SqlRenderer.create(new RenderContextFactory(dialect).createRenderContext());
        this.columns = new Columns(entity, mappingContext, converter);
        this.renderContext = new RenderContextFactory(dialect).createRenderContext();
    }

    private BindMarker getBindMarker(SqlIdentifier columnName) {
        return SQL.bindMarker(":" + parameterPattern.matcher(renderReference(columnName)).replaceAll(""));
    }

    public String getFindList(JCondition jCondition) {
        SelectBuilder.SelectFromAndJoin select = selectBuilder();
        select.where(this.toJdbcCondition(jCondition, false));
        return render(select.build());
    }

    public String getFindList(JCondition jCondition, Sort sort) {
        SelectBuilder.SelectFromAndJoin select = selectBuilder();
        select.where(this.toJdbcCondition(jCondition, false));
        select.orderBy(extractOrderByFields(sort));
        return render(select.build());
    }

    public String getFindPage(JCondition jCondition, Pageable pageable) {
        SelectBuilder.SelectFromAndJoin select = selectBuilder();
        select.where(this.toJdbcCondition(jCondition, false));
        select.limitOffset(pageable.getPageSize(), pageable.getOffset());
        select.orderBy(extractOrderByFields(pageable.getSort()));
        return render(select.build());
    }

    public String getCount(JCondition jCondition) {
        Table table = getTable();
        Select select = StatementBuilder
                .select(Functions.count(Expressions.asterisk()))
                .from(table)
                .where(this.toJdbcCondition(jCondition, false))
                .build();
        return render(select);
    }

    /**
     * Returns a query for selecting all simple properties of an entity, including those for one-to-one relationships.
     * Results are filtered using an {@code IN}-clause on the id column.
     *
     * @return a SQL statement. Guaranteed to be not {@code null}.
     */
    String getFindAllInList() {
        return findAllInListSql.get();
    }

    /**
     * Returns a query for selecting all simple properties of an entity, including those for one-to-one relationships.
     *
     * @return a SQL statement. Guaranteed to be not {@code null}.
     */
    String getFindAll() {
        return findAllSql.get();
    }

    /**
     * Returns a query for selecting all simple properties of an entity, including those for one-to-one relationships,
     * sorted by the given parameter.
     *
     * @return a SQL statement. Guaranteed to be not {@code null}.
     */
    String getFindAll(Sort sort) {
        return render(selectBuilder(Collections.emptyList(), sort, Pageable.unpaged()).build());
    }

    /**
     * Returns a query for selecting all simple properties of an entity, including those for one-to-one relationships,
     * paged and sorted by the given parameter.
     *
     * @return a SQL statement. Guaranteed to be not {@code null}.
     */
    String getFindAll(Pageable pageable) {
        return render(selectBuilder(Collections.emptyList(), pageable.getSort(), pageable).build());
    }

    /**
     * Returns a query for selecting all simple properties of an entity, including those for one-to-one relationships.
     * Results are limited to those rows referencing some other entity using the column specified by
     * {@literal columnName}. This is used to select values for a complex property ({@link Set}, {@link Map} ...) based on
     * a referencing entity.
     *
     * @param parentIdentifier name of the column of the FK back to the referencing entity.
     * @param keyColumn        if the property is of type {@link Map} this column contains the map key.
     * @param ordered          whether the SQL statement should include an ORDER BY for the keyColumn. If this is {@code true}, the
     *                         keyColumn must not be {@code null}.
     * @return a SQL String.
     */
    String getFindAllByProperty(Identifier parentIdentifier, @Nullable SqlIdentifier keyColumn, boolean ordered) {

        Assert.isTrue(keyColumn != null || !ordered,
                "If the SQL statement should be ordered a keyColumn to order by must be provided.");

        Table table = getTable();

        SelectBuilder.SelectWhere builder = selectBuilder( //
                keyColumn == null //
                        ? Collections.emptyList() //
                        : Collections.singleton(keyColumn) //
        );

        Condition condition = buildConditionForBackReference(parentIdentifier, table);
        SelectBuilder.SelectWhereAndOr withWhereClause = builder.where(condition);

        Select select = ordered //
                ? withWhereClause.orderBy(table.column(keyColumn).as(keyColumn)).build() //
                : withWhereClause.build();

        return render(select);
    }

    private Condition buildConditionForBackReference(Identifier parentIdentifier, Table table) {

        Condition condition = null;
        for (SqlIdentifier backReferenceColumn : parentIdentifier.toMap().keySet()) {

            Condition newCondition = table.column(backReferenceColumn).isEqualTo(getBindMarker(backReferenceColumn));
            condition = condition == null ? newCondition : condition.and(newCondition);
        }

        Assert.state(condition != null, "We need at least one condition");

        return condition;
    }

    /**
     * Create a {@code SELECT COUNT(id) FROM … WHERE :id = …} statement.
     *
     * @return the statement as a {@link String}. Guaranteed to be not {@literal null}.
     */
    String getExists() {
        return existsSql.get();
    }

    /**
     * Create a {@code SELECT … FROM … WHERE :id = …} statement.
     *
     * @return the statement as a {@link String}. Guaranteed to be not {@literal null}.
     */
    String getFindOne() {
        return findOneSql.get();
    }

    /**
     * Create a {@code SELECT COUNT(*) FROM …} statement.
     *
     * @return the statement as a {@link String}. Guaranteed to be not {@literal null}.
     */
    String getCount() {
        return countSql.get();
    }

    private String createFindOneSql() {

        Select select = selectBuilder().where(getIdColumn().isEqualTo(getBindMarker(ID_SQL_PARAMETER))) //
                .build();

        return render(select);
    }

    private String createFindAllSql() {
        return render(selectBuilder().build());
    }

    private SelectBuilder.SelectFromAndJoin selectBuilder() {
        return selectBuilder(Collections.emptyList());
    }

    private SelectBuilder.SelectFromAndJoin selectBuilder(Collection<SqlIdentifier> keyColumns) {

        Table table = getTable();

        List<Expression> columnExpressions = new ArrayList<>();

        List<Join> joinTables = new ArrayList<>();
        for (PersistentPropertyPath<RelationalPersistentProperty> path : mappingContext
                .findPersistentPropertyPaths(entity.getType(), p -> true)) {

            PersistentPropertyPathExtension extPath = new PersistentPropertyPathExtension(mappingContext, path);

            // add a join if necessary
            Join join = getJoin(extPath);
            if (join != null) {
                joinTables.add(join);
            }

            Column column = getColumn(extPath);
            if (column != null) {
                columnExpressions.add(column);
            }
        }

        for (SqlIdentifier keyColumn : keyColumns) {
            columnExpressions.add(table.column(keyColumn).as(keyColumn));
        }

        SelectBuilder.SelectAndFrom selectBuilder = StatementBuilder.select(columnExpressions);
        SelectBuilder.SelectJoin baseSelect = selectBuilder.from(table);

        for (Join join : joinTables) {
            baseSelect = baseSelect.leftOuterJoin(join.joinTable).on(join.joinColumn).equals(join.parentId);
        }

        return (SelectBuilder.SelectFromAndJoin) baseSelect;
    }

    private SelectBuilder.SelectOrdered selectBuilder(Collection<SqlIdentifier> keyColumns, Sort sort,
                                                      Pageable pageable) {

        SelectBuilder.SelectOrdered sortable = this.selectBuilder(keyColumns);
        sortable = applyPagination(pageable, sortable);
        return sortable.orderBy(extractOrderByFields(sort));

    }

    private SelectBuilder.SelectOrdered applyPagination(Pageable pageable, SelectBuilder.SelectOrdered select) {

        if (!pageable.isPaged()) {
            return select;
        }

        Assert.isTrue(select instanceof SelectBuilder.SelectLimitOffset,
                () -> String.format("Can't apply limit clause to statement of type %s", select.getClass()));

        SelectBuilder.SelectLimitOffset limitable = (SelectBuilder.SelectLimitOffset) select;
        SelectBuilder.SelectLimitOffset limitResult = limitable.limitOffset(pageable.getPageSize(), pageable.getOffset());

        Assert.state(limitResult instanceof SelectBuilder.SelectOrdered, String.format(
                "The result of applying the limit-clause must be of type SelectOrdered in order to apply the order-by-clause but is of type %s.",
                select.getClass()));

        return (SelectBuilder.SelectOrdered) limitResult;
    }

    /**
     * Create a {@link Column} for {@link PersistentPropertyPathExtension}.
     *
     * @param path the path to the column in question.
     * @return the statement as a {@link String}. Guaranteed to be not {@literal null}.
     */
    @Nullable
    Column getColumn(PersistentPropertyPathExtension path) {

        // an embedded itself doesn't give an column, its members will though.
        // if there is a collection or map on the path it won't get selected at all, but it will get loaded with a separate
        // select
        // only the parent path is considered in order to handle arrays that get stored as BINARY properly
        if (path.isEmbedded() || path.getParentPath().isMultiValued()) {
            return null;
        }

        if (path.isEntity()) {

            // Simple entities without id include there backreference as an synthetic id in order to distinguish null entities
            // from entities with only null values.

            if (path.isQualified() //
                    || path.isCollectionLike() //
                    || path.hasIdProperty() //
            ) {
                return null;
            }

            return sqlContext.getReverseColumn(path);
        }

        return sqlContext.getColumn(path);
    }

    @Nullable
    Join getJoin(PersistentPropertyPathExtension path) {

        if (!path.isEntity() || path.isEmbedded() || path.isMultiValued()) {
            return null;
        }

        Table currentTable = sqlContext.getTable(path);

        PersistentPropertyPathExtension idDefiningParentPath = path.getIdDefiningParentPath();
        Table parentTable = sqlContext.getTable(idDefiningParentPath);

        return new Join( //
                currentTable, //
                currentTable.column(path.getReverseColumnName()), //
                parentTable.column(idDefiningParentPath.getIdColumnName()) //
        );
    }

    private String createFindAllInListSql() {

        Select select = selectBuilder().where(getIdColumn().in(getBindMarker(IDS_SQL_PARAMETER))).build();

        return render(select);
    }

    private String createExistsSql() {

        Table table = getTable();

        Select select = StatementBuilder //
                .select(Functions.count(getIdColumn())) //
                .from(table) //
                .where(getIdColumn().isEqualTo(getBindMarker(ID_SQL_PARAMETER))) //
                .build();

        return render(select);
    }

    private String createCountSql() {

        Table table = getTable();

        Select select = StatementBuilder //
                .select(Functions.count(Expressions.asterisk())) //
                .from(table) //
                .build();

        return render(select);
    }


    private String render(Select select) {
        return this.sqlRenderer.render(select);
    }

    private String render(Insert insert) {
        return this.sqlRenderer.render(insert);
    }

    private String render(Update update) {
        return this.sqlRenderer.render(update);
    }

    private String render(Delete delete) {
        return this.sqlRenderer.render(delete);
    }

    private Table getTable() {
        return sqlContext.getTable();
    }

    private Column getIdColumn() {
        return sqlContext.getIdColumn();
    }

    private Column getVersionColumn() {
        return sqlContext.getVersionColumn();
    }

    private String renderReference(SqlIdentifier identifier) {
        return identifier.getReference(renderContext.getIdentifierProcessing());
    }

    private List<OrderByField> extractOrderByFields(Sort sort) {

        return sort.stream() //
                .map(this::orderToOrderByField) //
                .collect(Collectors.toList());
    }

    private OrderByField orderToOrderByField(Sort.Order order) {

        SqlIdentifier columnName = this.entity.getRequiredPersistentProperty(order.getProperty()).getColumnName();
        Column column = Column.create(columnName, this.getTable());
        return OrderByField.from(column, order.getDirection());
    }

    /**
     * Value object representing a {@code JOIN} association.
     */
    static final class Join {

        private final Table joinTable;
        private final Column joinColumn;
        private final Column parentId;

        Join(Table joinTable, Column joinColumn, Column parentId) {

            Assert.notNull(joinTable, "JoinTable must not be null.");
            Assert.notNull(joinColumn, "JoinColumn must not be null.");
            Assert.notNull(parentId, "ParentId must not be null.");

            this.joinTable = joinTable;
            this.joinColumn = joinColumn;
            this.parentId = parentId;
        }

        Table getJoinTable() {
            return this.joinTable;
        }

        Column getJoinColumn() {
            return this.joinColumn;
        }

        Column getParentId() {
            return this.parentId;
        }

        @Override
        public boolean equals(Object o) {

            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Join join = (Join) o;
            return joinTable.equals(join.joinTable) &&
                    joinColumn.equals(join.joinColumn) &&
                    parentId.equals(join.parentId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(joinTable, joinColumn, parentId);
        }

        @Override
        public String toString() {

            return "Join{" +
                    "joinTable=" + joinTable +
                    ", joinColumn=" + joinColumn +
                    ", parentId=" + parentId +
                    '}';
        }
    }

    /**
     * Value object encapsulating column name caches.
     *
     * <AUTHOR> Paluch
     * <AUTHOR> Schauder
     */
    static class Columns {

        private final MappingContext<RelationalPersistentEntity<?>, RelationalPersistentProperty> mappingContext;
        private final JdbcConverter converter;

        private final List<SqlIdentifier> columnNames = new ArrayList<>();
        private final List<SqlIdentifier> idColumnNames = new ArrayList<>();
        private final List<SqlIdentifier> nonIdColumnNames = new ArrayList<>();
        private final Set<SqlIdentifier> readOnlyColumnNames = new HashSet<>();

        Columns(RelationalPersistentEntity<?> entity,
                MappingContext<RelationalPersistentEntity<?>, RelationalPersistentProperty> mappingContext,
                JdbcConverter converter) {

            this.mappingContext = mappingContext;
            this.converter = converter;

            populateColumnNameCache(entity, "");

        }

        private void populateColumnNameCache(RelationalPersistentEntity<?> entity, String prefix) {

            entity.doWithProperties((PropertyHandler<RelationalPersistentProperty>) property -> {

                // the referencing column of referenced entity is expected to be on the other side of the relation
                if (!property.isEntity()) {
                    initSimpleColumnName(property, prefix);
                } else if (property.isEmbedded()) {
                    initEmbeddedColumnNames(property, prefix);
                }
            });
        }

        private void initSimpleColumnName(RelationalPersistentProperty property, String prefix) {

            SqlIdentifier columnName = property.getColumnName().transform(prefix::concat);

            columnNames.add(columnName);

            if (!property.getOwner().isIdProperty(property)) {
                nonIdColumnNames.add(columnName);
            } else {
                idColumnNames.add(columnName);
            }

            if (!property.isWritable()) {
                readOnlyColumnNames.add(columnName);
            }
        }

        private void initEmbeddedColumnNames(RelationalPersistentProperty property, String prefix) {
            String embeddedPrefix = property.getEmbeddedPrefix();
            RelationalPersistentEntity<?> embeddedEntity = mappingContext
                    .getRequiredPersistentEntity(converter.getColumnType(property));
            populateColumnNameCache(embeddedEntity, prefix + embeddedPrefix);
        }

    }

    /**
     * 将自定义JCondition转换为Spring data的Condition
     *
     * @param: jCondition
     * @param: nested 是否嵌套的,首层无需嵌套
     * @return: org.springframework.data.relational.core.sql.Condition
     * <AUTHOR>
     * @date 2020/12/10 上午11:20
     **/
    private Condition toJdbcCondition(JCondition jCondition, boolean nested) {
        if (jCondition instanceof JConditionS) {
            JConditionS conditionS = (JConditionS) jCondition;
            PersistentPropertyPath propertyPath = mappingContext.getPersistentPropertyPath(conditionS.getFieldName(), entity.getType());
            PersistentPropertyPathExtension pathExtension = new PersistentPropertyPathExtension(mappingContext, propertyPath);
            Column column = sqlContext.getColumn(pathExtension);
            switch (jCondition.getOperator()) {
                case EQ:
                    return column.isEqualTo(SQL.bindMarker(conditionS.getBindMarker()));
                case NE:
                    return column.isNotEqualTo(SQL.bindMarker(conditionS.getBindMarker()));
                case CONTAINS:
                case STARTWITH:
                case ENDWITH:
                    return column.like(SQL.bindMarker(conditionS.getBindMarker()));
                case NOTCONTAINS: return column.notLike(SQL.bindMarker(conditionS.getBindMarker()));
                case GT:
                    return column.isGreater(SQL.bindMarker(conditionS.getBindMarker()));
                case LT:
                    return column.isLess(SQL.bindMarker(conditionS.getBindMarker()));
                case GTE:
                    return column.isGreaterOrEqualTo(SQL.bindMarker(conditionS.getBindMarker()));
                case LTE:
                    return column.isLessOrEqualTo(SQL.bindMarker(conditionS.getBindMarker()));
                case IN:
                    return column.in(SQL.bindMarker(conditionS.getBindMarker()));
                case NOTIN:
                    return column.notIn(SQL.bindMarker(conditionS.getBindMarker()));
                case ISNULL:
                    return column.isNull();
                case NOTNULL:
                    return column.isNotNull();
            }
        }
        if (jCondition instanceof JConditionL) {
            JConditionL jcl = (JConditionL) jCondition;
            List<Condition> conditions = new ArrayList();
            for (JCondition subJCondition : jcl.getConditions()) {
                Condition subCondition = toJdbcCondition(subJCondition, true);
                if (subCondition != null) {
                    conditions.add(subCondition);
                }
            }
            if (conditions.isEmpty()) {
                return null;
            } else if (conditions.size() == 1) {
                return conditions.get(0);
            } else {
                Condition multipleCondition = conditions.get(0);
                conditions.remove(multipleCondition);
                for (Condition condition : conditions) {
                    if (Operator.OR.equals(jCondition.getOperator())) {
                        multipleCondition = multipleCondition.or(condition);
                    } else {
                        multipleCondition = multipleCondition.and(condition);
                    }
                }
                return nested ? Conditions.nest(multipleCondition) : multipleCondition;
            }
        }
        if(jCondition instanceof JConditionC jConditionC){
            return Conditions.just(jConditionC.getSql());
        }
        return null;
    }
}
