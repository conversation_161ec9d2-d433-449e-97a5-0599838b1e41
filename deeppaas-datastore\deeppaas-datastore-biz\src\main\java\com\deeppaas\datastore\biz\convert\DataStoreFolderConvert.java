package com.deeppaas.datastore.biz.convert;

import com.deeppaas.datastore.biz.entity.DataStoreFolderDO;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderBaseVO;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据表文件夹转换器
 *
 * <AUTHOR>
 * @date 2022/07/30
 */
public class DataStoreFolderConvert {


    public static List<DataStoreFolderVO> entityToVOS(List<DataStoreFolderDO> folderDOList,
                                                      List<DataStoreFolderDO> parentFolderDOList) {
        List<DataStoreFolderVO> voList = new ArrayList<>();
        Map<String, DataStoreFolderDO> parentFolderMap = parentFolderDOList.stream().collect(Collectors.toMap(DataStoreFolderDO::getId, Function.identity()));
        for (DataStoreFolderDO folderDO : folderDOList) {
            DataStoreFolderVO vo = new DataStoreFolderVO();
            vo.setId(folderDO.getId());
            vo.setName(folderDO.getName());
            vo.setParentId(folderDO.getParentId());
            String parentName = "";
            if (parentFolderMap.containsKey(folderDO.getParentId())) {
                parentName = parentFolderMap.get(folderDO.getParentId()).getName();
            }
            vo.setParentName(parentName);
            vo.setUsable(folderDO.getUsable());
            vo.setSortNo(folderDO.getSortNo());
            voList.add(vo);
        }
        return voList;
    }

    public static List<DataStoreFolderBaseVO> entityToBaseVOS(List<DataStoreFolderDO> folderDOList) {
        if (CollectionUtils.isEmpty(folderDOList)) {
            return Collections.emptyList();
        }
        // 翻转一下list
        Collections.reverse(folderDOList);

        List<DataStoreFolderBaseVO> voList = new ArrayList<>();
        for (DataStoreFolderDO folderDO : folderDOList) {
            DataStoreFolderBaseVO vo = new DataStoreFolderBaseVO();
            vo.setId(folderDO.getId());
            vo.setName(folderDO.getName());
            vo.setParentId(folderDO.getParentId());
            vo.setUsable(folderDO.getUsable());
            voList.add(vo);
        }
        return voList;
    }
}
