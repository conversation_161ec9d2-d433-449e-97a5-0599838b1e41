package com.deeppaas.msg.biz.service;

import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.msg.biz.dao.UserMsgBoxDao;
import com.deeppaas.msg.biz.dao.UserMsgDao;
import com.deeppaas.msg.biz.entity.UserMsgBoxDO;
import com.deeppaas.msg.biz.entity.UserMsgDO;
import com.deeppaas.msg.biz.web.param.UserMsgSearchParam;
import com.deeppaas.msg.biz.web.vo.UserMsgCountVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@Service
public class UserMsgService {
    private final UserMsgDao userMsgDao;
    private final UserMsgBoxDao userMsgBoxDao;

    public UserMsgService(UserMsgDao userMsgDao, UserMsgBoxDao userMsgBoxDao) {
        this.userMsgDao = userMsgDao;
        this.userMsgBoxDao = userMsgBoxDao;
    }

    public Page<UserMsgDO> findForPage(UserMsgSearchParam param, String userCode) {
        JCondition condition = JCondition.and(
                JCondition.eq("boxId", param.getBoxId(), true),
                JCondition.eq("readFlag", param.getReadFlag(), true),
                JCondition.eq("toUser", userCode,false),
                JCondition.eq("del", BoolHelper.INT_FALSE, false)
        );
        PageRequest pageRequest = param.getPageable();
        if(pageRequest.getSort()==null){
            pageRequest.withSort(Sort.Direction.DESC, "id");
        }
        return userMsgDao.queryForPage(condition, param.getPageable());
    }

    @Transactional
    public UserMsgDO readAndGet(String id) {
        UserMsgDO msgDO = userMsgDao.getById(id);
        if(msgDO!=null && !BoolHelper.intToBool(msgDO.getReadFlag())){
            msgDO.setReadFlag(BoolHelper.INT_TRUE);
            msgDO.setReadTime(LocalDateTime.now());
            userMsgDao.save(msgDO);
        }
        return msgDO;
    }

    @Transactional
    public List<UserMsgCountVO> findUnReadMsgCount(String userCode) {
        List<UserMsgCountVO> countVOS = userMsgDao.countForBoxUnRead(userCode);
        Map<String, Long> moduleCountMap = countVOS.stream().collect(Collectors.toMap(UserMsgCountVO::getBoxId, UserMsgCountVO::getMsgCount));
        List<UserMsgBoxDO> boxDOS = userMsgBoxDao.findAll();
        UserMsgBoxDO systemBox = new UserMsgBoxDO();
        systemBox.setId("");
        systemBox.setName("系统消息");
        boxDOS.add(systemBox);
        List<UserMsgCountVO> moduleCountVOS = boxDOS.stream().map(box -> {
            UserMsgCountVO countVO = new UserMsgCountVO(box.getId(), box.getName());
            Long boxCount = moduleCountMap.get(box.getId());
            if (boxCount != null) {
                countVO.setMsgCount(boxCount);
            }
            return countVO;
        }).collect(Collectors.toList());
        moduleCountVOS.sort(Comparator.comparingLong(UserMsgCountVO::getMsgCount));
        return moduleCountVOS;
    }

    @Transactional
    public void readByIds(List<String> ids, String userCode) {
        userMsgDao.updateReadByIds(ids, userCode, LocalDateTime.now());
    }

    @Transactional
    public void readByBox(String boxId, String userCode) {
        if (StringUtils.hasText(boxId)) {
            userMsgDao.updateReadByBox(boxId, userCode, LocalDateTime.now());
        } else {
            userMsgDao.updateReadByUser(userCode, LocalDateTime.now());
        }

    }

    @Transactional
    public void deleteByIds(List<String> ids, String userCode) {
        userMsgDao.updateDelByIdsO(ids, userCode, LocalDateTime.now());
    }

    @Transactional
    public void saveAll(List<UserMsgDO> msgList) {
        userMsgDao.saveAll(msgList);
    }
}
