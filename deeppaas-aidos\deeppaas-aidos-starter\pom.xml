<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>deeppaas-aidos</artifactId>
        <groupId>com.deeppaas</groupId>
        <version>2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>deeppaas-aidos-starter</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <!--datasource -->
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-datastore-biz</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-view-biz</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-datastore-event</artifactId>
            <version>2.0</version>
        </dependency>
        <!--aidos-biz -->
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-aidos-biz</artifactId>
            <version>2.0</version>
        </dependency>
        <!--tenant-biz -->
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-tenant-biz</artifactId>
            <version>2.0</version>
        </dependency>
        <!-- license -->
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-license-open</artifactId>
            <version>2.0</version>
        </dependency>
        <!-- rule-bia -->
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-rule-biz</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-file-biz</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.deeppaas</groupId>
            <artifactId>deeppaas-msg-biz</artifactId>
            <version>2.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>aidos</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>