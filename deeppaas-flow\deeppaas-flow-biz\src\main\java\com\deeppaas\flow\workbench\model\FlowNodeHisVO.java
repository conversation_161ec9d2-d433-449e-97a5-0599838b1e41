package com.deeppaas.flow.workbench.model;

import com.deeppaas.account.api.client.model.User;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@Data
public class FlowNodeHisVO {
    /** 节点关键字 */
    private String code;
    /** 节点中文名 */
    private String name;
    /** 审批人 */
    private List<User> auditors;

    public FlowNodeHisVO(String code, String name, List<User> auditors) {
        this.code = code;
        this.name = name;
        this.auditors = auditors;
    }
}
