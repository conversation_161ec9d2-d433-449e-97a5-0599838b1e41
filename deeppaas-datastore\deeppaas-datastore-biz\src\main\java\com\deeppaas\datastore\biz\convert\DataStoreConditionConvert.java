package com.deeppaas.datastore.biz.convert;

import com.deeppaas.datastore.biz.web.req.DataStoreDataQueryParam;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.DataStoreConditionL;
import com.deeppaas.datastore.common.DataStoreConditionS;
import com.deeppaas.datastore.common.emnus.DataStoreOperator;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/21
 */
public class DataStoreConditionConvert {
    public static DataStoreCondition toDataStoreCondition(DataStoreDataQueryParam.QueryCondition queryCondition) {
        if(queryCondition!=null){
            DataStoreOperator operator = DataStoreOperator.valueOf(queryCondition.getOperator());
            switch (operator){
                case AND, OR ->{
                    List<DataStoreDataQueryParam.QueryCondition> children = queryCondition.getConditions();
                    List<DataStoreCondition> childConditions = children.stream()
                            .map(child -> DataStoreConditionConvert.toDataStoreCondition(child))
                            .filter(condition -> condition!=null).toList();
                    if (CollectionUtils.isEmpty(childConditions)) {
                        return null;
                    }
                    if (childConditions.size() == 1) {
                        return childConditions.get(0);
                    }
                    return new DataStoreConditionL(childConditions.toArray(new DataStoreCondition[childConditions.size()]), operator);
                }
                case ISNULL, NOTNULL -> {
                    return DataStoreConditionS.of(queryCondition.getField(), null, operator);
                }
                default -> {
                    if(queryCondition.getValue()!=null && StringUtils.hasText(queryCondition.getValue().toString())){
                        return DataStoreConditionS.of(queryCondition.getField(), queryCondition.getValue(),operator);
                    }
                    return null;
                }
            }
        }
        return null;
    }
}
