[{"stepId": "step1", "type": "DATA_GET", "returned": false, "returnVar": true, "varName": "varApplyOrder", "varType": "DATA_ENTITY", "varEntity": "userAbsenceApply", "name": "获取员工请假申请单", "dataEntity": "ET_UserAbsenceApply", "permits": [], "defaultCondition": {"field": "id", "operator": "EQ", "dataBind": {"targetType": "CONST", "value": "c2dd9f1d9c334f75a37843400278a954", "dataType": "TEXT"}}, "next": "step2"}, {"stepId": "step2", "type": "VARIABLE", "returned": true, "returnVar": false, "varName": "resultData", "varType": "RULE_ENTITY", "varEntity": "06p22er400217hyf87", "name": "员工请假信息列表", "dataBind": {"targetType": "VARS", "value": "varApplyOrder", "dataType": "RULE_ENTITY", "fields": [{"code": "empCode", "dataBind": {"targetType": "VARS", "dataType": "TEXT", "value": "varApplyOrder.applyUserCode"}}, {"code": "empName", "dataBind": {"targetType": "VARS", "dataType": "TEXT", "value": "varApplyOrder.applyUserName"}}, {"code": "empDays", "dataBind": {"targetType": "VARS", "dataType": "TEXT", "value": "varApplyOrder.applyDays"}}]}}]