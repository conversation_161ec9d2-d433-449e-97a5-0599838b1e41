package com.deeppaas.taskInfo.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Data
@Table("aidos_call_back_image")
public class TaskInfoImageDo  extends BaseUuidDO {
    /**
     *
     */
    @Column("name")
    private String name;
    /**
     *
     */
    @Column("task_info_id")
    private String taskInfoId;
    /**
     *
     */
    @Column("path")
    private String path;
    /**
     *
     */
    @Column("python_path")
    private String pythonPath;
    /**
     *
     */
    @Column("category")
    private String category;
    /**
     *
     */
    @Column("call_back_image_id")
    private String callBackImageId;
    /**
     *
     */
    @Column("call_back_id")
    private String callBackId;
    /**
     *
     */
    @Column("tag")
    private String tag;

}
