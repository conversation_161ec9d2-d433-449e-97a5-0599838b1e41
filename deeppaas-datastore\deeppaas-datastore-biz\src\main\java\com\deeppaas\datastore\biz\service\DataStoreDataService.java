package com.deeppaas.datastore.biz.service;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.ExcelReader;
import com.deeppaas.common.model.PageData;
import com.deeppaas.datastore.biz.convert.DataStoreConditionConvert;
import com.deeppaas.datastore.biz.factory.DataStoreJdbcRepositoryFactory;
import com.deeppaas.datastore.biz.web.req.DataStoreDataQueryParam;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.DataStoreConditionS;
import com.deeppaas.datastore.common.DataStoreRepository;
import com.deeppaas.datastore.common.emnus.DataStoreOperator;
import com.deeppaas.datastore.common.event.DataStoreEvent;
import com.deeppaas.datastore.common.event.DataStoreEventHandler;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.datastore.common.web.req.DataStoreDataSearch;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@Service
public class DataStoreDataService {
    private DataStoreJdbcRepositoryFactory dataStoreJdbcRepositoryFactory;
    private DataStoreEventHandler dataStoreEventHandler;

    public DataStoreDataService(DataStoreJdbcRepositoryFactory dataStoreJdbcRepositoryFactory, DataStoreEventHandler dataStoreEventHandler) {
        this.dataStoreJdbcRepositoryFactory = dataStoreJdbcRepositoryFactory;
        this.dataStoreEventHandler = dataStoreEventHandler;
    }

    public DataStoreData getData(String entity, Object id) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(entity);
        DataStoreData dataStoreData = repository.getById(id);
        // TODO 字段加密
        return dataStoreData;
    }

    public List<DataStoreData> search(DataStoreDataSearch search) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(search.getEntity());
        DataStoreCondition paramCondition = null;
        if(!CollectionUtils.isEmpty(search.getParams())){
            List<DataStoreCondition> paramConditions = search.getParams().stream().map(param ->{
                DataStoreOperator operator = DataStoreOperator.valueOf(param.getOperator());
                return DataStoreConditionS.of(param.getFieldCode(), param.getValue(), operator);
            }).collect(Collectors.toList());
            paramCondition = DataStoreCondition.and(paramConditions.toArray(new DataStoreCondition[paramConditions.size()]));
        }
        return repository.findForList(paramCondition, search.getPageable().getSort());
    }


    public PageData<DataStoreData> pageSearch(DataStoreDataSearch search) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(search.getEntity());
        List<DataStoreCondition> paramConditions = search.getParams().stream().map(param ->{
            DataStoreOperator operator = DataStoreOperator.valueOf(param.getOperator());
            return DataStoreConditionS.of(param.getFieldCode(), param.getValue(), operator);
        }).collect(Collectors.toList());
        DataStoreCondition paramCondition = DataStoreCondition.and(paramConditions.toArray(new DataStoreCondition[paramConditions.size()]));
        return repository.findForPage(paramCondition, search.getPageable());
    }

    @Transactional
    public DataStoreData save(DataStoreData data) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(data.getEntity());
        boolean isInsert = repository.isExist(data);
        if(isInsert){
            dataStoreEventHandler.execute(DataStoreEvent.BEFORE_INSERT, data);
        }else {
            dataStoreEventHandler.execute(DataStoreEvent.BEFORE_UPDATE, data);
        }
        data = repository.save(data);
        if(isInsert){
            dataStoreEventHandler.execute(DataStoreEvent.AFTER_INSERT, data);
        }else {
            dataStoreEventHandler.execute(DataStoreEvent.AFTER_UPDATE, data);
        }
        return data;
    }

    public void delete(String entity, String id) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(entity);
        // TODO 事件
        repository.deleteById(id);
    }

    public List<DataStoreData> query(DataStoreDataQueryParam queryParam) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(queryParam.getEntity());
        DataStoreCondition dataStoreCondition = null;
        if(queryParam.getCondition()!=null){
            DataStoreDataQueryParam.QueryCondition queryCondition = queryParam.getCondition();
            dataStoreCondition = DataStoreConditionConvert.toDataStoreCondition(queryCondition);
        }
        if(queryParam.notPageQuery()){
            if(queryParam.buildSort()==null){
                return repository.findForList(dataStoreCondition);
            }
            return repository.findForList(dataStoreCondition, queryParam.buildSort());
        }
        return repository.findForList(dataStoreCondition, queryParam.buildPageable());
    }

    public PageData<DataStoreData> pageQuery(DataStoreDataQueryParam queryParam) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(queryParam.getEntity());
        DataStoreCondition dataStoreCondition = null;
        if(queryParam.getCondition()!=null){
            DataStoreDataQueryParam.QueryCondition queryCondition = queryParam.getCondition();
            dataStoreCondition = DataStoreConditionConvert.toDataStoreCondition(queryCondition);
        }
        return repository.findForPage(dataStoreCondition, queryParam.buildPageable());
    }

    public DataStoreData queryOne(DataStoreDataQueryParam queryParam) {
        List<DataStoreData> list = query(queryParam);
        if(list.isEmpty()){
            return null;
        }else if (list.size()==1){
            return list.get(0);
        }else {
            throw RunException.error("查询异常，应返回1条数据，实际返回了"+list.size()+"条");
        }
    }

    @Transactional
    public void delete(DataStoreDataQueryParam queryParam) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(queryParam.getEntity());
        List<DataStoreData> list = query(queryParam);
        if(!list.isEmpty()){
            for(DataStoreData data : list){
                repository.delete(data);
            }
        }
    }

    @Transactional
    public void importData(String entityCode, List<DataStoreData> importDataList) {
        DataStoreRepository repository = dataStoreJdbcRepositoryFactory.getRepository(entityCode);
        for(DataStoreData data : importDataList){
            repository.save(data);
        }
    }
}
