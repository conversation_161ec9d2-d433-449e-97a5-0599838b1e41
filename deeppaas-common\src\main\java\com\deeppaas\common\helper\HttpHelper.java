//package com.deeppaas.common.helper;
//
//import com.deeppaas.extapi.biz.enums.BodyType;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.http.HttpMethod;
//
//import javax.net.ssl.*;
//import java.io.*;
//import java.net.ConnectException;
//import java.net.URL;
//import java.nio.charset.StandardCharsets;
//import java.security.cert.X509Certificate;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Set;
//
//public class HttpHelper {
//
//    private static final Logger log = LoggerFactory.getLogger(HttpHelper.class);
//    /**
//     * 请求超时设置-30秒
//     */
//    private static final int TIMEOUT_MILLS = 30000;
//
//    /**
//     * 发起https请求并获取结果
//     *
//     * @param requestUrl 请求地址
//     * @param httpMethod 请求方式（GET、POST、PUT、DELETE、ANY）
//     * @param bodyType   请求内容类型
//     *                   原生Form表单：application/x-www-form-urlencoded
//     *                   FormData表单：multipart/form-data
//     *                   JSON 字符串：application/json
//     *                   二进制文件：application/octet-stream
//     *                   XML：text/xml
//     * @param bodyMap    提交的数据
//     * @param headerMap  请求头数据
//     * @return String
//     */
//    public static String httpsRequest(String requestUrl, HttpMethod httpMethod,
//                                      BodyType bodyType, Map<String, Object> bodyMap, Map<String, String> headerMap) {
//
//        if (HttpMethod.GET.equals(httpMethod)) {
//            return get(requestUrl, headerMap);
//        }
//        if (BodyType.FORM_DATA.equals(bodyType)) {
//            // FormData方式
//            return postFormData(requestUrl, bodyMap, headerMap);
//        }
//
//        StringBuilder stringBuilder = new StringBuilder();
//        try {
//            // 获取连接
//            HttpsURLConnection httpUrlConn = getHttpsURLConnection(requestUrl);
//            // 设置请求头
//            setRequestHeader(httpUrlConn, headerMap);
////            httpUrlConn.setRequestProperty("Accept", contentType);
////            httpUrlConn.setRequestProperty("Authorization", "AuthorizationXX");
//            // 设置请求方式&ContentType&超时
//            httpUrlConn.setRequestMethod(httpMethod.name());
//            httpUrlConn.setRequestProperty("Content-Type", bodyType.getContentType());
//            httpUrlConn.setReadTimeout(TIMEOUT_MILLS);
//            // 当有数据需要提交时
//            if (null != bodyMap && !bodyMap.isEmpty()) {
//                OutputStream outputStream = httpUrlConn.getOutputStream();
//                // 注意编码格式，防止中文乱码
//                if (BodyType.JSON.equals(bodyType)) {
//                    // {"key1":"value1","key1":value2}
//                    String bodyParams = JsonHelper.toJson(bodyMap);
//                    // 注意编码格式，防止中文乱码
//                    outputStream.write(bodyParams.getBytes(StandardCharsets.UTF_8));
//                    outputStream.close();
//                }
//                if (BodyType.FORM_URLENCODED.equals(bodyType)) {
//                    // key1=value1&key2=value2
//                    String bodyParams = StringHelper.mapToQueryString(bodyMap);
//                    // 注意编码格式，防止中文乱码
//                    outputStream.write(bodyParams.getBytes(StandardCharsets.UTF_8));
//                    outputStream.close();
//                }
//            }
//            // 将返回的输入流转换成字符串
//            InputStream inputStream = httpUrlConn.getInputStream();
//            stringBuilder.append(getResultString(inputStream));
//            // 释放资源
//            inputStream.close();
//            httpUrlConn.disconnect();
//        } catch (ConnectException ce) {
//            log.error("Remote server connection timed out by httpsRequest");
//        } catch (Exception e) {
//            log.error("url:{}. https request error:{}", requestUrl, e);
//        }
//        return stringBuilder.toString();
//    }
//
//
//    /**
//     * 发起Get请求并获取结果
//     *
//     * @param requestUrl 请求地址
//     * @param headerMap  请求头数据
//     * @return String 操作结果
//     */
//    private static String get(String requestUrl, Map<String, String> headerMap) {
//        StringBuilder stringBuilder = new StringBuilder();
//        try {
//            // 获取连接
//            HttpsURLConnection httpUrlConn = getHttpsURLConnection(requestUrl);
//            // 设置请求头
//            setRequestHeader(httpUrlConn, headerMap);
//            // 设置请求方式&ContentType&超时
//            httpUrlConn.setRequestProperty("Content-Type", "application/json");
//            httpUrlConn.setRequestMethod(HttpMethod.GET.name());
//            httpUrlConn.setReadTimeout(TIMEOUT_MILLS);
//            // 连接
//            httpUrlConn.connect();
//            // 将返回的输入流转换成字符串
//            InputStream inputStream = httpUrlConn.getInputStream();
//            stringBuilder.append(getResultString(inputStream));
//            // 释放资源
//            inputStream.close();
//            httpUrlConn.disconnect();
//        } catch (ConnectException ce) {
//            log.error("Remote server connection timed out by httpsRequest");
//        } catch (Exception e) {
//            log.error("url:{}. https request error:{}", requestUrl, e);
//        }
//        return stringBuilder.toString();
//    }
//
//    /**
//     * 发起Post请求并获取结果
//     */
//    private static String post(String requestUrl, BodyType bodyType, Map<String, Object> bodyMap, Map<String, String> headerMap) {
//        StringBuffer buffer = new StringBuffer();
//        try {
//            // 获取连接
//            HttpsURLConnection httpUrlConn = getHttpsURLConnection(requestUrl);
//            // 设置请求头
//            setRequestHeader(httpUrlConn, headerMap);
//            // 设置请求方式&ContentType&超时
//            httpUrlConn.setRequestMethod(HttpMethod.POST.name());
//            httpUrlConn.setRequestProperty("Content-Type", bodyType.getContentType());
////            httpUrlConn.setRequestProperty("Authorization", "AuthorizationXX");
//            httpUrlConn.setReadTimeout(TIMEOUT_MILLS);
//            // 当有数据需要提交时
//            if (null != bodyMap && !bodyMap.isEmpty()) {
//                OutputStream outputStream = httpUrlConn.getOutputStream();
//                if (BodyType.JSON.equals(bodyType)) {
//                    // {"key1":"value1","key1":value2}
//                    String bodyParams = JsonHelper.toJson(bodyMap);
//                    // 注意编码格式，防止中文乱码
//                    outputStream.write(bodyParams.getBytes(StandardCharsets.UTF_8));
//                    outputStream.close();
//                }
//                if (BodyType.FORM_URLENCODED.equals(bodyType)) {
//                    // key1=value1&key2=value2
//                    String bodyParams = StringHelper.mapToQueryString(bodyMap);
//                    // 注意编码格式，防止中文乱码
//                    outputStream.write(bodyParams.getBytes(StandardCharsets.UTF_8));
//                    outputStream.close();
//                }
//            }
//            // 将返回的输入流转换成字符串
//            InputStream inputStream = httpUrlConn.getInputStream();
//            buffer.append(getResultString(inputStream));
//            // 释放资源
//            inputStream.close();
//            httpUrlConn.disconnect();
//        } catch (ConnectException ce) {
//            log.error("Remote server connection timed out by httpsRequest");
//        } catch (Exception e) {
//            log.error("url:{}. https request error:{}", requestUrl, e);
//        }
//        return buffer.toString();
//    }
//
//    private static String postFormData(String requestUrl, Map<String, Object> bodyMap, Map<String, String> headerMap) {
//        BufferedReader in = null;
//        OutputStream outputStream = null;
//        HttpsURLConnection httpUrlConn = null;
//        String rs = "";
//        // 获取连接
//        try {
//            httpUrlConn = getHttpsURLConnection(requestUrl);
//            // 设置请求头
//            setRequestHeader(httpUrlConn, headerMap);
//            // 设置请求方式&ContentType&超时
//            httpUrlConn.setRequestMethod(HttpMethod.POST.name());
//            httpUrlConn.setRequestProperty("Content-Type", BodyType.FORM_DATA.getContentType());
//            httpUrlConn.setReadTimeout(TIMEOUT_MILLS);
//
//            StringBuilder buffer = new StringBuilder();
//            outputStream = httpUrlConn.getOutputStream();
//            Set<Map.Entry<String, Object>> entries = bodyMap.entrySet();
//            for (Map.Entry<String, Object> entry : entries) {
//                // 每次都清空buffer，避免写入上次的数据
//                buffer.delete(0, buffer.length());
//                buffer.append("------footfoodapplicationrequestnetwork\r\n");
//                Object value = entry.getValue();
//                if (!(value instanceof File)) {
//                    buffer.append("Content-Disposition: form-data; name=\"").append(entry.getKey()).append("\"\r\n\r\n").append(entry.getValue()).append("\r\n");
//
//                    outputStream.write(buffer.toString().getBytes());
//                } else {
//                    File file = (File) entry.getValue();
//                    String fileName = file.getName();
//                    String fileType = "";
//                    if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
//                        fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
//                    }
//
//                    buffer.append("Content-Disposition: form-data; name=\"").append(entry.getKey()).append("\"; filename=\"").append(fileName).append("\"\r\n").append("Content-Type: ").append(fileType).append("\r\n\r\n");
//
//                    outputStream.write(buffer.toString().getBytes());
//                    DataInputStream ins = new DataInputStream(new FileInputStream(file));
//                    int bytes = 0;
//                    byte[] bufferOut = new byte[1024];
//                    while ((bytes = ins.read(bufferOut)) != -1) {
//                        outputStream.write(bufferOut, 0, bytes);
//                    }
//                    // 文件流后面添加换行，否则文件后面的一个参数会丢失
//                    outputStream.write("\r\n" .getBytes());
//                }
//            }
//            if (bodyMap.size() > 0) {
//                buffer.delete(0, buffer.length());
//                buffer.append("------footfoodapplicationrequestnetwork--\r\n");
//            }
//            outputStream.write(buffer.toString().getBytes());
//            httpUrlConn.connect();
//            if (httpUrlConn.getResponseCode() == 200) {
//                in = new BufferedReader(new InputStreamReader(httpUrlConn.getInputStream(), StandardCharsets.UTF_8));
//                String line = "";
//                while ((line = in.readLine()) != null) {
//                    rs += line;
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                outputStream.close();
//                if (in != null) {
//                    in.close();
//                }
//                if (httpUrlConn != null) {
//                    httpUrlConn.disconnect();
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//
//        return rs;
//    }
//
//    private static void setRequestHeader(HttpsURLConnection httpUrlConn, Map<String, String> headerMap) {
//        if (Objects.nonNull(headerMap) && !headerMap.isEmpty()) {
//            for (String key : headerMap.keySet()) {
//                httpUrlConn.setRequestProperty(key, headerMap.get(key));
//            }
//        }
//    }
//
//    /**
//     * 从SSLContext对象中得到SSLSocketFactory对象
//     */
//    private static SSLSocketFactory getSSLSocketFactory() {
//        try {
//            // 创建SSLContext对象，并使用我们指定的信任管理器初始化
//            TrustManager[] tm = new TrustManager[]{new X509TrustManager() {
//                @Override
//                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
//                    return null;
//                }
//
//                @Override
//                public void checkClientTrusted(X509Certificate[] certs, String authType) {
//                }
//
//                @Override
//                public void checkServerTrusted(X509Certificate[] certs, String authType) {
//                }
//            }};
//            SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
//            sslContext.init(null, tm, new java.security.SecureRandom());
//            // 从上述SSLContext对象中得到SSLSocketFactory对象
//            return sslContext.getSocketFactory();
//        } catch (Exception ex) {
//            log.error("buildSSLSocketFactory error");
//            ex.printStackTrace();
//        }
//        return null;
//    }
//
//    private static HttpsURLConnection getHttpsURLConnection(String requestUrl) throws Exception {
//        URL url = new URL(requestUrl);
//        HttpsURLConnection httpUrlConn = (HttpsURLConnection) url.openConnection();
//        httpUrlConn.setSSLSocketFactory(getSSLSocketFactory());
//        httpUrlConn.setDoOutput(true);
//        httpUrlConn.setDoInput(true);
//        httpUrlConn.setUseCaches(false);
//        return httpUrlConn;
//    }
//
//    private static String getResultString(InputStream inputStream) throws UnsupportedEncodingException, IOException {
//        InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
//        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
//
//        StringBuffer buffer = new StringBuffer();
//        String str = null;
//        while ((str = bufferedReader.readLine()) != null) {
//            buffer.append(str);
//        }
//        bufferedReader.close();
//        inputStreamReader.close();
//        return buffer.toString();
//    }
//
////    public static String htmlDecode(String text) {
////        if (text == null || "" .equals(text)) {
////            return "";
////        }
////        text = text.replaceAll("&lt;", "<");
////        text = text.replaceAll("&gt;", ">");
////        text = text.replaceAll("&nbsp;", " ");
////        text = text.replaceAll("&quot;", "\"");
////        text = text.replaceAll("<br/>", "\n");
////        text = text.replaceAll("&apos;", "\'");
////        return text;
////    }
////
////    public static String xmlDecode(String text) {
////        if (text == null || "" .equals(text)) {
////            return "";
////        }
////        text = text.replaceAll("&", "&amp;");
////        return text;
////    }
////
////    public static void setCharacterEncoding(HttpServletRequest request,
////                                            HttpServletResponse response) {
////        try {
////            request.setCharacterEncoding(StandardCharsets.UTF_8.name());
////            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
////        } catch (UnsupportedEncodingException e) {
////            e.printStackTrace();
////        }
////    }
////
////    public static void writeJson(HttpServletResponse response, String json) {
////        try {
////            response.getWriter().print(json);
////            response.getWriter().flush();
////        } catch (IOException e) {
////            e.printStackTrace();
////        }
////    }
//
//
//    public static void main(String[] args) {
//        Map<String, Object> bodyMap = new HashMap<>();
//        bodyMap.put("code", "xxxxx");
//        bodyMap.put("name", "廖高松");
//        bodyMap.put("age", 12);
//
//        try {
//            String json = JsonHelper.toJson(bodyMap);
//            System.out.println(json);
//            String formData = StringHelper.mapToQueryString(bodyMap);
//            System.out.println(formData);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//}
