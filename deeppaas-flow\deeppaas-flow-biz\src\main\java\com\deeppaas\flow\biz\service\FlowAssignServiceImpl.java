package com.deeppaas.flow.biz.service;

import com.deeppaas.account.api.client.PostClient;
import com.deeppaas.account.api.client.UserClient;
import com.deeppaas.account.api.client.model.User;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.FlowConst;
import com.deeppaas.flow.biz.dao.FlowAssignDao;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.flow.biz.entity.FlowAssignDO;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import com.deeppaas.flow.biz.enums.FlowEvent;
import com.deeppaas.flow.biz.model.AssignInfo;
import com.deeppaas.flow.biz.element.TaskNode.TaskActorGroup;
import com.deeppaas.flow.biz.element.TaskNode;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class FlowAssignServiceImpl implements FlowAssignService {
	private final FlowAssignDao flowAssignDao;
	private final UserClient userClient;
	private final PostClient postClient;
	private final FlowUserProxyService flowUserProxyService;

	public FlowAssignServiceImpl(FlowAssignDao flowAssignDao, UserClient userClient, PostClient postClient, FlowUserProxyService flowUserProxyService) {
		this.flowAssignDao = flowAssignDao;
		this.userClient = userClient;
		this.postClient = postClient;
		this.flowUserProxyService = flowUserProxyService;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void assign(Long instanceId, String nodeCode, List<String> actors, Integer priority, LocalDateTime limitedTime) {
		FlowAssignDO flowAssign = flowAssignDao.findByInstanceIdAndNodeCode(instanceId, nodeCode);
		if(flowAssign==null){
			flowAssign = new FlowAssignDO();
		}
		flowAssign.setInstanceId(instanceId);
		flowAssign.setNodeCode(nodeCode);
		flowAssign.setActors(actors);
		flowAssign.setPriority(priority);
		flowAssign.setAssignTime(LocalDateTime.now());
		flowAssign.setLimitedTime(limitedTime);
		flowAssign.setUsed(BoolHelper.INT_FALSE);
		flowAssignDao.save(flowAssign);
	}

	@Override
	public FlowAssignDO findByInstanceIdAndNodeCode(Long instanceId, String nodeCode) {
		return flowAssignDao.findByInstanceIdAndNodeCode(instanceId, nodeCode);
	}

	@Override
	@Transactional
	public void init(FlowAssignDO assign) {
		flowAssignDao.save(assign);
	}

	@Override
	public boolean doAssign(ExecuteItem executeItem, TaskNode taskNode) {
		FlowAssignDO assign = flowAssignDao.findByInstanceIdAndNodeCode(executeItem.getInstance().getId(),taskNode.getCode());
		if(assign!=null && !BoolHelper.intToBool(assign.getUsed())){
			assign.setUsed(BoolHelper.INT_TRUE);
			flowAssignDao.save(assign);
			if(CollectionUtils.isEmpty(assign.getActors())) {
				return false;
			}
		}else {
			return false;
		}
        int priority = assign.getPriority();
		List<String> actors = assign.getActors();
		FlowTaskService taskService = executeItem.getEngine().task();
		FlowEventService eventService = executeItem.getEngine().event();
		switch (taskNode.getTaskType()){
			case ANY,MANY_ANY -> {
				FlowTaskDO task = taskService.createTask(executeItem.getInstance().getId(), executeItem.getToken().getId(),
						taskNode, assign.getLimitedTime(), actors, FlowConst.TASK_STATE_OPEN,0, 0, priority);
				executeItem.setTask(task);
				eventService.fireNodeEvent(FlowEvent.NODE_TASK_OPEN, executeItem);
			}
			case MANY -> {
				for(String actor : actors){
					FlowTaskDO task = taskService.createTask(executeItem.getInstance().getId(), executeItem.getToken().getId(),
							taskNode, assign.getLimitedTime(), List.of(actor), FlowConst.TASK_STATE_OPEN,0, 0, priority);
					executeItem.setTask(task);
					eventService.fireNodeEvent(FlowEvent.NODE_TASK_OPEN, executeItem);
				}
			}
			case SORT -> {
				long waitTaskId = 0;
				int sortNo = 0;
				for(String actor : actors){
					if(waitTaskId==0){
						FlowTaskDO task = taskService.createTask(executeItem.getInstance().getId(), executeItem.getToken().getId(),
								taskNode, assign.getLimitedTime(), List.of(actor), FlowConst.TASK_STATE_OPEN, sortNo, waitTaskId, priority);
						executeItem.setTask(task);
						eventService.fireNodeEvent(FlowEvent.NODE_TASK_OPEN, executeItem);
					}else {
						FlowTaskDO task = taskService.createTask(executeItem.getInstance().getId(), executeItem.getToken().getId(),
								taskNode, assign.getLimitedTime(), List.of(actor), FlowConst.TASK_STATE_WAIT, sortNo, waitTaskId, priority);
						waitTaskId = task.getId();
					}
					sortNo++;
				}
			}
		}
		return true;
	}

	@Override
	public Collection<String> mockAssignUser(ExecuteItem executeItem, TaskNode taskNode) {
		List<TaskActorGroup> assignGroups = taskNode.getActorGroups();
		Set<String> optionActors = new HashSet<>();
		for(TaskActorGroup assignGroup : assignGroups){
			List<TaskNode.TaskActor> taskActors = assignGroup.getActors();
			for(TaskNode.TaskActor taskActor : taskActors){
				switch (taskActor.getType()){
					case DEPT -> {	// 部门
						Set<User> users = userClient.findUserByDepts(Set.of(taskActor.getCode()));
						Set<String> groupActors = users.stream().filter(user -> BoolHelper.intToBool(user.getUsable()))
								.map(User::getCode).collect(Collectors.toSet());
						optionActors.addAll(groupActors);
					}
					case POST -> {	// 岗位
						String postCode = taskActor.getCode();
						String deptFieldCode = taskActor.getDeptFieldCode();
						Integer deptLevel = taskActor.getDeptLevel();
						String deptCode = (String) executeItem.getOrderData().get(deptFieldCode);
						List<String> postActors = postClient.getManageUser(deptCode, postCode, deptLevel);
						if(!postActors.isEmpty()){
							optionActors.addAll(postActors);
						}
					}
					case USER -> {	// 用户
						optionActors.add(taskActor.getCode());
					}
					case ORG_USER -> { //组织结构中的人
						User user = userClient.findUserByDeptUserCode(taskActor.getCode());
						if(user!=null&& BoolHelper.intToBool(user.getUsable())){
							optionActors.add(user.getCode());
						}
					}
					case DEPT_FIELD -> {
						DataStoreData orderData = executeItem.getOrderData();
						List<String> deptCodes = new ArrayList<>();
						Object fieldValue = orderData.get(taskActor.getCode());
						if(fieldValue!=null){
							deptCodes.add(fieldValue.toString());
						}
						Set<User> users = userClient.findUserByDepts(deptCodes);
						optionActors.addAll(users.stream().filter(user -> BoolHelper.intToBool(user.getUsable()))
								.map(User::getCode).collect(Collectors.toSet()));
					}
					case USER_FIELD -> {
						DataStoreData orderData = executeItem.getOrderData();
						List<String> userCodes = new ArrayList<>();
						Object fieldValue = orderData.get(taskActor.getCode());
						if(fieldValue!=null){
							userCodes.add(fieldValue.toString());
						}
						optionActors.addAll(userCodes);
					}
					case ROLE -> {
						Set<String> userCodes = userClient.findUserCodeByRole(taskActor.getCode());
						optionActors.addAll(userCodes);
					}
				}
			}
		}
		return optionActors;
	}

	@Override
	public AssignInfo mockAssign(ExecuteItem executeItem, TaskNode taskNode, Collection<String> optionActors) {
		AssignInfo assignInfo = new AssignInfo();
		switch (taskNode.getTaskType()){
			case ANY -> {
				assignInfo.setAssignScope(AssignInfo.ASSIGN_SCOPE_ONE);
				break;
			}
			case MANY_ANY,MANY,SORT -> {
				if(taskNode.isActorAssign()){
					assignInfo.setAssignScope(AssignInfo.ASSIGN_SCOPE_MANY);
				}else{
					assignInfo.setAssignScope(AssignInfo.ASSIGN_SCOPE_ALL);
				}
			}
		}
		assignInfo.setLimitedTime(System.currentTimeMillis()+taskNode.getLimitedTime());
		FlowAssignService flowAssignService = executeItem.getEngine().assign();
		FlowAssignDO assign = flowAssignService.findByInstanceIdAndNodeCode(executeItem.getInstance().getId(),taskNode.getCode());

		//已使用用户
		List<String> oldActors = new ArrayList<>();
		if(assign==null){
			assignInfo.setAssigned(false);
		}else{
			if(BoolHelper.intToBool(assign.getUsed())){
				oldActors = assign.getActors();
				assignInfo.setAssigned(false);
			}else{
				assignInfo.setAssigned(true);
			}
		}
		assignInfo.setOptionActors(optionActors);
		assignInfo.setOldActors(oldActors);
		assignInfo.setNodeCode(taskNode.getCode());
		assignInfo.setNodeName(taskNode.getName());
		assignInfo.setNodeType(taskNode.getType().name());

		return assignInfo;
	}

}
