package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.datastore.biz.entity.DataStoreEntityDO;
import com.deeppaas.datastore.common.mapping.DataStoreEntityRelation;
import com.deeppaas.datastore.biz.service.DataStoreEntityRelationService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
@RestController
@RequestMapping("/api/datastore/entity/relation")
public class DataStoreEntityRelationApi {
    private final DataStoreEntityRelationService entityRelationService;

    public DataStoreEntityRelationApi(DataStoreEntityRelationService entityRelationService) {
        this.entityRelationService = entityRelationService;
    }

    /**
     * 数据模型关系-列表
     *
     * @param entityCode 当前数据模型编号
     * @return 关系数据模型列表
     */
    @GetMapping("/entities")
    public RestModel<List<DataStoreEntityDO>> list(String entityCode) {
        List<DataStoreEntityDO> list = entityRelationService.findRelationEntity(entityCode);
        return RestModel.data(list);
    }

    /**
     * 保存关系
     * @param relation
     * @return
     */
    @PostMapping("/save")
    public RestModel<Boolean> save(@RequestBody DataStoreEntityRelation relation) {
        boolean result = entityRelationService.saveRelation(relation);
        return RestModel.data(result);
    }
}
