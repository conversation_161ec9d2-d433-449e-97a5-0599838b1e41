package com.deeppaas.datastore.neo4j;

import org.neo4j.driver.Driver;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.Session;
import org.neo4j.driver.internal.InternalEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/7
 */
public class Neo4jUtils {

    public static List<Map<String,Object>> query(Driver driver, String cypher){
        Session session = driver.session();
        Result result = session.run( cypher );
        List<Map<String,Object>> results = new ArrayList<>();
        while ( result.hasNext() ) {
            Record record = result.next();
            Map<String, Object> data = record.asMap();
            Map<String, Object> recordMap = new HashMap<>();
            for (String key : data.keySet()) {
                Object object = data.get(key);
                if(object instanceof InternalEntity entity) {
                    recordMap.put(key, entity.asMap());
                }else {
                    recordMap.put(key, object);
                }
            }
            results.add(recordMap);
        }
        session.close();
        return results;
    }

}
