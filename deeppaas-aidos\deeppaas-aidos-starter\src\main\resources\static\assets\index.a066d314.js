import{r as n,C as Y,b as V,e as z,f as m,at as J,aA as re,ap as be,W as ke,_ as A,d as oe,aB as Pe,aq as Te,aC as he,aD as Ee,az as ae,aE as U,aF as pe,I as D,ac as le,aj as Oe}from"./index.689bcdcb.js";import{D as we}from"./DialogWrap.9951837c.js";import{B as L,c as Z}from"./button.34f11f85.js";import{c as Ne}from"./index.6f21ddaa.js";import{g as B,u as _e,c as Se}from"./reactNode.50ea6579.js";import{K as Re}from"./RightOutlined.ed31d8e3.js";import{T as Fe}from"./index.1f7b2316.js";import{g as Ae}from"./getRenderPropValue.1abeb751.js";var Ie=globalThis&&globalThis.__rest||function(t,e){var o={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(o[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)e.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(o[r[a]]=t[r[a]]);return o},G,$e=function(e){G={x:e.pageX,y:e.pageY},setTimeout(function(){G=null},100)};Ne()&&document.documentElement.addEventListener("click",$e,!0);var ce=function(e){var o,r=n.exports.useContext(Y),a=r.getPopupContainer,i=r.getPrefixCls,y=r.direction,l=function(c){var u=e.onCancel;u==null||u(c)},C=function(c){var u=e.onOk;u==null||u(c)},g=function(c){var u=e.okText,p=e.okType,O=e.cancelText,$=e.confirmLoading;return n.exports.createElement(n.exports.Fragment,null,n.exports.createElement(L,m({onClick:l},e.cancelButtonProps),O||c.cancelText),n.exports.createElement(L,m({},Z(p),{loading:$,onClick:C},e.okButtonProps),u||c.okText))},x=e.prefixCls,s=e.footer,k=e.visible,P=e.wrapClassName,T=e.centered,v=e.getContainer,d=e.closeIcon,f=e.focusTriggerAfterClose,h=f===void 0?!0:f,w=Ie(e,["prefixCls","footer","visible","wrapClassName","centered","getContainer","closeIcon","focusTriggerAfterClose"]),b=i("modal",x),N=i(),_=n.exports.createElement(J,{componentName:"Modal",defaultLocale:re()},g),R=n.exports.createElement("span",{className:"".concat(b,"-close-x")},d||n.exports.createElement(be,{className:"".concat(b,"-close-icon")})),F=V(P,(o={},z(o,"".concat(b,"-centered"),!!T),z(o,"".concat(b,"-wrap-rtl"),y==="rtl"),o));return n.exports.createElement(we,m({},w,{getContainer:v===void 0?a:v,prefixCls:b,wrapClassName:F,footer:s===void 0?_:s,visible:k,mousePosition:G,onClose:l,closeIcon:R,focusTriggerAfterClose:h,transitionName:B(N,"zoom",e.transitionName),maskTransitionName:B(N,"fade",e.maskTransitionName)}))};ce.defaultProps={width:520,confirmLoading:!1,visible:!1,okType:"primary"};var ie=ce;function ee(t){return!!(t&&!!t.then)}var Me=function(e){var o=n.exports.useRef(!1),r=n.exports.useRef(),a=ke(!1),i=A(a,2),y=i[0],l=i[1];n.exports.useEffect(function(){var T;if(e.autoFocus){var v=r.current;T=setTimeout(function(){return v.focus()})}return function(){T&&clearTimeout(T)}},[]);var C=function(v){var d=e.close;!ee(v)||(l(!0),v.then(function(){l(!1,!0),d.apply(void 0,arguments),o.current=!1},function(f){console.error(f),l(!1,!0),o.current=!1}))},g=function(v){var d=e.actionFn,f=e.close;if(!o.current){if(o.current=!0,!d){f();return}var h;if(e.emitEvent){if(h=d(v),e.quitOnNullishReturnValue&&!ee(h)){o.current=!1,f(v);return}}else if(d.length)h=d(f),o.current=!1;else if(h=d(),!h){f();return}C(h)}},x=e.type,s=e.children,k=e.prefixCls,P=e.buttonProps;return n.exports.createElement(L,m({},Z(x),{onClick:g,loading:y,prefixCls:k},P,{ref:r}),s)},X=Me,Be=function(e){var o=e.icon,r=e.onCancel,a=e.onOk,i=e.close,y=e.zIndex,l=e.afterClose,C=e.visible,g=e.keyboard,x=e.centered,s=e.getContainer,k=e.maskStyle,P=e.okText,T=e.okButtonProps,v=e.cancelText,d=e.cancelButtonProps,f=e.direction,h=e.prefixCls,w=e.wrapClassName,b=e.rootPrefixCls,N=e.iconPrefixCls,_=e.bodyStyle,R=e.closable,F=R===void 0?!1:R,E=e.closeIcon,c=e.modalRender,u=e.focusTriggerAfterClose;oe(!(typeof o=="string"&&o.length>2),"Modal","`icon` is using ReactNode instead of string naming in v4. Please check `".concat(o,"` at https://ant.design/components/icon"));var p=e.okType||"primary",O="".concat(h,"-confirm"),$="okCancel"in e?e.okCancel:!0,K=e.width||416,H=e.style||{},W=e.mask===void 0?!0:e.mask,q=e.maskClosable===void 0?!1:e.maskClosable,M=e.autoFocusButton===null?!1:e.autoFocusButton||"ok",Q=V(O,"".concat(O,"-").concat(e.type),z({},"".concat(O,"-rtl"),f==="rtl"),e.className),ye=$&&n.exports.createElement(X,{actionFn:r,close:i,autoFocus:M==="cancel",buttonProps:d,prefixCls:"".concat(b,"-btn")},v);return n.exports.createElement(Pe,{prefixCls:b,iconPrefixCls:N,direction:f},n.exports.createElement(ie,{prefixCls:h,className:Q,wrapClassName:V(z({},"".concat(O,"-centered"),!!e.centered),w),onCancel:function(){return i({triggerCancel:!0})},visible:C,title:"",footer:"",transitionName:B(b,"zoom",e.transitionName),maskTransitionName:B(b,"fade",e.maskTransitionName),mask:W,maskClosable:q,maskStyle:k,style:H,bodyStyle:_,width:K,zIndex:y,afterClose:l,keyboard:g,centered:x,getContainer:s,closable:F,closeIcon:E,modalRender:c,focusTriggerAfterClose:u},n.exports.createElement("div",{className:"".concat(O,"-body-wrapper")},n.exports.createElement("div",{className:"".concat(O,"-body")},o,e.title===void 0?null:n.exports.createElement("span",{className:"".concat(O,"-title")},e.title),n.exports.createElement("div",{className:"".concat(O,"-content")},e.content)),n.exports.createElement("div",{className:"".concat(O,"-btns")},ye,n.exports.createElement(X,{type:p,actionFn:a,close:i,autoFocus:M==="ok",buttonProps:T,prefixCls:"".concat(b,"-btn")},P)))))},se=Be,je=[],I=je,De=globalThis&&globalThis.__rest||function(t,e){var o={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(o[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)e.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(o[r[a]]=t[r[a]]);return o},ue="";function Ve(){return ue}function j(t){var e=document.createDocumentFragment(),o=m(m({},t),{close:i,visible:!0});function r(){U.exports.unmountComponentAtNode(e);for(var l=arguments.length,C=new Array(l),g=0;g<l;g++)C[g]=arguments[g];var x=C.some(function(P){return P&&P.triggerCancel});t.onCancel&&x&&t.onCancel.apply(t,C);for(var s=0;s<I.length;s++){var k=I[s];if(k===i){I.splice(s,1);break}}}function a(l){var C=l.okText,g=l.cancelText,x=l.prefixCls,s=De(l,["okText","cancelText","prefixCls"]);setTimeout(function(){var k=re(),P=pe(),T=P.getPrefixCls,v=P.getIconPrefixCls,d=T(void 0,Ve()),f=x||"".concat(d,"-modal"),h=v();U.exports.render(n.exports.createElement(se,m({},s,{prefixCls:f,rootPrefixCls:d,iconPrefixCls:h,okText:C||(s.okCancel?k.okText:k.justOkText),cancelText:g||k.cancelText})),e)})}function i(){for(var l=this,C=arguments.length,g=new Array(C),x=0;x<C;x++)g[x]=arguments[x];o=m(m({},o),{visible:!1,afterClose:function(){typeof t.afterClose=="function"&&t.afterClose(),r.apply(l,g)}}),a(o)}function y(l){typeof l=="function"?o=l(o):o=m(m({},o),l),a(o)}return a(o),I.push(i),{destroy:i,update:y}}function fe(t){return m(m({icon:n.exports.createElement(ae,null),okCancel:!1},t),{type:"warning"})}function me(t){return m(m({icon:n.exports.createElement(Te,null),okCancel:!1},t),{type:"info"})}function de(t){return m(m({icon:n.exports.createElement(he,null),okCancel:!1},t),{type:"success"})}function ve(t){return m(m({icon:n.exports.createElement(Ee,null),okCancel:!1},t),{type:"error"})}function Ce(t){return m(m({icon:n.exports.createElement(ae,null),okCancel:!0},t),{type:"confirm"})}function ze(t){var e=t.rootPrefixCls;oe(!1,"Modal","Modal.config is deprecated. Please use ConfigProvider.config instead."),ue=e}function Le(){var t=n.exports.useState([]),e=A(t,2),o=e[0],r=e[1],a=n.exports.useCallback(function(i){return r(function(y){return[].concat(D(y),[i])}),function(){r(function(y){return y.filter(function(l){return l!==i})})}},[]);return[o,a]}var Ke=function(e,o){var r=e.afterClose,a=e.config,i=n.exports.useState(!0),y=A(i,2),l=y[0],C=y[1],g=n.exports.useState(a),x=A(g,2),s=x[0],k=x[1],P=n.exports.useContext(Y),T=P.direction,v=P.getPrefixCls,d=v("modal"),f=v(),h=function(){C(!1);for(var b=arguments.length,N=new Array(b),_=0;_<b;_++)N[_]=arguments[_];var R=N.some(function(F){return F&&F.triggerCancel});s.onCancel&&R&&s.onCancel()};return n.exports.useImperativeHandle(o,function(){return{destroy:h,update:function(b){k(function(N){return m(m({},N),b)})}}}),n.exports.createElement(J,{componentName:"Modal",defaultLocale:le.Modal},function(w){return n.exports.createElement(se,m({prefixCls:d,rootPrefixCls:f},s,{close:h,visible:l,afterClose:r,okText:s.okText||(s.okCancel?w.okText:w.justOkText),direction:T,cancelText:s.cancelText||w.cancelText}))})},He=n.exports.forwardRef(Ke),te=0,We=n.exports.memo(n.exports.forwardRef(function(t,e){var o=Le(),r=A(o,2),a=r[0],i=r[1];return n.exports.useImperativeHandle(e,function(){return{patchElement:i}},[]),n.exports.createElement(n.exports.Fragment,null,a)}));function qe(){var t=n.exports.useRef(null),e=n.exports.useState([]),o=A(e,2),r=o[0],a=o[1];n.exports.useEffect(function(){if(r.length){var l=D(r);l.forEach(function(C){C()}),a([])}},[r]);var i=n.exports.useCallback(function(l){return function(g){var x;te+=1;var s=n.exports.createRef(),k,P=n.exports.createElement(He,{key:"modal-".concat(te),config:l(g),ref:s,afterClose:function(){k()}});return k=(x=t.current)===null||x===void 0?void 0:x.patchElement(P),{destroy:function(){function v(){var d;(d=s.current)===null||d===void 0||d.destroy()}s.current?v():a(function(d){return[].concat(D(d),[v])})},update:function(v){function d(){var f;(f=s.current)===null||f===void 0||f.update(v)}s.current?d():a(function(f){return[].concat(D(f),[d])})}}}},[]),y=n.exports.useMemo(function(){return{info:i(me),success:i(de),error:i(ve),warning:i(fe),confirm:i(Ce)}},[]);return[y,n.exports.createElement(We,{ref:t})]}function xe(t){return j(fe(t))}var S=ie;S.useModal=qe;S.info=function(e){return j(me(e))};S.success=function(e){return j(de(e))};S.error=function(e){return j(ve(e))};S.warning=xe;S.warn=xe;S.confirm=function(e){return j(Ce(e))};S.destroyAll=function(){for(;I.length;){var e=I.pop();e&&e()}};S.config=ze;var rt=S,ne=globalThis,Qe=globalThis&&globalThis.__rest||function(t,e){var o={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(o[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)e.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(o[r[a]]=t[r[a]]);return o},ge=n.exports.forwardRef(function(t,e){var o=n.exports.useContext(Y),r=o.getPrefixCls,a=_e(!1,{value:t.visible,defaultValue:t.defaultVisible}),i=A(a,2),y=i[0],l=i[1],C=function(c,u){var p;l(c,!0),(p=t.onVisibleChange)===null||p===void 0||p.call(t,c,u)},g=function(c){C(!1,c)},x=function(c){var u;return(u=t.onConfirm)===null||u===void 0?void 0:u.call(ne,c)},s=function(c){var u;C(!1,c),(u=t.onCancel)===null||u===void 0||u.call(ne,c)},k=function(c){c.keyCode===Re.ESC&&y&&C(!1,c)},P=function(c){var u=t.disabled;u||C(c)},T=function(c,u){var p=t.okButtonProps,O=t.cancelButtonProps,$=t.title,K=t.cancelText,H=t.okText,W=t.okType,q=t.icon,M=t.showCancel,Q=M===void 0?!0:M;return n.exports.createElement("div",{className:"".concat(c,"-inner-content")},n.exports.createElement("div",{className:"".concat(c,"-message")},q,n.exports.createElement("div",{className:"".concat(c,"-message-title")},Ae($))),n.exports.createElement("div",{className:"".concat(c,"-buttons")},Q&&n.exports.createElement(L,m({onClick:s,size:"small"},O),K||u.cancelText),n.exports.createElement(X,{buttonProps:m(m({size:"small"},Z(W)),p),actionFn:x,close:g,prefixCls:r("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},H||u.okText)))},v=t.prefixCls,d=t.placement,f=t.children,h=t.overlayClassName,w=Qe(t,["prefixCls","placement","children","overlayClassName"]),b=r("popover",v),N=r("popconfirm",v),_=V(N,h),R=n.exports.createElement(J,{componentName:"Popconfirm",defaultLocale:le.Popconfirm},function(E){return T(b,E)}),F=r();return n.exports.createElement(Fe,m({},w,{prefixCls:b,placement:d,onVisibleChange:P,visible:y,overlay:R,overlayClassName:_,ref:e,transitionName:B(F,"zoom-big",t.transitionName)}),Se(f,{onKeyDown:function(c){var u,p;n.exports.isValidElement(f)&&((p=f==null?void 0:(u=f.props).onKeyDown)===null||p===void 0||p.call(u,c)),k(c)}}))});ge.defaultProps={placement:"top",trigger:"click",okType:"primary",icon:n.exports.createElement(Oe,null),disabled:!1};var ot=ge;export{rt as M,ot as P};
