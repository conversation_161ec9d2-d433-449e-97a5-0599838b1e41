package com.deeppaas.common.data.ddl;

import lombok.Data;

import java.sql.JDBCType;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
@Data
public class ColumnData {
    private String name;
    private JDBCType type;
    private String definition;
    private String comment = "";
    private int length = 255;
    private int digits = 0;
    private boolean unique = false;
    private boolean nullable = true;
    private boolean id;

    public static ColumnData of(String name, boolean isId, JDBCType type) {
        ColumnData columnData = new ColumnData();
        columnData.setId(isId);
        columnData.setName(name);
        columnData.setType(type);
        return columnData;
    }

    public static ColumnData of(String name, boolean isId, ColumnDDL columnDDL) {
        ColumnData columnData = new ColumnData();
        columnData.setId(isId);
        columnData.setName(name);
        columnData.setType(columnDDL.type());
        columnData.setDefinition(columnDDL.definition());
        columnData.setComment(columnDDL.comment());
        columnData.setLength(columnDDL.length());
        columnData.setDigits(columnDDL.digits());
        columnData.setUnique(columnDDL.unique());
        columnData.setNullable(columnDDL.nullable());
        return columnData;
    }
}
