package com.deeppaas.common.script;

import com.deeppaas.common.exception.RunException;
import org.mvel2.MVEL;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mvel2.util.ParseTools.isWhitespace;

/**
 * @Description 脚本工具
 * <AUTHOR>
 * @Date 2019-06-05
 */
public class ScriptUtils {
    private static final List<Character> opts = List.of('(', ')', '+', '-', '*', '/');

    /**
     * @param expression
     * @param vars
     * @return java.lang.Object
     * @Description: 执行脚本
     * <AUTHOR>
     * @date 2019-05-29
     */
    public static Object eval(String expression, Map<String, Object> vars) {
        if (!StringUtils.hasText(expression)) {
            throw RunException.error("脚本表达式不能为空");
        }
        try {
            return MVEL.eval(expression, vars);
        } catch (Exception e) {
            e.printStackTrace();
            throw RunException.error("表达式执行失败，异常：" + e.getMessage());
        }
    }

    /**
     * @param expression
     * @return java.util.List<java.lang.String>
     * @Description: 获取数学计算表达式中的变量名
     * <AUTHOR>
     * @date 2019-06-05
     */
    public static List<String> getMathVarNames(String expression) {
        List<String> varNames = new ArrayList<>();
        if (expression != null && expression.length() != 0) {
            char[] echars = expression.toCharArray();
            StringBuffer varBuffer = new StringBuffer();
            for (char echar : echars) {
                if (opts.contains(Character.valueOf(echar))
                        || isWhitespace(echar)) {
                    if (varBuffer.length() > 0) {
                        varNames.add(varBuffer.toString());
                    }
                    varBuffer.setLength(0);
                } else {
                    varBuffer.append(echar);
                }
            }
            if (varBuffer.length() > 0) {
                varNames.add(varBuffer.toString());
            }
        }
        return varNames;
    }


    /**
     * @param expression 表达式
     * @param prefix     前缀
     * @return java.util.List<java.lang.String>
     * @Description: 获取数学计算表达式中的变量名
     * <AUTHOR>
     * @date 2019-06-05
     */
    public static List<String> getMathVarNames(String expression, String prefix) {
        if (!StringUtils.hasText(prefix)) {
            throw RunException.error("前缀不能为空");
        }
        List<String> varNames = new ArrayList<>();
        if (expression != null && expression.length() != 0) {
            char[] echars = expression.toCharArray();
            StringBuffer varBuffer = new StringBuffer();
            for (char echar : echars) {
                if (opts.contains(Character.valueOf(echar))
                        || isWhitespace(echar)) {
                    if (varBuffer.length() > 0) {
                        if (varBuffer.toString().startsWith(prefix)) {
                            varNames.add(varBuffer.toString());
                        }
                    }
                    varBuffer.setLength(0);
                } else {
                    varBuffer.append(echar);
                }
            }
            if (varBuffer.length() > 0) {
                if (varBuffer.toString().startsWith(prefix)) {
                    varNames.add(varBuffer.toString());
                }
            }
        }
        return varNames;
    }
}
