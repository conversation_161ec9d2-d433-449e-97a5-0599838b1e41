package com.deeppaas.flow.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowDefineDO;

import java.util.List;

public interface FlowDefineDao extends BaseJdbcDao<FlowDefineDO, String> {

	FlowDefineDO findByCodeAndState(String code, Integer state);

	FlowDefineDO findByCodeAndVersion(String code, Integer version);

	List<FlowDefineDO> findByState(Integer status);

    List<FlowDefineDO> findByCode(String code);

    List<FlowDefineDO> findByEntityCode(String entityCode);

    FlowDefineDO findByEntityCodeAndCodeAndState(String entityCode, String code, int state);
}
