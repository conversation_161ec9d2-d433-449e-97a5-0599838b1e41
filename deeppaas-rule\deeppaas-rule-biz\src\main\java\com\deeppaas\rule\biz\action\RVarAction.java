package com.deeppaas.rule.biz.action;

import com.deeppaas.common.data.DataClass;
import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;

/**
 * 定义变量动作
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
@EqualsAndHashCode
public class RVarAction extends RAction {

    /**
     * 赋值绑定
     */
    private RDataBind dataBind;

    public Object execute(RuleContext ruleContext){
        Object varValue = dataBind.getBindData(ruleContext);
        ruleContext.addVar(getVarName(), varValue);
        return super.returnOrNext(varValue, ruleContext);
    }

}
