package com.deeppaas.configuration.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProcessConfigEnum {

    IMAGESCAN("高拍仪图像处理", "hc_image_process", "rpa_chk_out02", 0),
    IMAGECHECK("图像质检", "image_quality", "rpa_chk_out05", 0),
    //TODO code错的
    CONTENTEXTRACTION("内容提取", "get_contentext", "", 0),
    IMAGEIDENTIFICATION("图像鉴别", "image_identification", "rpa_chk_out03", 1),
    OPENURL("网页打开", "open_url", "", 0),
    MESSAGE("消息同步", "message_sync", "rpa_chk_out06", 1),
    GETAPIPARAM("获取API参数", "get_api_params", "", 0),
    INTERFACEPROMPT("界面提示", "interface_prompt", "", 0);

    private String valueName;
    private String code;
    private String resultCode;
    private Integer isImage;


}
