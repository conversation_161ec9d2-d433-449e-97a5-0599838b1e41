package com.deeppaas.flow.biz.service;

import java.time.LocalDateTime;
import java.util.List;

import com.deeppaas.common.model.PageSearch;
import com.deeppaas.flow.biz.FlowAction;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import org.springframework.data.domain.Page;

/**
 * @ClassName: FlowTaskDOService 
 * @Description: 流程任务服务接口
 * <AUTHOR>
 */
public interface FlowTaskService {
	/**
	 * 根据ID获取任务实体
	 * @param taskId
	 * @return
	 * <AUTHOR>
	 */
	FlowTaskDO getTask(Long taskId);
	/**
	 * 打开任务
	 * @param task
	 * @return
	 * <AUTHOR>
	 * @date 2017年9月4日
	 */
	FlowTaskDO open(FlowTaskDO task);
	/**
	 * 关闭任务
	 * @param task
	 * @param userId
	 * @param action
	 * @param comment
	 * @return
	 * <AUTHOR>
	 * @date 2017年9月4日
	 */
	FlowTaskDO close(FlowTaskDO task, String userId, FlowAction action, String comment);
	/**
	 * 获取流程实例对应的未关闭的任务集合
	 * @param instanceId
	 * @return
	 * <AUTHOR>
	 */
	List<FlowTaskDO> findUnclosedTaskByInstanceId(Long instanceId);
	/**
	 * 获取流程实例和待办用户对应的打开的任务
	 * @param flowId
	 * @param userId
	 * @return
	 * <AUTHOR>
	 */
	Long findOpenTaskIdByInstanceIdForUser(Long flowId, String userId);

	/**
	 * 创建任务
	 * @param flowInstanceId
	 * @param tokenId
	 * @param flowNode
	 * @param assignLimitedTime
	 * @param todoUserIds
	 * @param taskState
	 * @param sortNo
	 * @return
	 */
	FlowTaskDO createTask(Long flowInstanceId, Long tokenId, FlowNode flowNode, LocalDateTime assignLimitedTime, List<String> todoUserIds, int taskState, int sortNo, long waitTaskId,int priority);

	/**
	 * 删除流程实例所有任务
	 * @param instanceId
	 * <AUTHOR>
	 */
    void deleteByInstance(Long instanceId);

    /**
     * 根据流程和环节获取任务
     * @param instanceId
     * @param nodeCode
     * @return
     * <AUTHOR>
     */
    List<FlowTaskDO> findByInstanceIdAndNodeCode(Long instanceId, String nodeCode);

	/**
	 * 查询流程实例打开任务
	 * @param instanceId
	 * @param userCode
	 * @return
	 */
    List<FlowTaskDO> findOpenTaskByInstanceIdForUser(Long instanceId, String userCode);

	/**
	 * 查询令牌为完成任务
	 * @param tokenIds
	 * @return
	 */
	List<FlowTaskDO> findUnclosedTaskByTokenIds(List<Long> tokenIds);

    List<FlowTaskDO> findOpenTaskByInstanceId(Long instanceId);

	/**
	 * 流程实例所有任务列表
	 * @param instanceId 流程实例ID
	 * @return 任务列表
	 */
	List<FlowTaskDO> findByInstanceId(Long instanceId);

	/**
	 * 任务分页查询
	 * @param pageSearch
	 * @return
	 */
    Page<FlowTaskDO> findForPage(PageSearch pageSearch);
}
