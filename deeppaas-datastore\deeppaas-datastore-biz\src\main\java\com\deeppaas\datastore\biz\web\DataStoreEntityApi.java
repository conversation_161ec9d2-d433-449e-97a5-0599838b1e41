package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.datastore.biz.entity.DataStoreEntityDO;
import com.deeppaas.datastore.biz.entity.DataStoreEntityFieldDO;
import com.deeppaas.datastore.biz.entity.DataStoreEntityFieldOptionDO;
import com.deeppaas.datastore.biz.enums.FieldIllegalCode;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.biz.service.DataStoreEntityService;
import com.deeppaas.datastore.biz.web.vo.DataStoreEntityVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@RestController
@RequestMapping("/api/datastore/entity")
public class DataStoreEntityApi {
    private DataStoreEntityService dataStoreEntityService;

    public DataStoreEntityApi(DataStoreEntityService dataStoreEntityService) {
        this.dataStoreEntityService = dataStoreEntityService;
    }

    /**
     * 获取指定ID数据模型
     *
     * @param id
     * @return
     */
    @GetMapping("/get")
    public RestModel<DataStoreEntityDO> get(String id) {
        DataStoreEntityDO entityDO = dataStoreEntityService.findById(id);
        return RestModel.data(entityDO);
    }

    /**
     * 获取指定编号数据模型
     *
     * @param code
     * @return
     */
    @GetMapping("/getByCode")
    public RestModel<DataStoreEntityDO> getByCode(String code) {
        DataStoreEntityDO entityDO = dataStoreEntityService.findByCode(code);
        return RestModel.data(entityDO);
    }

    /**
     * 数据模型-列表
     *
     * @param sourceId 数据源ID
     * @return 数据模型列表
     */
    @GetMapping("/list")
    public RestModel<List<DataStoreEntityVO>> list(String sourceId) {
        List<DataStoreEntityVO> list = dataStoreEntityService.list(sourceId);
        return RestModel.data(list);
    }

    /**
     * 数据模型-列表（模块）
     *
     * @param appCode
     * @return
     */
    @GetMapping("/listByApp")
    public RestModel<List<DataStoreEntityDO>> listByApp(@RequestParam String appCode) {
        List<DataStoreEntityDO> list = dataStoreEntityService.listByApp(appCode);
        return RestModel.data(list);
    }

    /**
     * 数据模型-分页
     *
     * @param pageSearch 分页查询参数
     * @return 数据模型列表
     */
    @GetMapping("/page")
    public RestModel<PageData<DataStoreEntityDO>> page(PageSearch pageSearch) {
        PageData<DataStoreEntityDO> pageData = dataStoreEntityService.page(pageSearch);
        return RestModel.data(pageData);
    }

    /**
     * 数据模型-按照名称和编号模糊匹配分页
     *
     * @param pageSearch 分页查询参数
     * @param codeOrName 名称或者编号
     * @return 数据模型列表
     */
    @GetMapping("/fuzzy/page")
    public RestModel<PageData<DataStoreEntityVO>> pageFuzzy(PageSearch pageSearch, String codeOrName) {
        PageData<DataStoreEntityVO> pageData = dataStoreEntityService.pageFuzzy(pageSearch, codeOrName);
        return RestModel.data(pageData);
    }

    /**
     * 数据模型-新增
     *
     * @param param 参数
     * @return 表信息
     */
    @PostMapping("/add")
    public RestModel<DataStoreEntityDO> add(@RequestBody DataStoreEntityDO param) {
        // 参数检查
        param.check();
        // 业务操作
        DataStoreEntityDO setDO = dataStoreEntityService.add(param);
        return RestModel.data(setDO);
    }

    /**
     * 数据模型重命名
     *
     * @param id   数据模型ID
     * @param name 数据模型名称
     * @return 操作结果
     */
    @PostMapping("/rename")
    public RestModel<Boolean> rename(@BodyParam String id, @BodyParam String name) {
        dataStoreEntityService.modify(id, name);
        return RestModel.data(true);
    }

    /**
     * 数据模型-删除
     *
     * @param id 模型主键ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    public RestModel<Boolean> delete(@BodyParam String id) {
        // 业务操作
        DataStoreEntityDO entityDO = dataStoreEntityService.findById(id);
        if (entityDO != null) {
            dataStoreEntityService.delete(entityDO);
        }
        return RestModel.data(true);
    }

    /**
     * 数据模型字段-列表
     *
     * @param entityCode 数据模型编号
     * @return 字段列表
     */
    @GetMapping("/field/list")
    public RestModel<List<DataStoreEntityFieldDTO>> listAllField(@RequestParam String entityCode) {
        List<DataStoreEntityFieldDTO> list = dataStoreEntityService.findFieldDTOByEntityCode(entityCode);
        return RestModel.data(list);
    }

    /**
     * 数据模型关联的字段-列表
     *
     * @param entityCode 数据模型编号
     * @param joinCode   关联关系编号
     * @return 字段列表
     */
    @GetMapping("/field/join")
    public RestModel<List<DataStoreEntityFieldDTO>> listJoinField(@RequestParam String entityCode, @RequestParam String joinCode) {
        List<DataStoreEntityFieldDTO> list = dataStoreEntityService.listJoinField(entityCode, joinCode);
        return RestModel.data(list);
    }

    /**
     * 获取数据集字段
     *
     * @param id 字段ID
     * @return 数据集字段
     */
    @GetMapping("/field/get")
    public RestModel<DataStoreEntityFieldDTO> getField(@RequestParam String id) {
        DataStoreEntityFieldDTO fieldDTO = dataStoreEntityService.findFieldById(id);
        return RestModel.data(fieldDTO);
    }

    /**
     * 数据模型字段-新增
     *
     * @param param 参数
     * @return 字段信息
     */
    @PostMapping("/field/add")
    public RestModel<DataStoreEntityFieldDTO> addField(@RequestBody DataStoreEntityFieldDO param) {
        // 字段编号检查
        FieldIllegalCode.check(param.getCode());
        // 业务处理
        DataStoreEntityFieldDTO fieldDTO = dataStoreEntityService.addField(param);
        return RestModel.data(fieldDTO);
    }

    /**
     * 数据模型字段-修改
     *
     * @param param 参数
     * @return 字段信息
     */
    @PostMapping("/field/modify")
    public RestModel<DataStoreEntityFieldDTO> modifyField(@RequestBody DataStoreEntityFieldDO param) {
        DataStoreEntityFieldDTO fieldDTO = dataStoreEntityService.modifyField(param);
        return RestModel.data(fieldDTO);
    }

    /**
     * 数据模型字段-删除
     *
     * @param fieldId 字段ID
     * @return 字段信息
     */
    @PostMapping("/field/delete")
    public RestModel<Boolean> deleteField(@BodyParam String fieldId) {
        dataStoreEntityService.deleteField(fieldId);
        return RestModel.data(true);
    }


    /**
     * 获取模型字段备选项配置信息
     *
     * @param fieldId 模型字段ID
     * @return 模型字段备选项信息
     */
    @GetMapping("/field/option/get")
    public RestModel<DataStoreEntityFieldOptionDO> getFieldOption(@RequestParam String fieldId) {
        DataStoreEntityFieldOptionDO fieldOptionDO = dataStoreEntityService.getFieldOption(fieldId);
        return RestModel.data(fieldOptionDO);
    }

    /**
     * 保存模型字段备选项
     *
     * @param param 模型字段参数
     * @return 模型字段
     */
    @PostMapping("/field/option/save")
    public RestModel<DataStoreEntityFieldOptionDO> saveFieldOption(@RequestBody DataStoreEntityFieldOptionDO param) {
        DataStoreEntityFieldOptionDO fieldOptionDO = dataStoreEntityService.saveFieldOption(param);
        return RestModel.data(fieldOptionDO);
    }

    /**
     * 删除模型字段备选项
     *
     * @param fieldOptionId 模型字段备选项主键ID
     * @return 操作结果
     */
    @PostMapping("/field/option/delete")
    public RestModel<Boolean> deleteFieldOption(@BodyParam String fieldOptionId) {
        dataStoreEntityService.deleteFieldOption(fieldOptionId);
        return RestModel.data(true);
    }
}
