package com.deeppaas.msg.biz.web;

import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.msg.biz.entity.UserMsgBoxDO;
import com.deeppaas.msg.biz.entity.UserMsgTemplateDO;
import com.deeppaas.msg.biz.entity.UserMsgTemplateParamDO;
import com.deeppaas.msg.biz.service.UserMsgTemplateService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
@RestController
@RequestMapping("/api/userMsgTemplate")
public class UserMsgTemplateApi {
    private final UserMsgTemplateService templateService;

    public UserMsgTemplateApi(UserMsgTemplateService templateService) {
        this.templateService = templateService;
    }

    /**
     * 获取消息模版
     * @return
     */
    @GetMapping("/get")
    public RestModel<UserMsgTemplateDO> getTemplate(String id) {
        UserMsgTemplateDO templateDO = templateService.getById(id);
        return RestModel.data(templateDO);
    }
    /**
     * 获取所有消息模版
     * @return
     */
    @GetMapping("/list")
    public RestModel<List<UserMsgTemplateDO>> templateList() {
        List<UserMsgTemplateDO> list = templateService.findAll();
        return RestModel.data(list);
    }
    /**
     * 获取所有消息模版
     * @return
     */
    @GetMapping("/page")
    public RestModel<PageData<UserMsgTemplateDO>> templatePage(PageSearch pageSearch) {
        Page<UserMsgTemplateDO> page = templateService.findByPage(pageSearch);
        return RestModel.page(page);
    }
    /**
     * 保存消息模版
     * @return
     */
    @PostMapping("/save")
    public RestModel<String> saveTemplate(@RequestBody UserMsgTemplateDO templateDO) {
        templateDO = templateService.save(templateDO);
        return RestModel.data(templateDO.getId());
    }

    /**
     * 删除消息模版
     * @return
     */
    @PostMapping("/delete")
    public RestModel<Boolean> deleteTemplate(@BodyParam String id) {
        templateService.delete(id);
        return RestModel.data(true);
    }


    /**
     * 获取消息模版参数
     * @return
     */
    @GetMapping("/param/get")
    public RestModel<UserMsgTemplateParamDO> getParam(String id) {
        UserMsgTemplateParamDO paramDO = templateService.getParamById(id);
        return RestModel.data(paramDO);
    }

    /**
     * 获取所有消息模模版参数
     * @return
     */
    @GetMapping("/param/list")
    public RestModel<List<UserMsgTemplateParamDO>> listParam(String templateId) {
        List<UserMsgTemplateParamDO> list = templateService.findParamByTemplateId(templateId);
        return RestModel.data(list);
    }

    /**
     * 保存消息模版参数
     * @return
     */
    @PostMapping("/param/save")
    public RestModel<String> saveParam(@RequestBody UserMsgTemplateParamDO paramDO) {
        paramDO = templateService.saveParam(paramDO);
        return RestModel.data(paramDO.getId());
    }

    /**
     * 删除消息模版参数
     * @return
     */
    @PostMapping("/param/delete")
    public RestModel<Boolean> deleteParam(@BodyParam String id) {
        templateService.deleteParam(id);
        return RestModel.data(true);
    }
}
