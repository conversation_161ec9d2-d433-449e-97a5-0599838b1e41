package com.deeppaas.rule.biz.action;

import com.deeppaas.common.model.PageData;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.deeppaas.rule.biz.model.RDataQuerySortOrder;
import com.deeppaas.rule.biz.model.RDataQueryPermit;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

/**
 * <AUTHOR>
 * @date 2022/6/18
 */
public class RDataEntityPageAction extends RDataEntityListAction{
    public RDataEntityPageAction(String dataEntity, RDataQueryPermit[] permits, RConditionModel defaultCondition,
                                 RDataBind pageNoBind, RDataBind pageSizeBind, RDataQuerySortOrder[] sortOrders) {
        super(dataEntity, permits, defaultCondition, pageNoBind, pageSizeBind, sortOrders);

    }

    @Override
    public Object execute(RuleContext ruleContext) {
        DataStoreCondition queryCondition = getQueryCondition(ruleContext);
        Pageable pageable = getQueryPageParam(ruleContext);
        PageData<DataStoreData> pageData = ruleContext.getRuleEngine()
                .dataStoreDataClient().queryForPage(dataEntity, queryCondition, pageable);
        return super.returnOrNext(pageData, ruleContext);
    }

    private Pageable getQueryPageParam(RuleContext ruleContext) {
        Integer pageNo = (Integer) pageNoBind.getBindData(ruleContext);
        Integer pageSize = (Integer) pageSizeBind.getBindData(ruleContext);
        if (pageNo == null || pageNo == 0) {
            pageNo = 1;
        }
        if (pageNo == null || pageSize == 0) {
            pageSize = 10;
        }
        Sort sort = super.getSort();
        Pageable pageParam = PageRequest.of(pageNo, pageSize, sort);
        return pageParam;
    }

}
