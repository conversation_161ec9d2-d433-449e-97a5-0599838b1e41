package com.deeppaas.flow.biz.element;

import java.util.ArrayList;
import java.util.List;

import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.MockExecuteItem;
import com.deeppaas.flow.biz.enums.FlowEvent;
import com.deeppaas.flow.biz.enums.FlowInstanceState;
import com.deeppaas.flow.biz.enums.NodeType;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.model.AssignInfo;

/**
 * 结束节点
 * <AUTHOR>
 *
 */
public class EndNode extends FlowPageNode {


	public EndNode(String code, String name, NodeType type, String remark, String page, List<FlowEventRule> eventRules) {
		super(code, name, type, remark, page, eventRules);
	}

	@Override
	public void execute(ExecuteItem executeItem) {
		FlowInstanceDO instance = executeItem.getInstance();
		executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_EXIT, executeItem);
		/**
		 * 结束当前流程实例
		 */
		executeItem.getEngine().instance().close(instance, FlowInstanceState.FINISH);
		executeItem.getEngine().event().fireInsEvent(FlowEvent.INSTANCE_FINISH, executeItem);
	}
	@Override
	public List<AssignInfo> mockExecute(MockExecuteItem execution) {
        return List.of(AssignInfo.instanceAutoNode(this));
	}

	@Override
	public List<String> check() {
		List<String> errors = new ArrayList<>();
		if(inLines.isEmpty()){
			errors.add("环节["+getCode()+"]没有进入连接线");
		}
		if(!outLines.isEmpty()){
			errors.add("环节["+getCode()+"]不应有流转连接线");
		}
		return errors;
	}
}
