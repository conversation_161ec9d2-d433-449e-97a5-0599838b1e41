package com.deeppaas.license.single.filter;

import com.deeppaas.license.api.filter.LicenseFilter;
import com.deeppaas.license.single.SingleLicenseHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/11/2
 */
@Component
public class SingleLicenseFilter implements LicenseFilter {
    private static final Logger logger = LoggerFactory.getLogger(LicenseFilter.class);
    /**
     * license地址
     **/
    private static final String licenseUrl = "/license";

    /**
     * 预处理
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        if (SingleLicenseHelper.localCheck() || request.getRequestURI().startsWith(licenseUrl)) {
            return true;
        }
        response.sendRedirect(licenseUrl);
        return false;
    }
}
