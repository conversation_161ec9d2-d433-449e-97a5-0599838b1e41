package com.deeppaas.rule.biz.action;

import com.deeppaas.datastore.api.client.DataStoreDataClient;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.convert.ConditionConvert;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RConditionModel;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 数据模型更新
 * <AUTHOR>
 * @date 2022/3/2
 */
@Data
public class RDataEntityUpdateAction extends RAction{
    /**
     * 更新数据模型编号
     */
    private String dataEntity;
    /**
     * 更新条件
     */
    private RConditionModel condition;
    /**
     * 更新字段
     */
    private UpdateField[] updateFields;

    @Override
    public Object execute(RuleContext ruleContext) {
        DataStoreDataClient dataStoreDataClient = ruleContext.getRuleEngine().dataStoreDataClient();

        DataStoreCondition dataStoreCondition = ConditionConvert.buildDataStoreCondition(condition, ruleContext);
        List<DataStoreData> updateList = dataStoreDataClient.query(dataEntity, dataStoreCondition);
        if(!CollectionUtils.isEmpty(updateList)){
            for(DataStoreData dataStoreData : updateList){
                ruleContext.setEffect(dataStoreData);
                for(UpdateField updateField : updateFields){
                    dataStoreData.put(updateField.field, updateField.dataBind.getBindData(ruleContext));
                }
            }
            dataStoreDataClient.update(dataEntity, updateList);
        }
        return super.returnOrNext(null, ruleContext);
    }

    /**
     * 更新字段
     */
    public static class UpdateField{
        /**
         * 更新数据模型字段
         */
        private String field;
        /**
         * 更新值绑定
         */
        private RDataBind dataBind;

        public String getField() {
            return field;
        }

        public void setField(String fieldName) {
            this.field = fieldName;
        }

        public RDataBind getDataBind() {
            return dataBind;
        }

        public void setDataBind(RDataBind dataBind) {
            this.dataBind = dataBind;
        }
    }
}
