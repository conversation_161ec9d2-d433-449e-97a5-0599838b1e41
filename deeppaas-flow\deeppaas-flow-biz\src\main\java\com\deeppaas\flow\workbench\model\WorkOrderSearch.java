package com.deeppaas.flow.workbench.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/11
 */
@Data
public class WorkOrderSearch {
    /**
     * OaOrder工单配置字段参数匹配规则
     */
    public static final String FROM_DATA_FIELD_PATTERN = "^f\\d+_(EQ|LIKE|IN|GT|LT|GTE|LTE)";
    private int pageNo = 1;						// 页码
    private int pageSize;						// 每页最大显示数量
    private String entity; 						// 对应数据集
    private String orderId;						// 工单ID
    private String orderCode;					// 工单编号
    private LocalDateTime startTimeGte; 		// 开始时间开始
    private LocalDateTime startTimeLte; 		// 开始时间结束
    private LocalDateTime endTimeGte; 			// 结束时间开始
    private LocalDateTime endTimeLte; 			// 结束时间结束
    private String nodeName; 					// 环节名称
    private Map<String,String> formDataParam; 	// 表单数据
    private Integer status; 					// 表单状态（草稿:0/审批中:1/审批结束:2）
    private String todoUserCode;				// 待办人Id
    private LocalDateTime createTimeGte; 		// 创建时间开始
    private LocalDateTime createTimeLte; 		// 创建时间结束
    private String sortInfo;					//ASC_id,DESC_createTime
}
