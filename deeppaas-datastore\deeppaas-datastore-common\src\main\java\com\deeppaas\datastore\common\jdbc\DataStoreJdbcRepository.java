package com.deeppaas.datastore.common.jdbc;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.model.PageData;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.DataStoreRepository;
import com.deeppaas.datastore.common.emnus.DBDialect;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.datastore.common.mapping.DataStoreEntity;
import com.deeppaas.datastore.common.mapping.DataStoreProperty;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
public class DataStoreJdbcRepository implements DataStoreRepository {
    private final DataStoreEntity entity;
    private final DataStoreSqlGenerator sqlGenerator;
    private final DataStorePropertyMapper propertyMapper;
    private final DataStoreDataRowMapper rowMapper;
    private final NamedParameterJdbcTemplate npJdbcTemplate;

    public DataStoreJdbcRepository(DBDialect dbDialect, DataStoreEntity entity, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.entity = entity;
        this.sqlGenerator = new DataStoreSqlGenerator(dbDialect, entity);
        this.propertyMapper = DataStorePropertyMapper.of(entity);
        this.rowMapper = DataStoreDataRowMapper.of(entity);
        this.npJdbcTemplate = namedParameterJdbcTemplate;
    }

    @Override
    public boolean isExist(DataStoreData data) {
        Object idValue = data.get(entity.getIdProperty().getName());
        if(idValue==null) {
            return false;
        }
        String sql = sqlGenerator.getFindOne();
        Map<String, Object> params = new HashMap<>();
        DataStoreProperty idProperty = entity.getIdProperty();
        params.put(idProperty.getName(), idValue);
        params.putAll(getDiscriminatorParam());
        List<DataStoreData> results = npJdbcTemplate.query(sql, params, rowMapper);
        return !results.isEmpty();
    }

    @Override
    public DataStoreData save(DataStoreData data) {
        DataStoreEntityType entityType = entity.getType();
        data = loadDiscriminator(data);
        switch (entityType){
            case SELF_BUILD -> {
                return saveSelfEntity(data);
            }
            case LOAD_BUILD -> {
                return saveLoadEntity(data);
            }
            default ->
                throw RunException.error("无法处理非物理表映射的数据模型保存");

        }
    }

    @Override
    public int delete(DataStoreData data) {
        String sql = sqlGenerator.getDeleteOne();
        Map<String, Object> params = new HashMap<>();
        DataStoreProperty idProperty = entity.getIdProperty();
        params.put(idProperty.getName(), data.get(idProperty.getName()));
        params.putAll(getDiscriminatorParam());
        return npJdbcTemplate.update(sql, params);
    }

    /**
     * 保存自定义表数据
     * @param data
     */
    private DataStoreData saveSelfEntity(DataStoreData data) {
        List<DataStoreProperty> uniqueProperties = entity.getUniqueProperties();
        if(data.get(entity.getIdProperty().getName())==null){
            return this.insertSelfEntity(data, uniqueProperties);
        }else {
            return this.updateSelfEntity(data, uniqueProperties);
        }
    }

    private DataStoreData insertSelfEntity(DataStoreData data, List<DataStoreProperty> uniqueProperties) {
        String insertSql = sqlGenerator.getInsert();
        Map<String, Object> insertParams = propertyMapper.getStoreParam(data);
        // 处理数据库字段赋值问题
        Map<String, DataStoreProperty> generatedColumnMap = new HashMap<>();
        entity.getProperties().forEach(property -> {
            if(!property.isVirtual() && property.isDbGenerated()){
                generatedColumnMap.put(property.getColumnName(), property);
            }
        });
        // 校验唯一
        uniqueProperties.forEach(property -> {
            List<DataStoreData> list = findForList(DataStoreCondition.eq(property.getName(), data.get(property.getName()), false));
            if(!list.isEmpty()){
                throw RunException.optReject("字段"+property.getName()+"为值唯一字段，已经有值为["+data.get(property.getName())+"]的记录了");
            }
        });
        if(generatedColumnMap.isEmpty()){
            npJdbcTemplate.update(insertSql, insertParams);
        }else {
            String[] generatedColumns = generatedColumnMap.keySet().toArray(new String[generatedColumnMap.size()]);
            KeyHolder keyHolder = new GeneratedKeyHolder();
            npJdbcTemplate.update(insertSql, new MapSqlParameterSource(insertParams),  keyHolder, generatedColumns);
            Map<String, Object> generated = keyHolder.getKeyList().get(0);
            for(String column : generated.keySet()){
                data.put(generatedColumnMap.get(column).getName(), generated.get(column));
            }
        }
        data.put(entity.getIdProperty().getName(), insertParams.get(entity.getIdProperty().getName()));
        return DataStoreData.instance(entity.getAlias(), insertParams.get(entity.getIdProperty().getName()), data);
    }

    private DataStoreData updateSelfEntity(DataStoreData data, List<DataStoreProperty> uniqueProperties) {
        // 校验唯一
        uniqueProperties.forEach(property -> {
            List<DataStoreData> list = findForList(DataStoreCondition.eq(property.getName(), data.get(property.getName()), false));
            if(!list.isEmpty()){
                list.forEach(oldData -> {
                    if(!Objects.equals(oldData.get(entity.getIdProperty().getName()), data.get(entity.getIdProperty().getName()))){
                        throw RunException.optReject("字段"+property.getName()+"为值唯一字段，已经有值为["+data.get(property.getName())+"]的记录了");
                    }
                });
            }
        });
        String updateSql = sqlGenerator.getUpdate();
        Map<String, Object> updateParams = propertyMapper.getStoreParam(data);
        int updateNum = npJdbcTemplate.update(updateSql, updateParams);
        if(updateNum==0){
            return insertSelfEntity(data, uniqueProperties);
        }
        return DataStoreData.instance(entity.getAlias(), updateParams.get(entity.getIdProperty().getName()), data);
    }

    private DataStoreData saveLoadEntity(DataStoreData data) {
        boolean isNew = true;
        Object idValue = data.get(entity.getIdProperty().getName());
        if(idValue!=null) {
            String sql = sqlGenerator.getFindOne();
            Map<String, Object> params = new HashMap<>();
            DataStoreProperty idProperty = entity.getIdProperty();
            params.put(idProperty.getName(), idValue);
            List<DataStoreData> results = npJdbcTemplate.query(sql, params, rowMapper);
            isNew = results.isEmpty();
        }
        List<DataStoreProperty> uniqueProperties = entity.getUniqueProperties();
        Map<String, DataStoreProperty> generatedColumnMap = new HashMap<>();
        entity.getProperties().forEach(property -> {
            if(!property.isVirtual() && property.isDbGenerated()){
                generatedColumnMap.put(property.getColumnName(), property);
            }
        });
        String executeSql;
        Map<String, Object> executeParams;
        if(isNew){
            // 校验唯一
            uniqueProperties.forEach(property -> {
                List<DataStoreData> list = findForList(DataStoreCondition.eq(property.getName(), data.get(property.getName()), false));
                if(!list.isEmpty()){
                    throw RunException.optReject("字段"+property.getName()+"为值唯一字段，已经有值为["+data.get(property.getName())+"]的记录了");
                }
            });
            executeSql = sqlGenerator.getInsert();
            executeParams = propertyMapper.getStoreParam(data);
        }else {
            // 校验唯一
            uniqueProperties.forEach(property -> {
                List<DataStoreData> list = findForList(DataStoreCondition.eq(property.getName(), data.get(property.getName()), false));
                if(!list.isEmpty()){
                    list.forEach(oldData -> {
                        if(!Objects.equals(oldData.get(entity.getIdProperty().getName()), data.get(entity.getIdProperty().getName()))){
                            throw RunException.optReject("字段"+property.getName()+"为值唯一字段，已经有值为["+data.get(property.getName())+"]的记录了");
                        }
                    });
                }
            });

            executeSql = sqlGenerator.getUpdate();
            executeParams = propertyMapper.getStoreParam(data);
        }
        if(generatedColumnMap.isEmpty()){
            npJdbcTemplate.update(executeSql, executeParams);
        }else {
            String[] generatedColumns = generatedColumnMap.keySet().toArray(new String[generatedColumnMap.size()]);
            KeyHolder keyHolder = new GeneratedKeyHolder();
            npJdbcTemplate.update(executeSql, new MapSqlParameterSource(executeParams),  keyHolder, generatedColumns);
            Map<String, Object> generated = keyHolder.getKeyList().get(0);
            for(String column : generated.keySet()){
                data.put(generatedColumnMap.get(column).getName(), generated.get(column));
            }
        }
        return data;
    }

    @Override
    public DataStoreData getById(Object idValue) {
        String sql = sqlGenerator.getFindOne();
        Map<String, Object> params = new HashMap<>();
        DataStoreProperty idProperty = entity.getIdProperty();
        params.put(idProperty.getName(), idValue);
        params.putAll(getDiscriminatorParam());
        List<DataStoreData> results = npJdbcTemplate.query(sql, params, rowMapper);
        if (CollectionUtils.isEmpty(results)) {
            return null;
        } else if (results.size() > 1) {
            throw new IncorrectResultSizeDataAccessException(1, results.size());
        } else {
            return results.iterator().next();
        }
    }

    @Override
    public DataStoreData getById(String idValueStr) {
        String sql = sqlGenerator.getFindOne();
        Map<String, Object> params = new HashMap<>();
        DataStoreProperty idProperty = entity.getIdProperty();
        SimpleDataType idDataType = SimpleDataType.valueOf(idProperty.getDataType());
        Object idValue = switch (idDataType){
            case NUMBER -> new BigDecimal(idValueStr);
            case TEXT -> idValueStr;
            default -> throw RunException.error("error id type +"+idProperty.getDataType()+"+ value: " + idValueStr);
        };
        params.put(idProperty.getName(), idValue);
        params.putAll(getDiscriminatorParam());
        List<DataStoreData> results = npJdbcTemplate.query(sql, params, rowMapper);
        if (CollectionUtils.isEmpty(results)) {
            return null;
        } else if (results.size() > 1) {
            throw new IncorrectResultSizeDataAccessException(1, results.size());
        } else {
            return results.iterator().next();
        }
    }

    @Override
    public int deleteById(Object idValue) {
        String sql = sqlGenerator.getDeleteOne();
        Map<String, Object> params = new HashMap<>();
        DataStoreProperty idProperty = entity.getIdProperty();
        params.put(idProperty.getName(), idValue);
        params.putAll(getDiscriminatorParam());
        return npJdbcTemplate.update(sql, params);
    }

    @Override
    public List<DataStoreData> findForList(DataStoreCondition condition){
        String sql = sqlGenerator.getFindList(condition);
        Map<String, Object> conditionParams = condition==null?new HashMap(): condition.getBindParam();
        conditionParams.putAll(getDiscriminatorParam());
        return npJdbcTemplate.query(sql, conditionParams, rowMapper);
    }

    @Override
    public List<DataStoreData> findForList(DataStoreCondition condition, Sort sort){
        String sql = sqlGenerator.getFindList(condition,sort);
        Map<String, Object> conditionParams = condition==null?new HashMap(): condition.getBindParam();
        conditionParams.putAll(getDiscriminatorParam());
        return npJdbcTemplate.query(sql, conditionParams, rowMapper);
    }

    @Override
    public List<DataStoreData> findForList(DataStoreCondition condition, Pageable pageable) {
        String querySql = sqlGenerator.getFindPage(condition, pageable);
        Map<String, Object> conditionParams = condition==null?new HashMap(): condition.getBindParam();
        conditionParams.putAll(getDiscriminatorParam());
        List<DataStoreData> list = npJdbcTemplate.query(querySql, conditionParams, rowMapper);
        return list;
    }

    @Override
    public PageData<DataStoreData> findForPage(DataStoreCondition condition, Pageable pageable){
        String querySql = sqlGenerator.getFindPage(condition, pageable);
        Map<String, Object> conditionParams = condition==null?new HashMap(): condition.getBindParam();
        conditionParams.putAll(getDiscriminatorParam());
        List<DataStoreData> list = npJdbcTemplate.query(querySql, conditionParams, rowMapper);
        String countSql = sqlGenerator.getCount(condition);
        Long total = npJdbcTemplate.queryForObject(countSql,conditionParams, Long.class);
        PageData pageData = PageData.init(pageable, list);
        pageData.setTotal(total);
        pageData.setTotalPages(total/pageData.getPageSize());
        return pageData;
    }

    public DataStoreEntity getEntity() {
        return entity;
    }

    /**
     * 载入鉴别器相关数据
     * @param data
     * @return
     */
    private DataStoreData loadDiscriminator(DataStoreData data) {
        entity.getProperties().forEach(property -> {
            // 如果是鉴别器字段，那么存入默认值
            if(property.isDiscriminator()){
                if(SimpleDataType.NUMBER.name().equals(property.getDataType())){
                    data.put(property.getName(), new BigDecimal(property.getDiscriminatorValue()));
                }else {
                    data.put(property.getName(), property.getDiscriminatorValue());
                }
            }
        });
        return data;
    }

    private Map<String, Object> getDiscriminatorParam(){
        Map<String, Object> discriminatorParam = new HashMap<>();
        entity.getProperties().forEach(property -> {
            // 如果是鉴别器字段，那么存入默认值
            if(property.isDiscriminator()){
                if(SimpleDataType.NUMBER.name().equals(property.getDataType())){
                    discriminatorParam.put(property.getName(), new BigDecimal(property.getDiscriminatorValue()));
                }else {
                    discriminatorParam.put(property.getName(), property.getDiscriminatorValue());
                }
            }
        });
        return discriminatorParam;
    }

}
