package com.deeppaas.common.data.jdbc;

import com.deeppaas.common.data.BaseDO;
import com.deeppaas.common.data.BaseUuidDO;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.UidGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jdbc.core.JdbcAggregateOperations;
import org.springframework.data.relational.core.mapping.RelationalPersistentEntity;
import org.springframework.data.util.Streamable;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/26
 */
@Transactional(readOnly = true)
public class JSimpleJdbcRepository<T, ID> implements BaseJdbcDao<T, ID> {
    private final JdbcAggregateOperations entityOperations;
    private final RelationalPersistentEntity<T> entity;
    private final SqlGenerator sqlGenerator;
    private final RowMapper entityRowMapper;
    private final NamedParameterJdbcOperations namedParameterJdbcOperations;

    public JSimpleJdbcRepository(JdbcAggregateOperations entityOperations,
                                 RelationalPersistentEntity<T> entity,
                                 SqlGenerator sqlGenerator,
                                 RowMapper rowMapper,
                                 NamedParameterJdbcOperations operations) {

        Assert.notNull(entityOperations, "EntityOperations must not be null.");
        Assert.notNull(entity, "Entity must not be null.");

        this.entityOperations = entityOperations;
        this.entity = entity;
        this.sqlGenerator = sqlGenerator;
        this.entityRowMapper = rowMapper;
        this.namedParameterJdbcOperations = operations;
    }

    @Transactional
    @Override
    public <S extends T> S insert(S instance) {
        if(instance instanceof BaseDO){
            if (instance instanceof BaseUuidDO baseUuidDO) {
                if (!StringUtils.hasText(baseUuidDO.getId())) {
                    baseUuidDO.setId(UidGenerator.nextId());
                    baseUuidDO.setCreateTime(LocalDateTime.now());
                }
                return (S) entityOperations.insert(baseUuidDO);
            }else {
                return entityOperations.insert(instance);
            }
        } else {
            throw RunException.optReject("保存实体没有集成com.deeppaas.common.data.BaseDO");
        }
    }

    @Transactional
    @Override
    public <S extends T> Iterable<S> insertAll(Iterable<S> entities) {

        return Streamable.of(entities).stream() //
                .map(this::insert) //
                .collect(Collectors.toList());
    }

    @Transactional
    @Override
    public <S extends T> S save(S instance) {
        if (instance instanceof BaseUuidDO baseUuidDO) {
            if (StringUtils.hasText(baseUuidDO.getId())) {
                return (S) entityOperations.save(baseUuidDO);
            } else {
                baseUuidDO.setId(UidGenerator.nextId());
                baseUuidDO.setCreateTime(LocalDateTime.now());
                return (S) entityOperations.insert(baseUuidDO);
            }
        } else {
            return entityOperations.save(instance);
            //throw RunException.optReject("保存实体没有集成com.deeppaas.common.data.BaseDO");
        }
    }

    @Transactional
    @Override
    public <S extends T> S save(S instance, ID id) {
        if (instance instanceof BaseUuidDO baseUuidDO) {
            if (StringUtils.hasText(baseUuidDO.getId())) {
                return (S) entityOperations.save(baseUuidDO);
            } else {
                baseUuidDO.setId(UidGenerator.nextId());
                baseUuidDO.setCreateTime(LocalDateTime.now());
                return (S) entityOperations.insert(baseUuidDO);
            }
        } else {
            if(id==null){
                return entityOperations.save(instance);
            }
            boolean exist = entityOperations.existsById(id, entity.getType());
            if(exist){
                return entityOperations.save(instance);
            }else {
                return entityOperations.insert(instance);
            }
        }
    }

    @Transactional
    @Override
    public <S extends T> Iterable<S> saveAll(Iterable<S> entities) {
        return Streamable.of(entities).stream() //
                .map(this::save) //
                .collect(Collectors.toList());
    }

    @Override
    public Optional<T> findById(ID id) {
        return Optional.ofNullable(entityOperations.findById(id, entity.getType()));
    }

    @Override
    public T getById(ID id) {
        if(id==null){
            return null;
        }
        return entityOperations.findById(id, entity.getType());
    }

    @Override
    public boolean existsById(ID id) {
        return entityOperations.existsById(id, entity.getType());
    }

    @Override
    public List<T> findAll() {
        List<T> list = new ArrayList<>();
        Iterable<T> iterable = entityOperations.findAll(entity.getType());
        if (iterable != null) {
            iterable.forEach(t -> list.add(t));
        }
        return list;
    }
    @Override
    public List<T> findByIds(Collection<ID> ids) {
        List<T> list = new ArrayList<>();
        Iterable<T> iterable = entityOperations.findAllById(ids, entity.getType());
        if (iterable != null) {
            iterable.forEach(t -> list.add(t));
        }
        return list;
    }

    @Override
    public Iterable<T> findAllById(Iterable<ID> ids) {
        return entityOperations.findAllById(ids, entity.getType());
    }

    @Override
    public long count() {
        return entityOperations.count(entity.getType());
    }

    @Transactional
    @Override
    public void deleteById(ID id) {
        entityOperations.deleteById(id, entity.getType());
    }

    @Transactional
    @Override
    public void delete(T instance) {
        entityOperations.delete(instance, entity.getType());
    }

    @Override
    public void deleteAllById(Iterable<? extends ID> ids) {
        ids.forEach((it) -> {
            this.entityOperations.deleteById(it, this.entity.getType());
        });
    }

    @Transactional
    @Override
    @SuppressWarnings("unchecked")
    public void deleteAll(Iterable<? extends T> entities) {
        entities.forEach(it -> entityOperations.delete(it, (Class<T>) it.getClass()));
    }

    @Transactional
    @Override
    public void deleteAll() {
        entityOperations.deleteAll(entity.getType());
    }

    @Override
    public int[] batchUpdate(String sql, Map<String, ?>[] paramArray) {
        return namedParameterJdbcOperations.batchUpdate(sql, paramArray);
    }

    @Override
    public List<T> findAll(Sort sort) {
        List<T> list = new ArrayList<>();
        Iterable<T> iterable = entityOperations.findAll(entity.getType(), sort);
        if (iterable != null) {
            iterable.forEach(t -> list.add(t));
        }
        return list;
    }

    @Override
    public Page<T> findAll(Pageable pageable) {
        return entityOperations.findAll(entity.getType(), pageable);
    }

    @Override
    public List<T> queryForList(JCondition jCondition) {
        jCondition.sortMarkNum();
        String sql1 = sqlGenerator.getFindList(jCondition);
        Map<String, Object> sqlParam = jCondition.getBindParam();
        return namedParameterJdbcOperations.query(sql1, sqlParam, entityRowMapper);
    }

    @Override
    public List<T> queryForList(JCondition jCondition, Sort sort) {
        jCondition.sortMarkNum();
        String sql1 = sqlGenerator.getFindList(jCondition, sort);
        Map<String, Object> sqlParam = jCondition.getBindParam();
        return namedParameterJdbcOperations.query(sql1, sqlParam, entityRowMapper);
    }

    @Override
    public Page<T> queryForPage(JCondition jCondition, Pageable pageable) {
        jCondition.sortMarkNum();
        Map<String, Object> sqlParam = jCondition.getBindParam();
        String countSql = sqlGenerator.getCount(jCondition);
        Long count = namedParameterJdbcOperations.queryForObject(countSql, sqlParam, Long.class);
        if (count == null) {
            return new PageImpl(Collections.emptyList(), pageable, 0);
        }
        String dataSql = sqlGenerator.getFindPage(jCondition, pageable);
        List<T> list = namedParameterJdbcOperations.query(dataSql, sqlParam, entityRowMapper);
        return new PageImpl<>(list, pageable, count);
    }

    @Override
    public <T> T queryForObject(String sql, Map<String, ?> param, Class<T> resultClass) {
        return namedParameterJdbcOperations.queryForObject(sql, param, resultClass);
    }

    @Override
    public Map<String, Object> queryForMap(String sql, Map<String, ?> param) {
        return namedParameterJdbcOperations.queryForMap(sql, param);
    }

    @Override
    public <T> List<T> queryForList(String sql, Map<String, ?> param, Class<T> resultClass) {
        return namedParameterJdbcOperations.queryForList(sql, param, resultClass);
    }

    @Override
    public List<Map<String, Object>> queryForMaps(String sql, Map<String, ?> param) {
        return namedParameterJdbcOperations.queryForList(sql, param);
    }

    @Override
    public int update(String sql, Map<String, ?> param) {
        return namedParameterJdbcOperations.update(sql, param);
    }


}
