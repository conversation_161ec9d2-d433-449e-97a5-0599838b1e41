package com.deeppaas.process.biz.dao;

import com.deeppaas.process.biz.entity.AidosProcessTaskDO;
import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AidosProcessTaskDao extends BaseJdbcDao<AidosProcessTaskDO, String> {

    List<AidosProcessTaskDO> findByUserIdLike(String userId);

    AidosProcessTaskDO findByNameAndProcessId(String name, String processId);

}
