[{"stepId": "step1", "type": "VARIABLE", "name": "申请请假类型", "varName": "leaveType1", "target": {"type": "FIELD", "value": "leaveType"}, "next": "step2"}, {"stepId": "step2", "type": "DECISION", "name": "是否是年假", "target": {"type": "EXPR", "value": "var.leaveType1 == '年假'"}, "next": {"tureNext": "step3", "falseNext": "RETURN"}}, {"stepId": "step3", "type": "VARIABLE", "name": "当前年", "varName": "nowYear", "target": {"type": "FUN", "value": {"name": "GetCurDate", "params": [{"type": "CONST", "value": "yyyy"}]}}, "next": "step4"}, {"stepId": "step4", "type": "VARIABLE", "name": "当前人编号", "varName": "userCode", "target": {"type": "FUN", "value": {"name": "GetCurUserInfo", "params": [{"type": "CONST", "value": "code"}]}}, "next": "step5"}, {"stepId": "step5", "type": "DATA_UPDATE", "name": "扣除剩余年假天数", "dataEntity": "dataSet123", "condition": {"operator": "AND", "conditions": [{"field": "userCode", "operator": "=", "target": {"type": "VAR", "value": "userCode"}}, {"field": "year", "operator": "=", "target": {"type": "VAR", "value": "nowYear"}}]}, "updateFields": [{"field": "days", "target": {"type": "EXPR", "value": "effect.days-data.days"}}, {"field": "updateTime", "target": {"type": "FIELD", "value": "createTime"}}], "next": null}]