package com.deeppaas.extapi.biz.service;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.LogerHelper;
import com.deeppaas.datastore.common.mapping.DataStoreFile;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.enums.ApiMethod;
import com.deeppaas.extapi.biz.enums.ApiPositionType;
import com.deeppaas.extapi.biz.enums.ApiReqBodyType;
import com.deeppaas.extapi.biz.enums.ApiResBodyType;
import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.file.api.model.FileEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/16
 */
public class SelfApiClient {
    private static final Logger logger = LoggerFactory.getLogger(ThirdApiClient.class);
    /**
     * 第三方接口
     */
    private final ExtApiDO extApi;
    /**
     * 接口参数
     */
    private final Map<String, ExtApiParamDO> extApiParams;

    private final FileClient fileClient;

    /**
     * 接口header参数信息
     */
    private Map<String, Object> headerParamMap = new HashMap<>();
    /**
     * 接口url地址中的参数信息
     */
    private Map<String, Object> pathParamMap = new HashMap<>();
    /**
     * 接口url地址?后的参数信息
     */
    private Map<String, Object> queryParamMap = new HashMap<>();
    /**
     * 接口请求体Body中的参数信息
     */
    private Map<String, Object> bodyParamMap = new HashMap<>();

    public SelfApiClient(ExtApiDO extApi, List<ExtApiParamDO> extApiParams, FileClient fileClient) {
        this.extApi = extApi;
        if(CollectionUtils.isEmpty(extApiParams)){
            this.extApiParams = new HashMap<>();
        }else {
            this.extApiParams = extApiParams.stream().collect(Collectors.toMap(ExtApiParamDO::getCode, Function.identity()));
        }
        this.fileClient = fileClient;
    }

    public static SelfApiClient of(ExtApiDO extApi, List<ExtApiParamDO> extApiParams, FileClient fileClient){
        return new SelfApiClient(extApi, extApiParams, fileClient);
    }

    /**
     * 调用执行
     * @param params
     * @return
     */
    public Object execute(Map<String, Object> params){
        loadParams(params);
        String apiFullUrl = buildReqUrl();
        HttpEntity httpEntity = buildHttpEntity();
        RestTemplate restTemplate = new RestTemplate();
        Class returnClass = getResponseBodyType();
        try {
            Object result = restTemplate.exchange(apiFullUrl, HttpMethod.valueOf(extApi.getMethod()), httpEntity, returnClass).getBody();
            return result;
        }catch (Exception ex){
            LogerHelper.errorLog(logger, ex);
            throw RunException.error(ex.getMessage());
        }

    }

    private HttpEntity buildHttpEntity() {
        HttpHeaders headers = buildHeaders();
        ApiMethod method = ApiMethod.valueOf(extApi.getMethod());
        switch (method){
            case PUT, POST -> {
                ApiReqBodyType apiReqBodyType = ApiReqBodyType.valueOf(extApi.getReqBodyType());
                switch (apiReqBodyType){
                    case JSON -> {
                        headers.setContentType(MediaType.APPLICATION_JSON);
                        return new HttpEntity<>(bodyParamMap, headers);
                    }
                    case FORM_DATA -> {
                        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                        MultiValueMap<String,Object> multiValueMap = toMultiValueMap(bodyParamMap);
                        return new HttpEntity(multiValueMap, headers);
                    }
                    case FORM_URLENCODED -> {
                        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                        MultiValueMap<String,Object> multiValueMap = toMultiValueMap(bodyParamMap);
                        return new HttpEntity(multiValueMap, headers);
                    }
                }
                return null;
            }
            default -> {
                return new HttpEntity<>(headers);
            }
        }

    }

    /**
     * 构建请求头信息
     * @return
     */
    private HttpHeaders buildHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headerParamMap.forEach((paramCode, paramValue) -> headers.add(paramCode, paramValue==null?"":paramValue.toString()));
        return headers;
    }

    /**
     * 构建完整请求地址
     * @return
     */
    private String buildReqUrl() {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(extApi.getUrl());
        queryParamMap.forEach((paramCode, paramValue) -> uriBuilder.queryParam(paramCode, paramValue));
        uriBuilder.uriVariables(pathParamMap);
        return uriBuilder.toUriString();
    }

    /**
     * 载入参数
     * @param params
     */
    private void loadParams(Map<String, Object> params) {
        for(ExtApiParamDO apiParam : extApiParams.values()){
            String paramCode = apiParam.getCode();
            Object paramValue = params.get(paramCode);
            ApiPositionType positionType = ApiPositionType.valueOf(apiParam.getPosition());
            if(SimpleDataType.FILE.name().equals(apiParam.getType())){
                paramValue = fileParam(paramValue);
            }
            if(SimpleDataType.FILES.name().equals(apiParam.getType())){
                paramValue = filesParam(paramValue);
            }
            switch (positionType){
                case PATH -> pathParamMap.put(paramCode, paramValue);
                case QUERY -> queryParamMap.put(paramCode, paramValue);
                case HEADER -> headerParamMap.put(paramCode, paramValue);
                case BODY -> bodyParamMap.put(paramCode, paramValue);
            }
        }
    }

    /**
     * 转换文件类型
     * @param paramValue
     * @return
     */
    private InputStreamResource fileParam(Object paramValue){
        if(paramValue==null){
            return null;
        }
        if(paramValue instanceof DataStoreFile dataStoreFile){
            FileEntity fileEntity = fileClient.getFile(dataStoreFile.getGroup(), dataStoreFile.getKey());
            InputStreamResource inputStreamResource = new InputStreamResource(fileEntity.getContent()){
                @Override
                public String getFilename() {
                    return dataStoreFile.getName();
                }
                @Override
                public long contentLength() {
                    return dataStoreFile.getSize();
                }
            };
            return inputStreamResource;
        }
        if(paramValue instanceof File file){
            FileInputStream fileInputStream = null;
            try {
                fileInputStream = new FileInputStream(file);
            } catch (FileNotFoundException e) {
                LogerHelper.errorLog(logger, e);
                throw new RuntimeException(e);
            }
            InputStreamResource inputStreamResource = new InputStreamResource(fileInputStream){
                @Override
                public String getFilename() {
                    return file.getName();
                }
                @Override
                public long contentLength() {
                    return file.length();
                }
            };
            return inputStreamResource;
        }
        throw RunException.error("不支持"+paramValue.getClass().getName()+"对流转换");
    }

    /**
     * 转换文件类型
     * @param paramValue
     * @return
     */
    private Object filesParam(Object paramValue){
        if(paramValue==null){
            return null;
        }
        List<Object> fileObjects = new ArrayList<>();
        if(paramValue.getClass().isArray()){
            fileObjects = Arrays.asList(paramValue);
        }
        if(paramValue instanceof Collection<?>){
            fileObjects.addAll((Collection<?>) paramValue);
        }
        List<InputStreamResource> result = fileObjects.stream().map(fileObject -> fileParam(fileObject)).toList();
        return result;
    }

    private static MultiValueMap<String,Object> toMultiValueMap(Map<String, Object> map){
        MultiValueMap<String,Object> multiValueMap = new LinkedMultiValueMap<>();
        map.forEach((k,v) -> {
            multiValueMap.add(k, v);
        });
        return multiValueMap;
    }

    /**
     * 获取返回体类型
     * @return
     */
    private Class getResponseBodyType() {
        ApiResBodyType resBodyType = ApiResBodyType.valueOf(extApi.getResBodyType());
        return switch (resBodyType){
            case TEXT_TEXT, HTML_TEXT -> String.class;
            case TEXT_NUMBER -> Number.class;
            case TEXT_BOOLEAN -> Boolean.class;
            case JSON_TEXTS -> String[].class;
            case JSON_NUMBERS -> Number[].class;
            case JSON_DATA_ENTITY, JSON_VO_ENTITY, JSON_RULE_ENTITY -> Map.class;
            case JSON_DATA_ENTITIES, JSON_RULE_ENTITIES -> Map[].class;
            case STREAM_FILE -> InputStream.class;
        };
    }
}
