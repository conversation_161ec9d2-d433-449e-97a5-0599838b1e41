package com.deeppaas.rule.biz.function;

/**
 * 函数枚举
 *
 * <AUTHOR>
 * @date 2022/3/4
 */
public enum RFunctions {
    /**
     * 数组包含
     */
    ArrayContains(Boolean.class, Object[].class, Object.class),
    /**
     * 数组包含
     */
    ArrayJoin(Object[].class, Object.class, Object.class),
    /**
     * 当前登录人属性
     */
    GetCurUserProperty(Object.class, String.class),
    /**
     * 当前用户所在部门编号列表
     */
    GetCurUserDeptCodes(Object.class),
    /**
     * 获取当前日期
     */
    GetCurDate(Object.class, String.class),
    /**
     * 是否相等
     */
    Equals(Boolean.class, Object.class, Object.class),
    /**
     * 图片转base64
     */
    IMAGE_BASE64(String.class, Object.class),
    /**
     * 获取deeppaas的一个唯一ID
     */
    NextDPUUID(String.class);

    private Class returnType;
    private Class[] paramTypes;

    RFunctions(Class returnType, Class... paramTypes) {
        this.returnType = returnType;
        this.paramTypes = paramTypes;
    }

    public Class getReturnType() {
        return returnType;
    }

    public Class[] getParamTypes() {
        return paramTypes;
    }
}
