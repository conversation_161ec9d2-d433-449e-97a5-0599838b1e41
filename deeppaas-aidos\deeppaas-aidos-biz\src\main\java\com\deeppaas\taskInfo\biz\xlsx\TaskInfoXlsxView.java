package com.deeppaas.taskInfo.biz.xlsx;

import com.deeppaas.taskInfo.biz.entity.TaskInfoDO;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.common.web.view.BaseXlsxView;
import org.apache.poi.ss.usermodel.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class TaskInfoXlsxView extends BaseXlsxView {
    @Override
    protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<TaskInfoDO> list = (List<TaskInfoDO>) model.get("list");

        // excel文件名称
        String fileName = "任务数据_" + DateHelper.getNowDateStr() + ".xlsx";
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        // SheetName
        String sheetName = "sheet1";
        Sheet sheet = workbook.createSheet(sheetName);

        final CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        int filedCount = list.size();
        int rowIndex = 0;
        Row row = sheet.createRow(rowIndex++);
        for (int i = 0; i < filedCount; i++) {
            row.createCell(0).setCellValue("办件业务员");
            row.createCell(1).setCellValue("办件时间");
            row.createCell(2).setCellValue("审核");
            row.createCell(3).setCellValue("机器人提示信息");
            row.createCell(4).setCellValue("办件类型");
            row.createCell(5).setCellValue("预审情况");
        }
        for (TaskInfoDO data : list) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(data.getHandlerUser());
            row.createCell(1).setCellValue(DateHelper.localDateTime2str(data.getCreateTime()));
            row.createCell(2).setCellValue(data.getCheckRemarks());
            row.createCell(3).setCellValue(data.getAiTips());
            row.createCell(4).setCellValue(data.getTaskName());
            row.createCell(5).setCellValue(Objects.equals(data.getProExamineStae(), BoolHelper.INT_FALSE)?"未通过":"通过");

        }
    }

}
