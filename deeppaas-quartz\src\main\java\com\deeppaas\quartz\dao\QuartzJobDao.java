package com.deeppaas.quartz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.quartz.entity.QuartzJobDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/10
 */
public interface QuartzJobDao extends BaseJdbcDao<QuartzJobDO, String> {

    @Modifying
    @Query("UPDATE quartz_job SET prev_fire_time=:fireTime,next_fire_time=:nextTime,prev_fire_result=:fireResult WHERE id=:jobId")
    void updateFireInfo(@Param("fireTime") LocalDateTime fireTime, @Param("nextTime") LocalDateTime nextTime, @Param("fireResult") String fireResult, @Param("jobId") String id);

    List<QuartzJobDO> findByAppCode(String appCode);
}
