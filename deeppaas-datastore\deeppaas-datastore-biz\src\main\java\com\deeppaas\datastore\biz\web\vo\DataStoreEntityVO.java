package com.deeppaas.datastore.biz.web.vo;

import com.deeppaas.common.model.BaseModel;
import com.deeppaas.tenant.api.client.model.AppCO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据模型VO
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Data
public class DataStoreEntityVO implements BaseModel {
    /**
     * 主键ID
     **/
    private String id;
    /**
     * 数据源
     **/
    private String sourceId;
    /**
     * 数据模型编号手动，全局唯一
     **/
    private String code;
    /**
     * 数据模型名称
     **/
    private String name;
    /**
     * 类型 @DataStoreEntityType
     */
    private String type;

    /**
     * 所属应用信息
     */
    private AppCO app;

    /**
     * 数据模型对应主表ID
     **/
    private String tableId;

    /**
     * 数据模型对应主表
     **/
    private String tableName;

    /**
     * 创建时间
     **/
    private LocalDateTime createTime;
    /**
     * 更新时间
     **/
    private LocalDateTime updateTime;
}
