package com.deeppaas.extapi.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.extapi.biz.entity.ExtApiUserRightDO;

import java.util.List;

/**
 * 接口授权用户关系DAO
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
public interface ExtApiUserRightDao extends BaseJdbcDao<ExtApiUserRightDO, String> {

    List<ExtApiUserRightDO> findByApiId(String apiId);

    List<ExtApiUserRightDO> findByUserId(String userId);

    boolean existsByApiIdAndUserId(String apiId, String userId);
}
