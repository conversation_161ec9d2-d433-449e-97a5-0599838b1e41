package com.deeppaas.file.api.config;

import com.deeppaas.file.FileConstant;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/21
 */
@Data
public class FileGroup {
    /** 组名 */
    private String name;
    /**
     * 模式 本地或OSS
     */
    private String mode;
    /** 访问控制 */
    private String type;
    /** 组指向(如OSS的bucket，本地硬盘文件夹的绝对路径) */
    private String bucket;
    /** 域名 */
    private String domain;

    public boolean isPublic(){
        return FileConstant.AUTH_PUBLIC.equalsIgnoreCase(type);
    }

}
