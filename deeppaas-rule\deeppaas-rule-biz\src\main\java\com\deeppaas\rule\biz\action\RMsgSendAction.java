package com.deeppaas.rule.biz.action;

import com.deeppaas.account.api.client.model.UserScope;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RUserScope;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
@Data
public abstract class RMsgSendAction extends RAction{
    /**
     * 发送人员
     */
    protected List<RUserScope> userScopes;
    /**
     * 模版参数
     */
    protected List<RUserMsgSendAction.Param> msgParams;

    protected Set<String> getToUsers(RuleContext ruleContext) {
        Set<String> toUsers = new HashSet<>();
        List<UserScope> searchUserScopes = new ArrayList<>();
        for(RUserScope rUserScope : userScopes){
            // 这里的值有可能是数组
            RDataBind codeBind = rUserScope.getCodeBind();
            Object codeValue = codeBind.getBindData(ruleContext);
            if(codeValue==null){
                continue;
            }
            List<String> codes = new ArrayList<>();
            codeBind.getDataType();
            switch (codeBind.getDataType()){
                case TEXTS -> {
                    if(codeValue instanceof Collection<?> texts){
                        for(Object text : texts){
                            codes.add(text.toString());
                        }
                    }
                    if(codeValue.getClass().isArray()){
                        for(Object text : (Object[])codeValue){
                            codes.add(text.toString());
                        }
                    }
                }
                case TEXT -> {
                    codes.add(codeValue.toString());
                }
                default -> throw RunException.error("类型错误");
            }
            for(String code : codes){
                UserScope searchUserScope = new UserScope();
                searchUserScope.setCode(code);
                switch (rUserScope.getType()){
                    case DEPT -> {	// 部门
                        searchUserScope.setType(UserScope.ScopeType.DEPT);
                        searchUserScopes.add(searchUserScope);
                    }
                    case POST -> {	// 岗位
                        searchUserScope.setType(UserScope.ScopeType.POST);
                        Object levelObj = rUserScope.getPostControlDeptLevelBind().getBindData(ruleContext);
                        if(levelObj!=null){
                            searchUserScope.setPostControlDeptLevel(Integer.valueOf(levelObj.toString()));
                        }
                        Object deptCodeObj = rUserScope.getPostControlDeptCodeBind().getBindData(ruleContext);
                        if(deptCodeObj!=null){
                            searchUserScope.setPostControlDeptCode(levelObj.toString());
                        }
                        searchUserScopes.add(searchUserScope);
                    }
                    case USER -> {	// 用户
                        toUsers.add(searchUserScope.getCode());
                    }
                    case ORG_USER -> { //组织结构中的人
                        searchUserScope.setType(UserScope.ScopeType.ORG_USER);
                        searchUserScopes.add(searchUserScope);
                    }
                    case ROLE -> {
                        searchUserScope.setType(UserScope.ScopeType.ROLE);
                        searchUserScopes.add(searchUserScope);
                    }
                }
            }
            Set<String> userCodes = ruleContext.getRuleEngine().userClient().findUserCodeByScopes(searchUserScopes);
            toUsers.addAll(userCodes);
        }
        return toUsers;
    }

    protected Map<String, Object> getParamMap(RuleContext ruleContext){
        Map<String, Object> paramMap = new HashMap<>();
        for(RUserMsgSendAction.Param param : msgParams){
            paramMap.put(param.getName(), param.valueBind.getBindData(ruleContext));
        }
        return paramMap;
    }

    /**
     * 模版参数
     */
    public static class Param{
        /**
         * 参数名称
         */
        private String name;
        /**
         * 参数值
         */
        private RDataBind valueBind;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public RDataBind getValueBind() {
            return valueBind;
        }

        public void setValueBind(RDataBind valueBind) {
            this.valueBind = valueBind;
        }
    }
}
