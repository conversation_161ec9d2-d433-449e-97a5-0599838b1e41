package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: FlowTask 
 * @Description: 流程任务
 * <AUTHOR>
 */
@Data
@Table("flow_task")
public class FlowTaskDO implements BaseModel {
	private static final long serialVersionUID = 7545285010569924797L;

	@Id
	@Column("id")
	private Long id;
	/** 流程实例ID */
	@Column("instance_id")
	private Long instanceId;
	/** 令牌ID */
	@Column("token_id")
	private Long tokenId;
	/** 环节编号 */
	@Column("node_code")
	private String nodeCode;
	/** 环节名称 */
	@Column("node_name")
	private String nodeName;
	/** 开始时间 */
	@Column("start_time")
	private LocalDateTime startTime;
	/** 结束时间 */
	@Column("end_time")
	private LocalDateTime endTime;
	/** 类型 */
	@Column("type")
	private Integer type;
	/** 状态 */
	@Column("state")
	private Integer state;
	/**
	 * 优先级
	 */
	@Column("priority")
	private Integer priority;
	/** 处理类型 */
	@Column("action")
	private String action;
	/** 处理人 */
	@Column("operator")
	private String operator;
	/** 限定时间 */
	@Column("limited_time")
	private LocalDateTime limitedTime;
	/** 参与人 */
	@Column("actors")
	private List<String> actors;
	/** 顺签顺序，非顺签为0 */
	@Column("sort_no")
	private Integer sortNo;
	/**
	 * 等待任务ID，用于加签记录发起加签的任务ID,非加签为0
	 */
	@Column("wait_task_id")
	private Long waitTaskId;
}
