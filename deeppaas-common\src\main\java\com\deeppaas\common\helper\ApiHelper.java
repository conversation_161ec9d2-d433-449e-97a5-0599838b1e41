package com.deeppaas.common.helper;

import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.Map;

/**
 * API调用工具类
 *
 * <AUTHOR>
 * @date 2022/03/31
 */
public class ApiHelper {
    private static final RestTemplate restTemplate;

    static {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        // 连接超时：8000毫秒
        requestFactory.setConnectTimeout(8000);
        // 读取超时：10000毫秒
        requestFactory.setReadTimeout(10000);
        restTemplate = new RestTemplate(requestFactory);
    }

    public static String get(String url) {
        String objects = restTemplate.getForObject(url, String.class);
        return objects;
    }

    public static String get(String url, Map<String, ?> params) {
        String objects = restTemplate.getForObject(url, String.class, params);
        return objects;
    }

    public static String get(String url, Map<String, ?> params, Map<String, String> headers) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        if (!headers.isEmpty()) {
            for (String key : headers.keySet()) {
                httpHeaders.add(key, headers.get(key));
            }
        }
        HttpEntity<String> httpEntity = new HttpEntity<>(httpHeaders);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class, params);
        return response.getBody();
    }


    public static String post(String url, Map<String, ?> params) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Content-Type", MediaType.MULTIPART_FORM_DATA_VALUE + ";charset=UTF-8");


        String response =  restTemplate.postForObject(url, params,String.class);
        return response;
    }
    public static String postImage(String url, Map<String, ?> params,File file) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Content-Type", MediaType.MULTIPART_FORM_DATA_VALUE + ";charset=UTF-8");
        FileSystemResource resource = new FileSystemResource(file);
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("img", resource);
        HttpEntity<MultiValueMap<String, Object>> files = new HttpEntity<>(form, httpHeaders);
        String response =  restTemplate.postForObject(url, files, String.class);
        return response;
    }

}
