package com.deeppaas.rule.biz.client;

import com.deeppaas.rule.api.client.EventRuleClient;
import com.deeppaas.rule.api.enums.RuleOwnerType;
import com.deeppaas.rule.biz.entity.EventRuleDO;
import com.deeppaas.rule.biz.service.EventRuleService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
@Component
public class EventRuleClientImpl implements EventRuleClient {
    private final EventRuleService eventRuleService;

    public EventRuleClientImpl(EventRuleService eventRuleService) {
        this.eventRuleService = eventRuleService;
    }

    @Override
    public List<String> findEventRules(RuleOwnerType ownerType, String ownerId, String event) {
        List<EventRuleDO> eventRuleList = eventRuleService.findByOwnerAndEvent(ownerType, ownerId, event);
        return eventRuleList.stream().map(EventRuleDO::getRuleId).toList();
    }
}
