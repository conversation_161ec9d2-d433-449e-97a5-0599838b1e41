package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.datastore.biz.entity.DataStoreEntityDO;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.biz.service.DataStoreDataService;
import com.deeppaas.datastore.biz.service.DataStoreEntityService;
import com.deeppaas.datastore.biz.web.req.DataStoreDataQueryParam;
import com.deeppaas.datastore.biz.web.view.DataStoreDataExcelView;
import com.deeppaas.datastore.biz.web.view.DataStoreDataJsonView;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 供前端动作调用的接口
 * <AUTHOR>
 * @date 2022/6/21
 */
@RequestAuth(auth = false)
@RestController
@RequestMapping("/api/datastore/dataAction")
public class DataStoreDataActionApi {
    private final DataStoreDataService dataStoreDataService;
    private final DataStoreEntityService entityService;

    public DataStoreDataActionApi(DataStoreDataService dataStoreDataService, DataStoreEntityService entityService) {
        this.dataStoreDataService = dataStoreDataService;
        this.entityService = entityService;
    }

    /**
     * 获取单条数据
     * @param entity
     * @param id
     * @return
     */
    @GetMapping("/get")
    public RestModel<DataStoreData> get(@RequestParam String entity, @RequestParam String id){
        DataStoreData data = dataStoreDataService.getData(entity, id);
        return RestModel.data(data);
    }


    /**
     * 获取数据模型合
     * @param queryParam
     * @return
     */
    @PostMapping("/getOne")
    public RestModel<DataStoreData> getOne(@RequestBody DataStoreDataQueryParam queryParam){
        DataStoreData dataStoreData = dataStoreDataService.queryOne(queryParam);
        return RestModel.data(dataStoreData);
    }

    /**
     * 获取数据模型合
     * @param queryParam
     * @return
     */
    @PostMapping("/query")
    public RestModel<List<DataStoreData>> query(@RequestBody DataStoreDataQueryParam queryParam){
        List<DataStoreData> list = dataStoreDataService.query(queryParam);
        return RestModel.data(list);
    }

    /**
     * 分页获取数据
     * @param queryParam
     * @return
     */
    @PostMapping("/pageQuery")
    public RestModel<PageData<DataStoreData>> pageQuery(@RequestBody DataStoreDataQueryParam queryParam){
        PageData<DataStoreData> page = dataStoreDataService.pageQuery(queryParam);
        return RestModel.data(page);
    }

    /**
     * 删除
     * @param queryParam
     * @return
     */
    @PostMapping("/delete")
    public RestModel<?> delete(@RequestBody DataStoreDataQueryParam queryParam){
        if(queryParam.getCondition()==null){
            return RestModel.error(ReqException.paramValidate("未发现删除条件，无法执行删除动作"));
        }
        dataStoreDataService.delete(queryParam);
        return RestModel.data(true);
    }

    /**
     * 导出
     * @param queryParam
     * @return
     */
    @PostMapping("/export")
    public ModelAndView exportData(@RequestBody DataStoreDataQueryParam queryParam){
        DataStoreEntityDO entityDO = entityService.findByCode(queryParam.getEntity());
        List<DataStoreEntityFieldDTO> fieldDTOS = entityService.findFieldDTOByEntityCode(queryParam.getEntity());
        List<DataStoreData> list = dataStoreDataService.query(queryParam);
        Map<String, Object> model = new HashMap<>();
        model.put(DataStoreDataExcelView.MODEL_KEY_ENTITY, entityDO.getName());
        model.put(DataStoreDataExcelView.MODEL_KEY_FIELDS, fieldDTOS);
        model.put(DataStoreDataExcelView.MODEL_KEY_LIMIT_FIELDS, queryParam.getFields());
        model.put(DataStoreDataExcelView.MODEL_KEY_DATA, list);
        return new ModelAndView(new DataStoreDataExcelView(), model);
    }

    /**
     * 导出Json
     * @param queryParam
     * @return
     */
    @PostMapping("/exportJson")
    public ModelAndView exportDataJson(@RequestBody DataStoreDataQueryParam queryParam){
        DataStoreEntityDO entityDO = entityService.findByCode(queryParam.getEntity());
        List<DataStoreEntityFieldDTO> fieldDTOS = entityService.findFieldDTOByEntityCode(queryParam.getEntity());
        List<DataStoreData> list = dataStoreDataService.query(queryParam);
        Map<String, Object> model = new HashMap<>();
        model.put(DataStoreDataJsonView.MODEL_KEY_ENTITY, entityDO.getName());
        model.put(DataStoreDataJsonView.MODEL_KEY_FIELDS, fieldDTOS);
        model.put(DataStoreDataJsonView.MODEL_KEY_LIMIT_FIELDS, queryParam.getFields());
        model.put(DataStoreDataJsonView.MODEL_KEY_DATA, list);
        return new ModelAndView(new DataStoreDataJsonView(), model);
    }

}
