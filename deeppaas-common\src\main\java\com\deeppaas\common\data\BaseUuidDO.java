package com.deeppaas.common.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 使用uuid为ID主键序列化基类
 * 增加@JsonIgnoreProperties(ignoreUnknown = true)注解解决json反序列化问题
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class BaseUuidDO extends BaseDO {
    /**
     * 主键，使用uuid，老板要求的
     */
    @Id
    @Column("id")
    private String id;

    /**
     * 创建时间
     **/
    @Column("create_time")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private LocalDateTime createTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseUuidDO baseUuidDO = (BaseUuidDO) o;
        return Objects.equals(id, baseUuidDO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
