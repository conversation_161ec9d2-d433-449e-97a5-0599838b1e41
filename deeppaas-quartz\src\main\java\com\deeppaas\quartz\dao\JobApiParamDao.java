package com.deeppaas.quartz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.quartz.entity.JobApiParamDO;

import java.util.List;

/**
 * 定时任务参数
 *
 * <AUTHOR>
 * @date 2022/04/02
 */
public interface JobApiParamDao extends BaseJdbcDao<JobApiParamDO, Long> {

    List<JobApiParamDO> findByJobId(String jobId);

    JobApiParamDO findByJobIdAndCode(String jobId, String code);
}
