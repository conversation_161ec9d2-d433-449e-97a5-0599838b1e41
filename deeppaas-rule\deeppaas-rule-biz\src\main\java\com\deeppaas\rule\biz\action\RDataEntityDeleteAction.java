package com.deeppaas.rule.biz.action;

import com.deeppaas.datastore.api.client.DataStoreDataClient;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.convert.ConditionConvert;
import com.deeppaas.rule.biz.model.RConditionModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/28
 */
@Data
public class RDataEntityDeleteAction extends RAction{
    /**
     * 更新数据模型编号
     */
    private String dataEntity;
    /**
     * 删除匹配条件
     */
    private RConditionModel condition;

    @Override
    public Object execute(RuleContext ruleContext) {
        DataStoreDataClient dataStoreDataClient = ruleContext.getRuleEngine().dataStoreDataClient();

        DataStoreCondition dataStoreCondition = ConditionConvert.buildDataStoreCondition(condition, ruleContext);
        dataStoreDataClient.delete(dataEntity, dataStoreCondition);
        return super.returnOrNext(null, ruleContext);
    }

}
