package com.deeppaas.extapi.biz.test;

import com.deeppaas.extapi.biz.enums.ApiReqBodyType;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.enums.ApiMethod;
import com.deeppaas.extapi.biz.enums.ApiResBodyType;
import com.deeppaas.extapi.biz.service.ThirdApiClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/27
 */
public class ThirdApiTest2 {

    public static void main(String[] args){
        ExtApiDO testApi = initTestApi();
        List<ExtApiParamDO> testApiParams = initTestApiParams();
        Map<String, Object> reqParams = initTestReqParams();
        ThirdApiClient client = ThirdApiClient.of(testApi, testApiParams, null);
        Object result = client.execute(reqParams);
        System.out.println(result);
    }

    private static ExtApiDO initTestApi() {
        ExtApiDO extApiDO = new ExtApiDO();
        extApiDO.setMethod(ApiMethod.GET.name());
        extApiDO.setUrl("http://*************:8080/api/user_num");
        extApiDO.setResBodyType(ApiReqBodyType.JSON.name());
        extApiDO.setResBodyType(ApiResBodyType.JSON_DATA_ENTITY.name());
        return extApiDO;
    }


    private static List<ExtApiParamDO> initTestApiParams() {
        List<ExtApiParamDO> apiParams = new ArrayList<>();
        return apiParams;
    }

    private static Map<String, Object> initTestReqParams() {
        Map<String, Object> params = new HashMap<>();
        return params;
    }

}
