package com.deeppaas.common.web.handler;

import com.deeppaas.common.web.context.BodyParamContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
@Component
public class CommonHandlerInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(CommonHandlerInterceptor.class);
    private static ThreadLocal<Long> startTimeLocal = new ThreadLocal<Long>();

    /**
     * 预处理
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        startTimeLocal.set(System.currentTimeMillis());
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
        long handleTime = System.currentTimeMillis() - startTimeLocal.get();
        logger.info("url:[{}][time:{}]", request.getRequestURI(), handleTime);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        /** 统一对使用ThreadLocal的线程信息进行管理 */
        // 清除当前REST请求POST传值参数上线文信息
        BodyParamContext.remove();
        startTimeLocal.remove();
    }
}