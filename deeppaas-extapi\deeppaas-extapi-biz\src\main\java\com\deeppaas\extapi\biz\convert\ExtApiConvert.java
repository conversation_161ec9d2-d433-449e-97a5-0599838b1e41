package com.deeppaas.extapi.biz.convert;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.datastore.api.client.model.DataStoreEntityFieldCO;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.enums.ApiResBodyType;
import com.deeppaas.extapi.biz.model.ExtApiParam;
import com.deeppaas.extapi.biz.model.ExtApiRuleParam;
import com.deeppaas.extapi.biz.model.ExtApiSimpleVO;
import com.deeppaas.extapi.biz.model.ExtApiVO;
import com.deeppaas.rule.api.enums.RuleOwnerType;
import com.deeppaas.rule.api.model.RuleEntityPropertyPO;
import com.deeppaas.rule.api.model.RulePO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class ExtApiConvert {

    public static ExtApiVO entityToVO(ExtApiDO apiDO) {
        if (apiDO == null) {
            return null;
        }
        ExtApiVO vo = new ExtApiVO();
        vo.setId(apiDO.getId());
        vo.setName(apiDO.getName());
        vo.setMethod(apiDO.getMethod());
        vo.setSelfApi(apiDO.getSelfApi());
        vo.setUrl(apiDO.getUrl());
        vo.setReqBodyType(apiDO.getReqBodyType());
        vo.setUsable(apiDO.getUsable());
        vo.setRemark(apiDO.getRemark());
        vo.setOwner(apiDO.getOwner());
        vo.setResBodyType(apiDO.getResBodyType());
        vo.setResBodyEntity(apiDO.getResBodyEntity());
        vo.setCreateTime(apiDO.getCreateTime());
        vo.setRuleId(apiDO.getRuleId());
        vo.setHasRule(StringHelper.isNotEmpty(apiDO.getRuleId()));
        return vo;
    }

    public static List<ExtApiVO> entityToVOS(List<ExtApiDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(ExtApiConvert::entityToVO).collect(Collectors.toList());
    }

    public static ExtApiDO paramToEntity(ExtApiParam param) {
        ExtApiDO apiDO = new ExtApiDO();
        apiDO.setName(param.getName());
        apiDO.setMethod(param.getMethod());
        apiDO.setSelfApi(param.getSelfApi());
        apiDO.setUrl(param.getUrl());
        apiDO.setReqBodyType(param.getReqBodyType());
        apiDO.setUsable(param.getUsable());
        apiDO.setRemark(param.getRemark());
        apiDO.setOwner(param.getOwner());
        apiDO.setResBodyType(param.getResBodyType());
        apiDO.setResBodyEntity(param.getResBodyEntity());
        return apiDO;
    }

    public static void paramToEntity(ExtApiDO apiDO, ExtApiParam param) {
        apiDO.setName(param.getName());
        apiDO.setMethod(param.getMethod());
        apiDO.setSelfApi(param.getSelfApi());
        apiDO.setUrl(param.getUrl());
        apiDO.setReqBodyType(param.getReqBodyType());
        apiDO.setUsable(param.getUsable());
        apiDO.setRemark(param.getRemark());
        apiDO.setOwner(param.getOwner());
        apiDO.setResBodyType(param.getResBodyType());
        apiDO.setResBodyEntity(param.getResBodyEntity());
    }

    public static RulePO ruleParamToPO(ExtApiDO apiDO, ExtApiRuleParam param) {
        RulePO rulePO = new RulePO();
        rulePO.setId(param.getId());
        rulePO.setName(param.getName());
        rulePO.setBackEnd(param.getBackEnd());
        rulePO.setOwnerType(RuleOwnerType.EXT_API.name());
        rulePO.setOwnerId(param.getApiId());
        rulePO.setContent(param.getContent());

        ApiResBodyType apiResBodyType = ApiResBodyType.valueOf(apiDO.getResBodyType());
        rulePO.setReturnType(apiResBodyType.getDataType().name());
        rulePO.setReturnEntity(apiDO.getResBodyEntity());

        rulePO.setParamType("");
        rulePO.setParamEntity("");

        return rulePO;
    }

    public static List<ExtApiSimpleVO> entityToSimpleVOS(List<ExtApiDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(ExtApiConvert::entityToSimpleVO).collect(Collectors.toList());
    }

    private static ExtApiSimpleVO entityToSimpleVO(ExtApiDO apiDO) {
        ExtApiSimpleVO vo = new ExtApiSimpleVO();
        vo.setId(apiDO.getId());
        vo.setName(apiDO.getName());
        vo.setMethod(apiDO.getMethod());
        return vo;
    }

    public static List<ExtApiParamDO> storeEntityFieldToParams(String apiId, String position,
                                                               List<DataStoreEntityFieldCO> fieldList) {
        List<ExtApiParamDO> paramDOList = new ArrayList<>();
        for (DataStoreEntityFieldCO fieldCO : fieldList) {
            ExtApiParamDO paramDO = new ExtApiParamDO();
            paramDO.setApiId(apiId);
            paramDO.setCode(fieldCO.getCode());
            paramDO.setName(fieldCO.getName());
            paramDO.setType(fieldCO.getDataType());
            paramDO.setEntity(fieldCO.getEntityCode());
            paramDO.setPosition(position);
            paramDO.setRequired(BoolHelper.INT_FALSE);
            paramDO.setRight(BoolHelper.INT_FALSE);
            paramDOList.add(paramDO);
        }
        return paramDOList;
    }

    public static List<ExtApiParamDO> ruleEntityFieldToParams(String apiId, String position, List<RuleEntityPropertyPO> fieldList) {
        List<ExtApiParamDO> paramDOList = new ArrayList<>();
        for (RuleEntityPropertyPO fieldPO : fieldList) {
            ExtApiParamDO paramDO = new ExtApiParamDO();
            paramDO.setApiId(apiId);
            paramDO.setCode(fieldPO.getName());
            paramDO.setName(fieldPO.getRemark());
            paramDO.setType(fieldPO.getType());
            paramDO.setEntity(fieldPO.getTypeEntity());
            paramDO.setPosition(position);
            paramDO.setRequired(BoolHelper.INT_FALSE);
            paramDO.setRight(BoolHelper.INT_FALSE);
            paramDOList.add(paramDO);
        }
        return paramDOList;
    }
}
