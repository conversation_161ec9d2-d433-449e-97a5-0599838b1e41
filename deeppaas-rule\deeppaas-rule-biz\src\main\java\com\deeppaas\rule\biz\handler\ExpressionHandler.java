package com.deeppaas.rule.biz.handler;

import com.deeppaas.common.script.ScriptUtils;
import com.deeppaas.rule.biz.RuleConst;
import com.deeppaas.rule.biz.RuleContext;

import java.util.HashMap;
import java.util.Map;

/**
 * 表达式处理器
 * <AUTHOR>
 * @date 2022/3/2
 */
public class ExpressionHandler {

    public static Object execute(String expression, RuleContext ruleContext) {
        Map<String, Object> params = new HashMap<>();
        params.put(RuleConst.EXPR_PARAM, ruleContext.getParam());
        params.put(RuleConst.EXPR_VARS, ruleContext.getVarMap());
        params.put(RuleConst.EXPR_EFFECT, ruleContext.getEffect());
        Object result = ScriptUtils.eval(expression, params);
        return result;
    }

}
