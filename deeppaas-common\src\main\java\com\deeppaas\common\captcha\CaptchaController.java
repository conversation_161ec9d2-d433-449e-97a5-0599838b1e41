package com.deeppaas.common.captcha;

import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.RestModel;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-06-18
 */
@RestController
@RequestMapping("/api/captcha")
public class CaptchaController {
    /**
     * @Description: 获取新的验证
     * @param response
     * <AUTHOR>
     * @date 2019-06-18
     */
    @GetMapping(value="/get")
    public RestModel<CaptchaToken> get(HttpServletResponse response){
        CaptchaToken token = CaptchaBuilder.initToken();
        Cookie userSecretCookie = new Cookie(CaptchaToken.COOKIE_NAME, token.getKey());
        userSecretCookie.setPath("/");
        userSecretCookie.setHttpOnly(true);
        response.addCookie(userSecretCookie);
        return RestModel.data(token);
    }

    /***
     * @Description: 检查
     * @param request
     * @param pointX
     * <AUTHOR>
     * @date 2019-06-18
     */
    @GetMapping(value="/check")
    public RestModel<Boolean> check(HttpServletRequest request, Integer pointX){
        String captchaKey = getCaptchaKey(request);
        Integer cachePointX = CaptchaCache.get(captchaKey);
        if(cachePointX!=null&&pointX!=null){
            int match = pointX-cachePointX;
            if(match<5 && match>-5){
                return RestModel.data(true);
            }
        }
        return RestModel.data(false);
    }

    private String getCaptchaKey(HttpServletRequest httpRequest) {
        Cookie[] cookies = httpRequest.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (CaptchaToken.COOKIE_NAME.equals(cookie.getName())
                        && StringHelper.isEmpty(cookie.getPath())
                        && cookie.getMaxAge() != 0) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}
