package com.deeppaas.processConfiguration.domain.model.vo;

import com.deeppaas.processConfiguration.domain.model.dto.TaskConfigDto;
import lombok.Data;

import java.util.List;

@Data
public class ProcessConfigVO {

    private String taskInfoId;
    private String processId;
    private String callbackId;
    private String processName;
    private String processTaskId;
    private String processTaskName;
    private String templates;
    private Integer templateNo;
    private List<TaskConfigDto> configs;

}
