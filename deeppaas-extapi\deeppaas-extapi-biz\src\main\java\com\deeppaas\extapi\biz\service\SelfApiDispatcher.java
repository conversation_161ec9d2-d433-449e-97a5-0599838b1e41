package com.deeppaas.extapi.biz.service;

import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.account.api.client.model.LocalUser;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.LogerHelper;
import com.deeppaas.common.logger.Loggers;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.enums.ApiPositionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.async.WebAsyncManager;
import org.springframework.web.context.request.async.WebAsyncUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.servlet.*;
import org.springframework.web.servlet.mvc.condition.PathPatternsRequestCondition;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.condition.RequestMethodsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.WebUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 平台API调度器
 * <AUTHOR>
 * @date 2022/8/17
 */
@Component
public class SelfApiDispatcher implements ApplicationContextAware {
    private static final String DEFAULT_STRATEGIES_PATH = "DispatcherServlet.properties";
    @Nullable
    private static Properties defaultStrategies;
    @Nullable
    private MultipartResolver multipartResolver;
    @Nullable
    private List<HandlerMapping> handlerMappings;
    private RequestMappingHandlerMapping requestMappingHandlerMapping;
    @Nullable
    private List<HandlerAdapter> handlerAdapters;
    @Nullable
    private RequestToViewNameTranslator viewNameTranslator;
    @Nullable
    private LocaleResolver localeResolver;
    @Nullable
    private List<ViewResolver> viewResolvers;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        initStrategies(context);
    }

    protected void initStrategies(ApplicationContext context) {
        initMultipartResolver(context);
        initLocaleResolver(context);
        initHandlerMappings(context);
        initHandlerAdapters(context);
        initRequestToViewNameTranslator(context);
        initViewResolvers(context);
    }

    private void initMultipartResolver(ApplicationContext context) {
        try {
            this.multipartResolver = context.getBean(DispatcherServlet.MULTIPART_RESOLVER_BEAN_NAME, MultipartResolver.class);
        }
        catch (NoSuchBeanDefinitionException ex) {
            // Default is no multipart resolver.
            this.multipartResolver = null;
        }
    }

    private void initLocaleResolver(ApplicationContext context) {
        try {
            this.localeResolver = context.getBean(DispatcherServlet.LOCALE_RESOLVER_BEAN_NAME, LocaleResolver.class);
        }
        catch (NoSuchBeanDefinitionException ex) {
            this.localeResolver = getDefaultStrategy(context, LocaleResolver.class);
        }
    }

    private void initHandlerMappings(ApplicationContext context) {
        this.handlerMappings = null;

        Map<String, HandlerMapping> matchingBeans =
                BeanFactoryUtils.beansOfTypeIncludingAncestors(context, HandlerMapping.class, true, false);
        if (!matchingBeans.isEmpty()) {
            this.handlerMappings = new ArrayList<>(matchingBeans.values());
            AnnotationAwareOrderComparator.sort(this.handlerMappings);
        }

        requestMappingHandlerMapping = (RequestMappingHandlerMapping) matchingBeans.get("requestMappingHandlerMapping");
        if (this.handlerMappings == null) {
            this.handlerMappings = getDefaultStrategies(context, HandlerMapping.class);
        }

    }

    private void initHandlerAdapters(ApplicationContext context) {
        this.handlerAdapters = null;

        Map<String, HandlerAdapter> matchingBeans =
                BeanFactoryUtils.beansOfTypeIncludingAncestors(context, HandlerAdapter.class, true, false);
        if (!matchingBeans.isEmpty()) {
            this.handlerAdapters = new ArrayList<>(matchingBeans.values());
            AnnotationAwareOrderComparator.sort(this.handlerAdapters);
        }

        if (this.handlerAdapters == null) {
            this.handlerAdapters = getDefaultStrategies(context, HandlerAdapter.class);
        }
    }

    private void initRequestToViewNameTranslator(ApplicationContext context) {
        try {
            this.viewNameTranslator =
                    context.getBean(DispatcherServlet.REQUEST_TO_VIEW_NAME_TRANSLATOR_BEAN_NAME, RequestToViewNameTranslator.class);
        }
        catch (NoSuchBeanDefinitionException ex) {
            this.viewNameTranslator = getDefaultStrategy(context, RequestToViewNameTranslator.class);
        }
    }

    private void initViewResolvers(ApplicationContext context) {
        this.viewResolvers = null;

        Map<String, ViewResolver> matchingBeans =
                BeanFactoryUtils.beansOfTypeIncludingAncestors(context, ViewResolver.class, true, false);
        if (!matchingBeans.isEmpty()) {
            this.viewResolvers = new ArrayList<>(matchingBeans.values());
            AnnotationAwareOrderComparator.sort(this.viewResolvers);
        }

        if (this.viewResolvers == null) {
            this.viewResolvers = getDefaultStrategies(context, ViewResolver.class);
        }
    }

    /**
     * 直接转发
     * @param extApi
     * @param request
     * @param response
     */
    public void forward(ExtApiDO extApi, HttpServletRequest request, HttpServletResponse response) {
        try {
            request.getRequestDispatcher(extApi.getUrl()).forward(request, response);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw RunException.error(ex.getMessage());
        }
    }

    /**
     * 动作调用转发
     * @param extApi
     * @param extApiParams
     * @param request
     * @param response
     */
    public void restActionForward(ExtApiDO extApi, List<ExtApiParamDO> extApiParams, Map<String, Object> paramMap, HttpServletRequest request, HttpServletResponse response) {
        String forwardUrl = extApi.getUrl();
        SelfApiRequest selfApiRequest = new SelfApiRequest(extApi.getUrl(), extApi.getMethod(), request);
        if(extApiParams!=null){
            for(ExtApiParamDO paramDO : extApiParams){
                String paramCode = paramDO.getCode();
                Object paramValue = paramMap.get(paramDO.getCode());
                ApiPositionType positionType = ApiPositionType.valueOf(paramDO.getPosition());
                switch (positionType){
                    case HEADER -> selfApiRequest.addHeader(paramCode, paramValue==null?"":paramValue.toString());
                    case PATH -> {
                        Map<String, String> uriTemplateVars = (Map<String, String>) selfApiRequest.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
                        uriTemplateVars = new HashMap<>(uriTemplateVars);
                        uriTemplateVars.put(paramCode, paramValue==null?"":paramValue.toString());
                        selfApiRequest.setAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE, uriTemplateVars);
                        forwardUrl = forwardUrl.replaceAll("\\{"+paramCode+"\\}",paramValue==null?"":paramValue.toString());
                    }
                    case QUERY -> selfApiRequest.addParameter(paramCode, paramValue);
                    // todo body处理
                    case BODY -> selfApiRequest.addParameter(paramCode, paramValue);
                }
            }
        }
        selfApiRequest.setRequestURI(forwardUrl);
        doForward(getTargetHandler(extApi), selfApiRequest, response);
    }

    public void formActionForward(ExtApiDO extApi, List<ExtApiParamDO> extApiParams, HttpServletRequest request, HttpServletResponse response) {
        String forwardUrl = extApi.getUrl();
        SelfApiRequest selfApiRequest = new SelfApiRequest(extApi.getUrl(), extApi.getMethod(), request);
        if(extApiParams!=null){
            for(ExtApiParamDO paramDO : extApiParams){
                String paramCode = paramDO.getCode();
                Object paramValue = request.getParameter(paramCode);
                ApiPositionType positionType = ApiPositionType.valueOf(paramDO.getPosition());
                switch (positionType){
                    case HEADER -> selfApiRequest.addHeader(paramCode, paramValue==null?"":paramValue.toString());
                    case PATH -> {
                        Map<String, String> uriTemplateVars = (Map<String, String>) selfApiRequest.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
                        uriTemplateVars = new HashMap<>(uriTemplateVars);
                        uriTemplateVars.put(paramCode, paramValue==null?"":paramValue.toString());
                        selfApiRequest.setAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE, uriTemplateVars);
                        forwardUrl = forwardUrl.replaceAll("\\{"+paramCode+"\\}",paramValue==null?"":paramValue.toString());
                        forwardUrl = forwardUrl.replaceAll("{"+paramCode+"}",paramValue==null?"":paramValue.toString());
                    }
                    case QUERY -> selfApiRequest.addParameter(paramCode, paramValue);
                }
            }
        }
        selfApiRequest.setRequestURI(forwardUrl);
        doForward(getTargetHandler(extApi), selfApiRequest, response);
    }
    private void doForward(HandlerMethod targetHandler, HttpServletRequest selfRequest, HttpServletResponse response) {
        WebAsyncManager asyncManager = WebAsyncUtils.getAsyncManager(selfRequest);
        try {
            ModelAndView mv = null;
            try {
                // Determine handler adapter for the current request.
                HandlerAdapter ha = getHandlerAdapter(targetHandler);
                LocalUser localUser = LocalUserContext.getLocalUser();
                if (localUser == null) {
                    Loggers.apiLogger.info("[nobody]Forward[{}:{}]", selfRequest.getMethod(), selfRequest.getRequestURI());
                } else {
                    Loggers.apiLogger.info("[{}/{}]Forward[{}:{}]", localUser.getCode(), localUser.getName(), selfRequest.getMethod(),selfRequest.getRequestURI());
                }
                // Actually invoke the handler.
                mv = ha.handle(selfRequest, response, targetHandler);

                if (asyncManager.isConcurrentHandlingStarted()) {
                    return;
                }
                applyDefaultViewName(selfRequest, mv);
            } catch (Exception ex) {
                LogerHelper.errorLog(Loggers.apiLogger, ex);
                throw RunException.error(ex.getMessage());
            } catch (Throwable err) {
                throw RunException.error(err.getMessage());
            }
            processDispatchResult(selfRequest, response, mv, null);
        }catch (Exception ex) {
            ex.printStackTrace();
            throw RunException.error(ex.getMessage());
        }catch (Throwable err) {
            throw RunException.error(err.getMessage());
        }
    }

    private HandlerMethod getTargetHandler(ExtApiDO extApi) {
        Map<RequestMappingInfo, HandlerMethod> map = requestMappingHandlerMapping.getHandlerMethods();
        for(Map.Entry<RequestMappingInfo, HandlerMethod> entry : map.entrySet()){
            RequestMappingInfo rmi = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();
            Set<String> patternUrls = null;
            PathPatternsRequestCondition pathPatternsCondition = rmi.getPathPatternsCondition();
            if(pathPatternsCondition!=null){
                patternUrls = pathPatternsCondition.getPatternValues();
            }else {
                PatternsRequestCondition patternsCondition = rmi.getPatternsCondition();
                patternUrls = patternsCondition==null ? null : patternsCondition.getPatterns();
            }
            if(patternUrls!=null){
                boolean urlMatch = patternUrls.stream().anyMatch(patternUrl -> extApi.getUrl().equals(patternUrl));
                if(urlMatch){
                    RequestMethodsRequestCondition methodsCondition = rmi.getMethodsCondition();
                    if(methodsCondition.getMethods().isEmpty()){
                        return handlerMethod.createWithResolvedBean();
                    }
                    for(RequestMethod method : methodsCondition.getMethods()) {
                        if(method.name().equalsIgnoreCase(extApi.getMethod())){
                            return handlerMethod.createWithResolvedBean();
                        }
                    }
                }
            }
        }
        throw RunException.error("没有对应API");
    }

    protected HandlerAdapter getHandlerAdapter(Object handler) throws ServletException {
        if (this.handlerAdapters != null) {
            for (HandlerAdapter adapter : this.handlerAdapters) {
                if (adapter.supports(handler)) {
                    return adapter;
                }
            }
        }
        throw new ServletException("No adapter for handler [" + handler +
                "]: The DispatcherServlet configuration needs to include a HandlerAdapter that supports this handler");
    }

    private void applyDefaultViewName(HttpServletRequest request, @Nullable ModelAndView mv) throws Exception {
        if (mv != null && !mv.hasView()) {
            String defaultViewName = getDefaultViewName(request);
            if (defaultViewName != null) {
                mv.setViewName(defaultViewName);
            }
        }
    }
    private void processDispatchResult(HttpServletRequest request, HttpServletResponse response,
                                       @Nullable ModelAndView mv,
                                       @Nullable Exception exception) throws Exception {

        boolean errorView = false;

        if (exception != null) {
            throw RunException.error(exception.getMessage());
        }

        // Did the handler return a view to render?
        if (mv != null && !mv.wasCleared()) {
            render(mv, request, response);
            if (errorView) {
                WebUtils.clearErrorRequestAttributes(request);
            }
        }

        if (WebAsyncUtils.getAsyncManager(request).isConcurrentHandlingStarted()) {
            // Concurrent handling started during a forward
            return;
        }

    }
    protected void render(ModelAndView mv, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // Determine locale for request and apply it to the response.
        Locale locale = (this.localeResolver != null ? this.localeResolver.resolveLocale(request) : request.getLocale());
        response.setLocale(locale);

        View view;
        String viewName = mv.getViewName();
        if (viewName != null) {
            // We need to resolve the view name.
            view = resolveViewName(viewName, mv.getModel(), locale, request);
            if (view == null) {
                throw new ServletException("Could not resolve view with name '" + mv.getViewName());
            }
        }
        else {
            // No need to lookup: the ModelAndView object contains the actual View object.
            view = mv.getView();
            if (view == null) {
                throw new ServletException("ModelAndView [" + mv + "] neither contains a view name nor a View object ");
            }
        }

        try {
            if (mv.getStatus() != null) {
                request.setAttribute(View.RESPONSE_STATUS_ATTRIBUTE, mv.getStatus());
                response.setStatus(mv.getStatus().value());
            }
            view.render(mv.getModel(), request, response);
        }
        catch (Exception ex) {
            throw ex;
        }
    }

    private boolean hasMultipartException(HttpServletRequest request) {
        Throwable error = (Throwable) request.getAttribute(WebUtils.ERROR_EXCEPTION_ATTRIBUTE);
        while (error != null) {
            if (error instanceof MultipartException) {
                return true;
            }
            error = error.getCause();
        }
        return false;
    }

    @Nullable
    protected String getDefaultViewName(HttpServletRequest request) throws Exception {
        return (this.viewNameTranslator != null ? this.viewNameTranslator.getViewName(request) : null);
    }

    @Nullable
    protected View resolveViewName(String viewName, @Nullable Map<String, Object> model,
                                   Locale locale, HttpServletRequest request) throws Exception {
        if (this.viewResolvers != null) {
            for (ViewResolver viewResolver : this.viewResolvers) {
                View view = viewResolver.resolveViewName(viewName, locale);
                if (view != null) {
                    return view;
                }
            }
        }
        return null;
    }

    protected <T> List<T> getDefaultStrategies(ApplicationContext context, Class<T> strategyInterface) {
        if (defaultStrategies == null) {
            try {
                // Load default strategy implementations from properties file.
                // This is currently strictly internal and not meant to be customized
                // by application developers.
                ClassPathResource resource = new ClassPathResource(DEFAULT_STRATEGIES_PATH, DispatcherServlet.class);
                defaultStrategies = PropertiesLoaderUtils.loadProperties(resource);
            }
            catch (IOException ex) {
                throw new IllegalStateException("Could not load '" + DEFAULT_STRATEGIES_PATH + "': " + ex.getMessage());
            }
        }

        String key = strategyInterface.getName();
        String value = defaultStrategies.getProperty(key);
        if (value != null) {
            String[] classNames = StringUtils.commaDelimitedListToStringArray(value);
            List<T> strategies = new ArrayList<>(classNames.length);
            for (String className : classNames) {
                try {
                    Class<?> clazz = ClassUtils.forName(className, DispatcherServlet.class.getClassLoader());
                    Object strategy = createDefaultStrategy(context, clazz);
                    strategies.add((T) strategy);
                }
                catch (ClassNotFoundException ex) {
                    throw new BeanInitializationException(
                            "Could not find DispatcherServlet's default strategy class [" + className +
                                    "] for interface [" + key + "]", ex);
                }
                catch (LinkageError err) {
                    throw new BeanInitializationException(
                            "Unresolvable class definition for DispatcherServlet's default strategy class [" +
                                    className + "] for interface [" + key + "]", err);
                }
            }
            return strategies;
        }
        else {
            return Collections.emptyList();
        }
    }

    protected Object createDefaultStrategy(ApplicationContext context, Class<?> clazz) {
        return context.getAutowireCapableBeanFactory().createBean(clazz);
    }

    protected <T> T getDefaultStrategy(ApplicationContext context, Class<T> strategyInterface) {
        List<T> strategies = getDefaultStrategies(context, strategyInterface);
        if (strategies.size() != 1) {
            throw new BeanInitializationException(
                    "DispatcherServlet needs exactly 1 strategy for interface [" + strategyInterface.getName() + "]");
        }
        return strategies.get(0);
    }
}
