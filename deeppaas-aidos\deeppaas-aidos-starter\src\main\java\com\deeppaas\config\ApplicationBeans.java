package com.deeppaas.config;

import com.deeppaas.account.biz.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/11/27
 */
@Configuration
public class ApplicationBeans implements ApplicationRunner {
    private static final Logger logger = LoggerFactory.getLogger(ApplicationRunner.class);
    @Autowired
    private UserService userService;

    @Override
    public void run(ApplicationArguments args) {
        userService.adminInitCheck();
        logger.info("管理员初始化检查完成");
        logger.info("启动成功！！！");
    }
}
