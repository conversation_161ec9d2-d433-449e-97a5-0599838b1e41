package com.deeppaas.file.desk.web;

import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.api.model.FileEntity;
import com.deeppaas.file.desk.model.DeskFileSign;
import com.deeppaas.file.desk.model.DeskTempUrlReq;
import org.springframework.http.CacheControl;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/2/25
 */
@Controller
@RequestMapping("/dss")
public class FileServiceController {
    private final FileClient fileClient;

    public FileServiceController(FileClient fileClient) {
        this.fileClient = fileClient;
    }

    @GetMapping("/{group}/**")
    public void get(HttpServletRequest request,
                    @PathVariable String group,
                    @RequestParam(required = false) String sign,
                    @RequestParam(required = false) String fileAlias,
                    @RequestParam(required = false) String process,
                    @RequestParam(required = false) Long expireTime,
                    HttpServletResponse response){
        if(request.getRequestURI().length()<6){
            redirectNoFile(response);
            return;
        }
        String key = request.getServletPath().substring(6+group.length());
        FileGroup fileGroup = fileClient.getFileGroup(group);
        if(fileGroup.isPublic()){
            response.setHeader("Cache-Control", CacheControl.maxAge(30, TimeUnit.DAYS).cachePublic().getHeaderValue());
        }else {
            DeskTempUrlReq tempUrlReq = new DeskTempUrlReq(group, key, fileAlias, process, expireTime, null);
            String encodeSign = DeskFileSign.getTempUrlSign(tempUrlReq);
            if(!encodeSign.equals(sign)){
                this.redirectAuthError(response);
                return;
            }
            if(System.currentTimeMillis()>expireTime){
                this.redirectTimeOut(response);
                return;
            }
        }
        FileEntity fileEntity = fileClient.getFile(group, key);

        if (fileEntity==null) {
            this.redirectNoFile(response);
        }else {
            try {
                if(StringUtils.hasText(fileAlias)){
                    response.setContentType("application/octet-stream");
                    response.setHeader("Content-disposition", "attachment;filename="+ URLEncoder.encode(fileAlias, "UTF-8"));
                }
                ServletOutputStream outputStream = response.getOutputStream();
                FileCopyUtils.copy(fileEntity.getContent(), outputStream);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void redirectNoFile(HttpServletResponse response) {
        try {
            response.getWriter().write("no file");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void redirectAuthError(HttpServletResponse response) {
        try {
            response.getWriter().write("no auth");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void redirectTimeOut(HttpServletResponse response) {
        try {
            response.getWriter().write("sign time out");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
