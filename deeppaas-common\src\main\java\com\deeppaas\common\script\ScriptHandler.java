package com.deeppaas.common.script;

import com.deeppaas.common.exception.RunException;
import org.mvel2.MVEL;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 脚本执行器
 * <AUTHOR>
 * @Date 2019-05-29
 */
public class ScriptHandler {
    /** 表达式脚本 */
    private String expression;
    /** 表达式绑定数据 */
    private Map<String, Object> vars;

    /**
     * @Description: 私有构造，不支持自建
     * @param
     * @return
     * <AUTHOR>
     * @date 2019-05-29
     */
    private ScriptHandler(){
        this.vars = new HashMap<>();
    }

    public String getExpression() {
        return expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }

    public Map<String, Object> getVars() {
        return vars;
    }

    public void addVar(String name, Object value){
        this.vars.put(name, value);
    }

    public Object execute(){
        if(!StringUtils.hasText(this.expression)){
            throw RunException.error("脚本表达式不能为空");
        }
        try {
            return MVEL.eval(expression, vars);
        } catch (Exception ex) {
            throw RunException.error("表达式执行失败，异常："+ex.getMessage());
        }
    }

    /***
     * @Description: 构造一个新的执行器
     * @param
     * @return ScriptHandler
     * <AUTHOR>
     * @date 2019-05-29
     */
    public static ScriptHandler instance(){
        return new ScriptHandler();
    }

    /***
     * @Description: 构造一个新的执行器
     * @param expression 表达式
     * @return ScriptHandler
     * <AUTHOR>
     * @date 2019-05-29
     */
    public static ScriptHandler instance(String expression){
        ScriptHandler handler = instance();
        handler.setExpression(expression);
        return handler;
    }


}
