package com.deeppaas.datastore.biz.client;

import com.deeppaas.common.data.DataClass;
import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.datastore.api.client.DataStoreEntityClient;
import com.deeppaas.datastore.api.client.model.DataStoreEntityCO;
import com.deeppaas.datastore.api.client.model.DataStoreEntityFieldCO;
import com.deeppaas.datastore.biz.convert.DataStoreEntityConvert;
import com.deeppaas.datastore.biz.entity.DataStoreEntityDO;
import com.deeppaas.datastore.biz.entity.DataStoreEntityFieldDO;
import com.deeppaas.datastore.biz.service.DataStoreEntityService;
import com.deeppaas.tenant.api.client.AppClient;
import com.deeppaas.tenant.api.client.model.AppCO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/20
 */
@Component
public class DataStoreEntityClientImpl implements DataStoreEntityClient {
    private final DataStoreEntityService dataStoreEntityService;
    private final AppClient appClient;

    public DataStoreEntityClientImpl(DataStoreEntityService dataStoreEntityService, @Lazy AppClient appClient) {
        this.dataStoreEntityService = dataStoreEntityService;
        this.appClient = appClient;
    }

    @Override
    public DataStoreEntityCO findByCode(String entityCode) {
        DataStoreEntityDO entityDO = dataStoreEntityService.findByCode(entityCode);
        // 获取所属应用
        AppCO appCO = appClient.findByCode(entityDO.getAppCode());
        return DataStoreEntityConvert.entityToCO(entityDO, appCO.getName());
    }

    @Override
    public List<DataStoreEntityCO> findByCodes(Collection<String> entityCodes) {
        List<DataStoreEntityDO> entityDOList = dataStoreEntityService.findByCodes(entityCodes);
        Set<String> appCodes = entityDOList.stream().map(DataStoreEntityDO::getAppCode).collect(Collectors.toSet());
        List<AppCO> appCOList = appClient.findByCodes(appCodes);
        return DataStoreEntityConvert.entityToCOS(entityDOList, appCOList);
    }

    @Override
    public Map<String, DataClass> getFieldClassMap(String entityCode) {
        List<DataStoreEntityFieldDO> fieldDOS = dataStoreEntityService.listAllField(entityCode);
        Map<String, DataClass> fieldClassMap = new HashMap<>();
        for (DataStoreEntityFieldDO fieldDO : fieldDOS) {
            fieldClassMap.put(fieldDO.getCode(), new DataClass(SimpleDataType.valueOf(fieldDO.getDataType()), null, null));
        }
        return fieldClassMap;
    }

    @Override
    public List<DataStoreEntityFieldCO> listEntityField(String entityCode) {
        List<DataStoreEntityFieldDO> fieldDOList = dataStoreEntityService.listAllField(entityCode);
        return DataStoreEntityConvert.entityFieldToVOS(fieldDOList);
    }

    @Override
    public void deleteByApp(String appCode) {
        dataStoreEntityService.deleteByApp(appCode);
    }
}
