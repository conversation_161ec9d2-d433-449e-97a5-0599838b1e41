package com.deeppaas.configuration.biz.service;

import com.deeppaas.configuration.biz.dao.AidosConfigVariableDao;
import com.deeppaas.configuration.biz.entity.AidosConfigVariableDO;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AidosConfigVariableService {

    @Autowired
    private AidosConfigVariableDao aidosConfigVariableDao;

    public List<AidosConfigVariableDO> getAll(){
        return aidosConfigVariableDao.findAll();
    }

    public void insert(AidosConfigVariableDO aidosProcessDO){
        String id =aidosProcessDO.getId();
        if(StringHelper.isEmpty(id)){
            aidosProcessDO.setCreateTime(LocalDateTime.now());
            aidosConfigVariableDao.insert(aidosProcessDO);
        }else {
            aidosConfigVariableDao.save(aidosProcessDO) ;
        }

    }
    public List<AidosConfigVariableDO> findByAidosProcessConfigId(String aidosProcessConfigId){
        return aidosConfigVariableDao.findByAidosProcessConfigId( aidosProcessConfigId);
    }
    public List<AidosConfigVariableDO> findByProcessTaskId(String aidosProcessConfigId){
        return aidosConfigVariableDao.findByProcessTaskId( aidosProcessConfigId);
    }
    public List<AidosConfigVariableDO> findByAidosProcessConfigIdAndKeyIn(String aidosProcessConfigId,List<String> key){
        return aidosConfigVariableDao.findByAidosProcessConfigIdAndKeyIn( aidosProcessConfigId,key);
    }

    public void insert(List<AidosConfigVariableDO> aidosProcessDO){
        aidosProcessDO.forEach(item->{
            String id =item.getId();
            if(StringHelper.isEmpty(id)){
                item.setCreateTime(LocalDateTime.now());
                aidosConfigVariableDao.insert(item);
            }else {
                aidosConfigVariableDao.save(item) ;
            }
        });
    }
    public void del(String id){
        aidosConfigVariableDao.deleteById(id);
    }

    public PageData<AidosConfigVariableDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<AidosConfigVariableDO> page = aidosConfigVariableDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

}
