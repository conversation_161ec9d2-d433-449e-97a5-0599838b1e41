package com.deeppaas.datastore.biz.service;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import org.apache.poi.hssf.usermodel.HSSFFormulaEvaluator;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFFormulaEvaluator;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据仓库数据导入EXCEL读取
 * <AUTHOR>
 * @date 2022/9/24
 */
public class DataStoreDataExcelReader {
    private static final DataFormatter formatter = new DataFormatter();
    private Workbook workbook;
    private final String entityCode;
    private final List<DataStoreEntityFieldDTO> fields;
    private final List<String> readErrors = new ArrayList<>();
    private FormulaEvaluator formulaEvaluator;

    private DataStoreDataExcelReader(Workbook workbook, String entityCode, List<DataStoreEntityFieldDTO> fields) {
        this.workbook = workbook;
        this.entityCode = entityCode;
        this.fields = fields;
        if (workbook instanceof HSSFWorkbook) {
            formulaEvaluator = new HSSFFormulaEvaluator((HSSFWorkbook) workbook);
        }
        if (workbook instanceof XSSFWorkbook) {
            formulaEvaluator = new XSSFFormulaEvaluator((XSSFWorkbook) workbook);
        }
        if (workbook instanceof SXSSFWorkbook) {
            formulaEvaluator = new SXSSFFormulaEvaluator((SXSSFWorkbook) workbook);
        }
    }
    public static DataStoreDataExcelReader of(InputStream inputStream, String entityCode, List<DataStoreEntityFieldDTO> fields) {
        try {
            return new DataStoreDataExcelReader(WorkbookFactory.create(inputStream), entityCode, fields);
        } catch (IOException e) {
            throw RunException.error("IO异常：" + e.getMessage());
        }
    }

    public static DataStoreDataExcelReader of(File file, String entityCode, List<DataStoreEntityFieldDTO> fields) {
        try {
            return new DataStoreDataExcelReader(WorkbookFactory.create(file), entityCode, fields);
        } catch (IOException e) {
            throw RunException.error("IO异常：" + e.getMessage());
        }
    }

    public List<String> getReadErrors() {
        return readErrors;
    }

    public List<DataStoreData> readDataList(){
        readErrors.clear();
        String[] headers = getHeaders(0, fields.size());
        final int fieldCount = headers.length;
        Map<String, DataStoreEntityFieldDTO> fieldNameMap = fields.stream().collect(Collectors.toMap(DataStoreEntityFieldDTO::getName, Function.identity()));
        DataStoreEntityFieldDTO[] fieldArray = new DataStoreEntityFieldDTO[fieldCount];
        for(int cellNum=0; cellNum<fieldCount; cellNum++){
            String fieldName = headers[cellNum];
            DataStoreEntityFieldDTO fieldDTO = fieldNameMap.get(fieldName);
            fieldArray[cellNum]=fieldDTO;
            if(fieldDTO!=null){
                fieldNameMap.remove(fieldName);
            }
        }
        if(!fieldNameMap.isEmpty()){
            String error = "没有找到字段";
            for(String fieldName : fieldNameMap.keySet()){
                error+="["+fieldName+"]";
            }
            readErrors.add(error+"列，请检查EXCEL");
        }
        List<DataStoreData> dataList = getData(0, 1, fieldArray);
        return dataList;
    }

    public String[] getHeaders(int sheetIndex, int columnSize) {
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        Row row = sheet.getRow(0);
        if (row == null) {
            return new String[columnSize];
        }
        String[] cellValues = new String[columnSize];
        for(int cellNum=0; cellNum<columnSize; cellNum++){
            Cell cell = row.getCell(cellNum);
            if(cell!=null){
                cellValues[cellNum] = cell.getStringCellValue();
            }
        }
        return cellValues;
    }

    public List<DataStoreData> getData(int sheetIndex, int rowStart, DataStoreEntityFieldDTO[] fieldArray) {
        List<DataStoreData> dataList = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        int totalRow = sheet.getLastRowNum() + 1;
        for (int rowNum = rowStart; rowNum < totalRow; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                continue;
            }
            Map<String, Object> dataItem = new HashMap<>();
            for(int cellNum=0; cellNum<fieldArray.length; cellNum++){
                DataStoreEntityFieldDTO field = fieldArray[cellNum];
                if(field!=null){
                    Object value = getFieldData(row.getCell(cellNum), field);
                    dataItem.put(field.getCode(), value);
                }
            }
            if(!dataItem.isEmpty()) {
                dataList.add(DataStoreData.instance(entityCode,dataItem));
            }
        }
        return dataList;
    }

    private Object getFieldData(Cell cell, DataStoreEntityFieldDTO field) {
        if(cell==null || field==null){
            return null;
        }
        SimpleDataType dataType = SimpleDataType.valueOf(field.getDataType());
        switch (dataType){
            case TEXT -> {
                String strValue = formatter.formatCellValue(cell, formulaEvaluator).trim();
                if(field.getLength()<strValue.length()){
                    readErrors.add("字段["+field.getName()+"]长度为"+field.getLength()+"，上传数据为["+strValue+"]超长了");
                    return null;
                }
                return strValue;
            }
            case NUMBER -> {
                try {
                    CellType cellType = cell.getCellType();
                    switch (cellType){
                        case STRING ->{
                            String strValue = cell.getStringCellValue();
                            if(!StringUtils.hasText(strValue)){
                                return null;
                            }
                            return new BigDecimal(strValue);
                        }
                        case NUMERIC -> {
                            BigDecimal number = new BigDecimal(cell.getNumericCellValue());
                            number = number.setScale(field.getDecimalDigits(), RoundingMode.HALF_UP);
                            return number;
                        }
                        case BLANK -> {
                            return null;
                        }
                        default -> {
                            readErrors.add("字段["+field.getName()+"]为数字类型，上传数据为["+formatter.formatCellValue(cell, formulaEvaluator).trim()+"]");
                            return null;
                        }
                    }
                }catch (Exception e) {
                    readErrors.add("字段["+field.getName()+"]为数字类型，上传数据为["+formatter.formatCellValue(cell, formulaEvaluator).trim()+"]");
                    return null;
                }
            }
            case BOOLEAN -> {
                try {
                    return cell.getBooleanCellValue();
                }catch (Exception e){
                    readErrors.add("字段["+field.getName()+"]为BOOLEAN类型，上传数据为["+formatter.formatCellValue(cell, formulaEvaluator).trim()+"]");
                    return null;
                }
            }
            case DATE_TIME -> {
                String strValue = formatter.formatCellValue(cell, formulaEvaluator).trim();
                if(StringUtils.hasText(strValue)) {
                    String dataStr = DateHelper.formatDataTime(strValue);
                    if(dataStr==null){
                        readErrors.add("字段["+field.getName()+"]为日期时间类型，上传数据为["+strValue+"]");
                        return null;
                    }
                    return dataStr;
                }
                return null;
            }
            default -> {
                return formatter.formatCellValue(cell, formulaEvaluator).trim();
            }
        }
    }

    public void close() {
        workbook = null;
        formulaEvaluator = null;
    }

}
