package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据模型字段
 *
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@Table("datastore_entity_field")
@EqualsAndHashCode(callSuper = true)
public class DataStoreEntityFieldDO extends BaseUuidDO {
    /**
     * 数据模型编号
     **/
    @Column("entity_code")
    private String entityCode;
    /**
     * 字段编号
     **/
    @Column("code")
    private String code;
    /**
     * 字段名称
     **/
    @Column("name")
    private String name;
    /**
     * 数据库列名
     */
    @Column("column_name")
    private String columnName;
    /**
     * 数据类型
     */
    @Column("data_type")
    private String dataType;
    /**
     * 是否是读虚列
     */
    @Column("read_only")
    private Integer readOnly;
    /**
     * 是否是鉴别器字段
     */
    @Column("discriminator")
    private Integer discriminator;
    /**
     * 鉴别器鉴别值
     */
    @Column("discriminator_value")
    private String discriminatorValue;
    /**
     * 所属关联
     */
    @Column("join_code")
    private String joinCode;
    /**
     * 页面表单控件类型
     */
    @Column("web_type")
    private String webType;
    /**
     * 是否已修改覆盖，用于判定继承数据表情况
     */
    @Column("overwrote")
    private Integer overwrote;
}
