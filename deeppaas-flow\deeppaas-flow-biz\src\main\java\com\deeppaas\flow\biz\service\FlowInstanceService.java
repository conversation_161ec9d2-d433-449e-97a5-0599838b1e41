package com.deeppaas.flow.biz.service;

import java.util.List;

import com.deeppaas.common.model.PageSearch;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowPositionDO;
import com.deeppaas.flow.biz.entity.FlowTrackDO;
import com.deeppaas.flow.biz.enums.FlowInstanceState;
import org.springframework.data.domain.Page;

/**
 * @ClassName: FlowInstanceService 
 * @Description: 流程实例服务接口
 * <AUTHOR>
 * @date 2017年9月4日
 */
public interface FlowInstanceService {
	/**
	 * @Title: getInstance 
	 * @Description: 获取流程实例
	 * @param instanceId
	 * @return
	 * <AUTHOR>
	 */
	FlowInstanceDO getInstance(Long instanceId);
	/**
	 * @Title: close
	 * @Description: 关闭流程实例
	 * @param instance
	 * @param state
	 * @return
	 * <AUTHOR>
	 */
	FlowInstanceDO close(FlowInstanceDO instance, FlowInstanceState state);
	/**
	 * @Title: findByOrderId 
	 * @Description: 根据工单ID获取对应流程实例
	 * @param orderId
	 * @return
	 * <AUTHOR>
	 */
	FlowInstanceDO findByOrderId(String orderId);

	/**
	 * 获取流程定义
	 * @param defineCode
	 * @param orderId
	 * @return
	 */
	FlowInstanceDO getInstance(String defineCode, String orderId);
	/**
	 * 获取打开的流程实例
	 * @param flowDefineCode
	 * @param orderId
	 * @return
	 */
	FlowInstanceDO getOpenInstance(String flowDefineCode, String orderId);
	/**
	 * @Title: save
	 * @Description: 保存流程实例
	 * @param instance
	 * @return
	 * <AUTHOR>
	 */
	FlowInstanceDO save(FlowInstanceDO instance);
	/**
	 * @Title: findPrevPosition 
	 * @Description: 获取上一次流程位置
	 * @param instance
	 * @param nodeCode
	 * @return
	 * <AUTHOR>
	 */
	FlowPositionDO findPrevPosition(FlowInstanceDO instance, String nodeCode);
	
	/**
	 * @Title: findTrack 
	 * @Description: 获取流程实例轨迹信息
	 * @param instanceId
	 * <AUTHOR>
	 */
	List<FlowTrackDO> findTrack(Long instanceId);

	/**
	 * 更新优先级
	 * @param instanceId
	 * @param urgencyLevel
	 * @return
	 */
	void updatePriority(Long instanceId, Integer urgencyLevel);
	/**
	 * 删除实例
	 * @param instance
	 * <AUTHOR>
	 */
    void delete(FlowInstanceDO instance);

    /**
     * @Description: 获取流程位置
     * @param instanceId
     * @return java.util.List<com.lh.oa.flow.entity.FlowPosition>
     * <AUTHOR>
     */
    List<FlowPositionDO> findAllPosition(Long instanceId);

	/**
	 * 重载流程实例相关用户
	 * @param instanceId
	 */
	void reloadInstanceUsers(Long instanceId);

	/**
	 * 分页查询流程实例
	 * @param pageSearch
	 * @return
	 */
    Page<FlowInstanceDO> findForPage(PageSearch pageSearch);
}
