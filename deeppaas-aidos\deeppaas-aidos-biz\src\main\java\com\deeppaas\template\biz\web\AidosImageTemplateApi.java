package com.deeppaas.template.biz.web;

import com.deeppaas.template.biz.entity.AidosImageTemplateDO;
import com.deeppaas.template.biz.service.AidosImageTemplateService;
import com.deeppaas.common.helper.LogerHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@RequestMapping("/api/imagetemplate")
public class AidosImageTemplateApi {

    private static final Logger logger = LoggerFactory.getLogger(AidosImageTemplateApi.class);
    @Autowired
    private AidosImageTemplateService aidosImageTemplateService;

    @PostMapping("/page")
    public RestModel<PageData<AidosImageTemplateDO>> page(@RequestBody PageSearch pageSearch) {
        pageSearch.setPageSize(99999);
        PageData<AidosImageTemplateDO> list = aidosImageTemplateService.page(pageSearch);
        return RestModel.data(list);
    }

    @GetMapping("/getAll")
    public RestModel<List<AidosImageTemplateDO>> list() {
        List<AidosImageTemplateDO> list = aidosImageTemplateService.getAll();
        return RestModel.data(list);
    }
    @GetMapping("/getById")
    public RestModel<AidosImageTemplateDO> getById(@RequestParam String id) {
        AidosImageTemplateDO model = aidosImageTemplateService.get(id);
        return RestModel.data(model);
    }

    @PostMapping("/save")
    public RestModel list(@RequestBody AidosImageTemplateDO aidosImageTemplateDO) {
        String message = aidosImageTemplateService.save(aidosImageTemplateDO);
        if (StringHelper.isNotEmpty(message)) {
            return RestModel.error(500, message);
        }
        return RestModel.success("保存成功");
    }

    @GetMapping("/del")
    public RestModel list(@RequestParam String id) {
        aidosImageTemplateService.del(id);
        return RestModel.success("删除成功");
    }

    @PostMapping("/upload")
    public RestModel upload(AidosImageTemplateDO aidosImageTemplateDO, @RequestParam("file") MultipartFile file) {


        String path = System.getProperty("user.dir");
        path = path + File.separator +"templates" + File.separator + file.getOriginalFilename();

        File desFile = new File(path);
        if (!desFile.getParentFile().exists()) {
            desFile.mkdirs();
        }
        try {

            String toPath = desFile.toPath().toString();
            byte[] bytes = toPath.getBytes(StandardCharsets.UTF_8);
            aidosImageTemplateDO.setPath(Base64Utils.encodeToUrlSafeString(bytes));
            aidosImageTemplateDO.setName(file.getOriginalFilename());
            String message = aidosImageTemplateService.save(aidosImageTemplateDO);
            if (StringHelper.isNotEmpty(message))
                return RestModel.error(5001, "已存在相同名称模板【" + message + "】");
            file.transferTo(desFile);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }


        return RestModel.success("新增成功");
    }

    @RequestMapping(value = "/{imageKey}")
    public void getImage(@PathVariable String imageKey, HttpServletResponse response) {
        String filePath = new String(Base64Utils.decodeFromUrlSafeString(imageKey),StandardCharsets.UTF_8);
        File imageFile = new File(filePath);
        if (imageFile.exists()) {
            InputStream input = null;
            OutputStream out = null;
            try {
                input = new FileInputStream(imageFile);
                out = response.getOutputStream();
                byte[] buff = new byte[1024];
                int index = 0;
                while ((index = input.read(buff)) != -1) {
                    out.write(buff, 0, index);
                    out.flush();
                }
            } catch (IOException ex) {
                LogerHelper.errorLog(logger, ex);
            } finally {
                try {
                    if (input != null) {
                        input.close();
                    }
                    out.close();
                } catch (IOException e) {
                    LogerHelper.errorLog(logger, e);
                }
            }

        }

    }

}
