import{r as s}from"./util.bf0e86fd.js";function n(e){return s.post("/process/save",e)}function r(){return s.get("/process/getAll")}function o(e){return s.get("/process/del?"+e)}function p(e){return s.post("/processTask/save",e)}function c(e){return s.post("/processTask/page",e)}function i(e){return s.get("/processTask/del?"+e)}function g(e){return s.get("/imagetemplateType/del?"+e)}function u(e){return s.get("/ai/process/getTaskTemplate?processTaskId="+e)}function T(e,t){return s.get(`/ai/process/getTemplateByType?processTaskId=${e}&id=${t}`)}function l(e){return s.get(`/ai/process/delTaskTemplate?${e}`)}function m(e){return s.post("/ai/process/saveTaskTemplate",e)}function f(e){return s.post("/imagetemplateType/save",e)}function k(e){return s.post("/imagetemplate/page",e)}function v(e){return s.post("/imagetemplate/save",e)}function d(e){return s.get("/imagetemplate/del?"+e)}function y(e){return s.get("/processTask/getTemplate?"+e)}function I(e){return s.post("/processTask/config/page",e)}function S(e){return s.post("/processTask/config/saves",e)}function C(e){return s.post("/processTask/config/save",e)}function b(e){return s.get("/processTask/config/del?"+e)}function B(e){return s.get("/config/variable/get?"+e)}function E(e){return s.get("/processTask/getById?id="+e)}export{T as a,m as b,k as c,B as d,E as e,S as f,y as g,C as h,b as i,n as j,f as k,c as l,u as m,r as n,o,I as p,g as q,i as r,p as s,l as t,v as u,d as v};
