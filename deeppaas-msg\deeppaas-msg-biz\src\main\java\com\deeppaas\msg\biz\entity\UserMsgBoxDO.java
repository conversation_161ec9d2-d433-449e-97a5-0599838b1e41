package com.deeppaas.msg.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 用户消息栏
 * <AUTHOR>
 * @date 2022/9/2
 */
@Data
@Table("deeppaas_user_msg_box")
public class UserMsgBoxDO extends BaseUuidDO {
    /**
     * 名称，唯一
     */
    @Column("name")
    private String name;

    /**
     * 消息图标
     */
    @Column("icon")
    private String icon;

    /**
     * 1:开启/0:关闭
     */
    @Column("used")
    private Integer used;
}
