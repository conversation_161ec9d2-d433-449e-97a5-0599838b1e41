package com.deeppaas.flow.workbench.service;

import com.deeppaas.account.api.client.UserClient;
import com.deeppaas.account.api.client.model.User;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.AssertHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.flow.biz.*;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.element.FlowPageNode;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowRecordDO;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import com.deeppaas.flow.biz.enums.FlowInstanceState;
import com.deeppaas.flow.biz.model.FlowDefineModel;
import com.deeppaas.flow.biz.model.PastNodeInfo;
import com.deeppaas.flow.biz.model.vo.FlowRecordVO;
import com.deeppaas.flow.biz.service.FlowRecordService;
import com.deeppaas.flow.workbench.model.AssignParam;
import com.deeppaas.flow.workbench.model.WorkOrderAuth;
import com.deeppaas.flow.workbench.model.WorkTask;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务服务层
 *
 * <AUTHOR>
 * @date 2022/3/10
 */
@Service
public class WorkTaskService {
    private final FlowEngine flowEngine;
    private final UserClient userClient;
    private final FlowRecordService flowRecordService;

    public WorkTaskService(FlowEngine flowEngine, UserClient userClient, FlowRecordService flowRecordService) {
        this.flowEngine = flowEngine;
        this.userClient = userClient;
        this.flowRecordService = flowRecordService;
    }

    /**
     * 提交工单
     *
     * @param flowDefineCode
     * @param orderId
     * @param userCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult submit(String flowDefineCode, String startNode, String orderId, String userCode) {
        FlowInstanceDO instance = flowEngine.createInstance(flowDefineCode, startNode, orderId, userCode);
        ExecuteResult result = flowEngine.startInstance(instance, userCode);
        return result;
    }


    /**
     * 指定审批人并流转
     *
     * @param assignSubmitParams
     * @param userCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult assignSubmit(AssignParam[] assignSubmitParams, String userCode) {
        // 保存已选审批用户
        for (AssignParam assignSubmitParam : assignSubmitParams) {
            flowEngine.assign().assign(assignSubmitParam.getInstanceId(),
                    assignSubmitParam.getNodeCode(), assignSubmitParam.getActors(),
                    assignSubmitParam.getPriority(), assignSubmitParam.getLimitedTime());
        }
        FlowInstanceDO instance = flowEngine.instance().getInstance(assignSubmitParams[0].getInstanceId());
        // 更新流程实例紧急程度
        int priority = 1;
        for (AssignParam assignParam : assignSubmitParams) {
            if (assignParam.getPriority() != null && assignParam.getPriority() > priority) {
                priority = assignParam.getPriority();
            }
        }
        instance.setPriority(priority);
        // 查询流程实例
        ExecuteResult result = flowEngine.startInstance(instance, userCode);
        return result;
    }


    /**
     * @param taskId
     * @param userCode
     * @return
     * @Title: auditCheck
     * @Description: 审批检查，是否需要指派，是否需要必填审批意见
     */
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult auditCheck(Long taskId, String userCode) {
        FlowTaskDO task = flowEngine.task().getTask(taskId);
        return flowEngine.forwardExecuteCheck(task, userCode);
    }

    /**
     * @param taskId
     * @param userCode
     * @param comment
     * @Title: audit
     * @Description: 任务审批
     */
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult audit(Long taskId, String userCode, String comment) {
        FlowTaskDO task = flowEngine.task().getTask(taskId);
        ExecuteResult result = flowEngine.forwardExecute(task, userCode, comment);
        // TODO 审批事件？？
        return result;
    }

    /**
     * 指派并处理任务
     *
     * @param taskId
     * @param comment
     * @param assignParams
     * @param userCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult assignAudit(Long taskId, String comment, AssignParam[] assignParams, String userCode) {
        for (AssignParam assignParam : assignParams) {
            flowEngine.assign().assign(assignParam.getInstanceId(),
                    assignParam.getNodeCode(), assignParam.getActors(),
                    assignParam.getPriority(), assignParam.getLimitedTime());
        }
        return audit(taskId, userCode, comment);
    }

    /**
     * 撤销流程
     *
     * @param instanceId
     * @param userCode
     * @param comment
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long instanceId, String userCode, String comment) {
        FlowInstanceDO instance = flowEngine.instance().getInstance(instanceId);
        flowEngine.closeInstance(instance, userCode, FlowAction.cancel, comment);
    }

    /**
     * 中止流程，只有当前审批人才可以中止
     *
     * @param instanceId
     * @param userCode
     * @param comment
     */
    @Transactional(rollbackFor = Exception.class)
    public void stop(Long instanceId, String userCode, String comment) {
        FlowInstanceDO instance = flowEngine.instance().getInstance(instanceId);
        if (FlowInstanceState.OPEN.intValueEquals(instance.getState())) {
            throw new FlowException("流程已经关闭了");
        }
        List<FlowTaskDO> openTasks = flowEngine.task().findOpenTaskByInstanceIdForUser(instanceId, userCode);
        if (!openTasks.isEmpty()) {
            ExecuteResult result = flowEngine.closeInstance(instance, userCode, FlowAction.stop, comment);
        } else {
            throw new FlowException("您不是当前审批人，不能中止");
        }
    }

    /**
     * 获取驳回环节信息
     *
     * @param taskId
     * @param userCode
     * @return
     */
    public List<PastNodeInfo> getBackInfo(Long taskId, String userCode) {
        FlowTaskDO task = flowEngine.task().getTask(taskId);
        return flowEngine.getBackNodes(task, userCode);
    }

    /**
     * 驳回
     *
     * @param taskId
     * @param backNodeCode
     * @param comment
     * @param userCode
     * @return
     */
    public ExecuteResult back(Long taskId, String backNodeCode, String comment, String userCode) {
        FlowTaskDO task = flowEngine.task().getTask(taskId);
        ExecuteResult result = flowEngine.backwardExecute(task, backNodeCode, userCode, comment);
        if (result.isSuccess()) {
            // 驳回事件？？
        }
        return result;
    }

    /**
     * 撤回检查是否可撤回
     *
     * @param instanceId
     * @param userCode
     * @return
     */
    public List<PastNodeInfo> takeBackCheck(Long instanceId, String userCode) {
        return flowEngine.taskTakeBackTarget(instanceId, userCode);
    }

    /**
     * @param instanceId
     * @param userCode
     * @param comment
     * @return com.lh.oa.flow.ExecuteResult
     * @Description: 撤回
     * <AUTHOR>
     * @date 2019-02-26
     */
    public ExecuteResult takeBack(Long instanceId, String nodeCode, String userCode, String comment) {
        ExecuteResult executeResult = flowEngine.taskTakeBackExecute(instanceId, nodeCode, userCode, comment);
        return executeResult;
    }

    /**
     * 流程中止检查，是否可用中止
     *
     * @param instanceId
     * @param userCode
     * @return
     */
    public Boolean stopCheck(Long instanceId, String userCode) {
        FlowInstanceDO instance = flowEngine.instance().getInstance(instanceId);
        if (!FlowInstanceState.OPEN.intValueEquals(instance.getState())) {
            return false;
        }
        return true;
    }

    public String getFlowNodePage(String defineCode, String node) {
        FlowDefineModel flowDefineModel = flowEngine.define().getModelByCode(defineCode);
        if (flowDefineModel == null) {
            throw RunException.dataNotFound("没有找到编号为" + defineCode + "的流程");
        }
        String pageId = null;
        if (flowDefineModel.getNodes() != null) {
            FlowNode flowNode = flowDefineModel.getNode(node);
            if (flowNode instanceof FlowPageNode flowPageNode) {
                pageId = flowPageNode.getPage();
            } else {
                throw RunException.dataNotFound("编号为" + node + "的流程环节没有页面");
            }
        } else {
            throw RunException.dataNotFound("没有找到编号为" + node + "的流程环节");
        }
        return pageId;
    }

    public PageData<WorkTask> allTask(PageSearch pageSearch, String defineCode, String userCode) {
        // TODO 用户待办
        return null;
    }

    public List<WorkTask> openTask(String defineCode, String orderId, String userCode) {
        FlowInstanceDO flowInstanceDO = flowEngine.instance().getInstance(defineCode, orderId);
        AssertHelper.notNull(flowInstanceDO, "未找到流程实例信息，请联系系统管理员");
        List<FlowTaskDO> openTaskList = flowEngine.task().findOpenTaskByInstanceId(flowInstanceDO.getId());
        return buildOrderTasks(openTaskList, userCode);
    }

    private List<WorkTask> buildOrderTasks(List<FlowTaskDO> flowTaskDOList, String userCode) {
        if (flowTaskDOList.isEmpty()) {
            return Collections.emptyList();
        }
        Set<String> actorCodes = new HashSet<>();
        for (FlowTaskDO taskDO : flowTaskDOList) {
            if (StringHelper.isNotEmpty(taskDO.getOperator())) {
                actorCodes.add(taskDO.getOperator());
            }
            actorCodes.addAll(taskDO.getActors());
        }
        Map<String, User> actorMap = userClient.getByUserCode(actorCodes);
        List<WorkTask> workTaskList = new ArrayList<>();
        for (FlowTaskDO taskDO : flowTaskDOList) {
            WorkTask workTask = new WorkTask();
            workTask.setId(taskDO.getId());
            workTask.setInstanceId(taskDO.getInstanceId());
            workTask.setTokenId(taskDO.getTokenId());
            workTask.setNodeCode(taskDO.getNodeCode());
            workTask.setNodeName(taskDO.getNodeName());
            workTask.setStartTime(taskDO.getStartTime());
            workTask.setEndTime(taskDO.getEndTime());
            workTask.setType(taskDO.getType());
            workTask.setState(taskDO.getState());
            workTask.setAction(taskDO.getAction());
            workTask.setLimitedTime(taskDO.getLimitedTime());
            workTask.setSortNo(taskDO.getSortNo());
            workTask.setWaitTaskId(taskDO.getWaitTaskId());
            workTask.setOperator(StringHelper.isNotEmpty(taskDO.getOperator()) ? actorMap.get(taskDO.getOperator()) : null);
            List<User> actors = taskDO.getActors().stream().map(actorMap::get).collect(Collectors.toList());
            workTask.setActors(actors);
            workTask.setOrderAuth(buildOrderAuth(taskDO, userCode));
            workTaskList.add(workTask);
        }
        return workTaskList;
    }

    private WorkOrderAuth buildOrderAuth(FlowTaskDO taskDO, String userCode) {
        boolean audit = taskDO.getActors().stream().anyMatch(actor -> actor.contains(userCode));
        boolean stop = stopCheck(taskDO.getInstanceId(), userCode);
        boolean revoke = false;
        boolean delete = false;
        return WorkOrderAuth.instanceOf(audit, stop, revoke, delete);
    }

    /**
     * 工单的流程操作记录
     *
     * @param defineCode 流程定义编号
     * @param orderId    工单ID
     * @return 流程操作记录
     */
    public List<FlowRecordVO> findFlowRecord(String defineCode, String orderId) {
        FlowInstanceDO flowInstanceDO = flowEngine.instance().getInstance(defineCode, orderId);
        AssertHelper.notNull(flowInstanceDO, "未找到流程实例信息，请联系系统管理员");
        List<FlowRecordDO> recordDOList = flowRecordService.findRecord(flowInstanceDO.getId());
        return buildFlowRecordVO(recordDOList);
    }

    private List<FlowRecordVO> buildFlowRecordVO(List<FlowRecordDO> recordDOList) {
        if (recordDOList.isEmpty()) {
            return Collections.emptyList();
        }
        Set<String> userCodes = recordDOList.stream().map(FlowRecordDO::getExecuteUser).collect(Collectors.toSet());
        Map<String, User> userMap = userClient.getByUserCode(userCodes);
        return recordDOList.stream().map(recordDO -> FlowRecordVO.instanceOf(recordDO, userMap.get(recordDO.getExecuteUser()))).collect(Collectors.toList());
    }
}
