package com.deeppaas.extapi.biz.service;

import org.springframework.http.HttpHeaders;

import javax.servlet.http.*;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
public class SelfApiRequest extends HttpServletRequestWrapper {
    private String apiMethod;

    private String requestURI;

    private HttpHeaders headers = new HttpHeaders();
    private Map<String, String[]> parameterMap = new HashMap<>();

    public SelfApiRequest(String apiUrl, String apiMethod, HttpServletRequest request) {
        super(request);
        this.apiMethod= apiMethod;
        this.requestURI = apiUrl;
        Enumeration<String> reqHeaderNames = request.getHeaderNames();
        if (reqHeaderNames!=null){
            while (reqHeaderNames.hasMoreElements()){
                String headerName = reqHeaderNames.nextElement();
                this.headers.add(headerName, request.getHeader(headerName));
            }
        }
        Map<String, String[]> reqParameterMap = request.getParameterMap();
        if(reqParameterMap!=null){
            this.parameterMap.putAll(reqParameterMap);
        }
//        System.out.println("getPathInfo="+super.getPathInfo());
//        System.out.println("getPathTranslated="+super.getPathTranslated());
//        System.out.println("getContextPath="+super.getContextPath());
//        System.out.println("getQueryString="+super.getQueryString());
//        System.out.println("getRequestURI="+super.getRequestURI());
//        System.out.println("getRequestURL="+super.getRequestURL());
//        System.out.println("getServletPath="+super.getServletPath());

    }

//    @Override
//    public long getDateHeader(String name) {
//        return this._getHttpServletRequest().getDateHeader(name);
//    }

    @Override
    public String getHeader(String name) {
        return this.headers.getFirst(name);
    }

    @Override
    public Enumeration<String> getHeaders(String name) {
        return new Vector(this.headers.get(name)).elements();
    }

    @Override
    public Enumeration<String> getHeaderNames() {
        return new Vector(headers.keySet()).elements();
    }

    @Override
    public int getIntHeader(String name) {
        return Integer.valueOf(getHeader(name));
    }

    @Override
    public String getMethod() {
        return this.apiMethod;
    }


//    @Override
//    public String getQueryString() {
//        return this._getHttpServletRequest().getQueryString();
//    }
//

    public void setRequestURI(String requestURI){
        this.requestURI = requestURI;
    }
    @Override
    public String getRequestURI() {
        return this.requestURI;
    }

//    @Override
//    public StringBuffer getRequestURL() {
//        return this._getHttpServletRequest().getRequestURL();
//    }

    @Override
    public String getServletPath() {
        return this.requestURI;
    }


    /**
     * 在获取所有的参数名,必须重写此方法，否则对象中参数值映射不上
     *
     * @return
     */
    @Override
    public Enumeration<String> getParameterNames() {
        return new Vector(parameterMap.keySet()).elements();
    }
    /**
     * 重写getParameter方法
     *
     * @param name 参数名
     * @return 返回参数值
     */
    @Override
    public String getParameter(String name) {
        String[] values = parameterMap.get(name);
        if (values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] values = parameterMap.get(name);
        if (values == null || values.length == 0) {
            return null;
        }
        return values;
    }
    /**
     * 增加多个参数
     *
     * @param otherParams 增加的多个参数
     */
    public void addAllParameters(Map<String, Object> otherParams) {
        for (Map.Entry<String, Object> entry : otherParams.entrySet()) {
            addParameter(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 增加参数
     *
     * @param name  参数名
     * @param value 参数值
     */
    public void addParameter(String name, Object value) {
        if (value != null) {
            if (value instanceof String[]) {
                parameterMap.put(name, (String[]) value);
            } else if (value instanceof String) {
                parameterMap.put(name, new String[]{(String) value});
            } else {
                parameterMap.put(name, new String[]{String.valueOf(value)});
            }
        }
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> result = new LinkedHashMap<>();
        Enumeration<String> names = getParameterNames();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            result.put(name, getParameterValues(name));
        }
        return result;
    }

    public void addHeader(String name, String value) {
        this.headers.add(name, value);
    }

}
