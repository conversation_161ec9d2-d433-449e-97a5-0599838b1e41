package com.deeppaas.flow.biz.model.vo;

import com.deeppaas.account.api.client.model.User;
import com.deeppaas.common.model.BaseModel;
import com.deeppaas.flow.biz.entity.FlowRecordDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程实例处理记录
 */
@Data
public class FlowRecordVO implements BaseModel {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 流程实例ID
     */
    private Long instanceId;
    /**
     * 环节名称
     */
    private String nodeName;
    /**
     * 处理动作
     */
    private String action;
    /**
     * 处理人
     */
    private User executeUser;
    /**
     * 处理时间
     */
    private LocalDateTime executeTime;
    /**
     * 处理意见
     */
    private String comment;

    public static FlowRecordVO instanceOf(FlowRecordDO obj, User user) {
        FlowRecordVO vo = new FlowRecordVO();
        vo.setId(obj.getId());
        vo.setInstanceId(obj.getInstanceId());
        vo.setNodeName(obj.getNodeName());
        vo.setAction(obj.getAction());
        vo.setExecuteUser(user);
        vo.setExecuteTime(obj.getExecuteTime());
        vo.setComment(obj.getComment());
        return vo;
    }
}
