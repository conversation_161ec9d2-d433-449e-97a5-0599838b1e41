package com.deeppaas.datastore.common.mapping;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

/**
 * 数据库表字段VO
 *
 * <AUTHOR>
 * @date 2019-09-18
 */
@Data
public class DBColumn implements BaseModel {

    /**
     * 字段名
     **/
    private String name;

    /**
     * 字段类型
     **/
    private String type;

    /**
     * 备注
     */
    private String comment;

    /**
     * 长度
     */
    private Integer length;
    /**
     * 小数位数
     */
    private Integer decimalDigits;

    /**
     * 是否主键标识
     */
    private Boolean idKey;

    /**
     * 是否是数据库生成的数据
     */
    private Boolean dbGenerated;
}
