package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreTableIndexDO;

import java.util.List;

/**
 * 索引DAO
 *
 * <AUTHOR>
 * @date 2022/3/17
 */
public interface DataStoreTableIndexDao extends BaseJdbcDao<DataStoreTableIndexDO, String> {

    boolean existsByCode(String code);

    List<DataStoreTableIndexDO> findByTableId(String tableId);

    long countByTableId(String tableId);
}
