package com.deeppaas.rule.biz.model;

import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;

/**
 * 人员选择范围
 * <AUTHOR>
 * @date 2022/9/3
 */
@Data
public class RUserScope {
    /**
     * 人员范围类型
     */
    private ScopeType type;
    /**
     * 编号
     */
    private RDataBind codeBind;
    /**
     * 岗位类型需要的部门级别
     */
    private RDataBind postControlDeptLevelBind;
    /**
     * 岗位类型需要的部门编号信息
     */
    private RDataBind postControlDeptCodeBind;

    public enum ScopeType{
        /**
         * 部门
         */
        DEPT,
        /**
         * 岗位
         */
        POST,
        /**
         * 用户
         */
        USER,
        /**
         * 岗位用户，岗位用户关系数据
         */
        ORG_USER,
        /**
         * 指定角色
         */
        ROLE
    }
}
