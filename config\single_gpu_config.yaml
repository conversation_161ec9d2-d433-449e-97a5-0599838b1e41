# 单GPU配置 - 适用于6GB显存环境
device:
  force_mode: "single"  # 强制单GPU模式
  min_memory_per_gpu: 6144  # 6GB

models:
  load_strategy: "dynamic"  # 动态加载，节省显存

  ocr:
    engine_type: "rapidocr"  # 使用RapidOCR引擎替代子进程
    model_name: "RapidOCR"
    memory_usage: 600  # RapidOCR内存使用更少
    device_id: 0
    config:
      use_angle_cls: true
      lang: "ch"
      det_limit_side_len: 960
      rec_batch_num: 6
      force_cpu: false  # 启用GPU加速
      # PP-OCRv5配置 - 默认使用Mobile版本（46%性能提升）
      ocr_version: "ppocrv5"  # ppocrv4 | ppocrv5
      model_type: "mobile"    # mobile | server (mobile更快，server更准确)
  
  structure:
    model_name: "PP-Structure"
    memory_usage: 500
    config:
      layout_model_dir: null
      table_model_dir: null
  
  table:
    model_name: "PP-Table"
    memory_usage: 400
    config:
      table_model_dir: null
  
  embedding:
    model_name: "dengcao/qwen3-embedding-4b:q4_k_m"  # 实际使用的0.6B embedding模型
    memory_usage: 3000
    config:
      model_path: null
      max_length: 512
      batch_size: 4  # 小批量
  
  llm:
    model_name: "qwen3:4b" # "qwen3:1.7b" #"qwen3:4b"  # 1.7B模型，更轻量级
    memory_usage: 2500
    config:
      model_path: null
      max_length: 2048
      temperature: 0.1
      top_p: 0.9
      do_sample: true

pipeline:
  max_workers: 1  # 单线程，避免显存冲突
  timeout: 300
  enable_cache: true
  cache_size: 50  # 减少缓存

image_preprocessing:
  stamp_processing:
    enabled: true  # 启用印章检测功能
    model_name: "la"  # 使用la.pt印章检测模型（更稳定，无框架冲突）
    confidence_threshold: 0.5  # 降低阈值，提高检测率
    create_multiple_versions: true
    remove_red_blue_channels: true
    color_separation_method: "opencv_split"  # 使用OpenCV通道分离方法（效果最佳）

extraction:
  default_options:
    confidence_threshold: 0.7
    max_retries: 2
    enable_multi_strategy: true
  
  archive:
    fusion_rules:
      题名:
        preferred_regions: ["title", "header"]
        position_weights:
          top: 0.8
          center: 0.6
        semantic_keywords: ["题名", "标题", "名称"]
      
      责任者:
        preferred_regions: ["text", "signature"]
        position_weights:
          bottom: 0.8
          right: 0.6
        semantic_keywords: ["单位", "机构", "责任者", "发文"]
      
      文号:
        preferred_regions: ["text", "header"]
        pattern_matching: "[\\w\\d]+号|[\\w\\d]+字"
        semantic_keywords: ["文号", "编号", "字号"]

      成文日期:
        preferred_regions: ["text", "footer"]
        pattern_matching: "\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?"
        semantic_keywords: ["日期", "时间", "成文日期"]

system:
  auto_initialize: true
  max_file_size: 52428800  # 50MB
  supported_formats:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".bmp"
    - ".tiff"
    - ".pdf"
    - ".doc"
    - ".docx"
  log_level: "INFO"
  enable_performance_monitoring: true
