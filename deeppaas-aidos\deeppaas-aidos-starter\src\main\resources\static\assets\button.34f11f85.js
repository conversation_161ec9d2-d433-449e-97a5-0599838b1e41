import{M as ue,L as fe,r as o,a1 as de,b as pe,e as d,f as W,P as B,J as we,K as Ie,a0 as Oe,a2 as Re,T as Le,c as Ae,C as me,R as T,a3 as ie,Q as ze,a4 as $e,_ as oe,a as ve,d as se}from"./index.689bcdcb.js";import{o as We}from"./index.71d34973.js";import{c as ge,t as U}from"./reactNode.50ea6579.js";var Be=ue(function n(r){fe(this,n),this.error=new Error("unreachable case: ".concat(JSON.stringify(r)))}),je=globalThis&&globalThis.__rest||function(n,r){var i={};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&r.indexOf(e)<0&&(i[e]=n[e]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,e=Object.getOwnPropertySymbols(n);t<e.length;t++)r.indexOf(e[t])<0&&Object.prototype.propertyIsEnumerable.call(n,e[t])&&(i[e[t]]=n[e[t]]);return i},Ue=function(r){return o.exports.createElement(de,null,function(i){var e,t=i.getPrefixCls,a=i.direction,s=r.prefixCls,c=r.size,l=r.className,h=je(r,["prefixCls","size","className"]),m=t("btn-group",s),p="";switch(c){case"large":p="lg";break;case"small":p="sm";break;case"middle":case void 0:break;default:console.warn(new Be(c).error)}var x=pe(m,(e={},d(e,"".concat(m,"-").concat(p),p),d(e,"".concat(m,"-rtl"),a==="rtl"),e),l);return o.exports.createElement("div",W({},h,{className:x}))})},De=Ue,Fe=0,C={};function w(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,i=Fe++,e=r;function t(){e-=1,e<=0?(n(),delete C[i]):C[i]=B(t)}return C[i]=B(t),i}w.cancel=function(r){r!==void 0&&(B.cancel(C[r]),delete C[r])};w.ids=C;var A;function ce(n){return!n||n.offsetParent===null||n.hidden}function Me(n){var r=(n||"").match(/rgba?\((\d*), (\d*), (\d*)(, [\d.]*)?\)/);return r&&r[1]&&r[2]&&r[3]?!(r[1]===r[2]&&r[2]===r[3]):!0}var he=function(n){we(i,n);var r=Ie(i);function i(){var e;return fe(this,i),e=r.apply(this,arguments),e.containerRef=o.exports.createRef(),e.animationStart=!1,e.destroyed=!1,e.onClick=function(t,a){var s,c,l=e.props,h=l.insertExtraNode,m=l.disabled;if(!(m||!t||ce(t)||t.className.indexOf("-leave")>=0)){e.extraNode=document.createElement("div");var p=Oe(e),x=p.extraNode,N=e.context.getPrefixCls;x.className="".concat(N(""),"-click-animating-node");var v=e.getAttributeName();if(t.setAttribute(v,"true"),a&&a!=="#ffffff"&&a!=="rgb(255, 255, 255)"&&Me(a)&&!/rgba\((?:\d*, ){3}0\)/.test(a)&&a!=="transparent"){x.style.borderColor=a;var u=((s=t.getRootNode)===null||s===void 0?void 0:s.call(t))||t.ownerDocument,S=u instanceof Document?u.body:(c=u.firstChild)!==null&&c!==void 0?c:u;A=Re(`
      [`.concat(N(""),"-click-animating-without-extra-node='true']::after, .").concat(N(""),`-click-animating-node {
        --antd-wave-shadow-color: `).concat(a,`;
      }`),"antd-wave",{csp:e.csp,attachTo:S})}h&&t.appendChild(x),["transition","animation"].forEach(function(E){t.addEventListener("".concat(E,"start"),e.onTransitionStart),t.addEventListener("".concat(E,"end"),e.onTransitionEnd)})}},e.onTransitionStart=function(t){if(!e.destroyed){var a=e.containerRef.current;!t||t.target!==a||e.animationStart||e.resetEffect(a)}},e.onTransitionEnd=function(t){!t||t.animationName!=="fadeEffect"||e.resetEffect(t.target)},e.bindAnimationEvent=function(t){if(!(!t||!t.getAttribute||t.getAttribute("disabled")||t.className.indexOf("disabled")>=0)){var a=function(c){if(!(c.target.tagName==="INPUT"||ce(c.target))){e.resetEffect(t);var l=getComputedStyle(t).getPropertyValue("border-top-color")||getComputedStyle(t).getPropertyValue("border-color")||getComputedStyle(t).getPropertyValue("background-color");e.clickWaveTimeoutId=window.setTimeout(function(){return e.onClick(t,l)},0),w.cancel(e.animationStartId),e.animationStart=!0,e.animationStartId=w(function(){e.animationStart=!1},10)}};return t.addEventListener("click",a,!0),{cancel:function(){t.removeEventListener("click",a,!0)}}}},e.renderWave=function(t){var a=t.csp,s=e.props.children;if(e.csp=a,!o.exports.isValidElement(s))return s;var c=e.containerRef;return Le(s)&&(c=Ae(s.ref,e.containerRef)),ge(s,{ref:c})},e}return ue(i,[{key:"componentDidMount",value:function(){var t=this.containerRef.current;!t||t.nodeType!==1||(this.instance=this.bindAnimationEvent(t))}},{key:"componentWillUnmount",value:function(){this.instance&&this.instance.cancel(),this.clickWaveTimeoutId&&clearTimeout(this.clickWaveTimeoutId),this.destroyed=!0}},{key:"getAttributeName",value:function(){var t=this.context.getPrefixCls,a=this.props.insertExtraNode;return a?"".concat(t(""),"-click-animating"):"".concat(t(""),"-click-animating-without-extra-node")}},{key:"resetEffect",value:function(t){var a=this;if(!(!t||t===this.extraNode||!(t instanceof Element))){var s=this.props.insertExtraNode,c=this.getAttributeName();t.setAttribute(c,"false"),A&&(A.innerHTML=""),s&&this.extraNode&&t.contains(this.extraNode)&&t.removeChild(this.extraNode),["transition","animation"].forEach(function(l){t.removeEventListener("".concat(l,"start"),a.onTransitionStart),t.removeEventListener("".concat(l,"end"),a.onTransitionEnd)})}}},{key:"render",value:function(){return o.exports.createElement(de,null,this.renderWave)}}]),i}(o.exports.Component);he.contextType=me;var z=function(){return{width:0,opacity:0,transform:"scale(0)"}},$=function(r){return{width:r.scrollWidth,opacity:1,transform:"scale(1)"}},Ge=function(r){var i=r.prefixCls,e=r.loading,t=r.existIcon,a=!!e;return t?T.createElement("span",{className:"".concat(i,"-loading-icon")},T.createElement(ie,null)):T.createElement(ze,{visible:a,motionName:"".concat(i,"-loading-icon-motion"),removeOnLeave:!0,onAppearStart:z,onAppearActive:$,onEnterStart:z,onEnterActive:$,onLeaveStart:$,onLeaveActive:z},function(s,c){var l=s.className,h=s.style;return T.createElement("span",{className:"".concat(i,"-loading-icon"),style:h,ref:c},T.createElement(ie,{className:l}))})},Ve=Ge,He=globalThis&&globalThis.__rest||function(n,r){var i={};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&r.indexOf(e)<0&&(i[e]=n[e]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,e=Object.getOwnPropertySymbols(n);t<e.length;t++)r.indexOf(e[t])<0&&Object.prototype.propertyIsEnumerable.call(n,e[t])&&(i[e[t]]=n[e[t]]);return i},le=/^[\u4e00-\u9fa5]{2}$/,j=le.test.bind(le);function Je(n){return typeof n=="string"}function P(n){return n==="text"||n==="link"}function Ke(n){return o.exports.isValidElement(n)&&n.type===o.exports.Fragment}function Qe(n,r){if(n!=null){var i=r?" ":"";return typeof n!="string"&&typeof n!="number"&&Je(n.type)&&j(n.props.children)?ge(n,{children:n.props.children.split("").join(i)}):typeof n=="string"?j(n)?o.exports.createElement("span",null,n.split("").join(i)):o.exports.createElement("span",null,n):Ke(n)?o.exports.createElement("span",null,n):n}}function qe(n,r){var i=!1,e=[];return o.exports.Children.forEach(n,function(t){var a=ve(t),s=a==="string"||a==="number";if(i&&s){var c=e.length-1,l=e[c];e[c]="".concat(l).concat(t)}else e.push(t);i=s}),o.exports.Children.map(e,function(t){return Qe(t,r)})}U("default","primary","ghost","dashed","link","text");U("default","circle","round");U("submit","button","reset");function tt(n){return n==="danger"?{danger:!0}:{type:n}}var Xe=function(r,i){var e,t=r.loading,a=t===void 0?!1:t,s=r.prefixCls,c=r.type,l=c===void 0?"default":c,h=r.danger,m=r.shape,p=m===void 0?"default":m,x=r.size,N=r.className,v=r.children,u=r.icon,S=r.ghost,E=S===void 0?!1:S,D=r.block,xe=D===void 0?!1:D,F=r.htmlType,be=F===void 0?"button":F,M=He(r,["loading","prefixCls","type","danger","shape","size","className","children","icon","ghost","block","htmlType"]),ye=o.exports.useContext($e),Ce=o.exports.useState(!!a),G=oe(Ce,2),b=G[0],V=G[1],Ne=o.exports.useState(!1),H=oe(Ne,2),O=H[0],J=H[1],R=o.exports.useContext(me),Ee=R.getPrefixCls,K=R.autoInsertSpaceInButton,ke=R.direction,y=i||o.exports.createRef(),Q=function(){return o.exports.Children.count(v)===1&&!u&&!P(l)},Te=function(){if(!(!y||!y.current||K===!1)){var _=y.current.textContent;Q()&&j(_)?O||J(!0):O&&J(!1)}},k=ve(a)==="object"&&a.delay?a.delay||!0:!!a;o.exports.useEffect(function(){var g=null;return typeof k=="number"?g=window.setTimeout(function(){g=null,V(k)},k):V(k),function(){g&&(window.clearTimeout(g),g=null)}},[k]),o.exports.useEffect(Te,[y]);var q=function(_){var L=r.onClick,Pe=r.disabled;if(b||Pe){_.preventDefault();return}L==null||L(_)};se(!(typeof u=="string"&&u.length>2),"Button","`icon` is using ReactNode instead of string naming in v4. Please check `".concat(u,"` at https://ant.design/components/icon")),se(!(E&&P(l)),"Button","`link` or `text` button can't be a `ghost` button.");var f=Ee("btn",s),X=K!==!1,Se={large:"lg",small:"sm",middle:void 0},Y=x||ye,Z=Y&&Se[Y]||"",_e=b?"loading":u,ee=pe(f,(e={},d(e,"".concat(f,"-").concat(p),p!=="default"&&p),d(e,"".concat(f,"-").concat(l),l),d(e,"".concat(f,"-").concat(Z),Z),d(e,"".concat(f,"-icon-only"),!v&&v!==0&&!!_e),d(e,"".concat(f,"-background-ghost"),E&&!P(l)),d(e,"".concat(f,"-loading"),b),d(e,"".concat(f,"-two-chinese-chars"),O&&X),d(e,"".concat(f,"-block"),xe),d(e,"".concat(f,"-dangerous"),!!h),d(e,"".concat(f,"-rtl"),ke==="rtl"),e),N),te=u&&!b?u:o.exports.createElement(Ve,{existIcon:!!u,prefixCls:f,loading:!!b}),ne=v||v===0?qe(v,Q()&&X):null,re=We(M,["navigate"]);if(re.href!==void 0)return o.exports.createElement("a",W({},re,{className:ee,onClick:q,ref:y}),te,ne);var ae=o.exports.createElement("button",W({},M,{type:be,className:ee,onClick:q,ref:y}),te,ne);return P(l)?ae:o.exports.createElement(he,{disabled:!!b},ae)},I=o.exports.forwardRef(Xe);I.displayName="Button";I.Group=De;I.__ANT_BUTTON=!0;var nt=I;export{nt as B,he as W,tt as c};
