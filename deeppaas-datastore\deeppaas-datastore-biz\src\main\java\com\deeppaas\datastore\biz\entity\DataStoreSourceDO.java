package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据源记录实体
 *
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@Table("datastore_source")
public class DataStoreSourceDO extends BaseUuidDO {
    public static final String ID_DEFAULT = "DEEPPAAS";
    /**
     * 数据源名称
     **/
    @Column("name")
    private String name;
    /**
     * 数据库类型 @DBDialect
     **/
    @Column("db_type")
    private String dbType;
    /**
     * jdbc连接地址
     **/
    @Column("jdbc_url")
    private String jdbcUrl;
    /**
     * 访问用户
     **/
    @Column("user")
    private String user;
    /**
     * 访问密码
     **/
    @Column("pwd")
    private String pwd;
    /**
     * 模式/schema
     **/
    @Column("default_schema")
    private String defaultSchema;
    /**
     * 驱动类名
     **/
    @Column("driver_class")
    private String driverClass;

}
