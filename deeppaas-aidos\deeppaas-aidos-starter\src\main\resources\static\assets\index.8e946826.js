import{r as t,_ as B,c as Ce,C as U,a as Y,d as Ee,b as L,e as N,f as P,t as be,u as Pe,j as c,g as D,N as Z,O as Se}from"./index.689bcdcb.js";import{u as Ne}from"./useMutation.b5bc9a93.js";import{u as Oe}from"./useQuery.d623ee6b.js";import{g as Re,l as ze}from"./user.32d1e8da.js";import{e as we}from"./util.bf0e86fd.js";import{U as _e}from"./useUser.01c90442.js";import{l as $e,U as Ie}from"./kubaologo.27088668.js";import{M as ee}from"./index.41297259.js";import{D as je}from"./dropdown.cc3aab1b.js";import{R as Ae}from"./index.71d34973.js";import{r as Fe}from"./responsiveObserve.da8166ca.js";import{u as Te}from"./useBreakpoint.ddfb7c4a.js";import{g as ke,c as Me}from"./reactNode.50ea6579.js";import{T as Be}from"./index.1f7b2316.js";import{g as te}from"./getRenderPropValue.1abeb751.js";import{D as De}from"./DownOutlined.cc0b2189.js";import"./Overflow.1478d911.js";import"./RightOutlined.ed31d8e3.js";import"./LeftOutlined.6bce5536.js";import"./button.34f11f85.js";import"./useForceUpdate.a9f3ccc5.js";var W=t.exports.createContext("default"),re=function(e){var n=e.children,r=e.size;return t.exports.createElement(W.Consumer,null,function(a){return t.exports.createElement(W.Provider,{value:r||a},n)})},Le=globalThis&&globalThis.__rest||function(o,e){var n={};for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&e.indexOf(r)<0&&(n[r]=o[r]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(o);a<r.length;a++)e.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(o,r[a])&&(n[r[a]]=o[r[a]]);return n},We=function(e,n){var r,a,d=t.exports.useContext(W),f=t.exports.useState(1),s=B(f,2),u=s[0],v=s[1],x=t.exports.useState(!1),m=B(x,2),p=m[0],A=m[1],z=t.exports.useState(!0),w=B(z,2),O=w[0],_=w[1],g=t.exports.useRef(),h=t.exports.useRef(),$=Ce(n,g),F=t.exports.useContext(U),T=F.getPrefixCls,I=function(){if(!(!h.current||!g.current)){var i=h.current.offsetWidth,E=g.current.offsetWidth;if(i!==0&&E!==0){var K=e.gap,M=K===void 0?4:K;M*2<E&&v(E-M*2<i?(E-M*2)/i:1)}}};t.exports.useEffect(function(){A(!0)},[]),t.exports.useEffect(function(){_(!0),v(1)},[e.src]),t.exports.useEffect(function(){I()},[e.gap]);var se=function(){var i=e.onError,E=i?i():void 0;E!==!1&&_(!1)},ie=e.prefixCls,V=e.shape,Q=e.size,R=e.src,le=e.srcSet,y=e.icon,ce=e.className,ue=e.alt,me=e.draggable,X=e.children,fe=e.crossOrigin,j=Le(e,["prefixCls","shape","size","src","srcSet","icon","className","alt","draggable","children","crossOrigin"]),l=Q==="default"?d:Q,ve=Object.keys(Y(l)==="object"?l||{}:{}).some(function(b){return["xs","sm","md","lg","xl","xxl"].includes(b)}),q=Te(ve),pe=t.exports.useMemo(function(){if(Y(l)!=="object")return{};var b=Fe.find(function(E){return q[E]}),i=l[b];return i?{width:i,height:i,lineHeight:"".concat(i,"px"),fontSize:y?i/2:18}:{}},[q,l]);Ee(!(typeof y=="string"&&y.length>2),"Avatar","`icon` is using ReactNode instead of string naming in v4. Please check `".concat(y,"` at https://ant.design/components/icon"));var C=T("avatar",ie),de=L((r={},N(r,"".concat(C,"-lg"),l==="large"),N(r,"".concat(C,"-sm"),l==="small"),r)),J=t.exports.isValidElement(R),xe=L(C,de,(a={},N(a,"".concat(C,"-").concat(V),!!V),N(a,"".concat(C,"-image"),J||R&&O),N(a,"".concat(C,"-icon"),!!y),a),ce),ge=typeof l=="number"?{width:l,height:l,lineHeight:"".concat(l,"px"),fontSize:y?l/2:18}:{},S;if(typeof R=="string"&&O)S=t.exports.createElement("img",{src:R,draggable:me,srcSet:le,onError:se,alt:ue,crossOrigin:fe});else if(J)S=R;else if(y)S=y;else if(p||u!==1){var k="scale(".concat(u,") translateX(-50%)"),he={msTransform:k,WebkitTransform:k,transform:k},ye=typeof l=="number"?{lineHeight:"".concat(l,"px")}:{};S=t.exports.createElement(Ae,{onResize:I},t.exports.createElement("span",{className:"".concat(C,"-string"),ref:function(i){h.current=i},style:P(P({},ye),he)},X))}else S=t.exports.createElement("span",{className:"".concat(C,"-string"),style:{opacity:0},ref:function(i){h.current=i}},X);return delete j.onError,delete j.gap,t.exports.createElement("span",P({},j,{style:P(P(P({},ge),pe),j.style),className:xe,ref:$}),S)},G=t.exports.forwardRef(We);G.displayName="Avatar";G.defaultProps={shape:"circle",size:"default"};var oe=G,Ue=globalThis&&globalThis.__rest||function(o,e){var n={};for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&e.indexOf(r)<0&&(n[r]=o[r]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(o);a<r.length;a++)e.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(o,r[a])&&(n[r[a]]=o[r[a]]);return n},H=t.exports.forwardRef(function(o,e){var n=o.prefixCls,r=o.title,a=o.content,d=Ue(o,["prefixCls","title","content"]),f=t.exports.useContext(U),s=f.getPrefixCls,u=function(p){if(!(!r&&!a))return t.exports.createElement(t.exports.Fragment,null,r&&t.exports.createElement("div",{className:"".concat(p,"-title")},te(r)),t.exports.createElement("div",{className:"".concat(p,"-inner-content")},te(a)))},v=s("popover",n),x=s();return t.exports.createElement(Be,P({},d,{prefixCls:v,ref:e,overlay:u(v),transitionName:ke(x,"zoom-big",d.transitionName)}))});H.displayName="Popover";H.defaultProps={placement:"top",trigger:"hover",mouseEnterDelay:.1,mouseLeaveDelay:.1,overlayStyle:{}};var Ge=H,He=function(e){var n=t.exports.useContext(U),r=n.getPrefixCls,a=n.direction,d=e.prefixCls,f=e.className,s=f===void 0?"":f,u=e.maxCount,v=e.maxStyle,x=e.size,m=r("avatar-group",d),p=L(m,N({},"".concat(m,"-rtl"),a==="rtl"),s),A=e.children,z=e.maxPopoverPlacement,w=z===void 0?"top":z,O=e.maxPopoverTrigger,_=O===void 0?"hover":O,g=be(A).map(function(T,I){return Me(T,{key:"avatar-key-".concat(I)})}),h=g.length;if(u&&u<h){var $=g.slice(0,u),F=g.slice(u,h);return $.push(t.exports.createElement(Ge,{key:"avatar-popover-key",content:F,trigger:_,placement:w,overlayClassName:"".concat(m,"-popover")},t.exports.createElement(oe,{style:v},"+".concat(h-u)))),t.exports.createElement(re,{size:x},t.exports.createElement("div",{className:p,style:e.style},$))}return t.exports.createElement(re,{size:x},t.exports.createElement("div",{className:p,style:e.style},g))},Ve=He,ne=oe;ne.Group=Ve;var Qe=ne;const ae=[{path:"/",title:"\u529E\u4EF6\u60C5\u51B5"},{path:"eventCenter",title:"\u4E8B\u9879\u4E2D\u5FC3"},{path:"settings",title:"\u7CFB\u7EDF\u8BBE\u7F6E"}];function dt(){const[o,e]=t.exports.useState(!1),n=Pe(),[r,a]=t.exports.useState(ae),[d,f]=t.exports.useState(),{data:s}=Oe("me",we(Re),{enabled:n.pathname!=="/login"});t.exports.useEffect(()=>{(s==null?void 0:s.aidosRole)==="user"?a([{path:"/",title:"\u529E\u4EF6\u60C5\u51B5"}]):a(ae),(s==null?void 0:s.aidosRole)==="user"&&/\/(eventCenter|settings|addMould|process)/i.test(n.pathname)?f(!1):f(!0)},[n.pathname,s]);const u=["/addMould","/login","/process","/fileReporting"],v=Ne(ze,{onSuccess(){window.location.replace(`/login?original=${encodeURIComponent(window.location.pathname+window.location.search)}`)}}),x=c(ee,{className:"w-22",children:c(ee.Item,{onClick:()=>{v.mutate()},children:"\u9000\u51FA"})},1);return t.exports.useEffect(()=>{u.indexOf(n.pathname)>=0?e(!1):e(!0)},[n.pathname,u]),c(_e.Provider,{value:{user:s},children:D("div",{className:" bg-[#F7F7F7]",children:[o?c("div",{children:D("div",{className:"h-14 bg-white relative",children:[c(Z,{to:"/",children:c("img",{src:$e,width:"92",height:"24",alt:"",className:"absolute top-[11px] left-6"})}),c("nav",{className:"flex absolute left-0 right-0 m-auto w-[264px] text-base justify-between items-center h-full",children:r.map(m=>c(Z,{to:m.path,className:({isActive:p})=>p?"text-primary-default":"text-black",children:m.title},m.path))}),c(je,{overlay:x,children:D("div",{className:"absolute right-[26px] flex items-center h-full cursor-pointer",children:[c(Qe,{icon:c(Ie,{})}),c("div",{className:"ml-2 mr-2",children:s==null?void 0:s.name}),c(De,{})]})})]})}):null,d?c(Se,{}):null]})})}export{dt as default};
