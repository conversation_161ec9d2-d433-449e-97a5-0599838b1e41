package com.deeppaas.configuration.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.sql.Date;
import java.time.LocalDateTime;

@Data
@Table("aidos_process_config")
public class AidosProcessConfigDO extends BaseUuidDO {

    /**
     * 事项名称
     */
    @Column("process_id")
    private String processId;
    /**
     * 事项名称
     */
    @Column("process_task_id")
    private String processTaskId;
    /**
     * 配置名称
     */
    @Column("name")
    private String name;
    /**
     * 配置别名
     */
    @Column("alias")
    private String alias;

    /**
     * 配置值对象
     */
    @Column("configs")
    private String configs;
    /**
     * 配置值对象
     */
    @Column("sort")
    private Integer sort;

    public Integer getSort() {

        if(sort==null)
            sort=0;
        return sort;
    }

    /**
     * 创建时间
     * add by xby 2024.9.19
     */
    @Column("create_time")
    private LocalDateTime createTime;
}
