import{E as C,J as N,K as S,L as D,M as $,r as o,T as q,c as K,U as H,t as T,f as F}from"./index.689bcdcb.js";import{b as J}from"./reactNode.50ea6579.js";function ee(r,t){var e=C({},r);return Array.isArray(t)&&t.forEach(function(n){delete e[n]}),e}var d=new Map;function U(r){r.forEach(function(t){var e,n=t.target;(e=d.get(n))===null||e===void 0||e.forEach(function(s){return s(n)})})}var M=new J(U);function V(r,t){d.has(r)||(d.set(r,new Set),M.observe(r)),d.get(r).add(t)}function X(r,t){d.has(r)&&(d.get(r).delete(t),d.get(r).size||(M.unobserve(r),d.delete(r)))}var Y=function(r){N(e,r);var t=S(e);function e(){return D(this,e),t.apply(this,arguments)}return $(e,[{key:"render",value:function(){return this.props.children}}]),e}(o.exports.Component),E=o.exports.createContext(null);function G(r){var t=r.children,e=r.onBatchResize,n=o.exports.useRef(0),s=o.exports.useRef([]),i=o.exports.useContext(E),v=o.exports.useCallback(function(a,f,u){n.current+=1;var c=n.current;s.current.push({size:a,element:f,data:u}),Promise.resolve().then(function(){c===n.current&&(e==null||e(s.current),s.current=[])}),i==null||i(a,f,u)},[e,i]);return o.exports.createElement(E.Provider,{value:v},t)}function Q(r){var t=r.children,e=r.disabled,n=o.exports.useRef(null),s=o.exports.useRef(null),i=o.exports.useContext(E),v=typeof t=="function",a=v?t(n):t,f=o.exports.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),u=!v&&o.exports.isValidElement(a)&&q(a),c=u?a.ref:null,p=o.exports.useMemo(function(){return K(c,n)},[c,n]),h=o.exports.useRef(r);h.current=r;var m=o.exports.useCallback(function(l){var w=h.current,z=w.onResize,P=w.data,_=l.getBoundingClientRect(),x=_.width,y=_.height,R=l.offsetWidth,g=l.offsetHeight,b=Math.floor(x),k=Math.floor(y);if(f.current.width!==b||f.current.height!==k||f.current.offsetWidth!==R||f.current.offsetHeight!==g){var O={width:b,height:k,offsetWidth:R,offsetHeight:g};f.current=O;var I=R===Math.round(x)?x:R,L=g===Math.round(y)?y:g,W=C(C({},O),{},{offsetWidth:I,offsetHeight:L});i==null||i(W,l,P),z&&Promise.resolve().then(function(){z(W,l)})}},[]);return o.exports.useEffect(function(){var l=H(n.current)||H(s.current);return l&&!e&&V(l,m),function(){return X(l,m)}},[n.current,e]),o.exports.createElement(Y,{ref:s},u?o.exports.cloneElement(a,{ref:p}):a)}var Z="rc-observer-key";function A(r){var t=r.children,e=typeof t=="function"?[t]:T(t);return e.map(function(n,s){var i=(n==null?void 0:n.key)||"".concat(Z,"-").concat(s);return o.exports.createElement(Q,F({},r,{key:i}),n)})}A.Collection=G;var re=function(t,e,n,s){var i=n?n.call(s,t,e):void 0;if(i!==void 0)return!!i;if(t===e)return!0;if(typeof t!="object"||!t||typeof e!="object"||!e)return!1;var v=Object.keys(t),a=Object.keys(e);if(v.length!==a.length)return!1;for(var f=Object.prototype.hasOwnProperty.bind(e),u=0;u<v.length;u++){var c=v[u];if(!f(c))return!1;var p=t[c],h=e[c];if(i=n?n.call(s,p,h,c):void 0,i===!1||i===void 0&&p!==h)return!1}return!0};export{A as R,ee as o,re as s};
