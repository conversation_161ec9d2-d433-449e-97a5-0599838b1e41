package com.deeppaas.flow.workbench.model;

import com.deeppaas.datastore.common.mapping.DataStoreData;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/12
 */
@Data
public class WorkOrderParam {
    /**
     * 流程定义编号
     */
    private String flowDefineCode;

    /**
     * 开始环节
     */
    private String startNode;
    /**
     * 所属实体
     */
    private String entity;

    private DataStoreData data;

}
