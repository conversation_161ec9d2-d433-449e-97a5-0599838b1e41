package com.deeppaas.taskInfo.biz.web;

import com.deeppaas.taskInfo.biz.entity.TaskInfoDO;
import com.deeppaas.taskInfo.biz.entity.TaskInfoImageDo;
import com.deeppaas.taskInfo.biz.model.CheckImageVo;
import com.deeppaas.taskInfo.biz.model.TaskInfoVo;
import com.deeppaas.taskInfo.biz.service.TaskInfoImageService;
import com.deeppaas.taskInfo.biz.service.TaskInfoService;
import com.deeppaas.taskInfo.biz.xlsx.TaskInfoXlsxView;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/taskInfo")
public class TaskInfoApi {

    private static final Logger logger = LoggerFactory.getLogger(TaskInfoApi.class);
    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private TaskInfoImageService taskInfoImageService;

    @PostMapping("/page")
    public RestModel<PageData<TaskInfoDO>> page(@RequestBody PageSearch pageSearch) {
        PageData<TaskInfoDO> list = taskInfoService.page(pageSearch);
        return RestModel.data(list);
    }

    @GetMapping("/getAll")
    public RestModel<List<TaskInfoDO>> list() {
        List<TaskInfoDO> list = taskInfoService.getAll();
        return RestModel.data(list);
    }

    @GetMapping("/get")
    public RestModel get(@RequestParam String  taskId) {
        taskInfoService.get(taskId);
        return RestModel.success("保存成功");
    }

    @PostMapping("/save")
    public RestModel list(@RequestBody TaskInfoDO aidosImageTemplateDO) {
        taskInfoService.insert(aidosImageTemplateDO);
        return RestModel.success("保存成功");
    }

    @GetMapping("/del")
    public RestModel list(@RequestParam String id) {
        taskInfoService.del(id);
        return RestModel.success("删除成功");
    }
    @GetMapping("/getById")
    public RestModel<TaskInfoDO> getById(@RequestParam String id) {
        TaskInfoDO model = taskInfoService.get(id);
        return RestModel.data(model);
    }

    @GetMapping("/export")
    public ModelAndView export() {
        PageSearch pageSearch =new PageSearch();
        pageSearch.setPageSize(99999);
        PageData<TaskInfoDO> list = taskInfoService.page(pageSearch);
        Map<String, Object> model = new HashMap<>();
        model.put("list" ,list.getList());
        return new ModelAndView(new TaskInfoXlsxView(), model);
    }

    @GetMapping("/taskInfo")
    public RestModel<TaskInfoVo> taskInfo(@RequestParam String taskId) {
        //TaskInfoDO taskInfoDO= taskInfoService.get(taskId);
        List<TaskInfoImageDo> lists=taskInfoImageService.getByTaskInfoId(taskId);

        //TODO 需要去工厂组装
        TaskInfoVo taskInfoVo=new TaskInfoVo();
        CheckImageVo checkImageVo=new CheckImageVo();
        if(!CollectionUtils.isEmpty(lists)){
            checkImageVo.setSuccessImage( lists.stream().filter(item-> Objects.equals(item.getTag(),"success")).collect(Collectors.toList()));;
            checkImageVo.setErrorImage(lists.stream().filter(item-> Objects.equals(item.getTag(),"error")).collect(Collectors.toList()));;
        }
        taskInfoVo.setCheckImageVo(checkImageVo);
        return RestModel.data(taskInfoVo);
    }


}
