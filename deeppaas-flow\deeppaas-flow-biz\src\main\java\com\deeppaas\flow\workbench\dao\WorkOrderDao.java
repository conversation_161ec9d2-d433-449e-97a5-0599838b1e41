package com.deeppaas.flow.workbench.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.common.model.PageData;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.workbench.model.WorkOrderDO;
import com.deeppaas.flow.workbench.model.WorkOrderSearch;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
public interface WorkOrderDao extends BaseJdbcDao<FlowInstanceDO, Long> {

    default PageData<WorkOrderDO> findByPage(WorkOrderSearch orderSearch){
        return null;
    }
}
