package com.deeppaas.datastore.api.client;

import com.deeppaas.common.model.PageData;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

/**
 * 数据模型数据客户端
 * <AUTHOR>
 * @date 2022/3/5
 */
public interface DataStoreDataClient {

    /**
     * 获取数据
     * @param entityCode
     * @param id
     * @return
     */
    DataStoreData getData(String entityCode, String id);

    /**
     * 保存数据
     * @param param
     * @return
     */
    DataStoreData saveData(DataStoreData param);

    /**
     * 删除数据
     * @param entity
     * @param id
     * @param userCode
     */
    void removeData(String entity, String id, String userCode);

    /**
     * 查询数据
     * @param entityCode
     * @param condition
     * @return
     */
    List<DataStoreData> query(String entityCode, DataStoreCondition condition);
    List<DataStoreData> query(String entityCode, DataStoreCondition condition, Sort sort);
    List<DataStoreData> query(String entityCode, DataStoreCondition condition, Pageable pageParam);
    PageData<DataStoreData> queryForPage(String dataEntity, DataStoreCondition queryCondition, Pageable pageable);
    /**
     * 删除数据
     * @param entityCode
     * @param condition
     * @return
     */
    int delete(String entityCode, DataStoreCondition condition);

    /**
     * 更新数据
     * @param entityCode
     * @param updateList
     */
    void update(String entityCode, List<DataStoreData> updateList);

}
