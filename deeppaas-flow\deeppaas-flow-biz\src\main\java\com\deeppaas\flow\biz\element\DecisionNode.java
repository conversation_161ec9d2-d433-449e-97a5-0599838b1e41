package com.deeppaas.flow.biz.element;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.FlowException;
import com.deeppaas.flow.biz.MockExecuteItem;
import com.deeppaas.flow.biz.enums.NodeType;
import com.deeppaas.flow.biz.model.AssignInfo;
import org.springframework.util.CollectionUtils;

/**
 * 判断节点
 * <AUTHOR>
 *
 */
public class DecisionNode extends FlowNode implements Serializable{
	private static final long serialVersionUID = -8691256870011843625L;

	public DecisionNode(String code, String name, NodeType type, String remark, List<FlowEventRule> eventRules) {
		super(code, name, type, remark, eventRules);
	}


	@Override
	public void execute(ExecuteItem execution) {
		boolean isfound = false;
		for(FlowLine line : this.getOutLines()) {
			line.execute(execution);
			isfound = true;
		}
		if(!isfound) {
			throw new FlowException(execution.getInstance().getId() + "->decision节点无法确定下一步执行路线");
		}
	}
	
	@Override
	public List<AssignInfo> mockExecute(MockExecuteItem execution) {
		List<AssignInfo> infoList = null;
		for(FlowLine line : getOutLines()) {
			infoList = line.mockExecute(execution);
			if(!CollectionUtils.isEmpty(infoList)){
				break;
			}
		}
		if(CollectionUtils.isEmpty(infoList)) {
			throw new FlowException(execution.getInstance().getId() + "->decision节点无法确定下一步执行路线");
		}
		return infoList;
	}

	@Override
	public List<String> check() {
		List<String> errors = new ArrayList<>();
		if(inLines.isEmpty()){
			errors.add("环节["+getCode()+"]没有进入连接线");
		}
		if(outLines.isEmpty()){
			errors.add("环节["+getCode()+"]没有流转连接线");
		}
		return errors;
	}
}
