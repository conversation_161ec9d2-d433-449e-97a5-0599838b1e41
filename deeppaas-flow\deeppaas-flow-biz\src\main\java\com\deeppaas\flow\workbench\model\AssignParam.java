package com.deeppaas.flow.workbench.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 指派处理人并流转参数
 * <AUTHOR>
 * @date 2022/3/12
 */
@Data
public class AssignParam {
    /**
     * 流程实例ID
     */
    private Long instanceId;
    /**
     * 流程环节编号
     */
    private String nodeCode;
    /**
     * 工单ID
     */
    private String orderId;
    /**
     * 处理人
     */
    private List<String> actors;
    /**
     * 限定时间
     */
    private LocalDateTime limitedTime;
    /**
     * 紧急程度（非必填）
     */
    private Integer priority;
}
