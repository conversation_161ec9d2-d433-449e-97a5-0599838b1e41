import{V as E,W as D,_ as H,r as c,X as P}from"./index.689bcdcb.js";var M=function(){if(typeof Map!="undefined")return Map;function e(t,n){var r=-1;return t.some(function(i,o){return i[0]===n?(r=o,!0):!1}),r}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(n){var r=e(this.__entries__,n),i=this.__entries__[r];return i&&i[1]},t.prototype.set=function(n,r){var i=e(this.__entries__,n);~i?this.__entries__[i][1]=r:this.__entries__.push([n,r])},t.prototype.delete=function(n){var r=this.__entries__,i=e(r,n);~i&&r.splice(i,1)},t.prototype.has=function(n){return!!~e(this.__entries__,n)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(n,r){r===void 0&&(r=null);for(var i=0,o=this.__entries__;i<o.length;i++){var s=o[i];n.call(r,s[1],s[0])}},t}()}(),w=typeof window!="undefined"&&typeof document!="undefined"&&window.document===document,p=function(){return typeof global!="undefined"&&global.Math===Math?global:typeof self!="undefined"&&self.Math===Math?self:typeof window!="undefined"&&window.Math===Math?window:Function("return this")()}(),V=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(p):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),W=2;function G(e,t){var n=!1,r=!1,i=0;function o(){n&&(n=!1,e()),r&&u()}function s(){V(o)}function u(){var a=Date.now();if(n){if(a-i<W)return;r=!0}else n=!0,r=!1,setTimeout(s,t);i=a}return u}var I=20,N=["top","right","bottom","left","width","height","size","weight"],j=typeof MutationObserver!="undefined",k=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=G(this.refresh.bind(this),I)}return e.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},e.prototype.removeObserver=function(t){var n=this.observers_,r=n.indexOf(t);~r&&n.splice(r,1),!n.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},e.prototype.updateObservers_=function(){var t=this.observers_.filter(function(n){return n.gatherActive(),n.hasActive()});return t.forEach(function(n){return n.broadcastActive()}),t.length>0},e.prototype.connect_=function(){!w||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),j?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){!w||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(t){var n=t.propertyName,r=n===void 0?"":n,i=N.some(function(o){return!!~r.indexOf(o)});i&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),A=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},d=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||p},C=m(0,0,0,0);function b(e){return parseFloat(e)||0}function R(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(r,i){var o=e["border-"+i+"-width"];return r+b(o)},0)}function F(e){for(var t=["top","right","bottom","left"],n={},r=0,i=t;r<i.length;r++){var o=i[r],s=e["padding-"+o];n[o]=b(s)}return n}function q(e){var t=e.getBBox();return m(0,0,t.width,t.height)}function B(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return C;var r=d(e).getComputedStyle(e),i=F(r),o=i.left+i.right,s=i.top+i.bottom,u=b(r.width),a=b(r.height);if(r.boxSizing==="border-box"&&(Math.round(u+o)!==t&&(u-=R(r,"left","right")+o),Math.round(a+s)!==n&&(a-=R(r,"top","bottom")+s)),!$(e)){var f=Math.round(u+o)-t,l=Math.round(a+s)-n;Math.abs(f)!==1&&(u-=f),Math.abs(l)!==1&&(a-=l)}return m(i.left,i.top,u,a)}var U=function(){return typeof SVGGraphicsElement!="undefined"?function(e){return e instanceof d(e).SVGGraphicsElement}:function(e){return e instanceof d(e).SVGElement&&typeof e.getBBox=="function"}}();function $(e){return e===d(e).document.documentElement}function K(e){return w?U(e)?q(e):B(e):C}function X(e){var t=e.x,n=e.y,r=e.width,i=e.height,o=typeof DOMRectReadOnly!="undefined"?DOMRectReadOnly:Object,s=Object.create(o.prototype);return A(s,{x:t,y:n,width:r,height:i,top:n,right:t+r,bottom:i+n,left:t}),s}function m(e,t,n,r){return{x:e,y:t,width:n,height:r}}var Y=function(){function e(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=m(0,0,0,0),this.target=t}return e.prototype.isActive=function(){var t=K(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},e}(),J=function(){function e(t,n){var r=X(n);A(this,{target:t,contentRect:r})}return e}(),Q=function(){function e(t,n,r){if(this.activeObservations_=[],this.observations_=new M,typeof t!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=n,this.callbackCtx_=r}return e.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element=="undefined"||!(Element instanceof Object))){if(!(t instanceof d(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(t)||(n.set(t,new Y(t)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element=="undefined"||!(Element instanceof Object))){if(!(t instanceof d(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;!n.has(t)||(n.delete(t),n.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(n){n.isActive()&&t.activeObservations_.push(n)})},e.prototype.broadcastActive=function(){if(!!this.hasActive()){var t=this.callbackCtx_,n=this.activeObservations_.map(function(r){return new J(r.target,r.broadcastRect())});this.callback_.call(t,n,t),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),T=typeof WeakMap!="undefined"?new WeakMap:new M,S=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=k.getInstance(),r=new Q(t,n,this);T.set(this,r)}return e}();["observe","unobserve","disconnect"].forEach(function(e){S.prototype[e]=function(){var t;return(t=T.get(this))[e].apply(t,arguments)}});var oe=function(){return typeof p.ResizeObserver!="undefined"?p.ResizeObserver:S}();function se(e,t,n,r){var i=E.unstable_batchedUpdates?function(s){E.unstable_batchedUpdates(n,s)}:n;return e.addEventListener&&e.addEventListener(t,i,r),{remove:function(){e.removeEventListener&&e.removeEventListener(t,i)}}}function ae(e,t){var n=t||{},r=n.defaultValue,i=n.value,o=n.onChange,s=n.postState,u=D(function(){return i!==void 0?i:r!==void 0?typeof r=="function"?r():r:typeof e=="function"?e():e}),a=H(u,2),f=a[0],l=a[1],h=i!==void 0?i:f;s&&(h=s(h));var v=c.exports.useRef(o);v.current=o;var L=c.exports.useCallback(function(_,z){l(_,z),h!==_&&v.current&&v.current(_,h)},[h,v]),O=c.exports.useRef(i);return c.exports.useEffect(function(){i===void 0&&i!==O.current&&l(i),O.current=i},[i]),[h,L]}function ue(e,t){return e?e.contains(t):!1}var ce=c.exports.forwardRef(function(e,t){var n=e.didUpdate,r=e.getContainer,i=e.children,o=c.exports.useRef(),s=c.exports.useRef();c.exports.useImperativeHandle(t,function(){return{}});var u=c.exports.useRef(!1);return!u.current&&P()&&(s.current=r(),o.current=s.current.parentNode,u.current=!0),c.exports.useEffect(function(){n==null||n(e)}),c.exports.useEffect(function(){return s.current.parentNode===null&&o.current!==null&&o.current.appendChild(s.current),function(){var a,f;(a=s.current)===null||a===void 0||(f=a.parentNode)===null||f===void 0||f.removeChild(s.current)}},[]),s.current?E.createPortal(i,s.current):null}),Z=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return n},g=function(){return{height:0,opacity:0}},x=function(t){var n=t.scrollHeight;return{height:n,opacity:1}},ee=function(t){return{height:t?t.offsetHeight:0}},y=function(t,n){return(n==null?void 0:n.deadline)===!0||n.propertyName==="height"},te={motionName:"ant-motion-collapse",onAppearStart:g,onEnterStart:g,onAppearActive:x,onEnterActive:x,onLeaveStart:ee,onLeaveActive:g,onAppearEnd:y,onEnterEnd:y,onLeaveEnd:y,motionDeadline:500};Z("bottomLeft","bottomRight","topLeft","topRight");var fe=function(t){return t!==void 0&&(t==="topLeft"||t==="topRight")?"slide-down":"slide-up"},he=function(t,n,r){return r!==void 0?r:"".concat(t,"-").concat(n)},de=te,ne=c.exports.isValidElement;function re(e,t,n){return ne(e)?c.exports.cloneElement(e,typeof n=="function"?n(e.props||{}):n):t}function le(e,t){return re(e,e,t)}export{ce as P,de as a,oe as b,le as c,ue as d,se as e,fe as f,he as g,ne as i,Z as t,ae as u};
