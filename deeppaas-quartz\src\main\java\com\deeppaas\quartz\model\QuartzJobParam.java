package com.deeppaas.quartz.model;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

/**
 * 定时规则参数
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Data
public class QuartzJobParam implements BaseModel {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 所属应用
     */
    private String appCode;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务类型@JobType
     */
    private String jobType;

    /**
     * 执行任务详情
     * <p>
     * 根据类型存放对应的内容
     */
    private String jobContent;

    /**
     * 是否启用状态
     * <p>
     * 1=启用
     * 0=停用
     */
    private Integer used;

    /**
     * quartz表达式
     */
    private String cron;
}
