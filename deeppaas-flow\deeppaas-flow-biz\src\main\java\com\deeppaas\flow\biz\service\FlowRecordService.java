package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.FlowAction;
import com.deeppaas.flow.biz.entity.FlowRecordDO;

import java.util.List;

/**
 * @ClassName: FlowRecordService
 * @Description: 流程处理记录Service
 * <AUTHOR>
 * @date 2017年9月11日
 */
public interface FlowRecordService {

	/**
	 * @Title: saveRecord
	 * @Description: 保存处理记录
	 * @param instanceId
	 * @param nodeName
	 * @param action
	 * @param userId
	 * @param comment
	 * <AUTHOR>
	 * @date 2017年9月11日
	 */
	void saveRecord(Long instanceId, String nodeName, FlowAction action, String userId, String comment);

	/**
	 * @Title: findRecord
	 * @Description: 获得流程实例对应处理记录
	 * @param instanceId
	 * @return
	 * <AUTHOR>
	 * @date 2017年9月11日
	 */
	List<FlowRecordDO> findRecord(Long instanceId);

	/**
	 * 更新审批历史的流程ID为最新流程ID
     *
	 * @param newFlowId 新的流程ID
	 * @param oldFlowId 原流程ID
	 */
	void updateFlowId(Long newFlowId,Long oldFlowId);
}
