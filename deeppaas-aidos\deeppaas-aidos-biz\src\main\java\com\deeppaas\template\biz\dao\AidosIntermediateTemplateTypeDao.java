package com.deeppaas.template.biz.dao;

import com.deeppaas.template.biz.entity.AidosIntermediateTemplateTypeDO;
import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface AidosIntermediateTemplateTypeDao extends BaseJdbcDao<AidosIntermediateTemplateTypeDO, String> {

    @Query(value = "SELECT ait.id\n" +
            ",ait.`name`,ait.initials,ait.alias,ait.label,ait.path,aitt.`name` as typeName,aitt.alias as typeAlias\n" +
            "FROM aidos_intermediate_template_type as type\n " +
            "INNER JOIN aidos_image_template as ait ON type.image_template_id = ait.id\n" +
            "INNER JOIN aidos_image_template_type as aitt ON type.image_template_type_id = aitt.id\n" +
            "where type.process_task_id = :processTaskId and type.image_template_type_id = :imageTemplateTypeId")
    List<Map<String,Object>> getTemplate(String processTaskId, String imageTemplateTypeId);
    List<AidosIntermediateTemplateTypeDO> findByProcessTaskIdAndImageTemplateTypeId(String processId,String imageTemplateTypeId);
    List<AidosIntermediateTemplateTypeDO> findByProcessTaskId(String processId);
    List<AidosIntermediateTemplateTypeDO> findByProcessTaskIdAndImageTemplateId(String processId,String imageTemplateId);
    @Modifying
    @Query("DELETE from aidos_intermediate_template_type where image_template_type_id = :imageTemplateTypeId and image_template_id = :imageTemplateId")
    void deleteByProcessTaskIdAndImageTemplateId(String imageTemplateTypeId,String imageTemplateId);
}
