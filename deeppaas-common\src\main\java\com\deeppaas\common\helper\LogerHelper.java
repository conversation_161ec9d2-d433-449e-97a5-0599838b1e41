package com.deeppaas.common.helper;

import com.deeppaas.common.exception.BaseException;
import org.slf4j.Logger;

/**
 * @Description 日志工具
 * <AUTHOR>
 * @Date 2019-08-06
 */
public class LogerHelper {
    /**
     * @Description: 将异常信息打印到log中
     * @param logger
     * @param ex
     * @return void
     * <AUTHOR>
     * @date 2019-08-02
     */
    public static void errorLog(Logger logger, Exception ex) {
        logger.error("异常信息:{} {}", ex.getClass().getSimpleName(), ex.getMessage());
        logger.error("<<< error info begin >>>");
        boolean isBaseException = ex instanceof BaseException;
        StackTraceElement[] errorElements = ex.getStackTrace();
        for (StackTraceElement errorElement : errorElements) {
            if(isBaseException){
                if(errorElement.toString().indexOf("com.lh")>-1){
                    logger.error("位置:{}", errorElement.toString());
                }
            }else{
                logger.error("位置:{}", errorElement.toString());
            }
        }
        if (!isBaseException && ex.getCause() != null) {
            StackTraceElement[] causeElements = ex.getCause().getStackTrace();
            for (StackTraceElement causeElement : causeElements) {
                logger.error("cause by:{}", causeElement.toString());
            }
        }
        logger.error("<<< error info end >>>");
    }
}
