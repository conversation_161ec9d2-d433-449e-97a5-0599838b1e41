package com.deeppaas.flow.api.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/21
 */
@Data
public class AssignInfo {
    /** 人员选择范围-单个人 */
    public static final String ASSIGN_SCOPE_ONE = "ONE";
    /** 人员选择范围-多个人 */
    public static final String ASSIGN_SCOPE_MANY = "MANY";
    /** 人员选择范围-所有人 */
    public static final String ASSIGN_SCOPE_ALL = "ALL";
    /** 是否已经指派过了 */
    private boolean assigned = false;
    /** 节点编号 */
    private String nodeCode;
    /** 节点名称 */
    private String nodeName;
    /** 节点类型 */
    private String nodeType;
    /** 限定时间 */
    private Long limitedTime;
    /** 需要指派范围类型(ORG组织结构、POST所属部门通用岗位、FIELD工单字段、BOSS汇报人) */
    private String assignType;
    /** 人员选择范围(ONE选一个、MANY选多个、ALL默认全部选中不可修改) */
    private String assignScope;
    /**
     * 人员选择源的ID集合信息
     * 如果是组织需传递组织类型及组织对应ID信息
     * 如果是通用岗位需传递具体部门及部门岗位ID信息
     * 如果是字段，直接传递对应用户信息
     * 如果是汇报人，直接传递汇报人信息
     */
    private Collection<?> sources;
    /** 原指派人集合 */
    private List<String> oldUsers = new ArrayList<>();

}
