package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.helper.StringHelper;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.util.List;

/**
 * 数据仓库自建表索引记录
 * <p>
 * 一个表最多创建16个索引
 * 索引编号为数据源内唯一
 * 索引字段的长度有限制
 *
 * <AUTHOR>
 * @date 2022/3/17
 */
@Data
@Table("datastore_table_index")
@EqualsAndHashCode(callSuper = true)
public class DataStoreTableIndexDO extends BaseUuidDO {

    /**
     * 所属表
     **/
    @Column("table_id")
    private String tableId;

    /**
     * 索引编号（数据库索引名称）
     **/
    @Column("code")
    private String code;

    /**
     * 索引名称（索引中文名称）
     **/
    @Column("name")
    private String name;

    /**
     * 索引分类
     * <p>
     * 唯一索引=UNIQUE
     * 普通索引=INDEX
     **/
    @Column("type")
    private String type;

    /**
     * 索引字段列表
     **/
    @Column("field_ids")
    private List<String> fieldIds;

    @JsonIgnore
    public void check() {
        if (StringHelper.isEmpty(this.tableId)) {
            throw ReqException.paramNotFound("所属表ID不能为空");
        }
        if (StringHelper.isEmpty(this.code)) {
            throw ReqException.paramNotFound("索引编号不能为空");
        }
        if (StringHelper.isEmpty(this.name)) {
            throw ReqException.paramNotFound("索引名称不能为空");
        }
        if (StringHelper.isEmpty(this.type)) {
            this.type = "INDEX";
        }
    }
}
