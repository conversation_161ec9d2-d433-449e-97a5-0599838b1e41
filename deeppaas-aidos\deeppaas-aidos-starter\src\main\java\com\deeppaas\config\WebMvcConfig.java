package com.deeppaas.config;

import com.deeppaas.account.api.client.filter.UserFilter;
import com.deeppaas.handler.GlobalExceptionResolver;
import com.deeppaas.release.biz.PageReleaseFilter;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.util.unit.DataSize;
import org.springframework.util.unit.DataUnit;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.MultipartConfigElement;
import java.util.List;

/**
 * webmvc相关设置
 *
 * <AUTHOR>
 * @date 2017年8月1日
 */
@Component
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private final UserFilter userFilter;

    public WebMvcConfig(UserFilter userFilter) {
        this.userFilter = userFilter;
    }
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
    }

    /**
     * 增加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 页面发布资源
        registry.addInterceptor(new PageReleaseFilter()).excludePathPatterns("/api/**");

        // 用户安全拦截
        registry.addInterceptor(userFilter).addPathPatterns("/**");

    }

    /**
     * 增加跨域设置
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**");
    }

    /**
     * 自定义异常处理器
     */
    @Override
    public void configureHandlerExceptionResolvers(List<HandlerExceptionResolver> exceptionResolvers) {
        exceptionResolvers.add(new GlobalExceptionResolver());
    }

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setMaxFileSize(DataSize.of(10l, DataUnit.MEGABYTES));
        factory.setMaxRequestSize(DataSize.of(100l, DataUnit.MEGABYTES));
        return factory.createMultipartConfig();
    }
}
