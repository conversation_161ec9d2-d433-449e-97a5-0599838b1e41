package com.deeppaas.datastore.test;

import com.deeppaas.datastore.common.emnus.DBDialect;
import com.deeppaas.datastore.common.mapping.DataStoreEntity;
import com.deeppaas.datastore.common.mapping.DataStoreProperty;
import com.deeppaas.datastore.common.mapping.DataStoreTableJoin;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import com.deeppaas.datastore.common.jdbc.DataStoreSqlGenerator;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/1/25
 */
public class DataStoreSqlGeneratorTest {

    public static void main(String[] args){
        DataStoreEntity entity = mockEntity2();
        DataStoreSqlGenerator sqlGenerator = new DataStoreSqlGenerator(DBDialect.MYSQL, entity);
        String sql1 = sqlGenerator.getFindAll();
        System.out.println(sql1);
        String sql2 = sqlGenerator.getInsert();
        System.out.println(sql2);
        String sql3 = sqlGenerator.getUpdate();
        System.out.println(sql3);
    }

    private static DataStoreEntity mockEntity1() {
        DataStoreEntity entity = new DataStoreEntity();
        entity.setAlias("a1");
        entity.setTable("test_m");
        entity.setType(DataStoreEntityType.LOAD_BUILD);

        DataStoreProperty idProperty = new DataStoreProperty();
        idProperty.setName("id");
        idProperty.setColumnName("ID");
        idProperty.setDataType("NUMBER");
        idProperty.setPrimary(true);
        idProperty.setVirtual(false);
        entity.setIdProperty(idProperty);
        List<DataStoreProperty> properties = new ArrayList<>();
        properties.add(idProperty);
        for(int i=0;i<5;i++){
            DataStoreProperty property = new DataStoreProperty();
            property.setName("f1"+i);
            property.setColumnName("f_1"+i);
            property.setDataType("TEXT");
            property.setPrimary(false);
            property.setVirtual(false);
            properties.add(property);
        }
        entity.setProperties(properties);
        return entity;
    }

    private static DataStoreEntity mockEntity2() {
        DataStoreEntity entity = mockEntity1();
        DataStoreTableJoin join1 = new DataStoreTableJoin();
        join1.setAlias("j1");
        join1.setJoinTable("test_sub1");
        Map<String, String> join1Match = new HashMap<>();
        join1Match.put("f_12","f_22");
        join1.setMatchColumns(join1Match);
        Set<DataStoreTableJoin> joinSet = new HashSet<>();
        joinSet.add(join1);
        entity.setJoins(joinSet);
        for(int i=0;i<5;i++){
            DataStoreProperty property = new DataStoreProperty();
            property.setName("f2"+i);
            property.setColumnName("f_2"+i);
            property.setDataType("TEXT");
            property.setPrimary(true);
            property.setVirtual(false);
            property.setTableJoin(join1);
            entity.getProperties().add(property);
        }
        return entity;
    }
}
