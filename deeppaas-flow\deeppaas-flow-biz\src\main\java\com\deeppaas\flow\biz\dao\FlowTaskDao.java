package com.deeppaas.flow.biz.dao;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

public interface FlowTaskDao extends BaseJdbcDao<FlowTaskDO, Long> {
	/**
	 * 删除流程实例所有任务
	 * @param id
	 * <AUTHOR>
	 */
    @Modifying
    @Query(value="delete from flow_task where instance_id = :id")
    void deleteByInstanceId(Long id);

    @Modifying
    @Query(value="update flow_task set state=1 where id = :taskId and state=0")
    int updateTaskOpen(Long taskId);

    @Modifying
    @Query(value="update flow_task set `action`=:action,operator=:userId,end_time=:closeTime,state=0 where id = :taskId and state=1")
    int updateTaskClose(Long taskId, String action, String userId, LocalDateTime closeTime);

    /**
     * 查询状态不等于的
     * @param instanceId
     * @param state
     * <AUTHOR>
     */
    List<FlowTaskDO> findByInstanceIdAndStateNot(Long instanceId, int state);

    default Long findOpenTaskIdByInstanceIdForUser(Long instanceId, String userId){
        String sql = "SELECT id FROM flow_task WHERE instance_id=:instanceId AND state=1 AND JSON_CONTAINS(todo_users,'[:userId]')";
        Map<String, Object> params = new HashMap<>();
        params.put("instanceId" , instanceId);
        params.put("userId", userId);
        List<Long> ids = queryForList(sql, params, Long.class);
        if(ids.isEmpty()){
            return null;
        }
        return ids.get(0);
    }

    List<FlowTaskDO> findByInstanceIdAndNodeCode(Long instanceId, String nodeCode);

    @Query(value="select * flow_task where instance_id = :instanceId and state=:state and JSON_CONTAINS(todo_users,'[:userCode]')")
    List<FlowTaskDO> findByInstanceIdAndActorAndState(Long instanceId, String userCode, int state);

    List<FlowTaskDO> findByTokenIdIn(List<Long> tokenIds);

    List<FlowTaskDO> findByInstanceIdAndState(Long instanceId, int taskStateOpen);

    List<FlowTaskDO> findByInstanceId(Long instanceId);

    FlowTaskDO findByWaitTaskId(Long waitTaskId);
}
