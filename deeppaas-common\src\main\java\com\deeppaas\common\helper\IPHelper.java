package com.deeppaas.common.helper;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;

/**
 * 获取客户端真实IP工具类
 * <AUTHOR>
 * @date 2016年8月23日
 */
public class IPHelper {
	private static final String LOCAL_IP_V4 = "127.0.0.1";
	private static final String LOCAL_IP_V6 = "0:0:0:0:0:0:0:1";
	private static final String UNKNOWN = "unknown";

	public static String getIpAddr(HttpServletRequest request){
		String ipAddress = request.getHeader("x-forwarded-for");
		if(ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if(ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if(ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if(LOCAL_IP_V4.equals(ipAddress) || LOCAL_IP_V6.equals(ipAddress)){
				//根据网卡取本机配置的IP
				InetAddress inet=null;
				try {
					inet = InetAddress.getLocalHost();
					if(inet!=null){
						ipAddress= inet.getHostAddress();
					}
				} catch (java.net.UnknownHostException e) {
					//e.printStackTrace();
				}
			}
		}
		/**
		 * 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
 		 */
		if(ipAddress!=null && ipAddress.length()>15){
			if(ipAddress.indexOf(",")>0){
				ipAddress = ipAddress.substring(0,ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}

	public static boolean match(String authIpStr, String reqIpStr) {
		if(StringHelper.isEmpty(authIpStr)) {
			return true;
		}
		String reqIpStart = reqIpStr.substring(0,reqIpStr.lastIndexOf("."));
		Integer reqIpEnd = Integer.valueOf(reqIpStr.substring(reqIpStr.lastIndexOf(".")+1));

		String authIps = authIpStr.replaceAll("\r\n", "").replaceAll("\n", "");
		String[] authIpArray = authIps.split(",");
		for(String authIp : authIpArray) {
			if(authIp.equals(reqIpStr)) {
				return true;
			}
			if(authIp.indexOf("/")!=-1) {
				String authIpStart = authIp.substring(0,authIp.lastIndexOf("."));
				if(authIpStart.equals(reqIpStart)) {
					String authIpEnd = authIp.substring(authIp.lastIndexOf(".")+1);
					String[] authIpEnds = authIpEnd.split("/");
					Integer authEndStart = Integer.valueOf(authIpEnds[0]);
					Integer authEndEnd = Integer.valueOf(authIpEnds[1]);
					if(authEndStart<=reqIpEnd&&authEndEnd>=reqIpEnd){
						return true;
					}
				}
			}
		}
		return false;
	}
}
