package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.data.BaseDO;
import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * @ClassName: FlowDefine
 * @Description: 流程定义
 * <AUTHOR>
 */
@Data
@Table("flow_define")
public class FlowDefineDO extends BaseDO {
	/**
	 * ID（编号_版本号）
	 */
	@Id
	@Column("id")
	private String id;
	/**
	 * 编号（所有版本编号相同）
	 */
	@Column("code")
	private String code;
	/**
	 * 名称
	 */
	@Column("name")
	private String name;
	/**
	 * 版本
	 */
	@Column("version")
	private Integer version;
	/**
	 * 数据模型
	 */
	@Column("entity_code")
	private String entityCode;
	/**
	 * 流程定义内容
	 */
	@Column("def_json")
	private String defJson;
	/**
	 * 状态
	 */
	@Column("state")
	private Integer state;
	/**
	 * 附件
	 */
	@Column("attachment")
	private String attachment;
	/**
	 * 流程描述
	 */
	@Column("flow_message")
	private String flowMessage;
	/**
	 * 流程备注
	 */
	@Column("remark")
	private String remark;
	/**
	 * 创建时间
	 */
	@Column("create_time")
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	@Column("update_time")
	private LocalDateTime updateTime;
}
