package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.datastore.biz.entity.DataStoreSourceDO;
import com.deeppaas.datastore.biz.service.DataStoreSourceService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据源接口
 *
 * <AUTHOR>
 * @date 2022/2/15
 */
@RestController
@RequestMapping("/api/datastore/source")
public class DataStoreSourceApi {
    private DataStoreSourceService dataStoreSourceService;


    public DataStoreSourceApi(DataStoreSourceService dataStoreSourceService) {
        this.dataStoreSourceService = dataStoreSourceService;
    }

    /**
     * 获取指定ID数据源
     * @param id
     * @return
     */
    @GetMapping("/get")
    public RestModel<DataStoreSourceDO> get(String id) {
        DataStoreSourceDO dataStoreSourceDO = dataStoreSourceService.findById(id);
        return RestModel.data(dataStoreSourceDO);
    }

    /**
     * 获取数据源列表
     * @return
     */
    @GetMapping("/list")
    public RestModel<List<DataStoreSourceDO>> list() {
        List<DataStoreSourceDO> list = dataStoreSourceService.list();
        return RestModel.data(list);
    }

    /**
     * 保存数据源
     * @param dataStoreSource
     * @return
     */
    @PostMapping("/save")
    public RestModel<DataStoreSourceDO> save(@RequestBody DataStoreSourceDO dataStoreSource) {
        dataStoreSource = dataStoreSourceService.save(dataStoreSource);
        return RestModel.data(dataStoreSource);
    }

    /**
     * 删除指定ID数据源
     * @param id
     * @return
     */
    @PostMapping("/delete")
    public RestModel<Boolean> delete(@BodyParam String id) {
        dataStoreSourceService.deleteById(id);
        return RestModel.data(true);
    }

    /**
     * 测试连接
     */
    @PostMapping("/test")
    public RestModel<Boolean> test(@RequestBody DataStoreSourceDO dataSource) {
        Boolean success = dataStoreSourceService.testConnection(dataSource);
        return RestModel.data(success);
    }

}
