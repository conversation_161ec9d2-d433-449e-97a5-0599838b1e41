package com.deeppaas.flow.biz.element;

import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.MockExecuteItem;
import com.deeppaas.flow.biz.enums.NodeType;
import com.deeppaas.flow.biz.model.AssignInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 节点流转线
 *
 * <AUTHOR>
 */
public class FlowLine implements Serializable {
	/** 流程线编号 **/
	private final String code;
	/** 流程线文字 **/
	private final String text;
	/** 流出节点 **/
	private final FlowNode fromNode;
	/** 流向节点 **/
	private final FlowNode toNode;
	/** 执行条件 **/
	private final String condition;

	public FlowLine(String code, String text, FlowNode fromNode, FlowNode toNode, String condition) {
		this.code = code;
		this.text = text;
		this.fromNode = fromNode;
		this.toNode = toNode;
		this.condition = condition;
	}

	public String getCode() {
		return code;
	}

	public void execute(ExecuteItem executeItem) {
		// 线的源是判断节点
		if (NodeType.DECISION.equals(this.fromNode.getType())) {
			boolean enabled = executeItem.getEngine().event().decided(condition, executeItem);
			if (!enabled) {
				return;
			}
		}
		toNode.enter(executeItem);
	}

	public List<AssignInfo> mockExecute(MockExecuteItem executeItem) {
		// 线的源是判断节点
		if (NodeType.DECISION.equals(this.fromNode.getType())) {
			boolean enabled = executeItem.getEngine().event().decided(condition, executeItem);
			if (!enabled) {
				return null;
			}
		}
		return toNode.mockEnter(executeItem);
	}
}
