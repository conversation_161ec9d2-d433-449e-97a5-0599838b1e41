package com.deeppaas.datastore.biz.web.vo;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

import java.util.List;

/**
 * 数据表文件夹节点
 *
 * <AUTHOR>
 * @date 2022/07/30
 */
@Data
public class DataStoreFolderNode implements BaseModel {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 排序号
     */
    private Integer sortNo;

    /**
     * 子节点列表
     */
    private List<DataStoreFolderNode> childrenNodes;
}
