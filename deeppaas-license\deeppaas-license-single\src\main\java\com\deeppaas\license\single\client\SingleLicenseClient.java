package com.deeppaas.license.single.client;

import com.deeppaas.license.api.client.LicenseClient;
import com.deeppaas.license.api.model.License;
import com.deeppaas.license.single.SingleLicenseHelper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/11/2
 */
@Component
public class SingleLicenseClient implements LicenseClient {
    /**
     * 记录当前用户客户端信息，保持一个用户只有一个端登录
     */
    private static Map<String, String> userClientMap = new ConcurrentHashMap<>();
    /**
     * 记录在线用户
     */
    private static Map<String, Long> onlineUser = new ConcurrentHashMap<>();
    @Override
    public License getLicense() {
        return SingleLicenseHelper.getLocalLicense();
    }

    @Override
    public boolean addOnlineUser(String userId, String clientId, long expireTime) {
        License license = getLicense();
        if(license==null){
            return false;
        }
        if(license.getOnlineUserLimit()>License.INT_NO_LIMIT){
            int realOnlineUserSize = getOnlineUserSize();
            if(realOnlineUserSize>= license.getOnlineUserLimit()){
                return false;
            }
        }
        userClientMap.put(userId, clientId);
        onlineUser.put(userId, expireTime);
        return true;
    }

    public int getOnlineUserSize() {
        long curTime = System.currentTimeMillis()/1000;
        for(String userId : onlineUser.keySet()){
            if(onlineUser.get(userId)<curTime){
                onlineUser.remove(userId);
                userClientMap.remove(userId);
            }
        }
        return onlineUser.size();
    }

    @Override
    public void removeOnlineUser(String userId) {
        userClientMap.remove(userId);
        onlineUser.remove(userId);
    }

    @Override
    public boolean refreshOnlineUser(String userId, String clientId, long expireTime) {
        License license = getLicense();
        if(license==null){
            return false;
        }
        if(license.getOnlineUserLimit()>License.INT_NO_LIMIT){
            String onlineClientId = userClientMap.get(userId);
            if(!Objects.equals(clientId, onlineClientId)){
                return false;
            }
        }
        onlineUser.put(userId, expireTime);
        return true;
    }

}
