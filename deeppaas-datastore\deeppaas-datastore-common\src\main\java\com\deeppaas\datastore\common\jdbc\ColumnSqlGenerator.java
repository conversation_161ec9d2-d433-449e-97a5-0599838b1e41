package com.deeppaas.datastore.common.jdbc;

import com.deeppaas.common.data.enums.SimpleDataType;

import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022/6/16
 */
public class ColumnSqlGenerator {

    public static SimpleDataType getDataType(int sqlType){
        return switch (sqlType) {
            case Types.BIT, Types.TINYINT, Types.SMALLINT, Types.INTEGER, Types.BIGINT,
                    Types.FLOAT, Types.REAL, Types.DOUBLE, Types.NUMERIC, Types.DECIMAL
                    -> SimpleDataType.NUMBER;

            case Types.CHAR, Types.VARCHAR, Types.LONGVARCHAR,
                    Types.NCHAR, Types.NVARCHAR,Types.LONGNVARCHAR,
                    Types.BINARY, Types.VARBINARY, Types.LONGVARBINARY,
                    Types.BLOB, Types.CLOB, Types.NCLOB
                    -> SimpleDataType.TEXT;

            case Types.DATE, Types.TIME, Types.TIMESTAMP, Types.TIME_WITH_TIMEZONE, Types.TIMESTAMP_WITH_TIMEZONE
                    -> SimpleDataType.DATE_TIME;

            default -> null;
//                    Types.NULL, Types.OTHER, Types.JAVA_OBJECT,
//                    Types.DISTINCT, Types.STRUCT, Types.ARRAY,
//                    Types.REF, Types.DATALINK, Types.BOOLEAN, Types.ROWID, Types.SQLXML,
//                    Types.REF_CURSOR
        };
    }
}
