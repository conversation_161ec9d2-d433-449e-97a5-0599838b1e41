package com.deeppaas.rule.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import com.deeppaas.common.data.enums.SimpleDataType;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 规则内部使用的模型
 * todo 后期酌情看要不要增加记录校验
 * <AUTHOR>
 * @date 2022/6/16
 */
@Data
//@Table("rule_used_entity")
public class RuleUsedEntityDO extends BaseUuidDO {
    public static final SimpleDataType TYPE_DATA_ENTITY = SimpleDataType.DATA_ENTITY;
    public static final SimpleDataType TYPE_RULE_ENTITY = SimpleDataType.RULE_ENTITY;
    /**
     * 规则ID
     */
    @Column("rule_id")
    private String ruleId;

    /**
     * 使用模型类型：数据模型、计算模型
     */
    @Column("entity_type")
    private String entityType;

    /**
     * 模型索引，数据模型的code、计算模型的ID
     */
    @Column("entityKey")
    private String entityKey;

}
