package com.deeppaas.msg.biz.web.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@Data
public class UserMsgCountVO {
    private String boxId;
    private String boxName;
    private Long msgCount;

    public UserMsgCountVO() {
    }

    public UserMsgCountVO(String boxId, String boxName) {
        this.boxId = boxId;
        this.boxName = boxName;
        this.msgCount = 0l;
    }

    public UserMsgCountVO(String boxId, Long msgCount) {
        this.boxId = boxId;
        this.msgCount = msgCount;
    }
}
