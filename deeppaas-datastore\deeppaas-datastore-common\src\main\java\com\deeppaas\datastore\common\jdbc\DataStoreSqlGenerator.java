package com.deeppaas.datastore.common.jdbc;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.DataStoreConditionL;
import com.deeppaas.datastore.common.DataStoreConditionS;
import com.deeppaas.datastore.common.DataStoreException;
import com.deeppaas.datastore.common.emnus.DBDialect;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import com.deeppaas.datastore.common.emnus.DataStoreOperator;
import com.deeppaas.datastore.common.mapping.DataStoreEntity;
import com.deeppaas.datastore.common.mapping.DataStoreProperty;
import com.deeppaas.datastore.common.mapping.DataStoreSelf;
import com.deeppaas.datastore.common.mapping.DataStoreTableJoin;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.util.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/19
 */
public class DataStoreSqlGenerator {
    public static final String IDS_SQL_PARAMETER = "ids";
    protected static final Pattern parameterPattern = Pattern.compile("\\W");
    protected final DBDialect dbDialect;
    protected final DataStoreEntity entity;

    protected final Lazy<String> selectPartSql = Lazy.of(this::createSelectPartSql);
    protected final Lazy<String> fromPartSql = Lazy.of(this::createFromPartSql);
    protected final Lazy<String> findOneSql = Lazy.of(this::createFindOneSql);
    protected final Lazy<String> findAllSql = Lazy.of(this::createFindAllSql);
    protected final Lazy<String> findAllInListSql = Lazy.of(this::createFindAllInListSql);

    protected final Lazy<String> existsSql = Lazy.of(this::createExistsSql);
    protected final Lazy<String> countSql = Lazy.of(this::createCountSql);


    public DataStoreSqlGenerator(DBDialect dbDialect, DataStoreEntity jdbcEntity) {
        this.dbDialect = dbDialect;
        this.entity = jdbcEntity;
    }

    public String getFindList(DataStoreCondition condition) {
        String wherePartSql = createWherePartSql(condition);
        return selectPartSql.get() + fromPartSql.get() + wherePartSql;
    }

    public String getFindList(DataStoreCondition condition, Sort sort) {
        String wherePartSql = createWherePartSql(condition);
        String sortPartSql = createSortPartSql(sort);
        return selectPartSql.get() + fromPartSql.get() + wherePartSql + sortPartSql;
    }

    public String getFindPage(DataStoreCondition condition, Pageable pageable) {
        String wherePartSql = createWherePartSql(condition);
        String sortPartSql = createSortPartSql(pageable.getSort());
        return selectPartSql.get() + fromPartSql.get() + wherePartSql + sortPartSql + dbDialect.limitOffset(pageable);
    }

    private String createSortPartSql(Sort sort) {
        // 排序（xx asc,yy desc）
        if (null == sort || !sort.iterator().hasNext()) {
            // 默认排序
            return " ORDER BY " + entity.getAlias() + "." + dbDialect.backtick(DataStoreSelf.ID_SQL_PARAMETER) + " ASC ";
        }
        // 按照前端传入的字段排序
        StringBuilder sortInfo = new StringBuilder();
        Iterator<Sort.Order> orders = sort.iterator();
        while (orders.hasNext()) {
            Sort.Order order = orders.next();
            String property = order.getProperty();
            String direction = order.getDirection().name();
            sortInfo.append(dbDialect.backtick(property)).append(" ").append(direction).append(",");
        }
        sortInfo = new StringBuilder(sortInfo.substring(0, sortInfo.length() - 1));
        return " ORDER BY " + sortInfo;
    }

    public String getCount(DataStoreCondition condition) {
        String wherePartSql = createWherePartSql(condition);
        return "SELECT COUNT(*) " + fromPartSql.get() + wherePartSql;
    }

    public String getFindAllInList() {
        return findAllInListSql.get();
    }

    public String getFindAll() {
        return findAllSql.get();
    }

    public String getExists() {
        return existsSql.get();
    }

    public String getFindOne() {
        return findOneSql.get();
    }

    public String getCount() {
        return countSql.get();
    }

    public String getDeleteOne() {
        if(DataStoreEntityType.SQL_BUILD==entity.getType()){
            return null;
        }
        StringBuilder sqlBuilder = new StringBuilder("DELETE FROM ")
                .append(dbDialect.backtick(entity.getTable()))
                .append(" WHERE ")
                .append(dbDialect.backtick(entity.getIdProperty().getColumnName())).append("=:").append(entity.getIdProperty().getName());
        for(DataStoreProperty property : entity.getProperties()){
            if(property.isDiscriminator()){
                sqlBuilder.append(" AND ").append(dbDialect.backtick(property.getColumnName())).append("=:").append(property.getName());
            }
        }
        return sqlBuilder.toString();
    }

    private String createFindOneSql() {
        if(DataStoreEntityType.SQL_BUILD==entity.getType()){
            return null;
        }
        StringBuilder sqlBuilder = new StringBuilder(selectPartSql.get()).append(fromPartSql.get())
                .append(" WHERE ").append(entity.getAlias()).append(".").append(dbDialect.backtick(entity.getIdProperty().getColumnName()))
                .append("=:").append(entity.getIdProperty().getName());
        for(DataStoreProperty property : entity.getProperties()){
            if(property.isDiscriminator()){
                sqlBuilder.append(" AND ").append(dbDialect.backtick(entity.getAlias())).append(".").append(dbDialect.backtick(property.getColumnName())).append("=:").append(property.getName());
            }
        }
        return sqlBuilder.toString();
    }

    private String createFindAllSql() {
        StringBuilder sqlBuilder = new StringBuilder(selectPartSql.get()).append(fromPartSql.get());
        List<String> discriminators = entity.getProperties().stream().filter(DataStoreProperty::isDiscriminator)
                .map(property -> dbDialect.backtick(entity.getAlias())+"."+dbDialect.backtick(property.getColumnName()) + "=:" + property.getName()).toList();
        if(!discriminators.isEmpty()){
            sqlBuilder.append(" WHERE ").append(String.join(" AND ", discriminators));
        }
        return sqlBuilder.toString();
    }

    private String createFindAllInListSql() {
        if(DataStoreEntityType.SQL_BUILD==entity.getType()){
            return null;
        }
        StringBuilder sqlBuilder = new StringBuilder(selectPartSql.get()).append(fromPartSql.get())
                .append(" WHERE ").append(dbDialect.backtick(entity.getAlias())).append(".").append(dbDialect.backtick(entity.getIdProperty().getColumnName()))
                .append(" in :").append(IDS_SQL_PARAMETER);
        for(DataStoreProperty property : entity.getProperties()){
            if(property.isDiscriminator()){
                sqlBuilder.append(" AND ").append(dbDialect.backtick(entity.getAlias())).append(".").append(dbDialect.backtick(property.getColumnName())).append("=:").append(property.getName());
            }
        }
        return sqlBuilder.toString();
    }

    private String createExistsSql() {
        if(DataStoreEntityType.SQL_BUILD==entity.getType()){
            return null;
        }
        StringBuilder sqlBuilder = new StringBuilder(selectPartSql.get()).append(fromPartSql.get())
                .append(" WHERE ").append(dbDialect.backtick(entity.getAlias())).append(".").append(dbDialect.backtick(entity.getIdProperty().getColumnName()))
                .append("=:").append(entity.getIdProperty().getName());
        for(DataStoreProperty property : entity.getProperties()){
            if(property.isDiscriminator()){
                sqlBuilder.append(" AND ").append(dbDialect.backtick(entity.getAlias())).append(".").append(dbDialect.backtick(property.getColumnName())).append("=:").append(property.getName());
            }
        }
        return sqlBuilder.toString();
    }

    private String createCountSql() {
        return "SELECT COUNT(*) " + fromPartSql.get();
    }

    public String getInsert() {
        return switch (entity.getType()) {
            case SELF_BUILD -> selfTableInsert();
            case LOAD_BUILD -> loadTableInsert();
            case SQL_BUILD -> throw DataStoreException.sqlBuildStoreError();
            default -> throw DataStoreException.error("实体类型异常");
        };
    }

    private String loadTableInsert() {
        if (DataStoreEntityType.SQL_BUILD == entity.getType()) {
            throw DataStoreException.error("SQL类实体不能保存");
        }
        List<DataStoreProperty> properties = entity.getProperties();
        List<String> columns = new ArrayList<>();
        List<String> variates = new ArrayList<>();
        for (DataStoreProperty property : properties) {
            if (!property.isVirtual()) {
                columns.add(dbDialect.backtick(property.getColumnName()));
                variates.add(":" + property.getName());
            }
        }
        String insertSql = "INSERT INTO " + dbDialect.backtick(entity.getTable()) + " (" + String.join(",", columns) + ") values(" + String.join(",", variates) + ")";
        return insertSql;
    }

    private String selfTableInsert() {
        if (DataStoreEntityType.SQL_BUILD == entity.getType()) {
            throw DataStoreException.error("SQL类实体不能保存");
        }
        StringBuffer insertBuffer = new StringBuffer("INSERT INTO ")
                .append(dbDialect.backtick(entity.getTable())).append(" (")
                .append(DataStoreSelf.ID_SQL_PARAMETER).append(",")
                .append(DataStoreSelf.CREATE_TIME_SQL_PARAMETER)
                .append(",").append(DataStoreSelf.DATA_SQL_PARAMETER)
                .append(") values(:").append(DataStoreSelf.ID_SQL_PARAMETER)
                .append(", :").append(DataStoreSelf.CREATE_TIME_SQL_PARAMETER)
                .append(", :").append(DataStoreSelf.DATA_SQL_PARAMETER).append(")");
        return insertBuffer.toString();
    }

    public String getUpdate() {
        return switch (entity.getType()) {
            case SELF_BUILD -> selfTableUpdate();
            case LOAD_BUILD -> loadTableUpdate();
            case SQL_BUILD -> throw DataStoreException.sqlBuildStoreError();
            default -> throw DataStoreException.error("实体类型异常");
        };
    }

    private String loadTableUpdate() {
        List<DataStoreProperty> properties = entity.getProperties();
        List<String> setParts = new ArrayList<>();
        for (DataStoreProperty property : properties) {
            if (!property.isVirtual() && !property.isPrimary()) {
                setParts.add(dbDialect.backtick(property.getColumnName()) + "=:" + property.getName());
            }
        }
        StringBuffer updateBuffer = new StringBuffer("UPDATE ").append(dbDialect.backtick(entity.getTable())).append(" SET ");
        updateBuffer.append(String.join(",", setParts)).append(" WHERE ")
                .append(dbDialect.backtick(entity.getIdProperty().getColumnName())).append("=:").append(entity.getIdProperty().getName());
        return updateBuffer.toString();
    }

    private String selfTableUpdate() {
        StringBuffer updateBuffer = new StringBuffer("UPDATE ").append(dbDialect.backtick(entity.getTable())).append(" SET ")
                .append(DataStoreSelf.DATA_SQL_PARAMETER).append("=:").append(DataStoreSelf.DATA_SQL_PARAMETER)
                .append(" WHERE ").append(dbDialect.backtick(entity.getIdProperty().getColumnName())).append("=:").append(entity.getIdProperty().getName());
        return updateBuffer.toString();
    }

    private String createSelectPartSql() {
        List<DataStoreProperty> properties = entity.getProperties();
        Set<String> columns = new LinkedHashSet<>(properties.size());
        properties.forEach(property -> {
            DataStoreTableJoin join = property.getTableJoin();
            if (join == null) {
                columns.add(dbDialect.backtick(entity.getAlias()) + "." + dbDialect.backtick(property.getColumnName()) + " AS " + dbDialect.backtick(property.getName()));
            } else {
                columns.add(join.getAlias() + "." + dbDialect.backtick(property.getColumnName()) + " AS " + dbDialect.backtick(property.getName()));
            }
        });
        return "SELECT " + String.join(",", columns);
    }

    private String createFromPartSql() {
        final String alias = entity.getAlias();
        StringBuilder formBuilder = new StringBuilder(" FROM ");
        if (DataStoreEntityType.SQL_BUILD == entity.getType()) {
            formBuilder.append("(").append(entity.getTable()).append(")");
        } else {
            formBuilder.append(dbDialect.backtick(entity.getTable()));
        }
        if (!Objects.equals(entity.getAlias(), entity.getTable())) {
            formBuilder.append(" ").append(entity.getAlias());
        }
        Set<DataStoreTableJoin> tableJoins = entity.getJoins();
        for (DataStoreTableJoin tableJoin : tableJoins) {
            if(CollectionUtils.isEmpty(tableJoin.getMatchColumns())){
               throw RunException.error("数据模型配置缺失，关联表["+tableJoin.getJoinTable()+"]没有设置匹配字段");
            }
            formBuilder.append(" LEFT OUTER JOIN ");
            if(tableJoin.isRealTable()){
                formBuilder.append(dbDialect.backtick(tableJoin.getJoinTable()));
            }else {
                formBuilder.append("(").append(tableJoin.getJoinTable()).append(")");
            }
            formBuilder.append(" AS ").append(tableJoin.getAlias()).append(" ON ");
            List<String> joinMatchList = new ArrayList<>();
            tableJoin.getMatchColumns().forEach((k, v) -> {
                joinMatchList.add(alias + "." + dbDialect.backtick(k) + "=" + tableJoin.getAlias() + "." + dbDialect.backtick(v) +"");
            });
            formBuilder.append(String.join(" AND ", joinMatchList));
            if (!CollectionUtils.isEmpty(tableJoin.getJoins())) {
                tableJoin.getJoins().forEach(childJoin -> {
                    formBuilder.append(createChildJoinPartSql(tableJoin, childJoin));
                });
            }
        }
        return formBuilder.toString();
    }

    private String createChildJoinPartSql(DataStoreTableJoin parentJoin, DataStoreTableJoin join) {
        StringBuffer joinBuffer = new StringBuffer();
        joinBuffer.append(" LEFT OUTER JOIN ").append(dbDialect.backtick(join.getJoinTable())).append(" AS ").append(join.getAlias()).append(" ON ");
        List<String> joinMatchList = new ArrayList<>();
        join.getMatchColumns().forEach((k, v) -> {
            joinMatchList.add(parentJoin.getAlias() + "." + dbDialect.backtick(k) + "=" + join.getAlias() + "." + dbDialect.backtick(v));
        });
        joinBuffer.append(" ").append(String.join(" AND ", joinMatchList));
        if (!CollectionUtils.isEmpty(join.getJoins())) {
            join.getJoins().forEach(childJoin -> {
                joinBuffer.append(createChildJoinPartSql(join, childJoin));
            });
        }
        return joinBuffer.toString();
    }

    private String createWherePartSql(DataStoreCondition condition) {
        List<String> discriminators = entity.getProperties().stream().filter(DataStoreProperty::isDiscriminator)
                .map(property -> dbDialect.backtick(entity.getAlias())+"."+dbDialect.backtick(property.getColumnName()) + "=:" + property.getName()).toList();
        if (condition == null) {
            return discriminators.isEmpty() ? "" : " WHERE " + String.join(" AND ", discriminators);
        }
        String whereCondition = createConditionSql(condition);

        if (StringUtils.hasText(whereCondition)) {
            return discriminators.isEmpty() ? " WHERE " + whereCondition :
                    " WHERE " + whereCondition + " AND " + String.join(" AND ", discriminators);
        }
        return discriminators.isEmpty() ? "" : " WHERE " + String.join(" AND ", discriminators);
    }

    private String createConditionSql(DataStoreCondition condition) {
        if (condition != null) {
            DataStoreOperator operator = condition.getOperator();
            if (condition instanceof DataStoreConditionL conditionL) {
                List<DataStoreCondition> childConditions = conditionL.getConditions();
                List<String> subConditions = childConditions.stream()
                        .map(subCondition -> createConditionSql(subCondition))
                        .filter(sql -> StringUtils.hasText(sql)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(subConditions)) {
                    return null;
                }
                if (subConditions.size() == 1) {
                    return subConditions.get(0);
                }
                return "("+String.join(" " + operator.getSqlOpt() + " ", subConditions)+")";
            }
            if (condition instanceof DataStoreConditionS conditionS) {
                String fieldName = conditionS.getFieldName();
                DataStoreProperty property = entity.getProperty(fieldName);
                String targetColumn;
                if (property.getTableJoin() == null) {
                    targetColumn = entity.getAlias() + "." + dbDialect.backtick(property.getColumnName());
                } else {
                    targetColumn = property.getTableJoin().getAlias() + "." + dbDialect.backtick(property.getColumnName());
                }
                return switch (operator){
                    case IN,NOTIN -> targetColumn + " " + operator.getSqlOpt() + " (:" + conditionS.getFieldName() + ")";
                    case ISNULL,NOTNULL -> targetColumn + " " + operator.getSqlOpt();
                    default -> targetColumn + " " + operator.getSqlOpt() + " :" + conditionS.getFieldName();
                };
            }
        }
        return "";
    }

}
