package com.deeppaas.common.data.ddl;

import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
public class MysqlGenerator implements DDLGenerator{
    private static final String BACKTICK = "`";

    @Override
    public String createTableSql(String schema, String tableName, String owner, List<ColumnData> columns) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("CREATE TABLE `").append(tableName).append("` (\n");
        ColumnData pkColumn = null;
        for(ColumnData column : columns){
            if(column.isId()){
                pkColumn = column;
            }
            sqlBuilder.append(BACKTICK).append(column.getName()).append(BACKTICK).append(" ");
            if(StringUtils.hasText(column.getDefinition())){
                sqlBuilder.append(column.getDefinition());
            }else {
                String definition = switch (column.getType()){
                    case VARCHAR -> "varchar("+column.getLength()+")";
                    case DECIMAL -> "DECIMAL("+column.getLength()+","+column.getDigits()+")";
                    default -> column.getType().getName();
                };
                sqlBuilder.append(definition);
            }
            if(!column.isNullable()){
                sqlBuilder.append(" NOT NULL");
            }
            sqlBuilder.append(",\n");
        }
        sqlBuilder.append("PRIMARY KEY (").append(pkColumn.getName()).append(")\n)");
        return sqlBuilder.toString();
    }
}
