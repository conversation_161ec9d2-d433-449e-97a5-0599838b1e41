package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreTableDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
public interface DataStoreTableDao extends BaseJdbcDao<DataStoreTableDO, String> {

    DataStoreTableDO findBySourceIdAndTableName(String sourceId, String tableName);

    @Modifying
    @Transactional
    @Query(value = "update datastore_table set sort_no = 1 where folder_id = ?1 and sort_no >= ?2")
    void modifySortNo(String folderId, Integer sortNo);

    List<DataStoreTableDO> findBySourceIdOrderByCreateTimeAsc(String sourceId);
}
