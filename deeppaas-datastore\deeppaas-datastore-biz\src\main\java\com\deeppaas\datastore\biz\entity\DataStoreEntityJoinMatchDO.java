package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据模型级联表匹配关系
 *
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@Table("datastore_entity_join_match")
@EqualsAndHashCode(callSuper = true)
public class DataStoreEntityJoinMatchDO extends BaseUuidDO {
    /**
     * 数据模型编号
     **/
    @Column("entity_code")
    private String entityCode;
    /**
     * 数据模型连接编号
     **/
    @Column("join_code")
    private String joinCode;
    /**
     * 匹配表达式左侧列名
     **/
    @Column("left_column_name")
    private String leftColumnName;
    /**
     * 匹配表达式右侧列名
     **/
    @Column("right_column_name")
    private String rightColumnName;
}
