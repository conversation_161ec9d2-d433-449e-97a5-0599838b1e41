package com.deeppaas.extapi.biz.service;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.extapi.biz.enums.ApiReqBodyType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.enums.ApiPositionType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 *
 * 自定义接口参数解析器
 * <AUTHOR>
 * @date 2022/6/23
 */
@Component
public class ExtApiParamResolver {

    /**
     * 解析对外接口参数
     * @param extApiDO
     * @param apiParams
     * @param request
     * @return
     */
    public Map<String, Object> resolveOpenApiParams(ExtApiDO extApiDO, List<ExtApiParamDO> apiParams, HttpServletRequest request){
        Map<String, Object> params = new HashMap<>();
        List<ExtApiParamDO> bodyApiParams = new ArrayList<>();
        for(ExtApiParamDO apiParam : apiParams){
            ApiPositionType positionType = ApiPositionType.valueOf(apiParam.getPosition());
            String paramCode = apiParam.getCode();
            SimpleDataType dataType = SimpleDataType.valueOf(apiParam.getType());
            switch (positionType){
                case QUERY -> params.put(paramCode, getParameterValue(request.getParameterValues(paramCode), dataType));
                case HEADER -> params.put(paramCode, request.getHeader(apiParam.getCode()));
                case BODY -> bodyApiParams.add(apiParam);
            }
        }
        ApiReqBodyType apiReqBodyType = ApiReqBodyType.valueOf(extApiDO.getReqBodyType());
        switch (apiReqBodyType) {
            case JSON -> params.putAll(getJsonBodyParams(bodyApiParams, request));
            case FORM_DATA, FORM_URLENCODED -> params.putAll(getFormBodyParams(bodyApiParams, request));
        }
        return params;
    }

    /**
     * 解析前端动作请求参数
     * @param extApiDO
     * @param apiParams
     * @param request
     * @return
     */
    public Map<String, Object> resolveApiActionParams(ExtApiDO extApiDO, List<ExtApiParamDO> apiParams, HttpServletRequest request){
        boolean hasFile = apiParams.stream().anyMatch(
                apiParam -> SimpleDataType.FILE.name().equals(apiParam.getType())
                        || SimpleDataType.FILE.name().equals(apiParam.getType()));
        if(hasFile){
            return getFormBodyParams(apiParams, request);
        }else {
            return getJsonBodyParams(apiParams, request);
        }
    }

    private Map<String,Object> getJsonBodyParams(List<ExtApiParamDO> bodyApiParams, HttpServletRequest request) {
        try {
            return JsonHelper.readValue(request.getInputStream(), Map.class);
        } catch (IOException e) {
            throw RunException.error("解析请求体JSON异常");
        }
    }

    private Map<String,Object> getFormBodyParams(List<ExtApiParamDO> bodyApiParams, HttpServletRequest request) {
        if(request instanceof MultipartHttpServletRequest multipartRequest){
            Map<String, Object> bodyParamMap = new HashMap<>();
            for(ExtApiParamDO apiParam : bodyApiParams){
                String paramCode = apiParam.getCode();
                SimpleDataType dataType = SimpleDataType.valueOf(apiParam.getType());
                switch (dataType){
                    case FILE -> bodyParamMap.put(paramCode, multipartRequest.getFile(paramCode));
                    case FILES -> bodyParamMap.put(paramCode, multipartRequest.getFiles(paramCode));
                    default -> bodyParamMap.put(paramCode, getParameterValue(request.getParameterValues(paramCode), dataType));
                }
            }
            return bodyParamMap;
        }else {
            throw RunException.error("接受请求体BODY异常");
        }
    }

    private Object getParameterValue(String[] paramValues, SimpleDataType dataType) {
        if(paramValues==null || paramValues.length==0){
            return null;
        }
        switch (dataType){
            case TEXT -> {
                return paramValues[0];
            }
            case NUMBER -> {
                return new BigDecimal(paramValues[0]);
            }
            case BOOLEAN -> {
                return Boolean.valueOf(paramValues[0]);
            }
            case DATE_TIME -> {
                return DateHelper.str2LocalDateTime(paramValues[0]);
            }
            case TEXTS -> {
                return paramValues;
            }
            case NUMBERS -> {
                return Arrays.stream(paramValues).map(item -> new BigDecimal(item)).toList();
            }
            case FILE, FILES -> {
                throw RunException.error("异常");
            }
            case DATA_ENTITY, RULE_ENTITY, DATA_ENTITIES, RULE_ENTITIES ->{
                throw RunException.error("暂不支持");
            }
            case VOID -> {
                return null;
            }
        }
        return null;
    }
}
