package com.deeppaas.datastore.biz.web.view;

import com.deeppaas.common.web.view.BaseXlsxView;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/6
 */
public class DataStoreDataExcelView extends BaseXlsxView {
    public static final String MODEL_KEY_ENTITY = "ENTITY";
    public static final String MODEL_KEY_FIELDS = "FIELDS";
    public static final String MODEL_KEY_LIMIT_FIELDS = "LIMIT_FIELDS";
    public static final String MODEL_KEY_DATA = "DATA";
    @Override
    protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String entityName = (String) model.get(MODEL_KEY_ENTITY);
        List<String> limitFields = (List<String>) model.get(MODEL_KEY_LIMIT_FIELDS);
        List<DataStoreEntityFieldDTO> fieldDTOS = (List<DataStoreEntityFieldDTO>) model.get(MODEL_KEY_FIELDS);
        List<DataStoreData> dataList = (List<DataStoreData>) model.get(MODEL_KEY_DATA);

        String fileName = entityName+".xlsx";
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/ms-excel");
        response.setHeader("Content-Disposition", "inline; filename="+ URLEncoder.encode(fileName, "UTF-8"));

        int rowNum = 0;
        int cellNum = 0;
        Sheet sheet = workbook.createSheet();
        Row row = sheet.createRow(rowNum++);
        List<String> fieldCodes = new ArrayList<>();
        if(CollectionUtils.isEmpty(limitFields)){
            for(DataStoreEntityFieldDTO field : fieldDTOS){
                row.createCell(cellNum++).setCellValue(field.getName());
                fieldCodes.add(field.getCode());
            }
        }else {
            Map<String, DataStoreEntityFieldDTO> fieldMap = fieldDTOS.stream().collect(Collectors.toMap(DataStoreEntityFieldDTO::getCode, Function.identity()));
            for(String fieldCode : limitFields){
                DataStoreEntityFieldDTO field = fieldMap.get(fieldCode);
                if(field!=null){
                    row.createCell(cellNum++).setCellValue(field.getName());
                    fieldCodes.add(field.getCode());
                }
            }
        }
        for (DataStoreData data : dataList){
            cellNum = 0;
            row = sheet.createRow(rowNum++);
            for(String fieldCode : fieldCodes){
                row.createCell(cellNum++).setCellValue(data.get(fieldCode)==null?null:data.get(fieldCode).toString());
            }

        }
    }
}
