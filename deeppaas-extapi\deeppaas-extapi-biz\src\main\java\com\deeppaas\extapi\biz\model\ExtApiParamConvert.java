package com.deeppaas.extapi.biz.model;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

/**
 * 接口参数转换参数
 *
 * <AUTHOR>
 * @date 2022/08/17
 */
@Data
public class ExtApiParamConvert implements BaseModel {

    /**
     * 接口主键ID
     */
    private String id;

    /**
     * 参数模型类型
     * <p>
     * 计算模型=RULE_ENTITY
     * 数据模型=DATA_ENTITY
     */
    private String type;

    /**
     * 对应模型
     * <p>
     * 数据模型为数据模型code
     * 计算模型为计算模型主键ID
     */
    private String entity;


    /**
     * 参数传输位置@ApiPositionType
     */
    private String position;
}
