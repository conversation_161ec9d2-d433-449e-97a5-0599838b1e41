package com.deeppaas.flow.biz.parser;

import com.deeppaas.flow.biz.element.FlowEventRule;
import com.deeppaas.flow.biz.enums.FlowEvent;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
public class FlowEventParser {
    /**
     * 环节事件规则-事件
     */
    private static final String KEY_EVENTS_EVENT = "event";
    /**
     * 环节事件规则-规则
     */
    private static final String KEY_EVENTS_RULE = "ruleIds";
    public static List<FlowEventRule> parse(JsonNode eventRulesJsonNode) {
        List<FlowEventRule> eventRules = new ArrayList<>();
        if(eventRulesJsonNode==null){
            return eventRules;
        }
        Iterator<JsonNode> eventRulesJson = eventRulesJsonNode.elements();
        while (eventRulesJson.hasNext()){
            JsonNode eventRuleJson = eventRulesJson.next();
            String eventName = eventRuleJson.get(KEY_EVENTS_EVENT).textValue();
            List<String> ruleIds = new ArrayList<>();
            Iterator<JsonNode> ruleIdsJson = eventRuleJson.get(KEY_EVENTS_RULE).elements();
            while (ruleIdsJson.hasNext()){
                ruleIds.add(ruleIdsJson.next().textValue());
            }
            FlowEventRule eventRule = new FlowEventRule();
            eventRule.setEvent(FlowEvent.valueOf(eventName));
            eventRule.setRuleIds(ruleIds);
            eventRules.add(eventRule);
        }
        return eventRules;
    }
}
