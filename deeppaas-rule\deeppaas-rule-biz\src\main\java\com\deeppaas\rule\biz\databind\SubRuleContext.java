package com.deeppaas.rule.biz.databind;

import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.RuleEngine;
import com.deeppaas.rule.biz.model.RuleModel;
import org.springframework.beans.*;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/16
 */
public class SubRuleContext extends RuleContext {

    public SubRuleContext(RuleModel rule, RuleEngine ruleEngine) {
        super(rule, ruleEngine);
    }

    public static SubRuleContext of(RuleContext ruleContext){
        SubRuleContext subRuleContext = new SubRuleContext(ruleContext.getRule(), ruleContext.getRuleEngine());
        Object param = ruleContext.getParam();
        if(param!=null){
            Object copyParam = BeanUtils.instantiateClass(param.getClass());
            BeanUtils.copyProperties(ruleContext.getParam(), copyParam);
            subRuleContext.setParam(ruleContext.getParam());
        }
        Map<String, Object> varMap = ruleContext.getVarMap();
        if(varMap!=null){
            subRuleContext.setVarMap(new HashMap<>(varMap));
        }
        return subRuleContext;
    }

    public void setParamByPath(String path, Object targetData) {
        if(super.getRule().getParamDataType().isEntity()){
            if(StringUtils.hasText(path)){
                Map<String, Object> param = (Map<String, Object>) super.getParam();
                setObjectByPath(param, path, targetData);
                super.setParam(param);
            }else {
                super.setParam(targetData);
            }
        }else {
            super.setParam(targetData);
        }
    }

    public void setVarByPath(String path, Object value) {
        setObjectByPath(super.getVarMap(), path, value);
    }

    protected void setObjectByPath(Map<String, Object> dataMap, String keyPath, Object value) {
        if(dataMap==null){
            dataMap = new HashMap<>();
        }
        String[] pathKeys = StringHelper.split(keyPath, "\\.");
        int keyCount = pathKeys.length;
        Map<String, Object> keyMap = dataMap;
        for(int i=0; i<keyCount; i++){
            if(i+1==keyCount){
                keyMap.put(pathKeys[i], value);
            }
            Map<String, Object> fieldDataMap = (Map<String, Object>) dataMap.get(pathKeys[i]);
            if(fieldDataMap==null){
                fieldDataMap = new HashMap<>();
                keyMap.put(pathKeys[i], fieldDataMap);
            }
            keyMap = fieldDataMap;
        }
    }
}
