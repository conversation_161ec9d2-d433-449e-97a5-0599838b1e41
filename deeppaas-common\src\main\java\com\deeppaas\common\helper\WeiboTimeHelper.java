package com.deeppaas.common.helper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.Date;

/**
 * 微博发布时间处理器
 *
 * <AUTHOR>
 * @date 2019-04-23
 */
public class WeiboTimeHelper {
    private static final Logger logger = LoggerFactory.getLogger(WeiboTimeHelper.class);
    private final static String JUST_NOW = "刚刚";
    private final static String SECONDS_BEFORE = "秒前";
    private final static String MINUTE_BEFORE = "分钟前";
    private final static String HOUR_BEFORE = "小时前";
    private final static String TODAY = "今天";
    private final static String YESTERDAY = "昨天";
    private final static String DATE_MONTH = "月";
    private final static String DATE_DAY = "日";
    private final static String YEAR = "-";

    private final static int YEAR_TIME_LENGTH = 16;
    private final static int YEAR_YEAR_LENGTH = 10;
    private final static int YEAR_DATE_LENGTH = 5;

    public static Long getTimeMilis(String timeStr) {
        try {
            if (JUST_NOW.equals(timeStr)) {
                return System.currentTimeMillis();
            }
            if (timeStr.endsWith(SECONDS_BEFORE)) {
                Long seconds = Long.valueOf(timeStr.replace(SECONDS_BEFORE, ""));
                return System.currentTimeMillis() - seconds * 1000;
            }
            if (timeStr.endsWith(MINUTE_BEFORE)) {
                Long minutes = Long.valueOf(timeStr.replace(MINUTE_BEFORE, ""));
                return System.currentTimeMillis() - minutes * 60 * 1000;
            }
            if (timeStr.endsWith(HOUR_BEFORE)) {
                Long minutes = Long.valueOf(timeStr.replace(HOUR_BEFORE, ""));
                return System.currentTimeMillis() - minutes * 60 * 1000;
            }
            if (timeStr.startsWith(TODAY)) {
                String tempTime = timeStr.replace(TODAY, "");
                tempTime = DateHelper.getNowDateStr() + " " + tempTime + ":00";
                return DateHelper.str2date(tempTime, DateHelper.TIME_PATTERN).getTime();
            }
            if (timeStr.startsWith(YESTERDAY)) {
                String tempTime = timeStr.replace(YESTERDAY, "").trim();
                Date yesterday = DateHelper.add(new Date(), -1, Calendar.DAY_OF_MONTH);
                tempTime = DateHelper.date2str(yesterday, DateHelper.DATE_PATTERN) + " " + tempTime + ":00";
                return DateHelper.str2date(tempTime, DateHelper.TIME_PATTERN).getTime();
            }
            if (timeStr.contains(DATE_MONTH) && timeStr.contains(DATE_DAY)) {
                String tempTime = timeStr.replace(DATE_MONTH, "-").replace(DATE_DAY, "");
                tempTime = DateHelper.getYearInt() + "-" + tempTime + ":00";
                return DateHelper.str2date(tempTime, DateHelper.TIME_PATTERN).getTime();
            }
            if (timeStr.contains(YEAR)) {
                if (timeStr.length() == YEAR_TIME_LENGTH) {
                    // 2018-12-24 17:21
                    String tempTime = timeStr + ":00";
                    return DateHelper.str2date(tempTime, DateHelper.TIME_PATTERN).getTime();
                }
                if (timeStr.length() == YEAR_YEAR_LENGTH) {
                    // 2018-12-24
                    String tempTime = timeStr + " 00:00:00";
                    return DateHelper.str2date(tempTime, DateHelper.TIME_PATTERN).getTime();
                }
                if (timeStr.length() == YEAR_DATE_LENGTH) {
                    // 02-23
                    String tempTime = DateHelper.getYearInt() + "-" + timeStr + " 00:00:00";
                    return DateHelper.str2date(tempTime, DateHelper.TIME_PATTERN).getTime();
                }
            }
        } catch (Exception e) {
            logger.error("微博日期转换异常|{}|{}", timeStr, e.getMessage());
        }
        return System.currentTimeMillis();
    }


//    public static void main(String[] args) {
//        List<String> weiboTimeList = Lists.newArrayList();
//        weiboTimeList.add("35秒前");
//        weiboTimeList.add("刚刚");
//        weiboTimeList.add("1分钟前");
//        weiboTimeList.add("50分钟前");
//        weiboTimeList.add("1小时前");
//        weiboTimeList.add("今天00:21");
//        weiboTimeList.add("昨天 11:15");
//        weiboTimeList.add("昨天11:15");
//        weiboTimeList.add("4月22日 20:31");
//        weiboTimeList.add("2018-12-24 17:21");
//        weiboTimeList.add("2018-12-24");
//        weiboTimeList.add("02-23");
//
//        for (String timeStr : weiboTimeList) {
//            System.out.println(timeStr + "=" + DateHelper.timeMillis2str(getTimeMilis(timeStr), DateHelper.TIME_PATTERN));
//        }
//    }
}
