package com.deeppaas.common.web.handler;

import org.springframework.core.convert.converter.Converter;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 时间转换器
 * 供MVC的Controller传递参数时处理对时间类型属性的转换
 * <AUTHOR>
 *
 */
public class DateConverterHandler implements Converter<String,Date> {
	@Override
	public Date convert(String text) {
		if (StringUtils.hasText(text)) {
			try {
				if (text.indexOf(":") == -1 && text.length() == 10) {
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					return dateFormat.parse(text);
				} else if (text.indexOf(":") > 0 && text.length() == 19) {
					SimpleDateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					return datetimeFormat.parse(text);
				}else{
					throw new IllegalArgumentException("Could not parse date, date format is error ");
				}
			} catch (ParseException ex) {
				IllegalArgumentException iae = new IllegalArgumentException("Could not parse date: " + ex.getMessage());
				iae.initCause(ex);
				throw iae;
			}
		} else {
			return null;
		}
	}

}
