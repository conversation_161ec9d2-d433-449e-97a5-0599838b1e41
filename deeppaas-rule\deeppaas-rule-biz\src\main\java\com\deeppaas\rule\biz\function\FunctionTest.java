package com.deeppaas.rule.biz.function;


/**
 * <AUTHOR>
 * @date 2022/3/7
 */
public class FunctionTest {

    public static void main(String[] args){
        SimpleFunction sf = new SimpleFunction();
        String[] array = new String[]{"A","B","C","D"};
        boolean result = sf.arrayContains(new Object[]{array, "A"});
        System.out.println(result);
    }

    public static void test1(){
        //String
    }

}
