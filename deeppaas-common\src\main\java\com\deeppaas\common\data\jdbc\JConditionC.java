package com.deeppaas.common.data.jdbc;

import com.deeppaas.common.data.Operator;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自定义SQL
 * <AUTHOR>
 * @date 2022/8/12
 */
public class JConditionC extends JCondition {
    private String sql;
    private Map<String, Object> param;
    private int markNum = 0;

    JConditionC(Operator operator) {
        super(operator);
    }

    public JConditionC(String sql, Map<String, Object> params) {
        super(null);
        this.sql = sql;
        this.param = params;
    }

    @Override
    public Map<String, Object> getBindParam() {
        return param;
    }

    @Override
    void sortMarkNum(AtomicInteger markNum) {
        this.markNum = markNum.addAndGet(1);
    }

    public String getSql() {
        return sql;
    }
}
