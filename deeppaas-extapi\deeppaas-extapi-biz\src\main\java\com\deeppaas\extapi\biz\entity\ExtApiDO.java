package com.deeppaas.extapi.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 外部接口
 *
 * <AUTHOR>
 * @date 2022/2/25
 */
@Data
@Table("ext_api")
@EqualsAndHashCode(callSuper = true)
public class ExtApiDO extends BaseUuidDO {
    /**
     * 接口为发布的对外接时owner=SELF
     */
    public static final String OWNER_SELF = "SELF";

    /**
     * 接口名称
     */
    @Column("name")
    private String name;

    /**
     * 接口方法
     * <p>
     * GET、POST
     */
    @Column("method")
    private String method;

    /**
     * 是否是平台本身就有的API
     */
    @Column("self_api")
    private Integer selfApi;
    /**
     * 接口地址
     */
    @Column("url")
    private String url;

    /**
     * 请求体类型@com.deeppaas.extapi.biz.enums.ApiReqBodyType
     */
    @Column("req_body_type")
    private String reqBodyType;
    /**
     * 回应体类型@com.deeppaas.extapi.biz.enums.ApiResBodyType
     */
    @Column("res_body_type")
    private String resBodyType;

    /**
     * 回应体所属模型
     */
    @Column("res_body_entity")
    private String resBodyEntity;

    /**
     * 是否可用
     * 1=可用、0=不可用
     */
    @Column("usable")
    private Integer usable;

    /**
     * 描述
     * <p>
     * 获取数据详情
     */
    @Column("remark")
    private String remark;

    /**
     * 如果是对外提供的，是第三方提供的接口所属那个用户ID
     */
    @Column("owner")
    private String owner;

    /**
     * self类型规则ID
     */
    @Column("rule_id")
    private String ruleId;

    @Transient
    @JsonIgnore
    public boolean isSelfApi() {
        return OWNER_SELF.equals(this.owner);
    }
}
