package com.deeppaas.file.api.client;

import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.api.model.FileEntity;
import com.deeppaas.file.api.model.UploadSign;

import java.io.File;
import java.io.InputStream;

/**
 * 文件处理API
 * <AUTHOR>
 */
public interface FileClient {
    /**
     * 获取文件组
     * @param fileGroup
     * @return
     */
    FileGroup getFileGroup(String fileGroup);
    /**
     * 获取上传签名
     * @param: fileGroup 文件组
     * @param: fileDir  文件夹
     * <AUTHOR>
     **/
    UploadSign getUploadSign(String fileGroup, String fileDir);

    /**
     * 获取上传签名
     * @param: fileGroup 文件组
     * @param: fileDir  文件夹
     * <AUTHOR>
     **/
    UploadSign getUploadSign(String fileGroup, String fileDir, int expireSeconds);

    /**
     * 上传文件
     * @param: fileGroup 文件组
     * @param: fileKey  文件相对全路径
     * <AUTHOR>
     **/
    void uploadFile(String fileGroup, String fileKey, File file);

    /**
     * 上传文件
     * @param: fileGroup 文件组
     * @param: fileKey  文件相对全路径
     * <AUTHOR>
     **/
    void uploadFile(String fileGroup, String fileKey, InputStream input);

    /**
     * 获取文件实体
     * @param: fileGroup 文件组
     * @param: fileKey  文件相对全路径
     * <AUTHOR>
     **/
    FileEntity getFile(String fileGroup, String fileKey);

    /**
     * 获取文件临时授权访问地址
     * @param: fileGroup  文件组名
     * @param: fileKey  文件相对全路径
     * @param: fileAlias  文件重命名
     * @param: process 附加处理，样式等
     * @return: java.lang.String
     * <AUTHOR>
     **/
    String getTempUrl(String fileGroup, String fileKey, String fileAlias, String process);

    /**
     * 获取文件临时授权访问地址
     * @param: fileGroup  文件组名
     * @param: fileKey  文件相对全路径
     * @param: fileAlias  文件重命名
     * @param: process 附加处理，样式等
     * @param: expireSeconds 过去秒数
     * @return: java.lang.String
     * <AUTHOR>
     **/
    String getTempUrl(String fileGroup, String fileKey, String fileAlias, String process, int expireSeconds);

    /**
     * 删除文件
     * @param fileGroup
     * @param fileKey
     */
    void deleteFile(String fileGroup, String fileKey);
}
