package com.deeppaas.datastore.common;

import com.deeppaas.common.model.PageData;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
public interface DataStoreRepository {
    boolean isExist(DataStoreData data);
    /**
     * 保存数据
     * @param data
     * @return
     */
    DataStoreData save(DataStoreData data);
    int delete(DataStoreData data);
    /**
     * 根据主键值获取数据
     * @param id
     * @return
     */
    DataStoreData getById(Object id);

    DataStoreData getById(String idStr);

    /**
     * 根据主键值删除数据
     * @param id
     * @return
     */
    int deleteById(Object id);

    List<DataStoreData> findForList(DataStoreCondition condition);

    List<DataStoreData> findForList(DataStoreCondition condition, Sort sort);

    List<DataStoreData> findForList(DataStoreCondition condition, Pageable pageable);

    PageData<DataStoreData> findForPage(DataStoreCondition searchCondition, Pageable pageable);



}
