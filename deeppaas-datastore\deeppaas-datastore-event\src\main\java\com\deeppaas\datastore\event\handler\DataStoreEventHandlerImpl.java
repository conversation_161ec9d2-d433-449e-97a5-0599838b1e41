package com.deeppaas.datastore.event.handler;

import com.deeppaas.datastore.common.event.DataStoreEvent;
import com.deeppaas.datastore.common.event.DataStoreEventHandler;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.api.client.EventRuleClient;
import com.deeppaas.rule.api.client.RuleClient;
import com.deeppaas.rule.api.enums.RuleOwnerType;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 事件处理器
 *
 * <AUTHOR>
 * @date 2022/3/4
 */
@Component
public class DataStoreEventHandlerImpl implements DataStoreEventHandler {
    private EventRuleClient eventClient;
    private RuleClient ruleClient;

    public DataStoreEventHandlerImpl(EventRuleClient eventClient, RuleClient ruleClient) {
        this.eventClient = eventClient;
        this.ruleClient = ruleClient;
    }

    @Override
    public void execute(DataStoreEvent event, DataStoreData data) {
        List<String> eventRules = eventClient.findEventRules(RuleOwnerType.DATASTORE_ENTITY, data.getEntity(), event.name());
        for (String ruleId : eventRules) {
            ruleClient.executeById(ruleId, data);
        }
    }

}
