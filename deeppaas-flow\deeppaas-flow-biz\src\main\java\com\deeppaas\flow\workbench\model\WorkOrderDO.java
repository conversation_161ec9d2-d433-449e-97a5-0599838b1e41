package com.deeppaas.flow.workbench.model;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
@Data
public class WorkOrderDO implements BaseModel {
    private Long id;

    /**
     * 流程定义
     */
    private String defineCode;

    /**
     * 流程版本
     */
    private Integer version;

    /**
     * 开始环节
     */
    private String startNode;

    /**
     * 工单ID
     */
    private String orderId;

    /**
     * 当前优先级
     */
    private Integer priority;

    /**
     * 流程实例状态@FlowInstanceState
     */
    private Integer state;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 代办人
     */
    private List<String> todoUsers;

    /**
     * 已办人
     */
    private List<String> doneUsers;
}
