package com.deeppaas.flow.biz.element;

import java.util.ArrayList;
import java.util.List;

import com.deeppaas.flow.biz.*;
import com.deeppaas.flow.biz.enums.FlowEvent;
import com.deeppaas.flow.biz.enums.FlowInstanceState;
import com.deeppaas.flow.biz.enums.NodeType;
import com.deeppaas.flow.biz.model.AssignInfo;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowTaskDO;

/**
 * 开始节点
 * <AUTHOR>
 *
 */
public class StartNode extends FlowPageNode {

	public StartNode(String code, String name, NodeType type, String remark, String page, List<FlowEventRule> eventRules) {
		super(code, name, type, remark, page, eventRules);
	}

	//进入
	@Override
	public void enter(ExecuteItem executeItem){
		// 开始环节没有源环节
		executeItem.setNode(this);
		executeItem.getInstance().setState(FlowInstanceState.READY.getIntValue());
		executeItem.setToken(executeItem.getEngine().token().passTokenAndRecord(executeItem.getToken(), this, executeItem.getOperator()));
		//如果是驳回或跳转，停在开始环节
		if(FlowAction.back.equals(executeItem.getAction())
			|| FlowAction.takeBack.equals(executeItem.getAction())){
			executeItem.getInstance().setState(FlowInstanceState.BACK.getIntValue());
			executeItem.getEngine().instance().close(executeItem.getInstance(), FlowInstanceState.BACK);
			List<FlowTaskDO> activeTasks = executeItem.getEngine().task().findUnclosedTaskByInstanceId(executeItem.getInstance().getId());
			for(FlowTaskDO task : activeTasks){
				executeItem.getEngine().task().close(task, executeItem.getOperator(), executeItem.getAction(), "");
				executeItem.setTask(task);
				//executeItem.getEngine().event().fire(FlowEvent.NODE_TASK_CLOSED, executeItem);
			}
			executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_ENTER, executeItem);
		}else{
			executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_ENTER, executeItem);
			this.execute(executeItem);
		}
	}
	//模拟进入
	@Override
	public List<AssignInfo> mockEnter(MockExecuteItem execution){
		FlowInstanceDO instance = execution.getInstance();
		execution.setInstance(instance);
		if(FlowAction.back.equals(execution.getAction())){	//如果是驳回或跳转，停在开始环节
			return List.of(AssignInfo.instanceAutoNode(this));
		}else{
			execution.setNode(this);
			return this.mockExecute(execution);
		}
	}

	@Override
	public List<String> check() {
		List<String> errors = new ArrayList<>();
		if(!inLines.isEmpty()){
			errors.add("环节["+getCode()+"]不应有进入连接线");
		}
		if(outLines.isEmpty()){
			errors.add("环节["+getCode()+"]没有流转连接线");
		}
		return errors;
	}

}
