package com.deeppaas.file.desk.model;

import com.deeppaas.file.FileConstant;
import com.deeppaas.file.api.model.UploadSign;

/**
 * 自建分布式文件上传签名
 * <AUTHOR>
 * @date 2020/7/15
 */
public class DeskUploadSign extends UploadSign {
    /** 上传文件组 */
    private String fileGroup;
    /** 上传文件夹 */
    private String dir;
    /** 上传接口地址 */
    private String host;
    /** 过期时间戳 */
    private String expireTime;
    /** 上传签名 */
    private String sign;

    public DeskUploadSign() {
        super(FileConstant.MODE_DESK);
    }

    public String getFileGroup() {
        return fileGroup;
    }

    public void setFileGroup(String fileGroup) {
        this.fileGroup = fileGroup;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
