package com.deeppaas.common.convert;

import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2018-12-27
 */
public  class BaseConvertV2 {


    public  <T>T to(Object  source,Class<T> result){
            if(source==null)
                return null;
        try {
            Object re=result.newInstance();
            BeanUtils.copyProperties(source, re);
            return (T) re;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
    public  <T>T tos(List<?> source,Class<T> result){
        if(source==null)
            return null;
        return (T) source.stream().map(obj->{return to(obj,result);}).collect(Collectors.toList());
    }





}
