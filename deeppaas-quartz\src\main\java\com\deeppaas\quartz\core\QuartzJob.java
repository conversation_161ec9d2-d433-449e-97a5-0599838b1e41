package com.deeppaas.quartz.core;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.quartz.entity.QuartzJobDO;
import com.deeppaas.quartz.service.QuartzJobService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/24
 */
@DisallowConcurrentExecution
public abstract class QuartzJob implements Job {
    private static final Logger log = LoggerFactory.getLogger(QuartzJob.class);
    private static final String RESULT_SUCCESS = "success";
    private static final String RESULT_FAIL = "fail";

    @Autowired
    private QuartzJobService jobService;

    @Override
    public void execute(JobExecutionContext context) {
        String fireResult = RESULT_SUCCESS;
        String appCode = context.getJobDetail().getKey().getGroup();
        String jobId = context.getJobDetail().getKey().getName();

        // 补偿：检测状态
        QuartzJobDO jobDO = jobService.findByJobId(jobId);
        if (jobDO == null || !BoolHelper.intToBool(jobDO.getUsed())) {
            boolean success = jobService.deleteScheduler(jobId, appCode);
            if (success) {
                log.info("执行检查-实例任务[" + appCode + "-" + jobId + "]删除成功");
            } else {
                log.warn("执行检查-实例任务[" + appCode + "-" + jobId + "]删除失败！");
            }
            return;
        }

        try {
            doExecute(context);
        } catch (Exception ex) {
            log.error("====================quartz-error-begin====================");
            log.error(ex.toString());
            StackTraceElement[] element = ex.getStackTrace();
            for (int i = 0; i < element.length; i++) {
                log.error("位置:" + element[i]);
            }
            log.error("====================quartz-error-end====================");
            // 更新执行状态为失败
            fireResult = RESULT_FAIL;
        } finally {
            LocalDateTime fireTime = DateHelper.toLocalDateTime(context.getFireTime());
            LocalDateTime nextTime = DateHelper.toLocalDateTime(context.getNextFireTime());
            jobService.updateFireInfo(appCode, jobId, fireTime, nextTime, fireResult);
        }
    }

    public abstract void doExecute(JobExecutionContext context);

    public abstract void rerun(Date targetTime);
}
