# 图像分件方式检测逻辑测试计划

## 测试目标
验证`ProjectTaskUploadImpl`类中新增的图像分件方式检测逻辑是否能正确识别不同的分件模式。

## 测试范围

### 1. 基础检测功能
- ✅ 单件处理模式检测 (`SINGLE_PIECE`)
- ✅ 起止页号模式检测 (`PAGE_RANGE`)  
- ✅ 首页号+尾页号模式检测 (`START_END_PAGE`)
- ✅ 首页号+页数模式检测 (`START_PAGE_COUNT`)

### 2. 优先级测试
- ✅ 起止页号 > 首页号+尾页号
- ✅ 首页号+尾页号 > 首页号+页数
- ✅ 任何分件模式 > 单件处理

### 3. 边界条件测试
- ✅ 空字段映射处理
- ✅ null字段映射处理
- ✅ 字段名模糊匹配（如"卷内首页号"包含"首页号"）
- ✅ 不完整字段组合（只有首页号没有尾页号/页数）

## 测试数据准备

### 测试用例1：单件处理模式
```json
{
  "ruleMapping": "[]"
}
```
**预期结果**: `SINGLE_PIECE`

### 测试用例2：起止页号模式
```json
{
  "ruleMapping": "[{\"key\":\"field1\",\"label\":\"起止页号\"}]"
}
```
**预期结果**: `PAGE_RANGE`

### 测试用例3：首页号+尾页号模式
```json
{
  "ruleMapping": "[{\"key\":\"field1\",\"label\":\"首页号\"},{\"key\":\"field2\",\"label\":\"尾页号\"}]"
}
```
**预期结果**: `START_END_PAGE`

### 测试用例4：首页号+页数模式
```json
{
  "ruleMapping": "[{\"key\":\"field1\",\"label\":\"首页号\"},{\"key\":\"field2\",\"label\":\"页数\"}]"
}
```
**预期结果**: `START_PAGE_COUNT`

### 测试用例5：优先级测试 - 起止页号优先
```json
{
  "ruleMapping": "[{\"key\":\"field1\",\"label\":\"起止页号\"},{\"key\":\"field2\",\"label\":\"首页号\"},{\"key\":\"field3\",\"label\":\"尾页号\"}]"
}
```
**预期结果**: `PAGE_RANGE`

### 测试用例6：优先级测试 - 首页号+尾页号优先
```json
{
  "ruleMapping": "[{\"key\":\"field1\",\"label\":\"首页号\"},{\"key\":\"field2\",\"label\":\"尾页号\"},{\"key\":\"field3\",\"label\":\"页数\"}]"
}
```
**预期结果**: `START_END_PAGE`

### 测试用例7：模糊匹配测试
```json
{
  "ruleMapping": "[{\"key\":\"field1\",\"label\":\"卷内首页号\"},{\"key\":\"field2\",\"label\":\"文件页数\"}]"
}
```
**预期结果**: `START_PAGE_COUNT`

### 测试用例8：不完整字段组合
```json
{
  "ruleMapping": "[{\"key\":\"field1\",\"label\":\"首页号\"}]"
}
```
**预期结果**: `SINGLE_PIECE`

## 测试执行步骤

### 步骤1：编译项目
```bash
cd g:\Java-projects\hegui\deeppaas2-java
mvn clean compile
```

### 步骤2：运行单元测试
```bash
# 运行测试类
java -cp target/classes test.backend.ImageGroupingTypeDetectionTest
```

### 步骤3：集成测试
1. 创建测试任务配置
2. 设置不同的字段映射
3. 调用`localImageAndDataKey`方法
4. 观察控制台输出的检测结果

### 步骤4：验证日志输出
检查控制台输出是否包含：
- "案卷级处理 - 检测到的分件方式: [模式名称]"
- 字段映射解析日志
- 检测逻辑执行日志

## 预期结果验证

### 成功标准
1. ✅ 所有测试用例都返回预期的分件方式
2. ✅ 优先级逻辑正确执行
3. ✅ 模糊匹配功能正常工作
4. ✅ 边界条件处理正确
5. ✅ 日志输出清晰可读

### 失败处理
如果测试失败，检查：
1. 字段映射解析是否正确
2. 字符串匹配逻辑是否准确
3. 优先级判断顺序是否正确
4. 空值处理是否完善

## 后续测试计划

### Phase 2: 页号范围解析器测试
- 测试不同页号格式的解析
- 验证页号范围计算逻辑
- 测试异常页号格式处理

### Phase 3: 图像分组处理测试
- 测试实际图像文件分组
- 验证Excel数据匹配
- 测试多件处理逻辑

## 测试环境要求
- Java 17+
- Maven 3.6+
- 测试数据库连接
- 模拟Excel数据文件

## 注意事项
1. 当前实现中多件分组处理暂时使用单件处理逻辑
2. 需要确保字段映射配置格式正确
3. 测试时注意观察控制台日志输出
4. 建议在开发环境中进行测试，避免影响生产数据
