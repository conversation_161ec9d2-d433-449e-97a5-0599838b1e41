package com.deeppaas.datastore.common;

import com.deeppaas.datastore.common.emnus.DataStoreOperator;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/1/19
 */
public class DataStoreConditionL extends DataStoreCondition{
    private List<DataStoreCondition> conditions;

    public DataStoreConditionL(DataStoreCondition[] conditions, DataStoreOperator operator){
        super(operator);
        List<DataStoreCondition> conditionList = new ArrayList<>();
        for(DataStoreCondition jCondition : conditions){
            if(null != jCondition){
                conditionList.add(jCondition) ;
            }
        }
        this.conditions = conditionList;
    }

    public List<DataStoreCondition> getConditions() {
        return conditions;
    }

    public void add(DataStoreCondition jCondition){
        conditions.add(jCondition);
    }
    @Override
    public Map<String, Object> getBindParam() {
        Map<String, Object> paramMap = new HashMap<>();
        for(DataStoreCondition condition : conditions){
            paramMap.putAll(condition.getBindParam());
        }
        return paramMap;
    }

    @Override
    void sortMarkNum(AtomicInteger markNum){
        for(DataStoreCondition condition : conditions){
            condition.sortMarkNum(markNum);
        }
    }
}
