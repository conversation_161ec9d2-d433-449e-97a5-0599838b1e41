package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.datastore.biz.entity.DataStoreEntityDO;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.biz.service.DataStoreDataExcelReader;
import com.deeppaas.datastore.biz.service.DataStoreDataJsonReader;
import com.deeppaas.datastore.biz.service.DataStoreEntityService;
import com.deeppaas.datastore.biz.web.view.DataStoreDataExcelView;
import com.deeppaas.datastore.biz.web.view.DataStoreDataJsonView;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.datastore.common.web.req.DataStoreDataSearch;
import com.deeppaas.datastore.biz.service.DataStoreDataService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 数据模型数据接口
 * <AUTHOR>
 * @date 2022/2/15
 */
@RestController
@RequestMapping("/api/datastore/data")
public class DataStoreDataApi {
    private final DataStoreDataService dataStoreDataService;
    private final DataStoreEntityService entityService;

    public DataStoreDataApi(DataStoreDataService dataStoreDataService, DataStoreEntityService entityService) {
        this.dataStoreDataService = dataStoreDataService;
        this.entityService = entityService;
    }

    /**
     * 获取单条数据
     * @param entity
     * @param id
     * @return
     */
    @GetMapping("/get")
    public RestModel<DataStoreData> get(@RequestParam String entity, @RequestParam String id){
        DataStoreData data = dataStoreDataService.getData(entity, id);
        return RestModel.data(data);
    }

    /**
     * 获取数据模型合
     * @param search
     * @return
     */
    @PostMapping("/list")
    public RestModel<List<DataStoreData>> list(@RequestBody DataStoreDataSearch search){
        List<DataStoreData> list = dataStoreDataService.search(search);
        return RestModel.data(list);
    }

    /**
     * 分页获取数据
     * @param search
     * @return
     */
    @PostMapping("/page")
    public RestModel<PageData<DataStoreData>> page(@RequestBody DataStoreDataSearch search){
        PageData<DataStoreData> page = dataStoreDataService.pageSearch(search);
        return RestModel.data(page);
    }

    /**
     * 保存数据
     * @param data
     * @return
     */
    @PostMapping("/save")
    public RestModel<DataStoreData> save(@RequestBody DataStoreData data){
        data = dataStoreDataService.save(data);
        return RestModel.data(data);
    }

    @PostMapping("/delete")
    public RestModel<Boolean> delete(@RequestParam String entity, @RequestParam String id){
        dataStoreDataService.delete(entity, id);
        return RestModel.data(true);
    }

    /**
     * 导出
     * @param search
     * @return
     */
    @PostMapping("/export")
    public ModelAndView exportData(@RequestBody DataStoreDataSearch search){
        DataStoreEntityDO entityDO = entityService.findByCode(search.getEntity());
        List<DataStoreEntityFieldDTO> fieldDTOS = entityService.findFieldDTOByEntityCode(search.getEntity());
        List<DataStoreData> list = dataStoreDataService.search(search);
        Map<String, Object> model = new HashMap<>();
        model.put(DataStoreDataExcelView.MODEL_KEY_ENTITY, entityDO.getName());
        model.put(DataStoreDataExcelView.MODEL_KEY_FIELDS, fieldDTOS);
        model.put(DataStoreDataExcelView.MODEL_KEY_DATA, list);
        return new ModelAndView(new DataStoreDataExcelView(), model);
    }

    /**
     * 导入
     * @param entityCode
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/import/{entityCode}")
    public RestModel<String> upload(@PathVariable String entityCode,
                                            @RequestParam("file") MultipartFile file){
        try{
            InputStream inputStream = file.getInputStream();
            List<DataStoreEntityFieldDTO> fieldDTOS = entityService.findFieldDTOByEntityCode(entityCode);
            DataStoreDataExcelReader excelReader = DataStoreDataExcelReader.of(inputStream, entityCode, fieldDTOS);
            List<DataStoreData> importDataList = excelReader.readDataList();
            inputStream.close();
            List<String> readErrors = excelReader.getReadErrors();
            if (readErrors.isEmpty()){
                dataStoreDataService.importData(entityCode, importDataList);
            }else {
                return RestModel.error(RunException.optReject(StringHelper.join(readErrors, "\n")));
            }
        }catch (Exception e){
            e.printStackTrace();
            throw RunException.optReject("导入失败，异常："+e.getMessage());
        }
        return RestModel.data("success");
    }

    /**
     * 导出json
     * @param search
     * @return
     */
    @PostMapping("/exportJson")
    public ModelAndView exportDataJson(@RequestBody DataStoreDataSearch search){
        DataStoreEntityDO entityDO = entityService.findByCode(search.getEntity());
        List<DataStoreEntityFieldDTO> fieldDTOS = entityService.findFieldDTOByEntityCode(search.getEntity());
        List<DataStoreData> list = dataStoreDataService.search(search);
        Map<String, Object> model = new HashMap<>();
        model.put(DataStoreDataJsonView.MODEL_KEY_ENTITY, entityDO.getName());
        model.put(DataStoreDataJsonView.MODEL_KEY_FIELDS, fieldDTOS);
        model.put(DataStoreDataJsonView.MODEL_KEY_DATA, list);
        return new ModelAndView(new DataStoreDataJsonView(), model);
    }

    /**
     * 导入Json
     * @param entityCode
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/importJson/{entityCode}")
    public RestModel<String> uploadJson(@PathVariable String entityCode,
                                    @RequestParam("file") MultipartFile file){
        try{
            InputStream inputStream = file.getInputStream();
            List<DataStoreEntityFieldDTO> fieldDTOS = entityService.findFieldDTOByEntityCode(entityCode);
            DataStoreDataJsonReader jsonReader = DataStoreDataJsonReader.of(inputStream, entityCode, fieldDTOS);
            List<DataStoreData> importDataList = jsonReader.readDataList();
            inputStream.close();
            List<String> readErrors = jsonReader.getReadErrors();
            if (readErrors.isEmpty()){
                dataStoreDataService.importData(entityCode, importDataList);
            }else {
                return RestModel.error(RunException.optReject(StringHelper.join(readErrors, "\n")));
            }
        }catch (Exception e){
            e.printStackTrace();
            throw RunException.optReject("导入失败，异常："+e.getMessage());
        }
        return RestModel.data("success");
    }
}
