package com.deeppaas.file.biz.service;

import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.file.FileConstant;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.biz.convert.FileGroupConvert;
import com.deeppaas.file.biz.dao.FileGroupDao;
import com.deeppaas.file.biz.entity.FileGroupDO;
import com.deeppaas.file.oss.model.OSSFileGroup;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
@Service
public class FileGroupService {
    private final FileGroupDao fileGroupDao;
    /**
     * 缓存
     */
    private static Map<String, FileGroup> groupCache = new ConcurrentHashMap<>();

    public FileGroupService(FileGroupDao fileGroupDao) {
        this.fileGroupDao = fileGroupDao;
    }

    public FileGroupDO getGroupDO(String id) {
        return fileGroupDao.getById(id);
    }

    public FileGroup getGroup(String id) {
        FileGroup fileGroup = groupCache.get(id);
        if(fileGroup==null){
            FileGroupDO fileGroupDO = fileGroupDao.getById(id);
            if(fileGroupDO!=null){
                if(FileConstant.MODE_OSS.equalsIgnoreCase(fileGroupDO.getMode())){
                    fileGroup = FileGroupConvert.buildOssFileGroup(fileGroupDO);
                }else {
                    fileGroup = FileGroupConvert.buildFileGroup(fileGroupDO);
                }
            }
            groupCache.put(id, fileGroup);
        }
        return fileGroup;
    }
    public List<FileGroupDO> findAll() {
        return fileGroupDao.findAll();
    }

    @Transactional
    public void saveGroup(FileGroup fileGroup) {
        FileGroupDO fileGroupDO = fileGroupDao.getById(fileGroup.getName());
        if(fileGroupDO==null){
            fileGroupDO = new FileGroupDO();
            fileGroupDO.setId(fileGroup.getName());
        }
        fileGroupDO.setMode(fileGroup.getMode());
        fileGroupDO.setType(fileGroup.getType());
        fileGroupDO.setBucket(fileGroup.getBucket());
        fileGroupDO.setDomain(fileGroup.getDomain());
        fileGroupDao.save(fileGroupDO, fileGroup.getName());
        groupCache.remove(fileGroup.getName());
    }
    @Transactional
    public void deleteById(String id) {
        fileGroupDao.deleteById(id);
        groupCache.remove(id);
    }

    public Map<String, Object> getGroupProperties(String id) {
        FileGroupDO fileGroupDO = fileGroupDao.getById(id);
        String propertiesSecret = fileGroupDO.getProperties();
        String propertiesJson = FileAes.decrypt(propertiesSecret);
        Map<String, Object> propertiesMap = JsonHelper.json2map(propertiesJson);
        return propertiesMap;
    }

    @Transactional
    public void saveProperties(String id, Map<String, String> properties) {
        FileGroupDO fileGroupDO = fileGroupDao.getById(id);
        String propertiesJson = JsonHelper.toJson(properties);
        String propertiesSecret = FileAes.encrypt(propertiesJson);
        fileGroupDO.setProperties(propertiesSecret);
        fileGroupDao.save(fileGroupDO);
        groupCache.remove(id);
    }
}
