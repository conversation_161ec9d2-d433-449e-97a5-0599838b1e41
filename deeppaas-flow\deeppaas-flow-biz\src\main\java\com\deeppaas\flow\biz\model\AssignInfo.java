package com.deeppaas.flow.biz.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.deeppaas.flow.biz.element.FlowNode;
import lombok.Data;

/**
 * @ClassName: AssignInfo
 * @Description: 指派信息
 * <AUTHOR>
 * @date 2017年8月21日
 */
@Data
public class AssignInfo implements Serializable{
	private static final long serialVersionUID = 4423649552564036498L;

	/** 人员选择范围-单个人 */
	public static final String ASSIGN_SCOPE_ONE = "ONE";
	/** 人员选择范围-多个人 */
	public static final String ASSIGN_SCOPE_MANY = "MANY";
	/** 人员选择范围-所有人 */
	public static final String ASSIGN_SCOPE_ALL = "ALL";
	/** 是否已经指派过了 */
	private boolean assigned = false;
	/** 节点编号 */
	private String nodeCode;
	/** 节点名称 */
	private String nodeName;
	/** 节点类型 */
	private String nodeType;
	/** 限定时间 */
	private Long limitedTime;
	/** 人员选择范围(ONE选一个、MANY选多个、ALL默认全部选中不可修改) */
	private String assignScope;
	/** 原指派人集合 */
	private List<String> oldActors = new ArrayList<>();
	/** 环节备选人集合 */
	private Collection<String> optionActors;
	/**
	 * 创建一个非任务节点的指派，认为指派过了
	 * @param node
	 * @return
	 */
	public static AssignInfo instanceAutoNode(final FlowNode node) {
		AssignInfo assignInfo = new AssignInfo();
		assignInfo.setNodeCode(node.getCode());
		assignInfo.setNodeName(node.getName());
		assignInfo.setNodeType(node.getType().name());
		assignInfo.setAssigned(true);
		return assignInfo;
	}

}
