package com.deeppaas.datastore.biz.service;

import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.data.jdbc.JConditionL;
import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.CodeHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.datastore.biz.convert.DataStoreEntityConvert;
import com.deeppaas.datastore.biz.dao.*;
import com.deeppaas.datastore.biz.entity.*;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.biz.web.vo.DataStoreEntityVO;
import com.deeppaas.datastore.common.DataStoreRepositoryCache;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import com.deeppaas.tenant.api.client.AppClient;
import com.deeppaas.tenant.api.client.model.AppCO;
import com.deeppaas.view.api.client.ComponentClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@Service
public class DataStoreEntityService {
    private final DataStoreEntityDao dataStoreEntityDao;
    private final DataStoreEntityFieldDao dataStoreEntityFieldDao;
    private final DataStoreEntityFieldOptionDao dataStoreEntityFieldOptionDao;
    private final DataStoreTableDao dataStoreTableDao;
    private final DataStoreTableFieldDao dataStoreTableFieldDao;
    private final DataStoreTableFieldOptionDao dataStoreTableFieldOptionDao;
    private final DataStoreRepositoryCache dataStoreRepositoryCache;
    private final AppClient appClient;
    private final ComponentClient componentClient;

    public DataStoreEntityService(DataStoreEntityDao dataStoreEntityDao, DataStoreEntityFieldDao dataStoreEntityFieldDao, DataStoreEntityFieldOptionDao dataStoreEntityFieldOptionDao, DataStoreTableDao dataStoreTableDao, DataStoreTableFieldDao dataStoreTableFieldDao, DataStoreTableFieldOptionDao dataStoreTableFieldOptionDao, DataStoreRepositoryCache dataStoreRepositoryCache, AppClient appClient,@Lazy ComponentClient componentClient) {
        this.dataStoreEntityDao = dataStoreEntityDao;
        this.dataStoreEntityFieldDao = dataStoreEntityFieldDao;
        this.dataStoreEntityFieldOptionDao = dataStoreEntityFieldOptionDao;
        this.dataStoreTableDao = dataStoreTableDao;
        this.dataStoreTableFieldDao = dataStoreTableFieldDao;
        this.dataStoreTableFieldOptionDao = dataStoreTableFieldOptionDao;
        this.dataStoreRepositoryCache = dataStoreRepositoryCache;
        this.appClient = appClient;
        this.componentClient = componentClient;
    }

    public DataStoreEntityDO findById(String id) {
        return dataStoreEntityDao.getById(id);
    }

    public DataStoreEntityDO findByCode(String code) {
        return dataStoreEntityDao.findByCode(code);
    }

    public List<DataStoreEntityDO> findByTableId(String tableId) {
        return dataStoreEntityDao.findByTableIdOrderByIdAsc(tableId);
    }

    public List<DataStoreEntityDO> findByCodes(Collection<String> entityCodes) {
        if (CollectionUtils.isEmpty(entityCodes)) {
            return new ArrayList<>();
        }
        return dataStoreEntityDao.findByCodeIn(entityCodes);
    }

    public List<DataStoreEntityVO> list(String sourceId) {
        List<DataStoreEntityDO> entityDOList;
        if (StringUtils.hasText(sourceId)) {
            entityDOList = dataStoreEntityDao.findBySourceIdOrderByIdAsc(sourceId);
        } else {
            entityDOList = dataStoreEntityDao.findAll(Sort.by(Sort.Direction.ASC, "createTime"));
        }
        return buildEntityVOS(entityDOList);
    }

    public List<DataStoreEntityDO> listByApp(String appCode) {
        return dataStoreEntityDao.findByAppCodeOrderByIdAsc(appCode);
    }

    public PageData<DataStoreEntityDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<DataStoreEntityDO> page = dataStoreEntityDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    public PageData<DataStoreEntityVO> pageFuzzy(PageSearch pageSearch, String codeOrName) {
        JCondition searchParam = JCondition.ofPageParam(pageSearch.getSearchParams());
        JConditionL fuzzyParam = JConditionL.or(JCondition.contains("code", codeOrName, true), JCondition.contains("name", codeOrName, true));

        JCondition condition = JCondition.and(searchParam, fuzzyParam);

        Page<DataStoreEntityDO> page = dataStoreEntityDao.queryForPage(condition, pageSearch.getPageable());
        List<DataStoreEntityVO> voList = buildEntityVOS(page.getContent());
        return PageData.init(page, voList);
    }

    @Transactional
    public DataStoreEntityDO add(DataStoreEntityDO param) {
        boolean exists = dataStoreEntityDao.existsByCode(param.getCode());
        if (exists) {
            throw ReqException.paramValidate("创建数据模型失败，数据模型已经存在");
        }
        DataStoreTableDO tableDO = dataStoreTableDao.getById(param.getTableId());
        param.setType(tableDO.getType());
        if (DataStoreEntityType.SQL_BUILD.name().equals(tableDO.getType())) {
            param.setTableName(tableDO.getBuildSql());
        } else {
            param.setTableName(tableDO.getTableName());
        }
        DataStoreEntityDO entityDO = dataStoreEntityDao.save(param);
        // 复制数据表字段
        List<DataStoreTableFieldDO> tableFieldDOList = dataStoreTableFieldDao.findByTableIdOrderByCreateTimeAsc(tableDO.getId());
        for (DataStoreTableFieldDO tableFieldDO : tableFieldDOList) {
            // 复制主表字段
            DataStoreEntityFieldDO entityFieldDO = DataStoreEntityConvert.newEntityField(entityDO.getCode(), tableFieldDO);
            entityFieldDO = dataStoreEntityFieldDao.save(entityFieldDO);
            // 获取字段备选项配置信息
            DataStoreTableFieldOptionDO tableFieldOptionDO = dataStoreTableFieldOptionDao.findByFieldId(tableFieldDO.getId());
            if (tableFieldOptionDO != null) {
                // 复制字段备选项配置信息
                DataStoreEntityFieldOptionDO entityFieldOptionDO = DataStoreEntityConvert.newEntityFieldOption(entityDO.getCode(), entityFieldDO.getId(), tableFieldOptionDO);
                dataStoreEntityFieldOptionDao.save(entityFieldOptionDO);
            }
        }
        dataStoreRepositoryCache.remove(entityDO.getCode());
        // 返回数据模型
        return entityDO;
    }

    @Transactional
    public void modify(String id, String name) {
        DataStoreEntityDO entityDO = dataStoreEntityDao.getById(id);
        entityDO.setName(name);
        dataStoreEntityDao.save(entityDO);
        dataStoreRepositoryCache.remove(entityDO.getCode());
    }

    @Transactional
    public void delete(DataStoreEntityDO entityDO) {
        String entityCode = entityDO.getCode();
        // 删除模型下组件
        componentClient.deleteByEntity(entityCode);
        // 删除模型下所有字段备选项信息
        dataStoreEntityFieldOptionDao.deleteByEntityCode(entityCode);
        // 删除模型所有字段
        dataStoreEntityFieldDao.deleteByEntityCode(entityCode);
        // 删除模型
        dataStoreEntityDao.delete(entityDO);
        // 清除缓存
        dataStoreRepositoryCache.remove(entityCode);
    }

    /**
     * 删除数据模型-来源数据表删除
     */
    @Transactional
    public void deleteByTable(String tableId) {
        // 数据模型列表
        List<DataStoreEntityDO> entityDOList = dataStoreEntityDao.findByTableIdOrderByIdAsc(tableId);
        if (entityDOList.isEmpty()) {
            return;
        }
        for (DataStoreEntityDO entityDO : entityDOList) {
            delete(entityDO);
        }
    }

    /**
     * 数据模型字段-列表
     */
    public List<DataStoreEntityFieldDO> listAllField(String entityCode) {
        return dataStoreEntityFieldDao.findByEntityCodeOrderByIdAsc(entityCode);
    }

    /**
     * 数据模型字段-列表
     */
    public List<DataStoreEntityFieldDTO> listJoinField(String entityCode, String joinCode) {
        List<DataStoreEntityFieldDO> entityFieldDOS = dataStoreEntityFieldDao.findByEntityCodeAndJoinCodeOrderByIdAsc(entityCode, joinCode);
        return buildEntityFieldDTOS(entityFieldDOS, new ArrayList<>());
    }

    /**
     * 获取指定ID数据集模型字段
     *
     * @param id
     * @return
     */
    public DataStoreEntityFieldDTO findFieldById(String id) {
        DataStoreEntityFieldDO entityFieldDO = dataStoreEntityFieldDao.getById(id);
        if (entityFieldDO == null) {
            return null;
        }
        DataStoreEntityDO entityDO = dataStoreEntityDao.findByCode(entityFieldDO.getEntityCode());
        List<DataStoreTableFieldDO> tableFieldDOS = dataStoreTableFieldDao.findByTableIdOrderByCreateTimeAsc(entityDO.getTableId());
        return buildEntityFieldDTOS(List.of(entityFieldDO), tableFieldDOS).get(0);
    }

    /**
     * 数据模型字段-新增
     */
    @Transactional
    public DataStoreEntityFieldDTO addField(DataStoreEntityFieldDO param) {
        boolean existsCode = dataStoreEntityFieldDao.existsByEntityCodeAndCode(param.getEntityCode(), param.getCode());
        if (existsCode) {
            throw ReqException.paramValidate("添加数据模型字段失败，数据模型字段编号已经存在");
        }
        boolean existsName = dataStoreEntityFieldDao.existsByEntityCodeAndName(param.getEntityCode(), param.getName());
        if (existsName) {
            throw ReqException.paramValidate("添加数据模型字段失败，数据模型字段名称已经存在");
        }

        DataStoreEntityFieldDO result = dataStoreEntityFieldDao.save(param);
        dataStoreRepositoryCache.remove(param.getEntityCode());
        DataStoreEntityDO entityDO = dataStoreEntityDao.findByCode(result.getEntityCode());
        List<DataStoreTableFieldDO> tableFieldDOS = dataStoreTableFieldDao.findByTableIdOrderByCreateTimeAsc(entityDO.getTableId());
        return buildEntityFieldDTOS(List.of(result), tableFieldDOS).get(0);
    }

    /**
     * 数据模型字段-修改
     */
    @Transactional
    public DataStoreEntityFieldDTO modifyField(DataStoreEntityFieldDO param) {
        if (StringUtils.hasText(param.getId())) {
            DataStoreEntityFieldDO old = dataStoreEntityFieldDao.getById(param.getId());
            param.setOverwrote(old.getOverwrote());
            boolean noChange = Objects.equals(param.getName(), old.getName())
                    && Objects.equals(param.getWebType(), old.getWebType());
            if (!noChange) {
                param.setOverwrote(BoolHelper.INT_TRUE);
            }
            if (!Objects.equals(param.getName(), old.getName())) {
                boolean existsName = dataStoreEntityFieldDao.existsByEntityCodeAndName(param.getEntityCode(), param.getName());
                if (existsName) {
                    throw ReqException.paramValidate("添加数据模型字段失败，数据模型字段名称已经存在");
                }
            }
        }
        DataStoreEntityFieldDO result = dataStoreEntityFieldDao.save(param);
        dataStoreRepositoryCache.remove(param.getEntityCode());
        DataStoreEntityDO entityDO = dataStoreEntityDao.findByCode(result.getEntityCode());
        List<DataStoreTableFieldDO> tableFieldDOS = dataStoreTableFieldDao.findByTableIdOrderByCreateTimeAsc(entityDO.getTableId());
        return buildEntityFieldDTOS(List.of(result), tableFieldDOS).get(0);
    }

    /**
     * 数据模型字段-删除
     */
    @Transactional
    public void deleteField(String fieldId) {
        DataStoreEntityFieldDO fieldDO = dataStoreEntityFieldDao.getById(fieldId);
        if (fieldDO != null) {
            // 删除模型下所有字段备选项信息
            dataStoreEntityFieldOptionDao.deleteByField(fieldId);
            // 删除字段
            dataStoreEntityFieldDao.deleteById(fieldId);
            // 清除缓存
            dataStoreRepositoryCache.remove(fieldDO.getEntityCode());
        }
    }

    public DataStoreEntityFieldOptionDO getFieldOption(String fieldId) {
        return dataStoreEntityFieldOptionDao.findByFieldId(fieldId);
    }

    @Transactional
    public DataStoreEntityFieldOptionDO saveFieldOption(DataStoreEntityFieldOptionDO param) {
        if (StringUtils.hasText(param.getId())) {
            DataStoreEntityFieldOptionDO old = dataStoreEntityFieldOptionDao.getById(param.getId());
            param.setOverwrote(old.getOverwrote());
            boolean noChange = Objects.equals(param.getSourceId(), old.getSourceId())
                    && Objects.equals(param.getOptionEntityCode(), old.getOptionEntityCode())
                    && Objects.equals(param.getValueField(), old.getValueField())
                    && Objects.equals(param.getDisplayField(), old.getDisplayField())
                    && Objects.equals(param.getCondition(), old.getCondition());
            if (!noChange) {
                param.setOverwrote(BoolHelper.INT_TRUE);
            }
        }
        DataStoreEntityFieldOptionDO result = dataStoreEntityFieldOptionDao.save(param);
        dataStoreRepositoryCache.remove(result.getEntityCode());
        return result;
    }

    @Transactional
    public void deleteFieldOption(String fieldOptionId) {
        DataStoreEntityFieldOptionDO optionDO = dataStoreEntityFieldOptionDao.getById(fieldOptionId);
        if (optionDO != null) {
            dataStoreEntityFieldOptionDao.deleteById(fieldOptionId);
            dataStoreRepositoryCache.remove(optionDO.getEntityCode());
        }
    }

    /**
     * 从数据表字段备选继承
     * 如果数据模型字段备选已覆盖修改则不再继承修改
     *
     * @param tableFieldDO 数据表字段
     * @param newField     是否是新字段
     */
    @Transactional
    public void extendsTableField(DataStoreTableFieldDO tableFieldDO, boolean newField) {
        List<DataStoreEntityDO> entityDOList = dataStoreEntityDao.findByTableIdOrderByIdAsc(tableFieldDO.getTableId());
        if (!entityDOList.isEmpty()) {
            for (DataStoreEntityDO entityDO : entityDOList) {
                if (newField) {
                    DataStoreEntityFieldDO entityFieldDO = DataStoreEntityConvert.newEntityField(entityDO.getCode(), tableFieldDO);
                    // 检查字段名称是否已经存在
                    String fieldName = entityFieldDO.getName();
                    boolean existsName = dataStoreEntityFieldDao.existsByEntityCodeAndName(entityDO.getCode(), fieldName);
                    if (existsName) {
                        // 随机补几位数字
                        entityFieldDO.setName(fieldName + CodeHelper.createSmsCode4());
                    }
                    dataStoreEntityFieldDao.save(entityFieldDO);
                    dataStoreRepositoryCache.remove(entityFieldDO.getEntityCode());
                } else {
                    // 获取模型字段
                    DataStoreEntityFieldDO entityFieldDO = dataStoreEntityFieldDao.findByEntityCodeAndCode(entityDO.getCode(), tableFieldDO.getCode());
                    if (entityFieldDO != null && !BoolHelper.intToBool(entityFieldDO.getOverwrote())) {
                        // 检查字段名称是否已经存在
                        String fieldName = tableFieldDO.getName();
                        if (!Objects.equals(entityFieldDO.getName(), fieldName)) {
                            boolean existsName = dataStoreEntityFieldDao.existsByEntityCodeAndName(entityDO.getCode(), fieldName);
                            if (existsName) {
                                // 随机补几位数字
                                fieldName = fieldName + CodeHelper.createSmsCode4();
                            }
                        }
                        entityFieldDO.setName(fieldName);
                        entityFieldDO.setWebType(tableFieldDO.getWebType());
                        dataStoreEntityFieldDao.save(entityFieldDO);
                        dataStoreRepositoryCache.remove(entityFieldDO.getEntityCode());
                    }
                }
            }
        }
    }

    /**
     * 删除数据模型字段-来源表字段
     */
    @Transactional
    public void deleteFieldByTable(String tableId, String fieldCode) {
        List<DataStoreEntityDO> entityDOList = dataStoreEntityDao.findByTableIdOrderByIdAsc(tableId);
        if (entityDOList.isEmpty()) {
            return;
        }
        for (DataStoreEntityDO entityDO : entityDOList) {
            // 获取模型字段
            DataStoreEntityFieldDO entityFieldDO = dataStoreEntityFieldDao.findByEntityCodeAndCode(entityDO.getCode(), fieldCode);
            if (entityFieldDO == null) {
                continue;
            }
            // 删除模型下所有字段备选项信息
            dataStoreEntityFieldOptionDao.deleteByField(entityFieldDO.getId());
            // 删除模型
            dataStoreEntityFieldDao.delete(entityFieldDO);
            // 清除模型字段缓存
            dataStoreRepositoryCache.remove(entityFieldDO.getEntityCode());
        }
    }

    /**
     * 从数据表字段备选继承
     * 如果数据模型字段备选已覆盖修改则不再继承修改
     */
    @Transactional
    public void extendsTableFieldOption(DataStoreTableFieldOptionDO tableFieldOption) {
        DataStoreTableFieldDO tableFieldDO = dataStoreTableFieldDao.getById(tableFieldOption.getFieldId());
        // 获取使用表的数据模型
        List<DataStoreEntityDO> linkedEntities = dataStoreEntityDao.findByTableIdOrderByIdAsc(tableFieldDO.getTableId());
        if (!CollectionUtils.isEmpty(linkedEntities)) {
            List<String> linkedEntityCodes = linkedEntities.stream().map(DataStoreEntityDO::getCode).toList();
            // 获取被关联的模型字段列表
            List<DataStoreEntityFieldDO> entityFieldDOList = dataStoreEntityFieldDao.findByEntityCodeInAndColumnNameAndJoinCodeIsNull(linkedEntityCodes, tableFieldDO.getColumnName());
            for (DataStoreEntityFieldDO entityFieldDO : entityFieldDOList) {
                // 获取字段备选项配置
                DataStoreEntityFieldOptionDO entityFieldOption = dataStoreEntityFieldOptionDao.findByFieldId(entityFieldDO.getId());
                if (entityFieldOption == null) {
                    entityFieldOption = DataStoreEntityConvert.newEntityFieldOption(entityFieldDO.getEntityCode(), entityFieldDO.getId(), tableFieldOption);
                    dataStoreEntityFieldOptionDao.save(entityFieldOption);
                    dataStoreRepositoryCache.remove(entityFieldDO.getEntityCode());
                } else if (!BoolHelper.intToBool(entityFieldOption.getOverwrote())) { //如果没有进行过覆盖修改
                    // 修改
                    entityFieldOption.setSourceId(tableFieldOption.getSourceId());
                    entityFieldOption.setOptionEntityCode(tableFieldOption.getOptionEntityCode());
                    entityFieldOption.setValueField(tableFieldOption.getValueField());
                    entityFieldOption.setDisplayField(tableFieldOption.getDisplayField());
                    entityFieldOption.setCondition(tableFieldOption.getCondition());
                    dataStoreEntityFieldOptionDao.save(entityFieldOption);
                    dataStoreRepositoryCache.remove(entityFieldDO.getEntityCode());
                }
            }
        }

    }

    /**
     * 检查是否是已经改变的备选信息
     *
     * @param eOption 数据模型字段设置的备选
     * @param tOption 数据表字段设置的备选
     * @return
     */
    private boolean isChangedOption(DataStoreEntityFieldOptionDO eOption, DataStoreTableFieldOptionDO tOption) {
        if (eOption == null) {
            return false;
        }
        if (tOption == null) {
            return true;
        }
        if (Objects.equals(eOption.getSourceId(), tOption.getSourceId())
                && Objects.equals(eOption.getOptionEntityCode(), tOption.getOptionEntityCode())
                && Objects.equals(eOption.getValueField(), tOption.getValueField())
                && Objects.equals(eOption.getDisplayField(), tOption.getDisplayField())
                && Objects.equals(eOption.getCondition(), tOption.getCondition())) {
            return false;
        }
        return true;
    }

    public List<DataStoreEntityVO> buildEntityVOS(List<DataStoreEntityDO> entityDOList) {
        if (CollectionUtils.isEmpty(entityDOList)) {
            return Collections.emptyList();
        }
        // 所属应用编号列表
        Set<String> appCodes = entityDOList.stream().map(DataStoreEntityDO::getAppCode).collect(Collectors.toSet());
        List<AppCO> appCOList = appClient.findByCodes(appCodes);
        return DataStoreEntityConvert.entityToVOS(entityDOList, appCOList);
    }

    public List<DataStoreEntityFieldDTO> findFieldDTOByEntityCode(String entityCode) {
        DataStoreEntityDO entityDO = dataStoreEntityDao.findByCode(entityCode);
        List<DataStoreTableFieldDO> tableFieldDOS = dataStoreTableFieldDao.findByTableIdOrderByCreateTimeAsc(entityDO.getTableId());
        List<DataStoreEntityFieldDO> entityFieldDOS = dataStoreEntityFieldDao.findByEntityCodeOrderByIdAsc(entityCode);
        return buildEntityFieldDTOS(entityFieldDOS, tableFieldDOS);
    }

    private List<DataStoreEntityFieldDTO> buildEntityFieldDTOS(List<DataStoreEntityFieldDO> entityFieldDOS, List<DataStoreTableFieldDO> tableFieldDOS) {
        List<DataStoreEntityFieldDTO> entityFieldDTOS = new ArrayList<>();
        Map<String, DataStoreTableFieldDO> tableFieldDOMap = tableFieldDOS.stream().collect(Collectors.toMap(DataStoreTableFieldDO::getColumnName, Function.identity()));
        for (DataStoreEntityFieldDO entityFieldDO : entityFieldDOS) {
            DataStoreEntityFieldDTO dto = new DataStoreEntityFieldDTO();
            dto.setId(entityFieldDO.getId());
            dto.setEntityCode(entityFieldDO.getEntityCode());
            dto.setCode(entityFieldDO.getCode());
            dto.setName(entityFieldDO.getName());
            dto.setColumnName(entityFieldDO.getColumnName());
            dto.setDataType(entityFieldDO.getDataType());
            dto.setWebType(entityFieldDO.getWebType());
            dto.setReadOnly(entityFieldDO.getReadOnly());
            dto.setDiscriminator(entityFieldDO.getDiscriminator());
            dto.setDiscriminatorValue(entityFieldDO.getDiscriminatorValue());
            dto.setJoinCode(entityFieldDO.getJoinCode());
            dto.setCreateTime(entityFieldDO.getCreateTime());
            if (StringUtils.hasText(entityFieldDO.getJoinCode())) {
                //dto.setLength();
                //dto.setDecimalDigits(tableFieldDO.getDecimalDigits());
                dto.setIdKey(BoolHelper.INT_FALSE);
                dto.setUnique(BoolHelper.INT_FALSE);
                dto.setDbGenerated(BoolHelper.INT_FALSE);
            } else {
                DataStoreTableFieldDO tableFieldDO = tableFieldDOMap.get(entityFieldDO.getColumnName());
                dto.setLength(tableFieldDO.getLength());
                dto.setDecimalDigits(tableFieldDO.getDecimalDigits());
                dto.setIdKey(tableFieldDO.getIdKey());
                dto.setUnique(tableFieldDO.getUnique());
                dto.setDbGenerated(tableFieldDO.getDbGenerated());
            }
            entityFieldDTOS.add(dto);
        }

        return entityFieldDTOS;
    }

    public DataStoreTableDO getTable(String tableId) {
        return dataStoreTableDao.getById(tableId);
    }


    public boolean existFieldByEntityCodeAndCode(String entityCode, String fieldCode) {
        return dataStoreEntityFieldDao.existsByEntityCodeAndCode(entityCode, fieldCode);
    }

    @Transactional
    public void deleteByApp(String appCode) {
        // 数据模型列表
        List<DataStoreEntityDO> entityDOList = dataStoreEntityDao.findByAppCodeOrderByIdAsc(appCode);
        if (entityDOList.isEmpty()) {
            return;
        }
        for (DataStoreEntityDO entityDO : entityDOList) {
            delete(entityDO);
        }
    }
}
