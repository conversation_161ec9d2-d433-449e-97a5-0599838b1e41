package com.deeppaas.common.web.handler;

import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.common.web.context.BodyParamContext;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;

/**
 * restful body传值单参数解析
 * <AUTHOR>
 * @date 2016年7月29日
 */
public class BodyParamHandler implements HandlerMethodArgumentResolver {
	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		BodyParam bodyParam = parameter.getParameterAnnotation(BodyParam.class);
		RequestBody requestBody = parameter.getParameterAnnotation(RequestBody.class);
		return bodyParam!=null || requestBody!=null;
	}

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
    	BodyParam bodyParam = parameter.getParameterAnnotation(BodyParam.class);
		if(bodyParam!=null){
			JsonNode bodyNode = getBodyNode(webRequest);
			String paramName = bodyParam.name();
			if(StringHelper.isEmpty(paramName)){
				paramName = parameter.getParameterName();
			}
			JsonNode paramValueNode = bodyNode.get(paramName);
			if(paramValueNode==null){
				if(bodyParam.required()){
					throw new RuntimeException("未发现必需参数["+paramName+"]信息");
				}
				return null;
			}
			try {
				Object paramValue = JsonHelper.readValue(paramValueNode, parameter.getParameterType());
				if(bodyParam.required() && paramValue==null){
					throw new RuntimeException("必需参数["+paramName+"]不能为空");
				}
				return paramValue;
			}catch (Exception ex){
				throw new RuntimeException("@BodyParam转换异常,error:"+(StringHelper.isEmpty(ex.getMessage())?"unknown":ex.getMessage()));
			}
		}
		RequestBody requestBody = parameter.getParameterAnnotation(RequestBody.class);
		if (requestBody != null) {
			JsonNode bodyNode = getBodyNode(webRequest);
			try {
				Type genericParameterType = parameter.getGenericParameterType();
				Object bodyValue = JsonHelper.readValueForType(bodyNode, genericParameterType);
				if(requestBody.required() && bodyValue==null){
					throw new RuntimeException("必需参数["+parameter.getParameterName()+"]不能为空");
				}
				return bodyValue;
			}catch (Exception ex){
				throw new RuntimeException("@RequestBody转换异常,error:"+(StringHelper.isEmpty(ex.getMessage())?"unknown":ex.getMessage()));
			}
        }
        return WebArgumentResolver.UNRESOLVED;
	}

	private JsonNode getBodyNode(NativeWebRequest webRequest) {
		JsonNode jsonNode = null;
		if(BodyParamContext.getJsonParam()==null){
			HttpServletRequest httpServletRequest = (HttpServletRequest) webRequest.getNativeRequest();
			try {
				httpServletRequest.setCharacterEncoding("UTF-8");
			} catch (UnsupportedEncodingException e) {
				throw new RuntimeException(e);
			}
			try{
				jsonNode = JsonHelper.readTree(httpServletRequest.getInputStream());
			}catch(Exception e){
				throw new RuntimeException("@RequestBoyd或@Body参数必须为标准JSON");
			}
			BodyParamContext.setJsonParam(jsonNode);
		}else{
			jsonNode = BodyParamContext.getJsonParam();
		}
		return jsonNode;
	}

}
