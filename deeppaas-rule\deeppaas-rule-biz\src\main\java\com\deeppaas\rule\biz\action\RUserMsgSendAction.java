package com.deeppaas.rule.biz.action;

import com.deeppaas.msg.api.client.model.UserMsgSendParam;
import com.deeppaas.rule.biz.RuleContext;
import lombok.Data;

import java.util.*;

/**
 * 消息发送动作
 * <AUTHOR>
 * @date 2022/9/3
 */
@Data
public class RUserMsgSendAction extends RMsgSendAction{
    /**
     * 消息盒子
     */
    private String boxId;
    /**
     * 标题模版
     */
    private String title;
    /**
     * 内容模版
     */
    private String content;
    /**
     * 链接模版
     */
    private String url;

    @Override
    public Object execute(RuleContext ruleContext) {
        UserMsgSendParam param = new UserMsgSendParam();
        param.setBoxId(boxId);
        param.setTitle(title);
        param.setContent(content);
        param.setMsgUrl(url);
        param.setToUser(getToUsers(ruleContext));
        ruleContext.getRuleEngine().msgClient().sendUserMsg(param);
        return null;
    }


}
