package com.deeppaas.taskInfo.biz.dao;

import com.deeppaas.taskInfo.biz.entity.TaskInfoImageDo;
import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TaskInfoImageDao extends BaseJdbcDao<TaskInfoImageDo, String> {

    List<TaskInfoImageDo> findByTaskInfoId(String taskInfoId);

    List<TaskInfoImageDo> findAllBycallBackImageIdIn(List<String> asList);
}
