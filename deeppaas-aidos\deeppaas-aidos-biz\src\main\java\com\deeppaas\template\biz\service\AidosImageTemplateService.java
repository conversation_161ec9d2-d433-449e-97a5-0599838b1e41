package com.deeppaas.template.biz.service;

import com.deeppaas.template.biz.dao.AidosImageTemplateDao;
import com.deeppaas.template.biz.entity.AidosImageTemplateDO;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
public class AidosImageTemplateService {

    @Autowired
    private AidosImageTemplateDao aidosImageTemplateDao;

    public List<AidosImageTemplateDO> getAll(){
        return aidosImageTemplateDao.findAll();
    }

    public String save(AidosImageTemplateDO aidosImageTemplateDO){
        String id =aidosImageTemplateDO.getId();

        if (isIdenticalName(aidosImageTemplateDO))
            return "已存在相同名称【" + aidosImageTemplateDO.getName() + "】";
        if(StringHelper.isEmpty(id)){
            aidosImageTemplateDO.setCreateTime(LocalDateTime.now());
            aidosImageTemplateDao.insert(aidosImageTemplateDO);
        }else {
            aidosImageTemplateDao.save(aidosImageTemplateDO) ;
        }
        return "";
    }
    private boolean isIdenticalName(AidosImageTemplateDO source) {
        String id = source.getId();
        AidosImageTemplateDO name = aidosImageTemplateDao.findByName(source.getName());
        if (name == null)
            return false;
        if (Objects.equals(name.getId(), id)) {
            return false;
        }
        return true;
    }
    public void insert(List<AidosImageTemplateDO> aidosImageTemplateDO){
        aidosImageTemplateDao.insertAll(aidosImageTemplateDO);
    }
    public void del(String id){
        aidosImageTemplateDao.deleteById(id);
    }

    public PageData<AidosImageTemplateDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<AidosImageTemplateDO> page = aidosImageTemplateDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    public List<AidosImageTemplateDO> findAllById(String... id) {
        return (List<AidosImageTemplateDO>) aidosImageTemplateDao.findAllById(Arrays.asList(id));
    }
    public List<AidosImageTemplateDO> findAllById(List<String> id) {
        return (List<AidosImageTemplateDO>) aidosImageTemplateDao.findAllById(id);
    }
    public AidosImageTemplateDO get(String id) {
        return aidosImageTemplateDao.getById(id);
    }
}
