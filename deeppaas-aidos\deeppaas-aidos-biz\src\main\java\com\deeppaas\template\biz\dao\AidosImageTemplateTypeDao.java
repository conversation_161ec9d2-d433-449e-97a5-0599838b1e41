package com.deeppaas.template.biz.dao;

import com.deeppaas.template.biz.entity.AidosImageTemplateTypeDO;
import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AidosImageTemplateTypeDao extends BaseJdbcDao<AidosImageTemplateTypeDO, String> {

    List<AidosImageTemplateTypeDO> findByProcessTaskId(String processTaskId);
    AidosImageTemplateTypeDO findByName(String name);


}
