package com.deeppaas.common.exception;

/**
 * @Description 程序运行异常
 * <AUTHOR>
 * @Date 2019-03-21
 */
public class RunException extends BaseException{
    /** 请求数据未找到异常码 */
    public static final int DATA_NOT_FOUND_CODE = 5001;
    /** 程序计算异常码 */
    public static final int RUN_ERROR_CODE = 5002;
    /** 计算被拒绝异常码 */
    public static final int OPT_REJECT_CODE = 5003;

    private RunException(int code, String message) {
        super(code, message);
    }

    /**
     * @Description: 未找到数据
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static RunException dataNotFound(String message) {
        return new RunException(DATA_NOT_FOUND_CODE, message);
    }

    /**
     * @Description: 运算发生错误
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static RunException error(String message) {
        return new RunException(RUN_ERROR_CODE, message);
    }

    /**
     * @Description: 运算被拒绝
     * <AUTHOR>
     * @date 2019-03-22
     */
    public static RunException optReject(String message) {
        return new RunException(OPT_REJECT_CODE, message);
    }

}
