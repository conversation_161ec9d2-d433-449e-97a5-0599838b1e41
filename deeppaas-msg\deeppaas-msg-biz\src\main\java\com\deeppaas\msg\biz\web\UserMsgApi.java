package com.deeppaas.msg.biz.web;

import com.deeppaas.account.api.client.UserClient;
import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.msg.api.client.MsgFormat;
import com.deeppaas.msg.biz.convert.UserMsgConvert;
import com.deeppaas.msg.biz.entity.UserMsgDO;
import com.deeppaas.msg.biz.entity.UserMsgTemplateDO;
import com.deeppaas.msg.biz.service.UserMsgService;
import com.deeppaas.msg.biz.service.UserMsgTemplateService;
import com.deeppaas.msg.biz.web.param.UserMsgSearchParam;
import com.deeppaas.msg.biz.web.param.UserTemplateMsgSendParam;
import com.deeppaas.msg.biz.web.vo.UserMsgCountVO;
import com.deeppaas.msg.biz.web.vo.UserMsgVO;
import org.springframework.data.domain.Page;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@RestController
@RequestMapping("/api/userMsg")
public class UserMsgApi {
    private final UserMsgService userMsgService;
    private final UserMsgTemplateService templateService;
    private final UserClient userClient;

    public UserMsgApi(UserMsgService userMsgService, UserMsgTemplateService templateService, UserClient userClient) {
        this.userMsgService = userMsgService;
        this.templateService = templateService;
        this.userClient = userClient;
    }

    /**
     * 分页查询用户消息列表
     * @return 用户消息列表
     */
    @GetMapping("/page")
    public RestModel<PageData<UserMsgVO>> page(UserMsgSearchParam pageSearch) {
        Page<UserMsgDO> page = userMsgService.findForPage(pageSearch, LocalUserContext.getCurUserCode());
        List<UserMsgVO> list = page.getContent().stream().map(UserMsgConvert::doToVo).toList();
        PageData<UserMsgVO> voPageData = PageData.init(page, list);
        return RestModel.data(voPageData);
    }

    /**
     * 获取当前用户未读消息数量
     *
     * @return 未读消息数量
     */
    @GetMapping(value = "/unReadCount")
    public RestModel<List<UserMsgCountVO>> unReadCount() {
        String userCode = LocalUserContext.getCurUserCode();
        if(StringHelper.isEmpty(userCode)){
            return RestModel.data(Collections.emptyList());
        }
        List<UserMsgCountVO> countVOS = userMsgService.findUnReadMsgCount(userCode);
        return RestModel.data(countVOS);
    }

    /**
     * 根据消息ID查询消息
     *
     * @param id 消息ID
     * @return 账号
     */
    @GetMapping(value = "/get")
    public RestModel<UserMsgVO> getById(@RequestParam String id) {
        UserMsgDO msgDO = userMsgService.readAndGet(id);
        UserMsgVO infoVO = UserMsgConvert.doToVo(msgDO);
        return RestModel.data(infoVO);
    }

    /**
     * 批量已读
     *
     * @param ids ID列表串，已逗号分隔
     * @return 操作结果
     */
    @PostMapping(value = "/read")
    public RestModel<String> read(@BodyParam List<String> ids) {
        userMsgService.readByIds(ids, LocalUserContext.getCurUserCode());
        return RestModel.data("已读成功");
    }

    /**
     * 全部标记为已读
     *
     * @return 操作结果
     */
    @PostMapping(value = "/readAll")
    public RestModel<String> readAll(@BodyParam(required=false) String boxId) {
        userMsgService.readByBox(boxId, LocalUserContext.getCurUserCode());
        return RestModel.data("全部已读");
    }


    /**
     * 删除消息
     *
     * @param ids ID列表串，已逗号分隔
     * @return 操作结果
     */
    @PostMapping(value = "/del")
    public RestModel<String> del(@BodyParam List<String> ids) {
        userMsgService.deleteByIds(ids, LocalUserContext.getCurUserCode());
        return RestModel.data("删除成功");
    }

    /**
     * 前端动作调用发送
     * @param param
     * @return
     */
    @PostMapping(value = "/action/send")
    public RestModel<String> actionSend(@RequestBody UserTemplateMsgSendParam param){
        UserMsgTemplateDO template = templateService.getById(param.getTemplateId());
        Set<String> toUsers = userClient.findUserCodeByScopes(param.getToUsers());
        if(!CollectionUtils.isEmpty(toUsers)){
            List<UserMsgDO> msgList = toUsers.stream().map(toUser -> {
                UserMsgDO userMsgDO = new UserMsgDO();
                userMsgDO.setBoxId(param.getBoxId());
                userMsgDO.setTitle(MsgFormat.format(template.getTitle(), param.getParams()));
                userMsgDO.setContent(MsgFormat.format(template.getContent(), param.getParams()));
                userMsgDO.setMsgUrl(MsgFormat.format(template.getMsgUrl(), param.getParams()));
                userMsgDO.setToUser(toUser);
                userMsgDO.setReadFlag(BoolHelper.INT_FALSE);
                userMsgDO.setDel(BoolHelper.INT_FALSE);
                userMsgDO.setCreateTime(LocalDateTime.now());
                return userMsgDO;
            }).toList();
            userMsgService.saveAll(msgList);
        }
        return RestModel.success("success");
    }

}
