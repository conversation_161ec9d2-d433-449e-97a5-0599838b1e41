package com.deeppaas.processConfiguration.domain.aggregate;

import com.deeppaas.configuration.biz.entity.AidosProcessConfigDO;
import com.deeppaas.configuration.biz.service.AidosProcessConfigService;
import com.deeppaas.process.biz.entity.AidosProcessDO;
import com.deeppaas.process.biz.entity.AidosProcessTaskDO;
import com.deeppaas.process.biz.service.AidosProcessService;
import com.deeppaas.process.biz.service.AidosProcessTaskService;
import com.deeppaas.processConfiguration.domain.factory.ProcessTaskConfigFactory;
import com.deeppaas.processConfiguration.domain.model.vo.ProcessConfigVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProcessTaskConfigImpl {
    @Autowired
    private AidosProcessService aidosProcessService;
    @Autowired
    private AidosProcessTaskService aidosProcessTaskService;
    @Autowired
    private AidosProcessConfigService aidosProcessConfigService;

    @Autowired
    private ProcessTaskConfigFactory processTaskConfigFactory;

    public List<ProcessConfigVO> get(String taskUserId) {

        List<AidosProcessTaskDO> aidosProcessTaskDOs = aidosProcessTaskService.like(taskUserId);
        List<String> processId = aidosProcessTaskDOs.stream().map(AidosProcessTaskDO::getProcessId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processId)) {
            return Lists.newArrayList();
        }
        List<AidosProcessDO> processDOList = aidosProcessService.getAll(processId);
        List<AidosProcessConfigDO> aidosProcessConfigDOs = aidosProcessConfigService.getAll(processId);
        return processTaskConfigFactory.tobuilds(processDOList, aidosProcessTaskDOs, aidosProcessConfigDOs);
    }

}
