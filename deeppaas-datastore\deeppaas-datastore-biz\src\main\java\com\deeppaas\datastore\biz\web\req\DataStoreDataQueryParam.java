package com.deeppaas.datastore.biz.web.req;

import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.util.List;

/**
 * 查询/导出数据模型参数
 * <AUTHOR>
 * @date 2022/6/21
 */
@Data
public class DataStoreDataQueryParam {
    /**
     * 对应哪一个数据模型，必须
     */
    private String entity;
    /**
     * 数据模型字段code集合，导出时使用，为空则导出所有字段
     */
    private List<String> fields;
    /**
     * 查询参数
     */
    private QueryCondition condition;
    /**
     * 排序
     */
    private List<QueryOrderSort> orderSorts;

    private Integer pageNo;
    private Integer pageSize;


    @Data
    public static class QueryCondition {
        /**
         * 条件运算符合/**
         * EQ、NE、GT、GTE、LT、LTE、LIKE、NOT_LIKE、IN、NOT_IN、CONTAINS、START_WITH、END_WITH、AND、OR
         */
        protected String operator;
        /**
         * 单字段匹配时：匹配字段编号
         */
        protected String field;
        /**
         * 单字段匹配时：匹配字段的匹配值
         */
        protected Object value;
        /**
         * 多字段匹配时：子匹配信息
         */
        private List<QueryCondition> conditions;
    }

    @Data
    public static class QueryOrderSort {
        /**
         * 排序字段
         */
        protected String field;
        /**
         * 排序方式：ASC正序，DESC倒序
         */
        protected String type;
    }

    public Sort buildSort() {
        if(orderSorts==null){
            return null;
        }
        Sort mainSort = null;
        for (QueryOrderSort queryOrderSort : orderSorts) {
            Sort sort = null;
            if ("ASC".equals(queryOrderSort.type)) {
                sort = Sort.by(Sort.Direction.ASC, queryOrderSort.field);
            } else if ("DESC".equals(queryOrderSort.type)) {
                sort = Sort.by(Sort.Direction.DESC, queryOrderSort.field);
            }
            if (sort != null) {
                if (mainSort == null) {
                    mainSort = sort;
                } else {
                    mainSort = mainSort.and(sort);
                }
            }
        }
        return mainSort;
    }
    public PageRequest buildPageable() {
        int searchPageNo = pageNo == null?0:pageNo;
        int searchPageSize = pageSize == null?0:pageSize;
        if (searchPageNo == 0) {
            searchPageNo = 1;
        }
        if (searchPageSize == 0) {
            searchPageSize = 10;
        }
        Sort sort = buildSort();
        if (sort==null) {
            return PageRequest.of(searchPageNo - 1, searchPageSize);
        } else {
            return PageRequest.of(searchPageNo - 1, searchPageSize, sort);
        }
    }

    public boolean notPageQuery(){
        return pageNo==null && pageSize==null;
    }
}
