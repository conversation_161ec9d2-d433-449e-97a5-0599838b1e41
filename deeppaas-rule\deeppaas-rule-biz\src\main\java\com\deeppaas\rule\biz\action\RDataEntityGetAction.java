package com.deeppaas.rule.biz.action;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.deeppaas.rule.biz.model.RDataQueryPermit;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/18
 */
public class RDataEntityGetAction extends RDataEntityQueryAction{

    public RDataEntityGetAction(String dataEntity, RDataQueryPermit[] permits, RConditionModel defaultCondition) {
        super(dataEntity, permits, defaultCondition);
    }

    @Override
    public Object execute(RuleContext ruleContext) {
        DataStoreCondition queryCondition = getQueryCondition(ruleContext);
        List<DataStoreData> list = ruleContext.getRuleEngine().dataStoreDataClient().query(dataEntity, queryCondition);
        if(CollectionUtils.isEmpty(list)){
            return super.returnOrNext(null, ruleContext);
        }else if(list.size()==1){
            return super.returnOrNext(list.get(0), ruleContext);
        }else {
            throw RunException.error("查询数据异常，应返回1条数据实际返回了"+list.size()+"条");
        }
    }
}
