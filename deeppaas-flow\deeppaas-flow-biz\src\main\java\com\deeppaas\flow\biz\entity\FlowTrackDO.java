package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * @ClassName: FlowTrack 
 * @Description: 记录流程流转轨迹
 * <AUTHOR>
 */
@Data
@Table("flow_track")
public class FlowTrackDO implements BaseModel {
	private static final long serialVersionUID = -1750914612898144148L;

	@Id
	@Column("id")
	private Long id;

	/**
	 * 流程实例ID
	 */
	@Column("instance_id")
	private Long instanceId;
	/**
	 * 从环节
	 */
	@Column("form_node")
	private String formNode;
	/**
	 * 到环节
	 */
	@Column("to_node")
	private String toNode;
	/**
	 * 经过时间点
	 */
	@Column("pass_time")
	private LocalDateTime passTime;
}
