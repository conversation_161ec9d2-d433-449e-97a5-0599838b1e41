package com.deeppaas.datastore.biz.model;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

/**
 * 数据模型连接
 *
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
public class DataStoreEntityJoinParam implements BaseModel {
    /**
     * 所属数据模型编号
     **/
    private String entityCode;

    /**
     * 本级联别编号
     **/
    private String code;

    /**
     * 级联表ID
     **/
    private String tableId;

    /**
     * 级联表名
     **/
    private String tableName;

    /**
     * 所属上级级联
     **/
    private String parentJoinCode;
}
