package com.deeppaas.extapi.biz.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
@Component
public class ExtApiCacheLocal implements ExtApiCache{
    private static Cache authCodeCache = new CaffeineCache(
            "authCodeCache", Caffeine.from("maximumSize=10000,expireAfterWrite=10m").build(), true);
    @Override
    public Cache getAuthCodeCache() {
        return authCodeCache;
    }
}
