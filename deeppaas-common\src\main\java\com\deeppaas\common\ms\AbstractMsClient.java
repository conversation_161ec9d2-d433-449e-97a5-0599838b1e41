//package com.deeppaas.common.ms;
//
//import com.deeppaas.common.helper.ClientSignHelper;
//import com.deeppaas.common.helper.LogerHelper;
//import com.deeppaas.common.model.RestModel;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.cloud.client.ServiceInstance;
//import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
//import org.springframework.core.ParameterizedTypeReference;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.RequestEntity;
//import org.springframework.http.ResponseEntity;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.client.RestTemplate;
//
//import java.net.URI;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @date 2021/5/11
// */
//public class AbstractMsClient {
//    private static Logger log = LoggerFactory.getLogger(AbstractMsClient.class);
//    private final LoadBalancerClient loadBalancerClient;
//    private final String msName;
//    private final RestTemplate restTemplate;
//
//    protected AbstractMsClient(LoadBalancerClient loadBalancerClient, String msName) {
//        this.loadBalancerClient = loadBalancerClient;
//        this.msName = msName;
//        this.restTemplate = new RestTemplate();
//    }
//
//    private static final Integer ACCOUNT_CODE_SUCCESS = 0;
//
//    protected <T> T doPost(String api, Object body, ParameterizedTypeReference<RestModel<T>> typeReference) {
//        return doMethod(HttpMethod.POST, api, body, typeReference);
//    }
//
//    protected <T> T doGet(String api, Object body, ParameterizedTypeReference<RestModel<T>> typeReference) {
//        return doMethod(HttpMethod.GET, api, body, typeReference);
//    }
//
//    private <T> T doMethod(HttpMethod method, String api, Object body, ParameterizedTypeReference<RestModel<T>> typeReference) {
//        try {
//            ServiceInstance serviceInstance = loadBalancerClient.choose(msName);
//            if (serviceInstance == null) {
//                log.error("调用[" + msName + api + "]异常，请检查提供程序实例是否正常");
//                return null;
//            }
//            String path = String.format("http://%s:%s", serviceInstance.getHost(), serviceInstance.getPort()) + api;
//            MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
//            headers.add("sign", ClientSignHelper.initSign());
//            RequestEntity requestEntity;
//            if (Objects.isNull(body)) {
//                requestEntity = new RequestEntity(headers, method, URI.create(path));
//            } else {
//                requestEntity = new RequestEntity(body, headers, method, URI.create(path));
//            }
//
//            ResponseEntity<RestModel<T>> responseEntity = restTemplate.exchange(requestEntity, typeReference);
//            RestModel<T> restModel = responseEntity.getBody();
//            if (restModel != null) {
//                if (!Objects.equals(ACCOUNT_CODE_SUCCESS, restModel.getCode())) {
//                    log.error("调用[" + msName + api + "]拉取数据异常，请检查|restModel={}", restModel);
//                    return null;
//                }
//                T dataResult = restModel.getData();
//                return dataResult;
//            }
//        } catch (Exception ex) {
//            LogerHelper.errorLog(log, ex);
//            ex.printStackTrace();
//        }
//        return null;
//    }
//}
