package com.deeppaas.quartz.job;

import com.deeppaas.quartz.core.QuartzJob;
import com.deeppaas.quartz.service.QuartzJobService;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 执行动作定时器
 *
 * <AUTHOR>
 * @date 2020-12-02
 */
@Component("quartz_QuartzActionJob")
public class QuartzActionJob extends QuartzJob {
    private static final Logger log = LoggerFactory.getLogger(QuartzActionJob.class);

    private QuartzJobService jobService;

    public QuartzActionJob(QuartzJobService jobService) {
        this.jobService = jobService;
    }

    @Override
    public void doExecute(JobExecutionContext context) {
        String appCode = context.getJobDetail().getKey().getGroup();
        String jobId = context.getJobDetail().getKey().getName();
        log.info("QuartzActionJob doExecute Start");
        log.info("appCode={},jobId={}", appCode, jobId);

        log.info("Do SomeThing..........");
        log.info("QuartzActionJob doExecute End");
    }


    @Override
    public void rerun(Date targetTime) {
        log.info("QuartzActionJob rerun Start");

        log.info("QuartzActionJob rerun End");
    }
}
