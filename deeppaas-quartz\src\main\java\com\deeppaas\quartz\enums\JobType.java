package com.deeppaas.quartz.enums;

/**
 * 定时任务类型
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
public enum JobType {
    /**
     * 执行动作
     */
    ACTION("com.deeppaas.quartz.job.QuartzActionJob"),
    /**
     * 执行API
     */
    API("com.deeppaas.quartz.job.QuartzApiJob"),
    /**
     * 执行SQL
     */
    SQL("com.deeppaas.quartz.job.QuartzSqlJob"),
    ;


    private String jobClass;

    JobType(String jobClass) {
        this.jobClass = jobClass;
    }

    public String getJobClass() {
        return jobClass;
    }
}
