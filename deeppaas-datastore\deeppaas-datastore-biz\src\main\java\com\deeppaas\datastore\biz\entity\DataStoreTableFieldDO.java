package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;

/**
 * 数据库表字段
 *
 * <AUTHOR>
 * @date 2022/2/19
 */
@Data
@Table("datastore_table_field")
@EqualsAndHashCode(callSuper = true)
public class DataStoreTableFieldDO extends BaseUuidDO {
    /**
     * 所属表
     **/
    @Column("table_id")
    private String tableId;

    /**
     * 字段编号
     **/
    @Column("code")
    private String code;

    /**
     * 字段名称
     **/
    @Column("name")
    private String name;
    /**
     * 数据库列名
     */
    @Column("column_name")
    private String columnName;

    /**
     * 数据类型
     */
    @Column("data_type")
    private String dataType;

    /**
     * 字段长度(字符串类型为总长度，数字类型为包含小数点前后的总位数)
     */
    @Column("length")
    private Integer length;

    /**
     * 小数点后多少位，除数字其他类型均为0
     */
    @Column("decimal_digits")
    private Integer decimalDigits;

    /**
     * 是否是主键
     */
    @Column("id_key")
    private Integer idKey;

    /**
     * 是否唯一
     */
    @Column("is_unique")
    private Integer unique;

    /**
     * 是否是数据库生成的数据
     */
    @Column("db_generated")
    private Integer dbGenerated;

    /**
     * 页面表单控件类型
     */
    @Column("web_type")
    private String webType;
}
