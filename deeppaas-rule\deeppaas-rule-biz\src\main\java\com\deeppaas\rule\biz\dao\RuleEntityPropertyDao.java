package com.deeppaas.rule.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.rule.biz.entity.RuleEntityPropertyDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/25
 */
public interface RuleEntityPropertyDao extends BaseJdbcDao<RuleEntityPropertyDO, String> {

    RuleEntityPropertyDO findByEntityIdAndName(String entityId, String name);

    @Modifying
    @Query("delete from rule_entity_property where entity_id=:entityId")
    void deleteByEntityId(@Param("entityId") String entityId);

    List<RuleEntityPropertyDO> findByEntityId(String entityId);
}
