package com.deeppaas.flow.biz;

import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.flow.biz.service.*;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import com.deeppaas.flow.biz.model.PastNodeInfo;

import java.util.List;

/**
 * @ClassName: FlowEngine 
 * @Description: 流程引擎API主接口
 * <AUTHOR>
 */
public interface FlowEngine {
	/**
	 * @Title: define 
	 * @Description: 流程定义服务接口
	 * @return
	 * <AUTHOR>
	 */
	FlowDefineService define();
	/**
	 * @Title: instance 
	 * @Description: 流程实例服务接口
	 * @return
	 * <AUTHOR>
	 */
	FlowInstanceService instance();

	/**
	 * 流程令牌服务
	 * @return
	 */
	FlowTokenService token();
	/**
	 * @Title: task 
	 * @Description: 流程任务服务接口
	 * @return
	 * <AUTHOR>
	 */
	FlowTaskService task();
	/**
	 * @Title: assign 
	 * @Description: 流程处理人指派服务接口
	 * @return
	 * <AUTHOR>
	 */
	FlowAssignService assign();

	/**
	 * 流程时间处理接口
	 * @return
	 */
	FlowEventService event();

	/**
	 * @Title: createInstance
	 * @Description: 创建流程实例
	 * @param defineCode
	 * @param startNodeCode
	 * @param orderId
	 * @param userId
	 * @return
	 * <AUTHOR>
	 */
	FlowInstanceDO createInstance(String defineCode, String startNodeCode, String orderId, String userId);

	/**
	 * 模拟流转
	 * @param instance
	 * @param userId
	 * @return
	 */
	ExecuteResult mockStartInstance(FlowInstanceDO instance, String userId);
	/**
	 * @Title: forwardExecute 
	 * @Description: 流程向前流转
	 * @param instance
	 * @param userId
	 * @return
	 * <AUTHOR>
	 */
	ExecuteResult startInstance(FlowInstanceDO instance, String userId);
	
	/**
	 * @Title: forwardExecuteCheck 
	 * @Description: 审批检查，是否需要指派，是否需要必填审批意见
	 * @param task
	 * @param userId
	 * @return
	 * <AUTHOR>
	 */
	ExecuteResult forwardExecuteCheck(FlowTaskDO task, String userId);
	/**
	 * @Title: forwardExecute 
	 * @Description: 审批任务并向前流转
	 * @param task
	 * @param userId
	 * @param comment
	 * @return
	 * <AUTHOR>
	 */
	ExecuteResult forwardExecute(FlowTaskDO task, String userId, String comment);

	/**
	 * @Title: closeInstance 
	 * @Description: 关闭流程实例
	 * @param instance
	 * @param userId
	 * @param action
	 * @param comment
	 * @return
	 * <AUTHOR>
	 */
	ExecuteResult closeInstance(FlowInstanceDO instance, String userId, FlowAction action, String comment);

	/**
	 * @Description: 获取可驳回节点
	 * @param task
	 * @param userId
	 * @return java.util.List<com.lh.oa.flow.define.entity.FlowNode>
	 * <AUTHOR>
	 */
    List<PastNodeInfo> getBackNodes(FlowTaskDO task, String userId);

    /**
     * @Description: 驳回
     * @param task
     * @param backNodeId
     * @param userId
     * @param comment
     * @return com.lh.oa.flow.ExecuteResult
     * <AUTHOR>
     */
	ExecuteResult backwardExecute(FlowTaskDO task, String backNodeId, String userId, String comment);

	/**
	 * 获取用户撤回环节
	 * @param instanceId
	 * @param userCode
	 * @return
	 */
	List<PastNodeInfo> taskTakeBackTarget(Long instanceId, String userCode);

	/**
	 * @Description: 撤回
	 * @param instanceId
	 * @param targetNodeCode 撤回目标
	 * @param userCode
	 * @param comment
	 * @return com.lh.oa.flow.ExecuteResult
	 * <AUTHOR>
	 * @date 2019-02-26
	 */
	ExecuteResult taskTakeBackExecute(Long instanceId, String targetNodeCode, String userCode, String comment);

	/**
	 * 获取申请单数据
	 * @param entityCode
	 * @param orderId
	 * @return
	 */
    DataStoreData getOrderData(String entityCode, String orderId);
}
