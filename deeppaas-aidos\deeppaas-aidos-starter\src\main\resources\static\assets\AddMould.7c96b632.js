var R=Object.defineProperty,$=Object.defineProperties;var q=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var N=(l,s,a)=>s in l?R(l,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):l[s]=a,C=(l,s)=>{for(var a in s||(s={}))G.call(s,a)&&N(l,a,s[a]);if(y)for(var a of y(s))H.call(s,a)&&N(l,a,s[a]);return l},I=(l,s)=>$(l,q(s));import{r as p,u as J,a7 as K,g as o,j as t,an as P,B as w}from"./index.689bcdcb.js";import{u as A}from"./useMutation.b5bc9a93.js";import{u as g}from"./useQuery.d623ee6b.js";import{A as F,w as W,e as X}from"./emptyImg.975752a6.js";import{g as Y,a as Z,s as ee,b as te,c as se}from"./process.855d0088.js";import{e as x}from"./util.bf0e86fd.js";import{I as ae,S as ie}from"./index.f2cb64d4.js";import{B as f}from"./button.34f11f85.js";import{C as T}from"./index.7f65f5a2.js";import{I as le}from"./index.0524b9fa.js";import"./reactNode.50ea6579.js";import"./index.71d34973.js";import"./scrollTo.edb0bd08.js";import"./EyeOutlined.70f6da72.js";import"./css.032fc6ca.js";import"./DialogWrap.9951837c.js";import"./getScrollBarSize.001053fa.js";import"./RightOutlined.ed31d8e3.js";import"./pickAttrs.04174e9b.js";import"./LeftOutlined.6bce5536.js";function Ae(){const{Link:l}=F,[s,a]=p.exports.useState(!1),[n,m]=p.exports.useState([]),[B,v]=p.exports.useState(!1),[E,L]=p.exports.useState(""),[k,j]=p.exports.useState(""),[d,O]=p.exports.useState([]),b=J(),r=b.state,D=K(),u=b.search.split("?")[1].split("&")[0].split("=")[1];g(["template-all",u],()=>x(()=>Y(`processTaskId=${u}&type=class`))(),{enabled:!!(u&&!r.id),onSuccess(e){const i=e.map(c=>c.id);m(i)}}),g(["template-all",u],()=>x(()=>Z(u,r.id))(),{enabled:!!(u&&r.id),onSuccess(e){console.log(e);const i=e.map(c=>String(c.imageTemplateId));m(i),console.log(i)}});const h=A(ee,{onSuccess(e){w.success(e.data.message),window.history.back()}}),M=A(te,{onSuccess(e){w.success(e.data.message),window.history.back()}}),{refetch:U}=g(["account-image"],()=>x(()=>se({param:{search_CONTAINS_name:k}}))(),{onSuccess(e){O(e.list)}});function V(){if(s)m([]);else{const e=[];d.map(i=>e.push(String(i.id))),m(e)}a(!s)}function _(e){const i=n.indexOf(String(e.id));m(i===-1?c=>[...c,String(e.id)]:c=>(c.splice(i,1),[...c]))}function z(e){L("/api/imagetemplate/"+e.path),v(!0)}function Q(){if(r.id){const e=n.reduce((i,c)=>(i.push({processTaskId:u,imageTemplateTypeId:r.id,imageTemplateId:c,processId:r.processId}),i),[]);console.log(e),M.mutate(e)}else h.mutate(I(C({},r.data),{templates:n.join(",")}))}function S(){D("/settings/template")}return o("div",{className:"h-screen bg-white overflow-y-auto pb-10",children:[o("div",{className:"h-14 flex bg-white px-6 items-center justify-between",style:{boxShadow:"0px 2px 24px 0px rgba(200, 201, 204, 0.5"},children:[t("div",{className:"font-bold",children:"\u65B0\u589E\u6A21\u677F"}),t(ae,{value:k,onChange:e=>{j(e.target.value)},style:{width:"180px"},placeholder:"\u8BF7\u8F93\u5165",suffix:t(ie,{className:"cursor-pointer",onClick:()=>{U()}})}),o("div",{children:[t(f,{loading:h.isLoading,onClick:()=>window.history.back(),children:"\u53D6\u6D88"}),t(f,{type:"primary",className:"ml-4",onClick:()=>{Q()},loading:h.isLoading,children:"\u65B0\u589E"})]})]}),o("div",{className:"w-[1400px] mx-auto mt-[18px] mb-2 text-primary-black-65",children:["\u8BF7\u5728\u4E0B\u9762\u9009\u62E9\u9700\u8981\u7684\u6587\u4EF6\u4F5C\u4E3A\u4E1A\u52A1\u6A21\u677F\u7684\u5FC5\u8981\u6750\u6599",t(f,{size:"small",className:"ml-3",onClick:S,children:"\u65B0\u589E\u6587\u4EF6"})]}),d.length>0?o("div",{className:"flex justify-start  w-[1400px] mx-auto",children:[o("div",{children:[o("div",{className:"bg-[#FAFAFA] flex items-center h-[54px] pl-6",children:[t(T,{onChange:V,checked:n.length===d.length,defaultChecked:n.length===d.length}),t("div",{className:"font-bold ml-8",children:"\u5168\u9009"})]}),t("div",{className:"pl-6 flex flex-wrap",children:d.map(e=>o("div",{className:"flex w-[420px] items-center h-[53px] border-b-2",id:e.initials,children:[t(T,{value:e.id,defaultChecked:n.indexOf(String(e.id))>-1,checked:n.indexOf(String(e.id))>-1,onChange:()=>_(e)}),t("div",{className:"ml-8 w-[291px] cursor-pointer",onClick:()=>{z(e)},children:e.name})]},"image"+e.id))})]}),t("div",{className:"mt-[54px] ml-auto",children:t(F,{children:W.map(e=>t(l,{href:"#"+e,title:e,className:"text-xs"},"link"+e))})})]}):o("div",{className:" mt-[280px]",children:[t(P,{description:!1,image:X,imageStyle:{width:"240px",height:"200px",margin:"auto"}}),o("div",{className:"text-center text-sm text-primary-black-65",children:["\u8BF7\u5728\u6A21\u677F\u7BA1\u7406\u4E0A\u4F20\u6A21\u677F",t("span",{className:"ml-1 cursor-pointer text-primary-default",onClick:()=>{S()},children:"\u6A21\u677F\u7BA1\u7406"})]})]}),t(le,{width:200,style:{display:"none"},preview:{visible:B,src:E,onVisibleChange:e=>{v(e)}}})]})}export{Ae as default};
