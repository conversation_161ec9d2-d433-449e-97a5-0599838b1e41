package com.deeppaas.datastore.biz.convert;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.datastore.api.client.model.DataStoreEntityCO;
import com.deeppaas.datastore.api.client.model.DataStoreEntityFieldCO;
import com.deeppaas.datastore.biz.entity.*;
import com.deeppaas.datastore.biz.web.vo.DataStoreEntityVO;
import com.deeppaas.tenant.api.client.model.AppCO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模型相关转换器
 *
 * <AUTHOR>
 * @date 2022/04/16
 */
public class DataStoreEntityConvert {

    /**
     * 表字段转换为模型字段
     *
     * @param entityCode 模型标号
     * @param fieldDO    表字段信息
     * @return 模型字段
     */
    public static DataStoreEntityFieldDO newEntityField(String entityCode, DataStoreTableFieldDO fieldDO) {
        DataStoreEntityFieldDO entityFieldDO = new DataStoreEntityFieldDO();
        entityFieldDO.setEntityCode(entityCode);
        entityFieldDO.setCode(fieldDO.getCode());
        entityFieldDO.setName(fieldDO.getName());
        entityFieldDO.setColumnName(fieldDO.getColumnName());
        entityFieldDO.setDataType(fieldDO.getDataType());
        entityFieldDO.setWebType(fieldDO.getWebType());
        entityFieldDO.setReadOnly(BoolHelper.INT_FALSE);
        entityFieldDO.setDiscriminator(BoolHelper.INT_FALSE);
        entityFieldDO.setOverwrote(BoolHelper.INT_FALSE);
        return entityFieldDO;
    }

    public static DataStoreEntityFieldOptionDO newEntityFieldOption(String entityCode,
                                                                                   String entityFieldId,
                                                                                   DataStoreTableFieldOptionDO tableFieldOptionDO) {
        // 复制字段备选项配置信息
        DataStoreEntityFieldOptionDO entityFieldOptionDO = new DataStoreEntityFieldOptionDO();
        entityFieldOptionDO.setEntityCode(entityCode);
        entityFieldOptionDO.setFieldId(entityFieldId);
        entityFieldOptionDO.setSourceId(tableFieldOptionDO.getSourceId());
        entityFieldOptionDO.setOptionEntityCode(tableFieldOptionDO.getOptionEntityCode());
        entityFieldOptionDO.setValueField(tableFieldOptionDO.getValueField());
        entityFieldOptionDO.setDisplayField(tableFieldOptionDO.getDisplayField());
        entityFieldOptionDO.setCondition(tableFieldOptionDO.getCondition());
        entityFieldOptionDO.setOverwrote(BoolHelper.INT_FALSE);
        return entityFieldOptionDO;
    }

    public static List<DataStoreEntityVO> entityToVOS(List<DataStoreEntityDO> entityDOList,
                                                      List<AppCO> appCOList) {
        Map<String, AppCO> appCOMap = appCOList.stream().collect(Collectors.toMap(AppCO::getCode, Function.identity()));
        return entityDOList.stream().map(entity -> entityToVO(entity, appCOMap.get(entity.getAppCode()))).collect(Collectors.toList());
    }

    public static DataStoreEntityVO entityToVO(DataStoreEntityDO entityDO, AppCO appCO) {
        DataStoreEntityVO vo = new DataStoreEntityVO();
        vo.setId(entityDO.getId());
        vo.setSourceId(entityDO.getSourceId());
        vo.setCode(entityDO.getCode());
        vo.setName(entityDO.getName());
        vo.setType(entityDO.getType());
        vo.setApp(appCO);
        vo.setTableId(entityDO.getTableId());
        vo.setTableName(entityDO.getTableName());
        vo.setCreateTime(entityDO.getCreateTime());
        vo.setUpdateTime(entityDO.getUpdateTime());
        return vo;
    }

    public static DataStoreEntityCO entityToCO(DataStoreEntityDO entityDO, String appName) {
        DataStoreEntityCO entityCO = new DataStoreEntityCO();
        entityCO.setSourceId(entityDO.getSourceId());
        entityCO.setCode(entityDO.getCode());
        entityCO.setName(entityDO.getName());
        entityCO.setType(entityDO.getType());
        entityCO.setAppCode(entityDO.getAppCode());
        entityCO.setAppName(appName);
        entityCO.setTableId(entityDO.getTableId());
        entityCO.setTableName(entityDO.getTableName());
        entityCO.setUpdateTime(entityDO.getUpdateTime());
        return entityCO;
    }

    public static List<DataStoreEntityCO> entityToCOS(List<DataStoreEntityDO> entityDOList,
                                                      List<AppCO> appCOList) {
        if (CollectionUtils.isEmpty(entityDOList)) {
            return Collections.emptyList();
        }
        Map<String, String> appNameMap = appCOList.stream().collect(Collectors.toMap(AppCO::getCode, AppCO::getName));
        List<DataStoreEntityCO> coList = Lists.newArrayList();
        for (DataStoreEntityDO entityDO : entityDOList) {
            coList.add(entityToCO(entityDO, appNameMap.get(entityDO.getAppCode())));
        }
        return coList;
    }

    public static List<DataStoreEntityFieldCO> entityFieldToVOS(List<DataStoreEntityFieldDO> fieldDOList) {
        if (CollectionUtils.isEmpty(fieldDOList)) {
            return Collections.emptyList();
        }
        List<DataStoreEntityFieldCO> fieldCOList = new ArrayList<>();
        for (DataStoreEntityFieldDO fieldDO : fieldDOList) {
            DataStoreEntityFieldCO fieldCO = new DataStoreEntityFieldCO();
            fieldCO.setId(fieldDO.getId());
            fieldCO.setEntityCode(fieldDO.getEntityCode());
            fieldCO.setCode(fieldDO.getCode());
            fieldCO.setName(fieldDO.getName());
            fieldCO.setDataType(fieldDO.getDataType());
            fieldCO.setWebType(fieldDO.getWebType());
            fieldCO.setColumnName(fieldDO.getColumnName());
            fieldCOList.add(fieldCO);
        }
        return fieldCOList;
    }
}
