package com.deeppaas.flow.biz.enums;

/**
 * 紧急程度
 * <AUTHOR>
 * @date 2021/12/23
 */
public enum Priority {
    L1(1, "一般"),
    L2(2, "紧急"),
    L3(3,"特急");

    private int value;
    private String text;

    Priority(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public int getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
