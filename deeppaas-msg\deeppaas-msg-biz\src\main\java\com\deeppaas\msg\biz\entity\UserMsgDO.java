package com.deeppaas.msg.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * 用户消息
 * <AUTHOR>
 * @date 2022/9/2
 */
@Data
@Table("deeppaas_user_msg")
public class UserMsgDO extends BaseUuidDO {
    /**
     * 所属消息模块
     */
    @Column("box_id")
    private String boxId;

    /**
     * 消息标题
     */
    @Column("title")
    private String title;

    /**
     * 消息正文
     */
    @Column("content")
    private String content;

    /**
     * 链接地址
     */
    @Column("msg_url")
    private String msgUrl;

    /**
     * 接收人
     */
    @Column("to_user")
    private String toUser;

    /**
     * 1:已读/0:未读
     */
    @Column("read_flag")
    private Integer readFlag = 0;

    /**
     * 删除标识 1:删除/0:未删除
     */
    @Column("del")
    private Integer del = 0;

    /**
     * 已读时间
     */
    @Column("read_time")
    private LocalDateTime readTime;

    /**
     * 删除时间
     */
    @Column("del_time")
    private LocalDateTime delTime;
}
