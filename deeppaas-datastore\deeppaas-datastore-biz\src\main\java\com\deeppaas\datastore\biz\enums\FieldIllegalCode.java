package com.deeppaas.datastore.biz.enums;

import com.deeppaas.common.exception.ReqException;

/**
 * 表&模型字段非法编号
 *
 * <AUTHOR>
 * @date 2022/06/14
 */
public enum FieldIllegalCode {
    /**
     * id
     */
    id,

    entity,
    ;

    public static void check(String code) {
        if (id.name().equalsIgnoreCase(code) || entity.name().equalsIgnoreCase(code)) {
            throw ReqException.paramValidate("非法的字段编号，请修改后保存");
        }
    }
}
