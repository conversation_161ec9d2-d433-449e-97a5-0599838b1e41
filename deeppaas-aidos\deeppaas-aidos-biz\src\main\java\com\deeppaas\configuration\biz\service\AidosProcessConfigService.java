package com.deeppaas.configuration.biz.service;

import com.deeppaas.configuration.biz.dao.AidosProcessConfigDao;
import com.deeppaas.configuration.biz.entity.AidosConfigVariableDO;
import com.deeppaas.configuration.biz.entity.AidosProcessConfigDO;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AidosProcessConfigService {

    @Autowired
    private AidosProcessConfigDao aidosProcessConfigDao;

    @Autowired
    private AidosConfigVariableService aidosConfigVariableService;

    public List<AidosProcessConfigDO> getAll() {
        return aidosProcessConfigDao.findAll();
    }

    private static Map<String, String> TIPSVARNAME = new HashMap<>();

    static {
        TIPSVARNAME.put("get_api_params", "tips_varname");
        TIPSVARNAME.put("hc_image_process", "tips_varname");
        TIPSVARNAME.put("image_quality", "tips_varname");
        TIPSVARNAME.put("image_identification", "required_tips_varname,optional_tips_varname,summary_tips_varname,result_tips_varname");
        TIPSVARNAME.put("file_migration", "result_tips_varname");
        TIPSVARNAME.put("naming_rule", "result_varname");
    }

    public List<AidosProcessConfigDO> getAll(List<String> processIds) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("IN_processId", processIds);
        JCondition condition = JCondition.ofPageParam(paramMap);
        List<AidosProcessConfigDO> lists = aidosProcessConfigDao.queryForList(condition);
        lists.sort(new Comparator<AidosProcessConfigDO>() {
            @Override
            public int compare(AidosProcessConfigDO person1, AidosProcessConfigDO person2) {
                return person1.getSort() - person2.getSort();
            }
        });
        return lists;
    }


    private Map<String, String> build(AidosProcessConfigDO aidosProcessDO) {
        Map<String, String> changeMap = null;
        String tipsvarName = TIPSVARNAME.get(aidosProcessDO.getName());
        if (StringHelper.isEmpty(tipsvarName))
            return null;
        List<AidosConfigVariableDO> variableDOS = aidosConfigVariableService.findByAidosProcessConfigId(aidosProcessDO.getId());
        Map<String, AidosConfigVariableDO> map = variableDOS.stream().collect(Collectors.toMap(AidosConfigVariableDO::getKey, Function.identity()));
        Map<String, AidosConfigVariableDO> mapValue = variableDOS.stream().filter(item -> StringHelper.isNotEmpty(item.getValue())).collect(Collectors.toMap(AidosConfigVariableDO::getValue, Function.identity()));
        //检查重复
        //   Set<String> mapValue = variableDOS.stream().filter(item->StringHelper.isNotEmpty(item.getValue())).map(AidosConfigVariableDO::getValue).collect(Collectors.toSet());
        String config = aidosProcessDO.getConfigs();
        if (StringHelper.isEmpty(config))
            return null;

        Map<String, Object> json = JsonHelper.json2map(config);

        String[] tip = tipsvarName.split(",");
        List<AidosConfigVariableDO> values = Lists.newArrayList();
        for (int i = 0; i < tip.length; i++) {
            Object value = json.get(tip[i]);
            if (value != null && StringHelper.isNotEmpty(value.toString()) && map.get(value.toString()) == null) {
                AidosConfigVariableDO aidosConfigVariableDO = mapValue.get(tip[i]);
                if (aidosConfigVariableDO == null) {
                    aidosConfigVariableDO = new AidosConfigVariableDO();
                } else {
                    changeMap = new HashMap<>();
                    if (!value.toString().equals(aidosConfigVariableDO.getKey()))
                        changeMap.put( aidosConfigVariableDO.getKey(),value.toString());

                }
                aidosConfigVariableDO.setProcessId(aidosProcessDO.getProcessId());
                aidosConfigVariableDO.setAidosProcessConfigId(aidosProcessDO.getId());
                aidosConfigVariableDO.setProcessTaskId(aidosProcessDO.getProcessTaskId());
                aidosConfigVariableDO.setKey(value.toString());
                aidosConfigVariableDO.setName(aidosProcessDO.getAlias());
                aidosConfigVariableDO.setCreateTime(LocalDateTime.now());
                aidosConfigVariableDO.setValue(tip[i]);
                values.add(aidosConfigVariableDO);
            }
        }
        if (!CollectionUtils.isEmpty(values))
            aidosConfigVariableService.insert(values);

        return changeMap;
    }

    public String insert(AidosProcessConfigDO aidosProcessDO) {
        String id = aidosProcessDO.getId();

        //TODO 解析
        Map<String, String> changeMap = build(aidosProcessDO);
        if (StringHelper.isEmpty(id)) {
            aidosProcessDO.setCreateTime(LocalDateTime.now());
            aidosProcessConfigDao.insert(aidosProcessDO);
        } else {
            aidosProcessConfigDao.save(aidosProcessDO);
        }
        if (changeMap != null) {
            return changeVariable(changeMap, aidosProcessDO.getProcessTaskId());
        }
        return null;
    }

    private String changeVariable(Map<String, String> changeMap, String processTaskId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("EQ_processTaskId", processTaskId);
        JCondition condition = JCondition.ofPageParam(paramMap);
        List<AidosProcessConfigDO> lists = aidosProcessConfigDao.queryForList(condition);
        List<String> result = new ArrayList<>();
        changeMap.forEach((k, v) -> {
            lists.forEach(item -> {
                String config = item.getConfigs();
                if (StringHelper.isNotEmpty(config) && config.contains(k)) {
                    item.setConfigs(config.replaceAll(k, v));
                    result.add("变量【" + k + "】改变，" + item.getAlias() + "环节，引用变量发生变化");
                    aidosProcessConfigDao.save(item);
                }
            });
        });
        if (!CollectionUtils.isEmpty(result))
            return StringHelper.join(result, ",");
        return null;
    }

    public void insert(List<AidosProcessConfigDO> aidosProcessDO) {
        aidosProcessDO.forEach(item -> {
            insert(item);
        });
    }

    public void del(String id) {
        aidosProcessConfigDao.deleteById(id);
    }

    public AidosProcessConfigDO get(String id) {
        return aidosProcessConfigDao.getById(id);
    }

    public PageData<AidosProcessConfigDO> page(PageSearch pageSearch) {
        pageSearch.setSortInfo("ASC_sort");
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<AidosProcessConfigDO> page = aidosProcessConfigDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

}
