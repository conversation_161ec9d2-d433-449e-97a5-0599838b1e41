package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityFieldOptionDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
public interface DataStoreEntityFieldOptionDao extends BaseJdbcDao<DataStoreEntityFieldOptionDO, String> {

    DataStoreEntityFieldOptionDO findByFieldId(String fieldId);

    @Modifying
    @Query("delete from datastore_entity_field_option where entity_code=:entityCode or option_entity_code=:entityCode")
    void deleteByEntityCode(@Param("entityCode") String entityCode);

    @Modifying
    @Query("delete from datastore_entity_field_option where field_id=:fieldId")
    void deleteByField(@Param("fieldId") String fieldId);
}
