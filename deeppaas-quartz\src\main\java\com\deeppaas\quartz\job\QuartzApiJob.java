package com.deeppaas.quartz.job;

import com.deeppaas.quartz.core.QuartzJob;
import com.deeppaas.quartz.entity.QuartzJobDO;
import com.deeppaas.quartz.service.QuartzJobParamService;
import com.deeppaas.quartz.service.QuartzJobService;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 执行API定时器
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@Component("quartz_QuartzApiJob")
public class QuartzApiJob extends QuartzJob {
    private static final Logger log = LoggerFactory.getLogger(QuartzApiJob.class);

    private QuartzJobService jobService;
    private QuartzJobParamService jobParamService;

    QuartzApiJob(QuartzJobService jobService, QuartzJobParamService jobParamService) {
        this.jobService = jobService;
        this.jobParamService = jobParamService;
    }

    @Override
    public void doExecute(JobExecutionContext context) {
        log.info("QuartzApiJob doExecute Start");
        String jobId = context.getJobDetail().getKey().getName();
        QuartzJobDO quartzJobDO = jobService.findByJobId(jobId);

        System.out.println(quartzJobDO.toString());

        log.info("QuartzApiJob doExecute End");
    }


    @Override
    public void rerun(Date targetTime) {
        log.info("QuartzApiJob rerun Start");

        log.info("QuartzApiJob rerun End");
    }
}
