package com.deeppaas.common.helper;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * @ClassName: HashHelper
 * @Description: HASH算法工具类
 * <AUTHOR>
 * @date 2017年9月26日
 */
public final class HashHelper {

	/**
	 * @Title: md5
	 * @Description: 获得MD5加密字符串
	 * @param str
	 * @return
	 * <AUTHOR>
	 * @date 2017年9月26日
	 */
	@SuppressWarnings("deprecation")
//	public static String md5(String str){
//		return DigestUtils.md5Hex(str);
//	}

	/**
	 * @Title: sha256
	 * @Description: 获得SHA256加密字符串
	 * @param str
	 * @return
	 * <AUTHOR>
	 * @date 2017年9月26日
	 */
	public static String sha256(String str){
		return DigestUtils.sha256Hex(str);
	}

}
