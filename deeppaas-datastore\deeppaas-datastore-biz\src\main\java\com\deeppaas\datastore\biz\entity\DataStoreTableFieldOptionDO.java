package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
@Table("datastore_table_field_option")
@EqualsAndHashCode(callSuper = true)
public class DataStoreTableFieldOptionDO extends BaseUuidDO {
    /**
     * 字段编号
     **/
    @Column("field_id")
    private String fieldId;

    /**
     * 目标数据源
     **/
    @Column("source_id")
    private String sourceId;

    /**
     * 备选数据模型
     **/
    @Column("option_entity_code")
    private String optionEntityCode;

    /**
     * 备选值字段
     */
    @Column("value_field")
    private String valueField;

    /**
     * 备选显示字段
     */
    @Column("display_field")
    private String displayField;

    /**
     * 过滤条件
     */
    @Column("condition")
    private String condition;
}
