package com.deeppaas.common.helper;


import com.deeppaas.common.exception.ReqException;

import java.util.Collection;

/**
 * <AUTHOR>
 * @ClassName: AssertHelper
 * @Description: 断言工具
 * @date 2017年7月31日
 */
public class AssertHelper {
    /**
     * 断言表达式为true
     *
     * @param message 异常打印信息
     */
    public static void isLogin(Long userId, String message) {
        if (userId == null || userId == 0) {
            throw ReqException.paramValidate(message);
        }
    }

    /**
     * 断言表达式为true
     *
     * @param expression
     * @param message    异常打印信息
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw ReqException.paramValidate(message);
        }
    }

    /**
     * 断言给定的object对象为空
     *
     * @param object
     * @param message 异常打印信息
     */
    public static void isNull(Object object, String message) {
        if (object != null) {
            throw ReqException.paramValidate(message);
        }
    }

    /**
     * 断言给定的object对象为非空
     *
     * @param object
     * @param message 异常打印信息
     */
    public static void notNull(Object object, String message) {
        if (object == null) {
            throw ReqException.paramValidate(message);
        }
    }

    /**
     * 断言给定的字符串为非空
     *
     * @param str
     * @param message
     */
    public static void notEmpty(String str, String message) {
        if (str == null || str.length() == 0) {
            throw ReqException.paramValidate(message);
        }
    }

    /**
     * 断言给定的集合为非空
     *
     * @param collection
     * @param message    void
     * @Methods Name notEmpty
     * @Create In 2016年8月3日 By liaogs
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw ReqException.paramValidate(message);
        }
    }

    /**
     * 断言给定的集合为空
     *
     * @param collection
     * @param message
     * @Title: isEmpty
     * <AUTHOR>
     * @CreateTime 2017年6月27日 上午11:43:06
     */
    public static void isEmpty(Collection<?> collection, String message) {
        if (collection != null && !collection.isEmpty()) {
            throw ReqException.paramValidate(message);
        }
    }
}
