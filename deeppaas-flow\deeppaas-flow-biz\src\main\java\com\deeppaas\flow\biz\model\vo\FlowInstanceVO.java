package com.deeppaas.flow.biz.model.vo;

import com.deeppaas.account.api.client.model.User;
import com.deeppaas.common.model.BaseModel;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程实例信息
 *
 * <AUTHOR>
 * @date 2022/05/30
 */
@Data
public class FlowInstanceVO implements BaseModel {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 流程定义
     */
    private String defineCode;

    /**
     * 流程版本
     */
    private Integer version;

    /**
     * 开始环节
     */
    private String startNode;

    /**
     * 工单ID
     */
    private String orderId;

    /**
     * 当前优先级
     */
    private Integer priority;

    /**
     * 流程实例状态（未流转:0/流转中:1）
     */
    private Integer state;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 父流程ID
     */
    private String parentId;

    /**
     * 父流程依赖的节点编号
     */
    private String parentNodeCode;

    /**
     * 流程创建人
     */
    private User creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public static FlowInstanceVO instanceOf(FlowInstanceDO fi, User creator) {
        FlowInstanceVO vo = new FlowInstanceVO();
        vo.setId(fi.getId());
        vo.setDefineCode(fi.getDefineCode());
        vo.setVersion(fi.getVersion());
        vo.setStartNode(fi.getStartNode());
        vo.setOrderId(fi.getOrderId());
        vo.setPriority(fi.getPriority());
        vo.setState(fi.getState());
        vo.setStartTime(fi.getStartTime());
        vo.setEndTime(fi.getEndTime());
        vo.setParentId(fi.getParentId());
        vo.setParentNodeCode(fi.getParentNodeCode());
        vo.setCreator(creator);
        vo.setCreateTime(fi.getCreateTime());
        return vo;
    }
}
