package com.deeppaas.configuration.biz.web;

import com.deeppaas.configuration.biz.entity.AidosConfigVariableDO;
import com.deeppaas.configuration.biz.entity.AidosProcessConfigDO;
import com.deeppaas.configuration.biz.service.AidosConfigVariableService;
import com.deeppaas.configuration.biz.service.AidosProcessConfigService;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/config/variable")
public class AidosConfigVariableApi {


    @Autowired
    private AidosConfigVariableService aidosConfigVariableService;

    @Autowired
    private AidosProcessConfigService aidosProcessConfigService;

    @PostMapping("/page")
    public RestModel<PageData<AidosConfigVariableDO>> page(@RequestBody PageSearch pageSearch) {
        PageData<AidosConfigVariableDO> list = aidosConfigVariableService.page(pageSearch);
        return RestModel.data(list);
    }

    @GetMapping("/getAll")
    public RestModel<List<AidosConfigVariableDO>> list() {
        List<AidosConfigVariableDO> list = aidosConfigVariableService.getAll();
        return RestModel.data(list);
    }
    @GetMapping("/get")
    public RestModel<List<AidosConfigVariableDO>> get(@RequestParam String id) {
        AidosProcessConfigDO aidosProcessConfigDO=aidosProcessConfigService.get(id);
        if(aidosProcessConfigDO==null)
            return RestModel.data(Lists.newArrayList());
        List<AidosConfigVariableDO> list = aidosConfigVariableService.findByProcessTaskId(aidosProcessConfigDO.getProcessTaskId());
        return RestModel.data(list);
    }
    @PostMapping("/save")
    public RestModel list(@RequestBody AidosConfigVariableDO aidosConfigVariableDO) {
        aidosConfigVariableService.insert(aidosConfigVariableDO);
        return RestModel.success("保存成功");
    }
    @PostMapping("/saves")
    public RestModel list(@RequestBody List<AidosConfigVariableDO> aidosConfigVariableDO) {
        aidosConfigVariableService.insert(aidosConfigVariableDO);
        return RestModel.success("保存成功");
    }
    @GetMapping("/del")
    public RestModel list(@RequestParam String id) {
        aidosConfigVariableService.del(id);
        return RestModel.success("删除成功");
    }


}
