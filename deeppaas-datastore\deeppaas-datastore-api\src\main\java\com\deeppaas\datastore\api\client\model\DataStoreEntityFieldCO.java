package com.deeppaas.datastore.api.client.model;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

/**
 * 数据模型字段
 *
 * <AUTHOR>
 * @date 2022/08/16
 */
@Data
public class DataStoreEntityFieldCO implements BaseModel {
    /**
     * 主键ID
     **/
    private String id;

    /**
     * 数据模型编号
     **/
    private String entityCode;

    /**
     * 字段编号
     **/
    private String code;

    /**
     * 字段名称
     **/
    private String name;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 页面表单控件类型
     */
    private String webType;

    /**
     * 数据库列名
     */
    private String columnName;
}
