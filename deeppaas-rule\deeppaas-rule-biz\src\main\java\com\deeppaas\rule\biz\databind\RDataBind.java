package com.deeppaas.rule.biz.databind;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.handler.ExpressionHandler;
import com.deeppaas.rule.biz.model.RFunction;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据绑定
 * <AUTHOR>
 * @date 2022/6/13
 */
public class RDataBind {
    /**
     * 数据绑定对应类型
     */
    private final RBindTargetType type;
    /**
     * 数据绑定对应的对象，比如变量就是变量code
     */
    private final Object _value;

    /**
     * 数据绑定对应的数据类型
     */
    private final SimpleDataType dataType;

    private final String targetKey;
    private Map<String, RDataBind> _children;

    public RDataBind(RBindTargetType type, Object _value, SimpleDataType dataType, Map<String, RDataBind> _children) {
        this.type = type;
        this._value = _value;
        this.dataType = dataType;
        this.targetKey = switch (type){
            case VARS, PARAM -> type.name()+"_"+_value+".";
            default -> UUID.randomUUID().toString();
        };
        this._children = _children;
    }

    public RBindTargetType getType() {
        return type;
    }

    public SimpleDataType getDataType() {
        return dataType;
    }

    public Object getValue() {
        return _value;
    }

    public Map<String, RDataBind> getChildren() {
        return _children;
    }

    /**
     * 获取绑定设置的信息
     * @return
     */
    public String getValueTarget(){
        switch (type){
            case VARS :
            case PARAM:
                return (String)_value;
            // case RULE -> 暂不支持
            default : return null;
        }
    }

    /**
     * 获取绑定数据
     * @param ruleContext
     * @return
     */
    public Object getBindData(RuleContext ruleContext) {
        Map<String, RDataBind> childrenBinds = this.getChildren();
        Object directData = getDirectValue(ruleContext);
        if(directData==null && CollectionUtils.isEmpty(childrenBinds)){
            return null;
        }
        if(CollectionUtils.isEmpty(childrenBinds)){
            return directData;
        }
        switch (dataType){
            case DATA_ENTITY, RULE_ENTITY -> {
                return getEntityBindData(directData, ruleContext);
            }
            case DATA_ENTITIES, RULE_ENTITIES -> {
                return getEntitiesBindData(directData, ruleContext);
            }
            default -> throw RunException.error("规则配置异常");
        }
    }

    private Map<String, Object> getEntityBindData(Object entityTargetData, RuleContext ruleContext) {
        Map<String, Object> entityTargetMap = (Map<String, Object>) entityTargetData;
        if(entityTargetMap == null){
            entityTargetMap = new HashMap<>();
        }
        Map<String, Object> bindData = new HashMap<>();
        for(String fieldName : _children.keySet()){
            RDataBind fieldBind = _children.get(fieldName);
            Object fieldValue = null;
            switch (fieldBind.dataType){
                case DATA_ENTITY, RULE_ENTITY: {
                    fieldValue = fieldBind.getEntityBindData(entityTargetMap.get(fieldName), ruleContext);
                    break;
                }
                case DATA_ENTITIES, RULE_ENTITIES: {
                    fieldValue = fieldBind.getEntitiesBindData(entityTargetMap.get(fieldName), ruleContext);
                    break;
                }
                default: {
                    fieldValue = fieldBind.getDirectValue(ruleContext);
                }
            }
            bindData.put(fieldName, fieldValue);
        }
        return bindData;
    }

    /**
     * 获取绑定对象数组数据
     * @param targetData
     * @param ruleContext
     * @return
     */
    private Object getEntitiesBindData(Object targetData, RuleContext ruleContext) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<Map<String, Object>> directListData = objectToMapList(targetData);
        SubRuleContext subRuleContext = SubRuleContext.of(ruleContext);
        for(Map<String, Object> entityTargetData : directListData){
            this.resetTarget(subRuleContext, entityTargetData);
            Map<String, Object> entityData = getEntityBindData(entityTargetData, subRuleContext);
            result.add(entityData);
        }
        return result;
    }

    /**
     * 重置上下文中的变量，将数组改为单体
     * @param subRuleContext
     * @param targetData
     */
    private void resetTarget(SubRuleContext subRuleContext, Object targetData) {
        switch (type){
            case VARS -> {
                subRuleContext.setVarByPath((String)_value, targetData);
            }
            case PARAM -> {
                subRuleContext.setParamByPath((String)_value, targetData);
            }
        }
    }

    /**
     * 直接返回(未做对象内映射)
     * @param ruleContext
     * @return
     */
    private Object getDirectValue(RuleContext ruleContext) {
        switch (type){
            case CONST : return _value;
            case VARS : return ruleContext.getVarByPath((String)_value);
            case PARAM: {
                if(_value==null || !StringUtils.hasText(_value.toString())){
                    return ruleContext.getParam();
                }
                return ruleContext.getParamByPath((String)_value);
            }
            case FUN : {
                RFunction _function = (RFunction)_value;
                List<RDataBind> params = _function.getParams();
                Object[] arguments = new Object[0];
                if(params!=null){
                    arguments = new Object[params.size()];
                    int arrayIndex = 0;
                    for(RDataBind param : params){
                        arguments[arrayIndex] = param.getDirectValue(ruleContext);
                        arrayIndex++;
                    }
                }
                return ruleContext.getRuleEngine().functionHandler().execute(_function, arguments);
            }

            case EXPR : return ExpressionHandler.execute((String)_value, ruleContext);
            // case RULE -> 暂不支持
            default : return null;
        }
    }

    /**
     * 更新绑定
     * @param originalValue 被更新的原值
     * @param ruleContext
     */
    public Object updateBind(Object originalValue, RuleContext ruleContext) {
        Map<String, RDataBind> childrenBinds = this.getChildren();
        Object targetData = getDirectValue(ruleContext);
        if(CollectionUtils.isEmpty(childrenBinds)){
            return targetData;
        }
        switch (dataType){
            case DATA_ENTITY, RULE_ENTITY -> {
                return entityUpdateBind(originalValue, targetData, ruleContext);
            }
            case DATA_ENTITIES, RULE_ENTITIES -> {
                return entitiesUpdateBind(originalValue, targetData, ruleContext);
            }
            default -> throw RunException.error("规则配置异常");
        }
    }

    private Object entityUpdateBind(Object originalValue, Object targetData, RuleContext ruleContext) {
        Map<String, RDataBind> childrenBinds = this.getChildren();
        // 如果没有字段级绑定，那么直接对象级替换
        if(CollectionUtils.isEmpty(childrenBinds)){
            return targetData;
        }

        // 如果存在字段级绑定，那么原对象不变，进行字段集替换
        Map<String, Object> originalValueMap = (Map<String, Object>) originalValue;
        if(originalValue==null){
            originalValueMap = new HashMap<>();
        }
        Map<String, Object> targetDataMap = targetData==null ? new HashMap<>() : (Map<String, Object>) targetData;
        for(String fieldName : _children.keySet()){
            Object originalFieldValue = originalValueMap.get(fieldName);
            RDataBind fieldBind = _children.get(fieldName);
            Object fieldValue = null;
            switch (fieldBind.dataType){
                case DATA_ENTITY, RULE_ENTITY: {
                    fieldValue = fieldBind.entityUpdateBind(originalFieldValue, targetDataMap.get(fieldName), ruleContext);
                    originalValueMap.put(fieldName, fieldValue);
                    break;
                }
                case DATA_ENTITIES, RULE_ENTITIES: {
                    fieldValue = fieldBind.entitiesUpdateBind(originalFieldValue, targetDataMap.get(fieldName), ruleContext);
                    originalValueMap.put(fieldName, fieldValue);
                    break;
                }
                default: {
                    fieldValue = fieldBind.getDirectValue(ruleContext);
                }
            }
            originalValueMap.put(fieldName, fieldValue);
        }
        return originalValueMap;
    }

    private Object entitiesUpdateBind(Object originalData, Object targetData, RuleContext ruleContext) {
        Map<String, RDataBind> childrenBinds = this.getChildren();
        // 如果没有字段级绑定，那么直接对象级替换
        if(CollectionUtils.isEmpty(childrenBinds)){
            return targetData;
        }
        List<Object> result = new ArrayList<>();
        List<Map<String, Object>> originalDataList = originalData== null ? new ArrayList<>() : objectToMapList(originalData);
        List<Map<String, Object>> targetDataList = targetData== null ? new ArrayList<>() : objectToMapList(targetData);

        // 情况1，无原值有映射
        if(originalDataList.isEmpty() && !targetDataList.isEmpty()){
            SubRuleContext subRuleContext = SubRuleContext.of(ruleContext);
            for(Map<String, Object> targetDataItem : targetDataList){
                this.resetTarget(subRuleContext, targetDataItem);
                Object entityData = entityUpdateBind(new HashMap<>(), targetDataItem, subRuleContext);
                result.add(entityData);
            }
            return result;
        }
        // 情况2，有原值无映射
        if(!originalDataList.isEmpty() && targetDataList.isEmpty()){
            SubRuleContext subRuleContext = SubRuleContext.of(ruleContext);
            for(Map<String, Object> originalDataItem : originalDataList){
                this.resetTarget(subRuleContext, originalDataItem);
                Object entityData = entityUpdateBind(originalDataItem, new HashMap<>(), subRuleContext);
                result.add(entityData);
            }
            return result;
        }

        // 情况3，原值和映射值都有，无匹配规则无法绑定
        throw RunException.error("无法处理两个对象数组的属性绑定");
    }

    private List<Map<String, Object>> objectToMapList(Object entities){
        if(entities.getClass().isArray()){
            Map<String, Object>[] array = (Map<String, Object>[]) entities;
            return Arrays.stream(array).toList();
        }
        if(entities instanceof Iterable<?>){
            return Lists.newArrayList(((Iterable<Map<String, Object>>) entities).iterator());
        }
        throw RunException.error("值类型异常");
    }
}
