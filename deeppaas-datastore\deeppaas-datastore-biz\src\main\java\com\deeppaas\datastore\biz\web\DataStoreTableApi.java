package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.datastore.biz.entity.DataStoreTableDO;
import com.deeppaas.datastore.biz.entity.DataStoreTableFieldDO;
import com.deeppaas.datastore.biz.entity.DataStoreTableFieldOptionDO;
import com.deeppaas.datastore.biz.entity.DataStoreTableIndexDO;
import com.deeppaas.datastore.biz.enums.FieldIllegalCode;
import com.deeppaas.datastore.biz.service.DataStoreTableService;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据仓库自建表API
 *
 * <AUTHOR>
 * @date 2022/2/19
 */
@RestController
@RequestMapping("/api/datastore/table/")
public class DataStoreTableApi {
    private final DataStoreTableService dataStoreTableService;

    public DataStoreTableApi(DataStoreTableService dataStoreTableService) {
        this.dataStoreTableService = dataStoreTableService;
    }

    /**
     * 获取数据表
     *
     * @param id
     * @return
     */
    @GetMapping("/get")
    public RestModel<DataStoreTableDO> get(String id) {
        DataStoreTableDO table = dataStoreTableService.get(id);
        return RestModel.data(table);
    }

    /**
     * 数据表-列表
     *
     * @param sourceId 数据源ID
     * @return 数据表列表
     */
    @GetMapping("/list")
    public RestModel<List<DataStoreTableDO>> list(String sourceId) {
        List<DataStoreTableDO> list = dataStoreTableService.list(sourceId);
        return RestModel.data(list);
    }

    /**
     * 数据表-分页
     *
     * @param pageSearch 分页查询参数
     * @return 数据表列表
     */
    @GetMapping("/page")
    public RestModel<PageData<DataStoreTableDO>> page(PageSearch pageSearch) {
        PageData<DataStoreTableDO> pageData = dataStoreTableService.page(pageSearch);
        return RestModel.data(pageData);
    }

    /**
     * 数据表-按照名称或编号模糊匹配分页
     *
     * @param pageSearch 分页查询参数
     * @param codeOrName 表中文名或者数据库表名
     * @return 数据模型列表
     */
    @GetMapping("/fuzzy/page")
    public RestModel<PageData<DataStoreTableDO>> pageFuzzy(PageSearch pageSearch, String codeOrName) {
        PageData<DataStoreTableDO> pageData = dataStoreTableService.pageFuzzy(pageSearch, codeOrName);
        return RestModel.data(pageData);
    }

    /**
     * 数据表新增
     *
     * @param param 参数
     * @return 表信息
     */
    @PostMapping("/add")
    public RestModel<DataStoreTableDO> add(@RequestBody DataStoreTableDO param) {
        // 参数检查
        param.check();
        // 业务操作
        DataStoreTableDO tableDO = dataStoreTableService.add(param);
        return RestModel.data(tableDO);
    }

    /**
     * 数据表修改
     *
     * @param param 参数
     * @return 表信息
     */
    @PostMapping("/modify")
    public RestModel<DataStoreTableDO> modify(@RequestBody DataStoreTableDO param) {
        DataStoreTableDO tableDO = dataStoreTableService.modify(param);
        return RestModel.data(tableDO);
    }

    /**
     * 修改所属文件夹
     *
     * @param id       主键ID
     * @param folderId 文件夹ID
     * @return 操作结果
     */
    @PostMapping("/modifyFolder")
    public RestModel<Boolean> modifyFolder(@BodyParam String id, @BodyParam String folderId) {
        // 业务处理
        dataStoreTableService.modifyFolder(id, folderId);
        return RestModel.data(true);
    }

    /**
     * 修改排序
     *
     * @param ids 主键ID数组
     * @return 操作结果
     */
    @PostMapping("/sort")
    public RestModel<Boolean> sort(@BodyParam String[] ids) {
        dataStoreTableService.sort(List.of(ids));
        return RestModel.data(true);
    }

    /**
     * 数据表删除
     *
     * @param tableId 表ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    public RestModel<Boolean> delete(@BodyParam String tableId) {
        dataStoreTableService.delete(tableId);
        return RestModel.data(true);
    }

    /**
     * 载入字段，包括已有字段和数据库表物理字段
     *
     * @param tableId
     * @return
     */
    @PostMapping("/field/load")
    public RestModel<List<DataStoreTableFieldDO>> loadField(@BodyParam String tableId) {
        List<DataStoreTableFieldDO> fieldList = dataStoreTableService.loadField(tableId);
        return RestModel.data(fieldList);
    }

    /**
     * 获取指定ID表字段
     *
     * @param id
     * @return
     */
    @GetMapping("/field/get")
    public RestModel<DataStoreTableFieldDO> getField(@RequestParam String id) {
        DataStoreTableFieldDO fieldDO = dataStoreTableService.getFieldById(id);
        return RestModel.data(fieldDO);
    }

    /**
     * 数据表字段-列表
     *
     * @param tableId 所属表ID
     * @return 字段列表
     */
    @GetMapping("/field/list")
    public RestModel<List<DataStoreTableFieldDO>> listField(@RequestParam String tableId) {
        List<DataStoreTableFieldDO> list = dataStoreTableService.listField(tableId);
        return RestModel.data(list);
    }

    /**
     * 数据表字段-新增
     *
     * @param param 参数
     * @return 字段信息
     */
    @PostMapping("/field/add")
    public RestModel<DataStoreTableFieldDO> addField(@RequestBody DataStoreTableFieldDO param) {
        // 字段编号检查
        FieldIllegalCode.check(param.getCode());
        // 检查表字段是否在模型字段中已经存在
        dataStoreTableService.checkEntityField(param.getTableId(), param.getCode());
        // 添加表字段
        DataStoreTableFieldDO fieldDO = dataStoreTableService.addField(param);
        return RestModel.data(fieldDO);
    }

    /**
     * 数据表字段-修改
     *
     * @param param 参数
     * @return 字段信息
     */
    @PostMapping("/field/modify")
    public RestModel<DataStoreTableFieldDO> modifyField(@RequestBody DataStoreTableFieldDO param) {
        DataStoreTableFieldDO fieldDO = dataStoreTableService.modifyField(param);
        return RestModel.data(fieldDO);
    }

    /**
     * 数据表字段-删除
     *
     * @param fieldId 字段ID
     * @return 字段信息
     */
    @PostMapping("/field/delete")
    public RestModel<Boolean> deleteField(@BodyParam String fieldId) {
        dataStoreTableService.deleteField(fieldId);
        return RestModel.data(true);
    }

    /**
     * 获取数据表字段备选项
     *
     * @param fieldId 参数
     * @return 字段信息
     */
    @GetMapping("/field/option/get")
    public RestModel<DataStoreTableFieldOptionDO> getFieldOption(@RequestParam String fieldId) {
        DataStoreTableFieldOptionDO fieldOptionDO = dataStoreTableService.getFieldOption(fieldId);
        return RestModel.data(fieldOptionDO);
    }

    /**
     * 保存数据表字段备选项
     *
     * @param param 参数
     * @return 字段信息
     */
    @PostMapping("/field/option/save")
    public RestModel<DataStoreTableFieldOptionDO> saveFieldOption(@RequestBody DataStoreTableFieldOptionDO param) {
        DataStoreTableFieldOptionDO fieldOptionDO = dataStoreTableService.saveFieldOption(param);
        return RestModel.data(fieldOptionDO);
    }

    /**
     * 删除数据表字段备选项
     *
     * @param fieldOptionId 表字段备选项主键ID
     * @return 字段信息
     */
    @PostMapping("/field/option/delete")
    public RestModel<Boolean> deleteFieldOption(@BodyParam String fieldOptionId) {
        dataStoreTableService.deleteFieldOption(fieldOptionId);
        return RestModel.data(true);
    }

    /**
     * 数据表索引-列表
     *
     * @param tableId 所属表ID
     * @return 索引列表
     */
    @GetMapping("/index/list")
    public RestModel<List<DataStoreTableIndexDO>> listIndex(@RequestParam String tableId) {
        List<DataStoreTableIndexDO> list = dataStoreTableService.listIndex(tableId);
        return RestModel.data(list);
    }

    /**
     * @param id
     * @return
     */
    @GetMapping("/index/get")
    public RestModel<DataStoreTableIndexDO> getIndex(@RequestParam String id) {
        DataStoreTableIndexDO indexDO = dataStoreTableService.getIndexById(id);
        return RestModel.data(indexDO);
    }

    /**
     * 数据表索引-添加&修改
     *
     * @param param 参数
     * @return 表信息
     */
    @PostMapping("/index/save")
    public RestModel<DataStoreTableIndexDO> saveIndex(@RequestBody DataStoreTableIndexDO param) {
        // 参数检查
        param.check();
        // 检查表类型是否满足索引条件
        DataStoreTableDO tableDO = dataStoreTableService.get(param.getTableId());
        if (tableDO == null || !DataStoreEntityType.SELF_BUILD.name().equals(tableDO.getType())) {
            throw RunException.optReject("表不存在或表类型不支持");
        }
        // 业务操作
        DataStoreTableIndexDO tableIndexDO = dataStoreTableService.saveIndex(tableDO, param);
        return RestModel.data(tableIndexDO);
    }

    /**
     * 数据表索引-删除
     *
     * @param indexId 索引ID
     * @return 操作结果
     */
    @PostMapping("/index/delete")
    public RestModel<Boolean> deleteIndex(@BodyParam String indexId) {
        // 业务操作
        dataStoreTableService.deleteIndex(indexId);
        return RestModel.data(true);
    }

//    /**
//     * 目标数据源下表是否存在
//     *
//     * @param sourceId  数据源ID
//     * @param tableName 表名称
//     * @return 是否
//     */
//    private boolean existsTable(String sourceId, String tableName) {
//        return dataStoreDBService.existsTable(sourceId, tableName) || dataStoreTableService.existsTable(sourceId, tableName);
//    }
}
