package com.deeppaas.datastore.biz.service;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据模型数据导入读取器
 * <AUTHOR>
 * @date 2022/9/26
 */
public class DataStoreDataJsonReader {
    private List<Map<String,Object>> readDataList;
    private final String entityCode;
    private final List<DataStoreEntityFieldDTO> fields;
    private final List<String> readErrors = new ArrayList<>();

    private DataStoreDataJsonReader(List<Map<String,Object>> readDataList, String entityCode, List<DataStoreEntityFieldDTO> fields) {
        this.readDataList = readDataList;
        this.entityCode = entityCode;
        this.fields = fields;
    }
    public static DataStoreDataJsonReader of(InputStream inputStream, String entityCode, List<DataStoreEntityFieldDTO> fields) {
        try {
            List<Map<String,Object>> readDataList = JsonHelper.getObjectMapper().readValue(inputStream, new TypeReference<List<Map<String,Object>>>(){});
            return new DataStoreDataJsonReader(readDataList, entityCode, fields);
        } catch (IOException e) {
            throw RunException.error("IO异常：" + e.getMessage());
        }
    }

    public static DataStoreDataJsonReader of(File file, String entityCode, List<DataStoreEntityFieldDTO> fields) {
        try {
            List<Map<String,Object>> readDataList = JsonHelper.getObjectMapper().readValue(file, new TypeReference<List<Map<String,Object>>>(){});
            return new DataStoreDataJsonReader(readDataList, entityCode, fields);
        } catch (IOException e) {
            throw RunException.error("IO异常：" + e.getMessage());
        }
    }

    public List<String> getReadErrors() {
        return readErrors;
    }

    public List<DataStoreData> readDataList(){
        readErrors.clear();
        Map<String, DataStoreEntityFieldDTO> fieldNameMap = fields.stream().collect(Collectors.toMap(DataStoreEntityFieldDTO::getName, Function.identity()));
        List<DataStoreData> dataList = new ArrayList<>();
        for(Map<String, Object> readData : readDataList){
            Map<String, Object> dataItem = new HashMap<>();
            for(Map.Entry<String, DataStoreEntityFieldDTO> entry : fieldNameMap.entrySet()){
                String fieldName = entry.getKey();
                DataStoreEntityFieldDTO fieldDTO = entry.getValue();
                dataItem.put(fieldDTO.getCode(), getFieldData(readData.get(fieldName), fieldDTO));
            }
            if(!dataItem.isEmpty()) {
                dataList.add(DataStoreData.instance(entityCode,dataItem));
            }
        }
        return dataList;
    }

    private Object getFieldData(Object value, DataStoreEntityFieldDTO field) {
        if(value==null){
            return null;
        }
        SimpleDataType dataType = SimpleDataType.valueOf(field.getDataType());
        switch (dataType){
            case TEXT -> {
                String strValue = value.toString().trim();
                if(field.getLength()<strValue.length()){
                    readErrors.add("字段["+field.getName()+"]长度为"+field.getLength()+"，上传数据为["+strValue+"]超长了");
                    return null;
                }
                return strValue;
            }
            case NUMBER -> {
                if(value instanceof Number){
                    return value;
                }else {
                    readErrors.add("字段["+field.getName()+"]为Number类型，上传数据["+value+"]类型为"+value.getClass().getSimpleName());
                    return null;
                }
            }
            case BOOLEAN -> {
                if(value instanceof Boolean){
                    return value;
                }else {
                    readErrors.add("字段["+field.getName()+"]为Boolean类型，上传数据["+value+"]类型为"+value.getClass().getSimpleName());
                    return null;
                }
            }
            case DATE_TIME -> {
                String strValue = value.toString();
                if(StringUtils.hasText(strValue)) {
                    String dataStr = DateHelper.formatDataTime(strValue);
                    if(dataStr==null){
                        readErrors.add("字段["+field.getName()+"]为日期时间类型，上传数据为["+strValue+"]");
                        return null;
                    }
                    return dataStr;
                }
                return null;
            }
            default -> {
                return value;
            }
        }
    }

}
