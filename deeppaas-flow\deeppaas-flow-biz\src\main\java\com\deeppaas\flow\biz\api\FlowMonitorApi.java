package com.deeppaas.flow.biz.api;

import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.flow.biz.ExecuteResult;
import com.deeppaas.flow.biz.FlowAction;
import com.deeppaas.flow.biz.FlowEngine;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 流程监控API
 * <AUTHOR>
 * @date 2022/9/15
 */
@RestController
@RequestMapping("/api/flow/monitor")
public class FlowMonitorApi {
    private final FlowEngine flowEngine;

    public FlowMonitorApi(FlowEngine flowEngine) {
        this.flowEngine = flowEngine;
    }

    /**
     * 流程实例分页
     * @return
     */
    @GetMapping("/instance/page")
    public RestModel<PageData<FlowInstanceDO>> instancePage(PageSearch pageSearch){
        Page<FlowInstanceDO> page = flowEngine.instance().findForPage(pageSearch);
        return RestModel.page(page);
    }

    /**
     * 流程关闭
     * @param instanceId
     * @return
     */
    @PostMapping("/instance/close")
    public RestModel<ExecuteResult> instanceClose(Long instanceId){
        String userCode = LocalUserContext.getCurUserCode();
        FlowInstanceDO instanceDO = flowEngine.instance().getInstance(instanceId);
        ExecuteResult result = flowEngine.closeInstance(instanceDO, userCode, FlowAction.stop, "管理员控制台关闭");
        return RestModel.data(result);
    }

    /**
     * 任务实例分页
     * @return
     */
    @GetMapping("/task/page")
    public RestModel<PageData<FlowTaskDO>> taskPage(PageSearch pageSearch){
        Page<FlowTaskDO> page = flowEngine.task().findForPage(pageSearch);
        return RestModel.page(page);
    }


}
