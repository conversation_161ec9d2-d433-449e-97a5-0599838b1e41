package com.deeppaas.flow.biz.element;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.FlowConst;
import com.deeppaas.flow.biz.FlowException;
import com.deeppaas.flow.biz.MockExecuteItem;
import com.deeppaas.flow.biz.enums.*;
import com.deeppaas.flow.biz.model.AssignInfo;
import com.deeppaas.flow.biz.service.FlowAssignService;
import com.deeppaas.flow.biz.entity.FlowAssignDO;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import lombok.Getter;

/**
 * 人工节点
 * <AUTHOR>
 *
 */
@Getter
public class TaskNode extends FlowPageNode {
	/** 审批模式 **/
	private final TaskType taskType;
	/** 人员是否需要手动指定 **/
	private final boolean actorAssign;
	/** 无审批人自动跳过 **/
	private final boolean actorNullPass;
	/** 与上一环节相同审批人自动跳过 **/
	private final boolean actorSamePass;
	/** 限定时间(0为不限定) **/
	private final long limitedTime;
	/** 允许动作（） **/
	private final String action;
	/** 是否必须审批意见 **/
	private final boolean comment;
	/** 驳回目标环节（START/SOURCE） **/
	private final BackTarget backTarget;
	/** 驳回是否发送给所有审批人消息 **/
	private final boolean backMsg;
	/** 处理人组 **/
	private final List<TaskActorGroup> actorGroups;

	public TaskNode(String code, String name, NodeType type, String remark, String page, TaskType taskType,
                    boolean actorAssign, boolean actorNullPass, boolean actorSamePass,
                    long limitedTime, String action, boolean comment, BackTarget backTarget,
                    boolean backMsg, List<TaskActorGroup> actorGroups, List<FlowEventRule> eventRules) {
		super(code, name, type, remark, page, eventRules);
		this.taskType = taskType;
		this.actorAssign = actorAssign;
		this.actorNullPass = actorNullPass;
		this.actorSamePass = actorSamePass;
		this.limitedTime = limitedTime;
		this.action = action;
		this.comment = comment;
		this.backTarget = backTarget;
		this.backMsg = backMsg;
		this.actorGroups = actorGroups;
	}

	@Override
	public void enter(ExecuteItem execution) {
		execution.setToken(execution.getEngine().token().passTokenAndRecord(execution.getToken(),this,execution.getOperator()));
		execution.setNode(this);
		FlowAssignService flowAssignService = execution.getEngine().assign();
		boolean assignResult = flowAssignService.doAssign(execution, this);
		//如果未分配任务
		if(assignResult==false){
			/**
			 * 如果设置了无人自动跳过
 			 */
			if(actorNullPass) {
				this.execute(execution);
			}
			/**
			 * 判断上一步审批是否包含本次所有审批人
			 */
			else if(actorSamePass) {
				FlowAssignDO assign = execution.getEngine().assign().findByInstanceIdAndNodeCode(execution.getInstance().getId(), execution.getSourceNode().getCode());
				//只有一个处理人
				List<String> actors = assign.getActors();
				if(assign!=null && actors.size()==1) {
					Collection<String> mockAssignUserIds = flowAssignService.mockAssignUser(execution, this);
					if(mockAssignUserIds!=null&&mockAssignUserIds.size()==1) {
						// 如果上一环节审批人包含这一环节审批人
						if(actors.containsAll(mockAssignUserIds)) {
							this.execute(execution);
						}
					}
				}
			}
			else {
			    throw new FlowException("流程环节[" + this.getName() + "]没有找到对应审批人");
			}
		}else{
			execution.getEngine().instance().reloadInstanceUsers(execution.getInstance().getId());
			execution.getEngine().event().fireNodeEvent(FlowEvent.NODE_ENTER, execution);
		}
	}

	private boolean checkOut(ExecuteItem executeItem) {
		List<FlowTaskDO> nodeTasks = executeItem.getEngine().task().findByInstanceIdAndNodeCode(executeItem.getInstance().getId(), executeItem.getNode().getCode());
		boolean haveOpenTask = nodeTasks.stream().anyMatch(flowTask -> FlowConst.TASK_STATE_OPEN==flowTask.getState());
		return !haveOpenTask;
	}

	@Override
	public void execute(ExecuteItem execution) {
		FlowNode jumpNode = execution.getJumpNode();
		if(jumpNode!=null){
			execution.getEngine().event().fireNodeEvent(FlowEvent.NODE_EXIT, execution);
			execution.setJumpNode(null);
			List<FlowTaskDO> callbackTasks = execution.getEngine().task().findUnclosedTaskByInstanceId(execution.getInstance().getId());
			for(FlowTaskDO task : callbackTasks){
				execution.getEngine().task().close(task, execution.getOperator(), execution.getAction(), "");
				execution.setTask(task);
				execution.getEngine().event().fireNodeEvent(FlowEvent.NODE_TASK_CLOSED, execution);
			}
			jumpNode.enter(execution);
		}else{
			if(checkOut(execution)){
				execution.getEngine().event().fireNodeEvent(FlowEvent.NODE_EXIT, execution);
				runOutTransition(execution);
			}
		}
	}
	
	@Override
	public List<AssignInfo> mockEnter(MockExecuteItem execution) {
		FlowAssignService flowAssignService = execution.getEngine().assign();
		Collection<String> mockAssignUserIds = flowAssignService.mockAssignUser(execution, this);
		// 判断上一步审批是否包含本次所有审批人
		if(actorSamePass) {
			FlowAssignDO assign = execution.getEngine().assign().findByInstanceIdAndNodeCode(execution.getInstance().getId(), execution.getSourceNode().getCode());
			//只有一个处理人
			List<String> actors = assign.getActors();
			if(assign!=null && actors.size()==1) {
				if(mockAssignUserIds!=null&&mockAssignUserIds.size()==1) {
					// 如果上一环节审批人包含这一环节审批人
					if(actors.containsAll(mockAssignUserIds)) {
						return this.mockRunOutTransition(execution);
					}
				}
			}
		}
		// 判断无审批人自动跳过
		if(actorNullPass) {
			if(mockAssignUserIds==null||mockAssignUserIds.size()==0) {
				return this.mockRunOutTransition(execution);
			}
		}
		execution.setNode(this);
		
		AssignInfo assignInfo = flowAssignService.mockAssign(execution, this, mockAssignUserIds);
		
		return List.of(assignInfo);
	}

	@Override
	public List<String> check() {
		List<String> errors = new ArrayList<>();
		if(!inLines.isEmpty()){
			errors.add("环节["+getCode()+"]不应有进入连接线");
		}
		if(outLines.isEmpty()){
			errors.add("环节["+getCode()+"]没有流转连接线");
		}
		if(taskType==null){
			errors.add("没有选择审批模式");
		}
		return errors;
	}

	/**
	 * 人工处理组
	 */
	public static class TaskActorGroup {
		/**
		 * 使用条件规则
		 */
		private String condition;
		/**
		 * 参与人
		 */
		private List<TaskActor> actors;

		public String getCondition() {
			return condition;
		}

		public void setCondition(String condition) {
			this.condition = condition;
		}

		public List<TaskActor> getActors() {
			return actors;
		}

		public void setActors(List<TaskActor> actors) {
			this.actors = actors;
		}
	}

	/**
	 * 参与对象
	 */
	public static class TaskActor {
		/**
		 * 类型：部门、岗位、用户、指定部门字段、指定人员字段
		 */
		private TaskActorType type;
		/**
		 * 具体数据
		 * 部门类型：部门编号
		 * 岗位类型：岗位编号+部门级别+部门字段
		 * 用户类型：用户编号
		 * 组织结构人类型：组织结构中部门和人的关系编号
		 * 部门字段类型：部门编号编号
		 * 人员字段类型：人员字段编号
		 * 角色类型：角色编号
		 */
		private String code;

		/**
		 * 岗位类型需要的部门级别
		 */
		private Integer deptLevel;
		/**
		 * 岗位类型需要的部门字段编号
		 */
		private String deptFieldCode;

		public TaskActorType getType() {
			return type;
		}

		public void setType(TaskActorType type) {
			this.type = type;
		}

		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

		public Integer getDeptLevel() {
			return deptLevel;
		}

		public void setDeptLevel(Integer deptLevel) {
			this.deptLevel = deptLevel;
		}

		public String getDeptFieldCode() {
			return deptFieldCode;
		}

		public void setDeptFieldCode(String deptFieldCode) {
			this.deptFieldCode = deptFieldCode;
		}
	}
}
