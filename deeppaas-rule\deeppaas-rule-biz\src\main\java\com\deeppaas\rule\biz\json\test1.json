[{"stepId": "step1", "type": "DATA_GET", "returned": false, "returnVar": true, "varName": "order", "varType": "DATA_ENTITY", "varEntity": "leeLeaveApply", "name": "获取申请单", "dataEntity": "leeLeaveApply", "permits": [], "defaultCondition": {"field": "id", "operator": "EQ", "dataBind": {"targetType": "CONST", "value": "oiquweoruqwoe", "dataType": "TEXT"}}, "next": "step2"}, {"stepId": "step2", "type": "VARIABLE", "name": "临时请求", "returned": true, "returnVar": true, "varName": "tempOrder", "varType": "DATA_ENTITY", "varEntity": "leeLeaveApply", "dataBind": {"targetType": "VARS", "value": "order", "dataType": "DATA_ENTITY", "fields": [{"code": "id", "dataBind": {"targetType": "VARS", "value": "order.id", "dataType": "TEXT"}}, {"code": "leaveDays", "dataBind": {"targetType": "EXPR", "value": "vars.order.leaveDays+1", "dataType": "NUMBER"}}]}}]