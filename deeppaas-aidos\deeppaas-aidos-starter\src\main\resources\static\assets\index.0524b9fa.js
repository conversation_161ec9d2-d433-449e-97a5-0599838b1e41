import{r as t,_ as d,P as Ve,E as x,e as $,a as Oe,F as ye,f as I,b as oe,D as Re,ap as rt,C as $e,ac as Ge}from"./index.689bcdcb.js";import{E as ot}from"./EyeOutlined.70f6da72.js";import{g as nt,a as at}from"./css.032fc6ca.js";import{u as Ae,e as me,g as be}from"./reactNode.50ea6579.js";import{D as it}from"./DialogWrap.9951837c.js";import{L as st}from"./LeftOutlined.6bce5536.js";import{R as lt}from"./RightOutlined.ed31d8e3.js";function ct(a){var e=t.exports.useRef(null),o=t.exports.useState(a),r=d(o,2),i=r[0],c=r[1],s=t.exports.useRef([]),v=function(g){e.current===null&&(s.current=[],e.current=Ve(function(){c(function(f){var P=f;return s.current.forEach(function(h){P=x(x({},P),h)}),e.current=null,P})})),s.current.push(g)};return t.exports.useEffect(function(){return function(){return e.current&&Ve.cancel(e.current)}},[]),[i,v]}function Ue(a,e,o,r){var i=e+o,c=(o-r)/2;if(o>r){if(e>0)return $({},a,c);if(e<0&&i<r)return $({},a,-c)}else if(e<0||i>r)return $({},a,e<0?c:-c);return{}}function ut(a,e,o,r){var i=nt(),c=i.width,s=i.height,v=null;return a<=c&&e<=s?v={x:0,y:0}:(a>c||e>s)&&(v=x(x({},Ue("x",o,a,c)),Ue("y",r,e,s))),v}var vt=["visible","onVisibleChange","getContainer","current"],ze=t.exports.createContext({previewUrls:new Map,setPreviewUrls:function(){return null},current:null,setCurrent:function(){return null},setShowPreview:function(){return null},setMousePosition:function(){return null},registerImage:function(){return function(){return null}}}),ft=ze.Provider,pt=function(e){var o=e.previewPrefixCls,r=o===void 0?"rc-image-preview":o,i=e.children,c=e.icons,s=c===void 0?{}:c,v=e.preview,u=Oe(v)==="object"?v:{},g=u.visible,f=g===void 0?void 0:g,P=u.onVisibleChange,h=P===void 0?void 0:P,z=u.getContainer,N=z===void 0?void 0:z,k=u.current,ue=k===void 0?0:k,W=ye(u,vt),F=t.exports.useState(new Map),b=d(F,2),Z=b[0],Y=b[1],ne=t.exports.useState(),X=d(ne,2),D=X[0],B=X[1],ae=Ae(!!f,{value:f,onChange:h}),O=d(ae,2),S=O[0],_=O[1],p=t.exports.useState(null),V=d(p,2),K=V[0],R=V[1],G=f!==void 0,U=Array.from(Z.keys()),E=U[ue],q=new Map(Array.from(Z).filter(function(M){var w=d(M,2),m=w[1].canPreview;return!!m}).map(function(M){var w=d(M,2),m=w[0],j=w[1].url;return[m,j]})),J=function(w,m){var j=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,se=function(){Y(function(T){var H=new Map(T),ve=H.delete(w);return ve?H:T})};return Y(function(Q){return new Map(Q).set(w,{url:m,canPreview:j})}),se},ie=function(w){w.stopPropagation(),_(!1),R(null)};return t.exports.useEffect(function(){B(E)},[E]),t.exports.useEffect(function(){!S&&G&&B(E)},[E,G,S]),t.exports.createElement(ft,{value:{isPreviewGroup:!0,previewUrls:q,setPreviewUrls:Y,current:D,setCurrent:B,setShowPreview:_,setMousePosition:R,registerImage:J}},i,t.exports.createElement(We,I({"aria-hidden":!S,visible:S,prefixCls:r,onClose:ie,mousePosition:K,src:q.get(D),icons:s,getContainer:N},W)))},mt=["prefixCls","src","alt","onClose","afterClose","visible","icons"],je=t.exports.useState,Te=t.exports.useEffect,Se={x:0,y:0},We=function(e){var o=e.prefixCls,r=e.src,i=e.alt,c=e.onClose;e.afterClose;var s=e.visible,v=e.icons,u=v===void 0?{}:v,g=ye(e,mt),f=u.rotateLeft,P=u.rotateRight,h=u.zoomIn,z=u.zoomOut,N=u.close,k=u.left,ue=u.right,W=je(1),F=d(W,2),b=F[0],Z=F[1],Y=je(0),ne=d(Y,2),X=ne[0],D=ne[1],B=ct(Se),ae=d(B,2),O=ae[0],S=ae[1],_=t.exports.useRef(),p=t.exports.useRef({originX:0,originY:0,deltaX:0,deltaY:0}),V=t.exports.useState(!1),K=d(V,2),R=K[0],G=K[1],U=t.exports.useContext(ze),E=U.previewUrls,q=U.current,J=U.isPreviewGroup,ie=U.setCurrent,M=E.size,w=Array.from(E.keys()),m=w.indexOf(q),j=J?E.get(q):r,se=J&&M>1,Q=t.exports.useState({wheelDirection:0}),T=d(Q,2),H=T[0],ve=T[1],de=function(){Z(1),D(0),S(Se)},le=function(){Z(function(n){return n+1}),S(Se)},ee=function(){b>1&&Z(function(n){return n-1}),S(Se)},Me=function(){D(function(n){return n+90})},ge=function(){D(function(n){return n-90})},Ie=function(n){n.preventDefault(),n.stopPropagation(),m>0&&ie(w[m-1])},we=function(n){n.preventDefault(),n.stopPropagation(),m<M-1&&ie(w[m+1])},te=oe($({},"".concat(o,"-moving"),R)),re="".concat(o,"-operations-operation"),fe="".concat(o,"-operations-icon"),Ne=[{icon:N,onClick:c,type:"close"},{icon:h,onClick:le,type:"zoomIn"},{icon:z,onClick:ee,type:"zoomOut",disabled:b===1},{icon:P,onClick:Me,type:"rotateRight"},{icon:f,onClick:ge,type:"rotateLeft"}],xe=function(){if(s&&R){var n=_.current.offsetWidth*b,C=_.current.offsetHeight*b,L=_.current.getBoundingClientRect(),A=L.left,Ce=L.top,he=X%180!==0;G(!1);var Pe=ut(he?C:n,he?n:C,A,Ce);Pe&&S(x({},Pe))}},_e=function(n){n.button===0&&(n.preventDefault(),n.stopPropagation(),p.current.deltaX=n.pageX-O.x,p.current.deltaY=n.pageY-O.y,p.current.originX=O.x,p.current.originY=O.y,G(!0))},pe=function(n){s&&R&&S({x:n.pageX-p.current.deltaX,y:n.pageY-p.current.deltaY})},Le=function(n){if(!!s){n.preventDefault();var C=n.deltaY;ve({wheelDirection:C})}};return Te(function(){var l=H.wheelDirection;l>0?ee():l<0&&le()},[H]),Te(function(){var l,n,C=me(window,"mouseup",xe,!1),L=me(window,"mousemove",pe,!1),A=me(window,"wheel",Le,{passive:!1});try{window.top!==window.self&&(l=me(window.top,"mouseup",xe,!1),n=me(window.top,"mousemove",pe,!1))}catch{}return function(){C.remove(),L.remove(),A.remove(),l&&l.remove(),n&&n.remove()}},[s,R]),t.exports.createElement(it,I({transitionName:"zoom",maskTransitionName:"fade",closable:!1,keyboard:!0,prefixCls:o,onClose:c,afterClose:de,visible:s,wrapClassName:te},g),t.exports.createElement("ul",{className:"".concat(o,"-operations")},Ne.map(function(l){var n=l.icon,C=l.onClick,L=l.type,A=l.disabled;return t.exports.createElement("li",{className:oe(re,$({},"".concat(o,"-operations-operation-disabled"),!!A)),onClick:C,key:L},t.exports.isValidElement(n)?t.exports.cloneElement(n,{className:fe}):n)})),t.exports.createElement("div",{className:"".concat(o,"-img-wrapper"),style:{transform:"translate3d(".concat(O.x,"px, ").concat(O.y,"px, 0)")}},t.exports.createElement("img",{onMouseDown:_e,ref:_,className:"".concat(o,"-img"),src:j,alt:i,style:{transform:"scale3d(".concat(b,", ").concat(b,", 1) rotate(").concat(X,"deg)")}})),se&&t.exports.createElement("div",{className:oe("".concat(o,"-switch-left"),$({},"".concat(o,"-switch-left-disabled"),m===0)),onClick:Ie},k),se&&t.exports.createElement("div",{className:oe("".concat(o,"-switch-right"),$({},"".concat(o,"-switch-right-disabled"),m===M-1)),onClick:we},ue))},dt=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","crossOrigin","decoding","loading","referrerPolicy","sizes","srcSet","useMap"],gt=["src","visible","onVisibleChange","getContainer","mask","maskClassName","icons"],He=0,Ee=function(e){var o=e.src,r=e.alt,i=e.onPreviewClose,c=e.prefixCls,s=c===void 0?"rc-image":c,v=e.previewPrefixCls,u=v===void 0?"".concat(s,"-preview"):v,g=e.placeholder,f=e.fallback,P=e.width,h=e.height,z=e.style,N=e.preview,k=N===void 0?!0:N,ue=e.className,W=e.onClick,F=e.onError,b=e.wrapperClassName,Z=e.wrapperStyle,Y=e.crossOrigin,ne=e.decoding,X=e.loading,D=e.referrerPolicy,B=e.sizes,ae=e.srcSet,O=e.useMap,S=ye(e,dt),_=g&&g!==!0,p=Oe(k)==="object"?k:{},V=p.src,K=p.visible,R=K===void 0?void 0:K,G=p.onVisibleChange,U=G===void 0?i:G,E=p.getContainer,q=E===void 0?void 0:E,J=p.mask,ie=p.maskClassName,M=p.icons,w=ye(p,gt),m=V!=null?V:o,j=R!==void 0,se=Ae(!!R,{value:R,onChange:U}),Q=d(se,2),T=Q[0],H=Q[1],ve=t.exports.useState(_?"loading":"normal"),de=d(ve,2),le=de[0],ee=de[1],Me=t.exports.useState(null),ge=d(Me,2),Ie=ge[0],we=ge[1],te=le==="error",re=t.exports.useContext(ze),fe=re.isPreviewGroup,Ne=re.setCurrent,xe=re.setShowPreview,_e=re.setMousePosition,pe=re.registerImage,Le=t.exports.useState(function(){return He+=1,He}),l=d(Le,1),n=l[0],C=k&&!te,L=t.exports.useRef(!1),A=function(){ee("normal")},Ce=function(y){F&&F(y),ee("error")},he=function(y){if(!j){var ke=at(y.target),Ze=ke.left,De=ke.top;fe?(Ne(n),_e({x:Ze,y:De})):we({x:Ze,y:De})}fe?xe(!0):H(!0),W&&W(y)},Pe=function(y){y.stopPropagation(),H(!1),j||we(null)},Je=function(y){L.current=!1,le==="loading"&&(y==null?void 0:y.complete)&&(y.naturalWidth||y.naturalHeight)&&(L.current=!0,A())};t.exports.useEffect(function(){var ce=pe(n,m);return ce},[]),t.exports.useEffect(function(){pe(n,m,C)},[m,C]),t.exports.useEffect(function(){te&&ee("normal"),_&&!L.current&&ee("loading")},[o]);var Qe=oe(s,b,$({},"".concat(s,"-error"),te)),et=te&&f?f:m,tt={crossOrigin:Y,decoding:ne,loading:X,referrerPolicy:D,sizes:B,srcSet:ae,useMap:O,alt:r,className:oe("".concat(s,"-img"),$({},"".concat(s,"-img-placeholder"),g===!0),ue),style:x({height:h},z)};return t.exports.createElement(t.exports.Fragment,null,t.exports.createElement("div",I({},S,{className:Qe,onClick:C?he:W,style:x({width:P,height:h},Z)}),t.exports.createElement("img",I({},tt,{ref:Je},te&&f?{src:f}:{onLoad:A,onError:Ce,src:o})),le==="loading"&&t.exports.createElement("div",{"aria-hidden":"true",className:"".concat(s,"-placeholder")},g),J&&C&&t.exports.createElement("div",{className:oe("".concat(s,"-mask"),ie)},J)),!fe&&C&&t.exports.createElement(We,I({"aria-hidden":!T,visible:T,prefixCls:u,onClose:Pe,mousePosition:Ie,src:et,alt:r,getContainer:q,icons:M},w)))};Ee.PreviewGroup=pt;Ee.displayName="Image";var wt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},xt=wt,Fe=function(e,o){return t.exports.createElement(Re,x(x({},e),{},{ref:o,icon:xt}))};Fe.displayName="RotateLeftOutlined";var Ct=t.exports.forwardRef(Fe),ht={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},Pt=ht,Ye=function(e,o){return t.exports.createElement(Re,x(x({},e),{},{ref:o,icon:Pt}))};Ye.displayName="RotateRightOutlined";var St=t.exports.forwardRef(Ye),yt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"},bt=yt,Xe=function(e,o){return t.exports.createElement(Re,x(x({},e),{},{ref:o,icon:bt}))};Xe.displayName="ZoomInOutlined";var Ot=t.exports.forwardRef(Xe),Rt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"},Et=Rt,Be=function(e,o){return t.exports.createElement(Re,x(x({},e),{},{ref:o,icon:Et}))};Be.displayName="ZoomOutOutlined";var Mt=t.exports.forwardRef(Be),It=globalThis&&globalThis.__rest||function(a,e){var o={};for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&e.indexOf(r)<0&&(o[r]=a[r]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(a);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(a,r[i])&&(o[r[i]]=a[r[i]]);return o},Ke={rotateLeft:t.exports.createElement(Ct,null),rotateRight:t.exports.createElement(St,null),zoomIn:t.exports.createElement(Ot,null),zoomOut:t.exports.createElement(Mt,null),close:t.exports.createElement(rt,null),left:t.exports.createElement(st,null),right:t.exports.createElement(lt,null)},Nt=function(e){var o=e.previewPrefixCls,r=e.preview,i=It(e,["previewPrefixCls","preview"]),c=t.exports.useContext($e),s=c.getPrefixCls,v=s("image-preview",o),u=s(),g=t.exports.useMemo(function(){if(r===!1)return r;var f=Oe(r)==="object"?r:{};return I(I({},f),{transitionName:be(u,"zoom",f.transitionName),maskTransitionName:be(u,"fade",f.maskTransitionName)})},[r]);return t.exports.createElement(Ee.PreviewGroup,I({preview:g,previewPrefixCls:v,icons:Ke},i))},_t=Nt,Lt=globalThis&&globalThis.__rest||function(a,e){var o={};for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&e.indexOf(r)<0&&(o[r]=a[r]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(a);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(a,r[i])&&(o[r[i]]=a[r[i]]);return o},qe=function(e){var o=e.prefixCls,r=e.preview,i=Lt(e,["prefixCls","preview"]),c=t.exports.useContext($e),s=c.getPrefixCls,v=s("image",o),u=s(),g=t.exports.useContext($e),f=g.locale,P=f===void 0?Ge:f,h=P.Image||Ge.Image,z=t.exports.useMemo(function(){if(r===!1)return r;var N=Oe(r)==="object"?r:{};return I(I({mask:t.exports.createElement("div",{className:"".concat(v,"-mask-info")},t.exports.createElement(ot,null),h==null?void 0:h.preview),icons:Ke},N),{transitionName:be(u,"zoom",N.transitionName),maskTransitionName:be(u,"fade",N.maskTransitionName)})},[r,h]);return t.exports.createElement(Ee,I({prefixCls:v,preview:z},i))};qe.PreviewGroup=_t;var Ut=qe;export{Ut as I};
