package com.deeppaas.template.biz.service;

import com.deeppaas.template.biz.dao.AidosImageTemplateDao;
import com.deeppaas.template.biz.dao.AidosImageTemplateTypeDao;
import com.deeppaas.template.biz.dao.AidosIntermediateTemplateTypeDao;
import com.deeppaas.template.biz.entity.AidosImageTemplateTypeDO;
import com.deeppaas.template.biz.entity.AidosIntermediateTemplateTypeDO;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
public class AidosImageTemplateTypeService {

    @Autowired
    private AidosImageTemplateTypeDao aidosImageTemplateTypeDao;
    @Autowired
    private AidosImageTemplateDao aidosImageTemplateDao;
    @Autowired
    private AidosIntermediateTemplateTypeDao aidosIntermediateTemplateTypeDao;

    public List<AidosImageTemplateTypeDO> getAll() {
        return aidosImageTemplateTypeDao.findAll();
    }

    public String save(AidosImageTemplateTypeDO aidosImageTemplateTypeDO) {
        String id = aidosImageTemplateTypeDO.getId();

//        if (isIdenticalName(aidosImageTemplateTypeDO))
//            return "已存在相同名称【" + aidosImageTemplateTypeDO.getName() + "】";
        if (StringHelper.isEmpty(id)) {
            aidosImageTemplateTypeDO.setCreateTime(LocalDateTime.now());
            aidosImageTemplateTypeDao.insert(aidosImageTemplateTypeDO);
        } else {
            aidosImageTemplateTypeDao.save(aidosImageTemplateTypeDO);
        }
        return "";
    }

    private boolean isIdenticalName(AidosImageTemplateTypeDO source) {
        String id = source.getId();
        AidosImageTemplateTypeDO name = aidosImageTemplateTypeDao.findByName(source.getName());
        if (name == null)
            return false;
        if (Objects.equals(name.getId(), id)) {
            return false;
        }
        return true;
    }

    public void insert(List<AidosImageTemplateTypeDO> aidosImageTemplateTypeDO) {
        aidosImageTemplateTypeDao.insertAll(aidosImageTemplateTypeDO);
    }

    public void del(String id) {
        aidosImageTemplateTypeDao.deleteById(id);
    }

    public PageData<AidosImageTemplateTypeDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<AidosImageTemplateTypeDO> page = aidosImageTemplateTypeDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    public List<AidosImageTemplateTypeDO> findAllById(String... id) {
        return (List<AidosImageTemplateTypeDO>) aidosImageTemplateTypeDao.findAllById(Arrays.asList(id));
    }

    public AidosImageTemplateTypeDO get(String id) {
        return aidosImageTemplateTypeDao.getById(id);
    }

    public List<AidosImageTemplateTypeDO> getByProcessId(String processId) {
        return aidosImageTemplateTypeDao.findByProcessTaskId(processId);
    }

    public List<AidosIntermediateTemplateTypeDO> findByProcessTaskId(String processTaskId, String typeId) {
        List<AidosIntermediateTemplateTypeDO> intermediates=aidosIntermediateTemplateTypeDao.findByProcessTaskIdAndImageTemplateTypeId(processTaskId, typeId);
        return intermediates;
    }

    public List<AidosIntermediateTemplateTypeDO> getInterByProcessId(String processId) {
        return aidosIntermediateTemplateTypeDao.findByProcessTaskId(processId);
    }

    public void save(List<AidosIntermediateTemplateTypeDO> aidosIntermediateTemplateTypeDO) {
        aidosIntermediateTemplateTypeDao.saveAll(aidosIntermediateTemplateTypeDO);
    }

    public void delAidosIntermediate(String id) {
        aidosIntermediateTemplateTypeDao.deleteById(id);
    }

    public void delAidosIntermediate(String imageTemplateTypeId, String imageTemplateId) {

        aidosIntermediateTemplateTypeDao.deleteByProcessTaskIdAndImageTemplateId(imageTemplateTypeId, imageTemplateId);
    }
}
