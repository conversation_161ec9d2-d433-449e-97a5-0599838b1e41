package com.deeppaas.flow.biz;

/**
 * @ClassName: FlowAction 
 * @Description: 流程动作
 * <AUTHOR>
 */
public enum FlowAction {
	/** 提交，启动流程实例 */
	submit(1),
	/** 审批，处理人工任务 */
	execute(2),
	/** 驳回，处理人工任务 */
	back(3),
	/** 撤单，由发起人自行关闭流程实例 */
	cancel(4),
	/** 中止，由管理员或指定参与人直接关闭流程实例 */
	stop(5),
	/** 跳转，一般用于流程特殊处理 */
	jump(6),
	/** 加签，一般用于在某环节增加处理任务 */
	addSign(7),
	/** 转办，用于任务更换处理人 */
	transfer(8),
	/** 撤回 */
	takeBack(9);
	
	private int intValue;
	
	private FlowAction(int intValue){
		this.intValue = intValue;
	}
	public int intValue() {
		return intValue;
	}
}
