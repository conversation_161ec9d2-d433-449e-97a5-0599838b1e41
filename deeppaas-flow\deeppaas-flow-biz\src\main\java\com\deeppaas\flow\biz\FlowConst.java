package com.deeppaas.flow.biz;

/**
 * 流程常量
 * <AUTHOR>
 */
public class FlowConst {
    /**
     * 流程定义-待发布（草稿）
     */
    public static final int DEFINE_STATE_EDIT = 0;
    /**
     * 流程定义-已发布（当前有效）
     */
    public static final int DEFINE_STATE_ISSUE = 1;
    /**
     * 流程定义-废弃状态（过期版本）
     */
    public static final int DEFINE_STATE_OVERDUE = 2;

    /** 任务状态-待办 */
    public static final int TASK_STATE_OPEN = 1;
    /** 任务状态-关闭 */
    public static final int TASK_STATE_CLOSE = 0;
    /** 任务状态-待开启 */
    public static final int TASK_STATE_WAIT = 2;

}
