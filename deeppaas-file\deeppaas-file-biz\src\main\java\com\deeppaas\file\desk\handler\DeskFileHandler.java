package com.deeppaas.file.desk.handler;

import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.file.FileConstant;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.api.model.FileEntity;
import com.deeppaas.file.api.model.UploadSign;
import com.deeppaas.file.biz.entity.FileGroupDO;
import com.deeppaas.file.desk.model.DeskFileEntity;
import com.deeppaas.file.desk.model.DeskFileSign;
import com.deeppaas.file.desk.model.DeskTempUrlReq;
import com.deeppaas.file.desk.model.DeskUploadSign;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
@Component
public class DeskFileHandler {

    public UploadSign getUploadSign(FileGroup fileGroup, String fileDir, int expireSeconds) {
        String expireTime = System.currentTimeMillis() + expireSeconds*1000 +"";
        DeskUploadSign selfUploadSign = new DeskUploadSign();
        selfUploadSign.setFileGroup(fileGroup.getName());
        selfUploadSign.setDir(fileDir);
        selfUploadSign.setHost(fileGroup.getDomain());
        selfUploadSign.setExpireTime(expireTime);
        selfUploadSign.setSign(DeskFileSign.getUploadSign(fileGroup.getName(), fileDir, expireTime));
        return selfUploadSign;
    }

    public void uploadFile(FileGroup fileGroup, String fileKey, File file) {
        try {
            File saveFile = new File(fileGroup.getBucket()+"/"+fileKey);
            if(!saveFile.exists()){
                saveFile.createNewFile();
            }
            FileCopyUtils.copy(file, saveFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void uploadFile(FileGroup fileGroup, String fileKey, InputStream inputStream) {
        try {
            File saveFile = new File(fileGroup.getBucket()+"/"+fileKey);
            if(!saveFile.exists()){
                if(!saveFile.getParentFile().exists()){
                    saveFile.getParentFile().mkdirs();
                }
                saveFile.createNewFile();
            }
            FileOutputStream outputStream = new FileOutputStream(saveFile);
            FileCopyUtils.copy(inputStream, outputStream);
            inputStream.close();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public FileEntity getFile(FileGroup fileGroup, String fileKey) {
        String filePath = fileGroup.getBucket()+"/"+fileKey;
        File file = new File(filePath);
        if(file.exists()){
            return new DeskFileEntity(fileGroup.getName(), fileKey, filePath);
        }
        return null;
    }

    public String getTempUrl(FileGroup fileGroup, String fileKey, String fileAlias, String process) {
        return getTempUrl(fileGroup, fileKey, fileAlias, process, 10*60);
    }

    public String getTempUrl(FileGroup fileGroup, String fileKey, String fileAlias, String process, int expireSeconds) {
        try{
            String tempUrl = null;
            if(FileConstant.AUTH_PUBLIC.equals(fileGroup.getType())){
                tempUrl = fileGroup.getDomain() + "/" +fileGroup.getName()+"/"+ fileKey;
                if(StringUtils.hasText(process)){
                    tempUrl += "?"+ DeskTempUrlReq.KEY_PROCESS+"="+process;
                }
                String[] parts = StringHelper.split(tempUrl, "/");
                StringBuffer urlBuffer = new StringBuffer();
                for(int i=0;i<parts.length;i++){
                    if(i>0){
                        urlBuffer.append("/");
                    }
                    urlBuffer.append(URLEncoder.encode(parts[i], "UTF-8"));
                }
                tempUrl = urlBuffer.toString();
                return tempUrl;
            }else {
                long expireTime = System.currentTimeMillis() + expireSeconds*1000;
                DeskTempUrlReq req = new DeskTempUrlReq(fileGroup.getName(), fileKey, fileAlias, process, expireTime, null);
                tempUrl = fileGroup.getDomain() + "/" +fileGroup.getName()+"/"+ req.getFileKey();
                String[] parts = StringHelper.split(tempUrl, "/");
                StringBuffer urlBuffer = new StringBuffer();
                for(int i=0;i<parts.length;i++){
                    if(i>0){
                        urlBuffer.append("/");
                    }
                    urlBuffer.append(URLEncoder.encode(parts[i], "UTF-8"));
                }
                tempUrl = urlBuffer.toString();
                List<String> queryList = Lists.newArrayList();
                if(StringUtils.hasText(req.getFileAlias())){
                    queryList.add(DeskTempUrlReq.KEY_FILE_ALIAS+"="+ URLEncoder.encode(req.getFileAlias(), "Utf-8"));
                }
                if(StringUtils.hasText(req.getProcess())){
                    queryList.add(DeskTempUrlReq.KEY_PROCESS+"="+req.getProcess());
                }
                queryList.add(DeskTempUrlReq.KEY_EXPIRE_TIME+"="+req.getExpireTime());
                queryList.add(DeskTempUrlReq.KEY_SIGN +"="+DeskFileSign.getTempUrlSign(req));
                if(!queryList.isEmpty()){
                    tempUrl += "?" + StringHelper.join(queryList, "&");
                }
                return tempUrl;
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public void deleteFile(FileGroup fileGroup, String fileKey) {
        String filePath = fileGroup.getBucket()+"/"+fileKey;
        File file = new File(filePath);
        if(file.exists()){
            file.deleteOnExit();
        }
    }

}
