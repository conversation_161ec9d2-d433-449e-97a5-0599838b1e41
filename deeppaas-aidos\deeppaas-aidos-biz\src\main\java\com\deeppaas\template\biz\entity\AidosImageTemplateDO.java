package com.deeppaas.template.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import org.springframework.data.relational.core.sql.In;

@Data
@Table("aidos_image_template")
public class AidosImageTemplateDO extends BaseUuidDO {
    /**
     * 模板名称
     */
    @Column("name")
    private String name;
    /**
     * 首字母
     */
    @Column("initials")
    private String initials;

    /**
     * 模板别名
     */
    @Column("alias")
    private String alias;

    /**
     * 模板别名
     */
    @Column("label")
    private String label;
    /**
     * 路径
     * */
    @Column("path")
    private String path;

    /**
     * 上传人
     * */
    @Column("upload_user_id")
    private Integer uploadUserId;

    /**
     * 上传人
     * */
    @Column("upload_user_name")
    private String uploadUserName;
}
