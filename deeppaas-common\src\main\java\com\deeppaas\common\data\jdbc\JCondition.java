package com.deeppaas.common.data.jdbc;

import com.deeppaas.common.data.Operator;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */
public abstract class JCondition {
    /**
     * 计算符
     */
    private final Operator operator;

    JCondition(Operator operator) {
        this.operator = operator;
    }

    /**
     * 直接使用原生SQL
     * @param sql
     * @param params
     * @return
     */
    public static JCondition just(String sql, Map<String, Object> params) {
        return new JConditionC(sql, params);
    }

    public Operator getOperator() {
        return operator;
    }

    /**
     * 等于
     */
    public static JCondition eq(String fieldName, Object value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.EQ);
    }

    /**
     * 不等于
     */
    public static JCondition ne(String fieldName, Object value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.NE);
    }

    /**
     * 大于
     */
    public static JConditionS gt(String fieldName, Object value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.GT);
    }

    /**
     * 小于
     */
    public static JConditionS lt(String fieldName, Object value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.LT);
    }

    /**
     * 小于等于
     */
    public static JConditionS lte(String fieldName, Object value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.LTE);
    }

    /**
     * 大于等于
     */
    public static JConditionS gte(String fieldName, Object value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.GTE);
    }

    /**
     * 值为null
     */
    public static JConditionS isNull(String fieldName) {
        return JConditionS.of(fieldName, null, Operator.ISNULL);
    }

    /**
     * 值不为Null
     */
    public static JConditionS isNotNull(String fieldName) {
        return JConditionS.of(fieldName, null, Operator.NOTNULL);
    }

    /**
     * 并且
     */
    public static JConditionL and(JCondition... conditions) {
        return new JConditionL(conditions, Operator.AND);
    }

    /**
     * 或者
     */
    public static JConditionL or(JCondition... conditions) {
        return new JConditionL(conditions, Operator.OR);
    }

    /**
     * 包含于
     */
    public static JCondition in(String fieldName, Collection value, boolean ignoreNull) {
        if (CollectionUtils.isEmpty(value)) {
            if (ignoreNull) {
                return null;
            } else {
                JCondition[] conditions = new JCondition[2];
                conditions[0] = JConditionS.of(fieldName, null, Operator.ISNULL);
                conditions[1] = JConditionS.of(fieldName, null, Operator.NOTNULL);
                return new JConditionL(conditions, Operator.AND);
            }
        } else {
            return JConditionS.of(fieldName, value, Operator.IN);
        }
    }

    /**
     * 不包含
     */
    public static JCondition notIn(String fieldName, Collection value, boolean ignoreNull) {
        if (ignoreNull && (value == null || value.isEmpty())) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.NOTIN);
    }

    /**
     * 字符串结束
     */
    public static JConditionS contains(String fieldName, String value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.CONTAINS);
    }

    /**
     * 字符串结束
     */
    public static JConditionS notContains(String fieldName, String value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.NOTCONTAINS);
    }

    /**
     * 字符串开始
     */
    public static JConditionS startWith(String fieldName, String value, boolean ignoreNull) {
        if (ignoreNull && isEmpty(value)) {
            return null;
        }
        return JConditionS.of(fieldName, value, Operator.STARTWITH);
    }

    public abstract Map<String, Object> getBindParam();

    public void sortMarkNum() {
        AtomicInteger markNum = new AtomicInteger();
        sortMarkNum(markNum);
    }

    abstract void sortMarkNum(AtomicInteger markNum);

    private static boolean isEmpty(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String str) {
            return !StringUtils.hasText(str);
        }
        return false;
    }

    public static JCondition ofPageParam(Map<String, Object> paramMap) {
        JConditionL jCondition = new JConditionL(Operator.AND);
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            // 过滤掉空值
            String key = entry.getKey();
            Object value = entry.getValue();
            // 拆分operator与filedAttribute
            String[] names = StringUtils.split(key, "_");
            if (names.length < 2) {
                throw new IllegalArgumentException(key + " is not a valid search filter name");
            }
            String filedName = "";
            Operator operator = null;
            for (int i = 0; i < names.length; i++) {
                if (i == 0) {
                    operator = Operator.valueOf(names[i]);
                } else if (i == 1) {
                    filedName += names[i];
                } else {
                    filedName = filedName.concat(".").concat(names[i]);
                }
            }
            if (value.getClass().isArray()) {
                String[] strValue = (String[]) value;
                jCondition.add(in(filedName, List.of(strValue), true));
            } else {
                jCondition.add(new JConditionS(filedName, value, operator));
            }
        }
        return jCondition;
    }
}
