package com.deeppaas.common.model;

import com.deeppaas.common.helper.StringHelper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @param <T> 分页包含泛型类
 * <AUTHOR>
 * @ClassName: PageData
 * @Description: 分页结果模型包装类
 * @date 2017年7月31日
 */
public class PageData<T> {
    private static final long serialVersionUID = 8648378576915680990L;
    private Integer pageNo;        //页号
    private Integer pageSize;    //每页数据数量
    private String sort;        //排序
    private long total;            //总数据数量
    private long totalPages;    //总页数
    private List<T> list;        //查询结果项

    public PageData() {

    }

    public PageData(Page<T> page) {
        this.pageNo = page.getNumber() + 1;
        this.pageSize = page.getSize();
        this.sort = toSortStr(page.getSort());
        this.total = page.getTotalElements();
        this.totalPages = page.getTotalPages();
        this.list = page.getContent();
    }

    public static <T> PageData<T> init(Page<T> page) {
        return new PageData<T>(page);
    }

    public static <T> PageData<T> init(Pageable page, List<T> list) {
        PageData<T> pageData = new PageData<T>();
        pageData.setPageNo(page.getPageNumber() + 1);
        pageData.setPageSize(page.getPageSize());
        pageData.setSort(toSortStr(page.getSort()));
        pageData.setTotal(0);
        pageData.setTotalPages(0);
        pageData.setList(list);
        return pageData;
    }

    public static <T> PageData<T> init(Page<?> page, List<T> list) {
        PageData<T> pageData = new PageData<T>();
        pageData.setPageNo(page.getNumber() + 1);
        pageData.setPageSize(page.getSize());
        pageData.setSort(toSortStr(page.getSort()));
        pageData.setTotal(page.getTotalElements());
        pageData.setTotalPages(page.getTotalPages());
        pageData.setList(list);
        return pageData;
    }

    public static <T> PageData<T> init(PageData<?> page, List<T> list) {
        PageData<T> pageData = new PageData<T>();
        pageData.setPageNo(page.getPageNo());
        pageData.setPageSize(page.getPageSize());
        pageData.setSort(page.getSort());
        pageData.setTotal(page.getTotal());
        pageData.setTotalPages(page.getTotalPages());
        pageData.setList(list);
        return pageData;
    }

    public static <T> PageData<T> empty(PageSearch pageSearch) {
        PageData<T> pageData = new PageData<T>();
        pageData.setPageNo(pageSearch.getPageNo());
        pageData.setPageSize(pageSearch.getPageSize());
        pageData.setSort(pageSearch.getSortInfo());
        pageData.setTotal(0);
        pageData.setTotalPages(0);
        pageData.setList(null);
        return pageData;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(long totalPages) {
        this.totalPages = totalPages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    private static String toSortStr(Sort sort) {
        if (sort == null) {
            return "";
        }
        Iterator<Order> orderIt = sort.iterator();
        List<String> sortStrList = new ArrayList<>();
        while (orderIt.hasNext()) {
            Order order = orderIt.next();
            sortStrList.add(order.getDirection().name() + "_" + order.getProperty());
        }
        return StringHelper.join(sortStrList, ",");
    }
}
