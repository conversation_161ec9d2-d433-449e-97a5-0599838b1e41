[{"stepId": "step1", "type": "DECISION", "name": "是否是年假", "target": {"type": "FUN", "value": {"name": "Equals", "params": [{"type": "CONST", "value": "年假"}, {"type": "FIELD", "value": "applyType"}]}}, "next": {"tureNext": "step3", "falseNext": "RETURN"}}, {"stepId": "step3", "type": "VARIABLE", "name": "当前年", "varName": "varNowYear", "target": {"type": "FUN", "value": {"name": "GetCurDate", "params": [{"type": "CONST", "value": "yyyy"}]}}, "next": "step4"}, {"stepId": "step4", "type": "VARIABLE", "name": "当前人编号", "varName": "varUserCode", "target": {"type": "FUN", "value": {"name": "GetCurUserProperty", "params": [{"type": "CONST", "value": "code"}]}}, "next": "step5"}, {"stepId": "step5", "type": "DATA_UPDATE", "name": "扣除剩余年假天数", "dataEntity": "ET_UserAnnualLeave", "condition": {"operator": "AND", "conditions": [{"field": "userCode", "operator": "EQ", "target": {"type": "VAR", "value": "varUserCode"}}, {"field": "year", "operator": "EQ", "target": {"type": "VAR", "value": "varNowYear"}}]}, "updateFields": [{"field": "remainDays", "target": {"type": "EXPR", "value": "effect.remainDays - data.applyDays"}}], "next": null}, {"stepId": "RETURN", "type": "RETURN", "name": "返回", "next": null}]