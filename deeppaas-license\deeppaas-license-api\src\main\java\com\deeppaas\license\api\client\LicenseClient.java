package com.deeppaas.license.api.client;

import com.deeppaas.license.api.model.License;

/**
 * <AUTHOR>
 * @date 2022/11/1
 */
public interface LicenseClient {
    /**
     * 获取license
     * @return
     */
    License getLicense();

    /**
     * 增加在线用户信息,同时检查在线用户数，如果超过授权运行最大用户数则返回false
     * @param userId 用户唯一标识
     * @param clientId 用户设备唯一标识
     * @param expireTime 到秒时间戳
     * @return
     */
    boolean addOnlineUser(String userId, String clientId, long expireTime);

    /**
     * 清除在线用户
     * @param userId 用户唯一标识
     */
    void removeOnlineUser(String userId);

    /**
     * 刷新指定clientId的在线用户信息，如果clientId与当前记录clientId不匹配则返回false
     * @param userId
     * @param clientId
     * @param expireTime 到秒时间戳
     * @return
     */
    boolean refreshOnlineUser(String userId, String clientId, long expireTime);
}
