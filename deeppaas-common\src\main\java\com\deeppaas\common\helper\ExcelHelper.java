package com.deeppaas.common.helper;

import org.apache.poi.hssf.usermodel.HSSFFormulaEvaluator;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFFormulaEvaluator;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * Excel工具
 * <AUTHOR>
 * @date 2020/6/12
 */
public class ExcelHelper {
    private static final DataFormatter formatter = new DataFormatter();

    /**
     * 读取单元格文本形式信息
     * @param: cell
     * @return: java.lang.String
     * <AUTHOR>
     * @date 2020/6/12 3:58 下午
     **/
    public static String getCellValue(Cell cell){
        if(cell==null){
            return "";
        }
        Workbook workbook = cell.getSheet().getWorkbook();
        FormulaEvaluator formulaEvaluator = null;
        if(workbook instanceof HSSFWorkbook){
            formulaEvaluator = new HSSFFormulaEvaluator((HSSFWorkbook)workbook);
        }
        if(workbook instanceof XSSFWorkbook){
            formulaEvaluator = new XSSFFormulaEvaluator((XSSFWorkbook)workbook);
        }
        if(workbook instanceof SXSSFWorkbook){
            formulaEvaluator = new SXSSFFormulaEvaluator((SXSSFWorkbook)workbook);
        }
        return formatter.formatCellValue(cell, formulaEvaluator);
    }
}
