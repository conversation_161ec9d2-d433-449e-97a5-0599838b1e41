package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据表文件夹
 *
 * <AUTHOR>
 * @date 2022/07/30
 */
@Data
@Table("datastore_folder")
@EqualsAndHashCode(callSuper = true)
public class DataStoreFolderDO extends BaseUuidDO {
    /**
     * 一级文件夹父部门ID
     */
    public static final String TOP_PARENT_ID = "ROOT";

    /**
     * 文件夹名称
     */
    @Column("name")
    private String name;

    /**
     * 父文件夹ID
     */
    @Column("parent_id")
    private String parentId;

    /**
     * 是否可用
     */
    @Column("usable")
    private Integer usable;

    /**
     * 排序号
     */
    @Column("sort_no")
    private Integer sortNo;
}
