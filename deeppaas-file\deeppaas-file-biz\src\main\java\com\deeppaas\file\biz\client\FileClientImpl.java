package com.deeppaas.file.biz.client;

import com.deeppaas.file.FileConstant;
import com.deeppaas.file.api.client.FileClient;
import com.deeppaas.file.api.config.FileGroup;
import com.deeppaas.file.api.model.FileEntity;
import com.deeppaas.file.api.model.UploadSign;
import com.deeppaas.file.biz.convert.FileGroupConvert;
import com.deeppaas.file.biz.entity.FileGroupDO;
import com.deeppaas.file.biz.service.FileGroupService;
import com.deeppaas.file.desk.handler.DeskFileHandler;
import com.deeppaas.file.oss.handler.OssFileHandler;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
@Component
public class FileClientImpl implements FileClient {
    private static final int defaultExpireSeconds = 600;
    private final FileGroupService fileGroupService;
    private final DeskFileHandler deskFileHandler;
    private final OssFileHandler ossFileHandler;

    public FileClientImpl(FileGroupService fileGroupService, DeskFileHandler deskFileHandler, OssFileHandler ossFileHandler) {
        this.fileGroupService = fileGroupService;
        this.deskFileHandler = deskFileHandler;
        this.ossFileHandler = ossFileHandler;
    }

    @Override
    public FileGroup getFileGroup(String name) {
        FileGroupDO fileGroupDO = fileGroupService.getGroupDO(name);
        return FileGroupConvert.buildFileGroup(fileGroupDO);
    }

    @Override
    public UploadSign getUploadSign(String groupName, String fileDir) {
        return getUploadSign(groupName, fileDir, defaultExpireSeconds);
    }

    @Override
    public UploadSign getUploadSign(String groupName, String fileDir, int expireSeconds) {
        FileGroup groupDO = fileGroupService.getGroup(groupName);
        if(FileConstant.MODE_DESK.equalsIgnoreCase(groupDO.getMode())){
            return deskFileHandler.getUploadSign(groupDO, fileDir, defaultExpireSeconds);
        }else if (FileConstant.MODE_OSS.equalsIgnoreCase(groupDO.getMode())){
            return ossFileHandler.getUploadSign(groupDO, fileDir, defaultExpireSeconds);
        }
        return null;
    }

    @Override
    public void uploadFile(String groupName, String fileKey, File file) {
        FileGroup groupDO = fileGroupService.getGroup(groupName);
        if(FileConstant.MODE_DESK.equalsIgnoreCase(groupDO.getMode())){
            deskFileHandler.uploadFile(groupDO, fileKey, file);
        }else if (FileConstant.MODE_OSS.equalsIgnoreCase(groupDO.getMode())){
            ossFileHandler.uploadFile(groupDO, fileKey, file);
        }
    }

    @Override
    public void uploadFile(String groupName, String fileKey, InputStream input) {
        FileGroup groupDO = fileGroupService.getGroup(groupName);
        if(FileConstant.MODE_DESK.equalsIgnoreCase(groupDO.getMode())){
            deskFileHandler.uploadFile(groupDO, fileKey, input);
        }else if (FileConstant.MODE_OSS.equalsIgnoreCase(groupDO.getMode())){
            ossFileHandler.uploadFile(groupDO, fileKey, input);
        }
    }

    @Override
    public FileEntity getFile(String groupName, String fileKey) {
        FileGroup fileGroup = fileGroupService.getGroup(groupName);
        if(FileConstant.MODE_DESK.equalsIgnoreCase(fileGroup.getMode())){
            return deskFileHandler.getFile(fileGroup, fileKey);
        }else if (FileConstant.MODE_OSS.equalsIgnoreCase(fileGroup.getMode())){
            return ossFileHandler.getFile(fileGroup, fileKey);
        }
        return null;
    }

    @Override
    public String getTempUrl(String groupName, String fileKey, String fileAlias, String process) {
        return getTempUrl(groupName, fileKey, fileAlias, process, defaultExpireSeconds);
    }

    @Override
    public String getTempUrl(String groupName, String fileKey, String fileAlias, String process, int expireSeconds) {
        FileGroup fileGroup = fileGroupService.getGroup(groupName);
        if(FileConstant.MODE_DESK.equalsIgnoreCase(fileGroup.getMode())){
            return deskFileHandler.getTempUrl(fileGroup, fileKey, fileAlias, process, expireSeconds);
        }else if (FileConstant.MODE_OSS.equalsIgnoreCase(fileGroup.getMode())){
            return ossFileHandler.getTempUrl(fileGroup, fileKey, fileAlias, process, expireSeconds);
        }
        return null;
    }

    @Override
    public void deleteFile(String groupName, String fileKey) {
        FileGroup fileGroup = fileGroupService.getGroup(groupName);
        if(FileConstant.MODE_DESK.equalsIgnoreCase(fileGroup.getMode())){
            deskFileHandler.deleteFile(fileGroup, fileKey);
        }else if (FileConstant.MODE_OSS.equalsIgnoreCase(fileGroup.getMode())){
            ossFileHandler.deleteFile(fileGroup, fileKey);
        }
    }
}
