package com.deeppaas.datastore.biz.web.view;

import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.view.AbstractView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据模型导出JSON视图
 * <AUTHOR>
 * @date 2022/9/26
 */
public class DataStoreDataJsonView extends AbstractView {
    public static final String MODEL_KEY_ENTITY = "ENTITY";
    public static final String MODEL_KEY_FIELDS = "FIELDS";
    public static final String MODEL_KEY_LIMIT_FIELDS = "LIMIT_FIELDS";
    public static final String MODEL_KEY_DATA = "DATA";

    @Override
    protected void renderMergedOutputModel(Map<String, Object> model, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String entityName = (String) model.get(MODEL_KEY_ENTITY);
        List<String> limitFields = (List<String>) model.get(MODEL_KEY_LIMIT_FIELDS);
        List<DataStoreEntityFieldDTO> fieldDTOS = (List<DataStoreEntityFieldDTO>) model.get(MODEL_KEY_FIELDS);
        List<DataStoreData> dataList = (List<DataStoreData>) model.get(MODEL_KEY_DATA);

        Map<String, DataStoreEntityFieldDTO> fieldMap = fieldDTOS.stream().collect(Collectors.toMap(DataStoreEntityFieldDTO::getCode, Function.identity()));
        if(!CollectionUtils.isEmpty(limitFields)){
            for(String fieldCode : limitFields){
                fieldMap.remove(fieldCode);
            }
        }

        List<Map<String, Object>> exportList = new ArrayList<>();

        for (DataStoreData data : dataList){
            Map<String, Object> exportMap = new HashMap<>();
            for(String fieldCode : fieldMap.keySet()){
                exportMap.put(fieldMap.get(fieldCode).getName(), data.get(fieldCode));
            }
            exportList.add(exportMap);
        }

        String fileName = entityName+".json";
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename="+URLEncoder.encode(fileName, "UTF-8"));
        response.getOutputStream().write(JsonHelper.toJsonByte(exportList));
    }
}
