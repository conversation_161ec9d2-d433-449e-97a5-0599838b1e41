package com.deeppaas.datastore.biz.service;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.LogerHelper;
import com.deeppaas.datastore.biz.entity.DataStoreTableDO;
import com.deeppaas.datastore.biz.entity.DataStoreTableFieldDO;
import com.deeppaas.datastore.biz.factory.DataStoreJdbcRepositoryFactory;
import com.deeppaas.datastore.common.emnus.DataStoreEntityType;
import com.deeppaas.datastore.common.emnus.TableIndexType;
import com.deeppaas.datastore.common.jdbc.ColumnSqlGenerator;
import com.deeppaas.datastore.common.mapping.DBColumn;
import com.deeppaas.datastore.common.mapping.DBTable;
import com.deeppaas.datastore.common.emnus.DBDialect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * 数据源对应数据库原生服务
 *
 * <AUTHOR>
 * @date 2022/03/04
 */
@Service
public class DataStoreDBService {
    private static final Logger logger = LoggerFactory.getLogger(DataStoreDBService.class);
    private final DataStoreJdbcRepositoryFactory repositoryFactory;

    public DataStoreDBService(DataStoreJdbcRepositoryFactory repositoryFactory) {
        this.repositoryFactory = repositoryFactory;
    }

    /**
     * 创建数据库自建表
     *
     * @param sourceId  目标源ID
     * @param tableName 表名
     * @return 是否成功
     */
    @Transactional
    public Boolean createTable(String sourceId, String tableName) {
        // 创建表
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(sourceId);
            DBDialect dbDialect = repositoryFactory.getDBDialect(sourceId);
            String schema = repositoryFactory.getDBSchema(sourceId);
            String owner = repositoryFactory.getOwner(sourceId);
            String sql = dbDialect.createSelfTableSql(schema, tableName, owner);
            jdbcTemplate.update(sql, new HashMap<>());
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
        return true;
    }

    /**
     * 删除数据库自建表
     *
     * @param sourceId  目标源ID
     * @param tableName 表名
     * @return 是否成功
     */
    @Transactional
    public Boolean dropTable(String sourceId, String tableName) {
        // 创建表
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(sourceId);
            DBDialect dbDialect = repositoryFactory.getDBDialect(sourceId);
            String sql = "DROP TABLE " + dbDialect.backtick(tableName) + "";
            jdbcTemplate.update(sql, new HashMap<>());
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
        return true;
    }

    /**
     * 创建自建表虚列
     *
     * @param tableDO
     * @param fieldDO
     * @return
     */

    @Transactional
    public boolean createSelfTableColumn(DataStoreTableDO tableDO, DataStoreTableFieldDO fieldDO) {
        // 创建字段
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(tableDO.getSourceId());
            DBDialect dbDialect = repositoryFactory.getDBDialect(tableDO.getSourceId());
            String schema = repositoryFactory.getDBSchema(tableDO.getSourceId());
            String sql = dbDialect.createSelfTableColumnSql(schema, tableDO.getTableName(), fieldDO.getCode(), fieldDO.getColumnName(),
                    fieldDO.getDataType(), fieldDO.getLength(), fieldDO.getDecimalDigits());
            jdbcTemplate.update(sql, new HashMap<>());
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
        return true;
    }

    /**
     * 修改自建表虚列
     *
     * @param tableDO
     * @param fieldDO
     * @return
     */
    @Transactional
    public boolean modifySelfTableColumn(DataStoreTableDO tableDO, DataStoreTableFieldDO fieldDO) {
        // 创建字段
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(tableDO.getSourceId());
            DBDialect dbDialect = repositoryFactory.getDBDialect(tableDO.getSourceId());
            String schema = repositoryFactory.getDBSchema(tableDO.getSourceId());
            String sql = dbDialect.modifySelfTableColumnSql(schema, tableDO.getTableName(), fieldDO.getCode(), fieldDO.getColumnName(),
                    fieldDO.getDataType(), fieldDO.getLength(), fieldDO.getDecimalDigits());
            jdbcTemplate.update(sql, new HashMap<>());
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
        return true;
    }

    /**
     * 删除自建表虚列
     *
     * @param tableDO
     * @param fieldDO
     * @return
     */
    @Transactional
    public boolean deleteSelfTableColumn(DataStoreTableDO tableDO, DataStoreTableFieldDO fieldDO) {
        // 创建字段
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(tableDO.getSourceId());
            DBDialect dbDialect = repositoryFactory.getDBDialect(tableDO.getSourceId());
            String sql = "ALTER TABLE " + dbDialect.backtick(tableDO.getTableName())
                    + " DROP COLUMN " + dbDialect.backtick(fieldDO.getColumnName());
            jdbcTemplate.update(sql, new HashMap<>());
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
        return true;
    }

    /**
     * 检查数据库表是否存在
     *
     * @param sourceId  目标源ID
     * @param tableName 表名
     * @return 是否存在
     */
    public Boolean existsTable(String sourceId, String tableName) {
        NamedParameterJdbcTemplate template = repositoryFactory.getJdbcTemplate(sourceId);
        if (template == null) {
            throw RunException.optReject("数据源链接不存在");
        }
        try {
            Connection connection = template.getJdbcTemplate().getDataSource().getConnection();
            String catalog = connection.getCatalog();
            String schema = connection.getSchema();
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tableSet = metaData.getTables(catalog, schema, tableName, null);
            boolean exists = tableSet.next();
            connection.close();
            return exists;
        } catch (SQLException ex) {
            LogerHelper.errorLog(logger, ex);
            throw RunException.error(ex.getMessage());
        }
    }

    /**
     * 批量查询数据源表列表
     *
     * @param sourceId 数据源ID
     * @return 数据库表列表
     */
    public List<DBTable> tables(String sourceId) {
        NamedParameterJdbcTemplate template = repositoryFactory.getJdbcTemplate(sourceId);
        try {
            Connection connection = template.getJdbcTemplate().getDataSource().getConnection();
            String catalog = connection.getCatalog();
            String schema = connection.getSchema();
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tableSet = metaData.getTables(catalog, schema, null, null);
            List<DBTable> dbTables = new ArrayList<>();
            while (tableSet.next()) {
                String tableType = tableSet.getString("TABLE_TYPE");
                if ("TABLE".equals(tableType)) {
                    String tableName = tableSet.getString("TABLE_NAME");
                    String tableRemarks = tableSet.getString("REMARKS");
                    DBTable dbTable = new DBTable();
                    dbTable.setName(tableName);
                    dbTable.setComment(tableRemarks);
                    dbTables.add(dbTable);
                }
            }
            connection.close();
            return dbTables;
        } catch (SQLException ex) {
            LogerHelper.errorLog(logger, ex);
            throw RunException.error(ex.getMessage());
        }
    }

    /**
     * 查询表所有列信息
     *
     * @param sourceId  数据源ID
     * @param tableName 表名称
     * @return 表列信息
     */
    public List<DBColumn> tableColumns(String sourceId, String tableName) {
        NamedParameterJdbcTemplate template = repositoryFactory.getJdbcTemplate(sourceId);
        List<DBColumn> dbColumns = new ArrayList<>();
        try {
            Connection connection = template.getJdbcTemplate().getDataSource().getConnection();
            String catalog = connection.getCatalog();
            String schema = connection.getSchema();
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet primarySet = metaData.getPrimaryKeys(catalog, schema, tableName);
            Set<String> primaryKeys = new HashSet<>();
            while (primarySet.next()){
                primaryKeys.add(primarySet.getString("COLUMN_NAME"));
            }
            ResultSet columnSet = metaData.getColumns(catalog, schema, tableName, "%");
            while (columnSet.next()) {
                String columnName = columnSet.getString("COLUMN_NAME");
                //String columnType = columnSet.getString("TYPE_NAME");
                int dataType = columnSet.getInt("DATA_TYPE");
                int dataSize = columnSet.getInt("COLUMN_SIZE");
                int digits = columnSet.getInt("DECIMAL_DIGITS");
                String isGeneratedColumn = columnSet.getString("IS_GENERATEDCOLUMN");
                String remarks = columnSet.getString("REMARKS");

                SimpleDataType simpleDataType = ColumnSqlGenerator.getDataType(dataType);
                DBColumn dbColumn = new DBColumn();
                dbColumn.setName(columnName);
                if(simpleDataType!=null){
                    dbColumn.setType(simpleDataType.name());
                }
                dbColumn.setComment(remarks);
                dbColumn.setLength(dataSize);
                dbColumn.setDecimalDigits(digits);
                dbColumn.setIdKey(primaryKeys.contains(columnName));
                dbColumn.setDbGenerated("YES".equals(isGeneratedColumn));
                dbColumns.add(dbColumn);
            }
            connection.close();
            return dbColumns;
        } catch (SQLException ex) {
            LogerHelper.errorLog(logger, ex);
            throw RunException.error(ex.getMessage());
        }
    }

    /**
     * 根据sql查询列信息
     *
     * @param sourceId  数据源ID
     * @param selectSql 查询sql
     * @return 表列信息
     */
    public List<DBColumn> sqlColumns(String sourceId, String selectSql) {
        NamedParameterJdbcTemplate template = repositoryFactory.getJdbcTemplate(sourceId);
        String sql = "SELECT * FROM (" + selectSql + ") t WHERE 1=0";
        List<DBColumn> columns = new ArrayList<>();
        SqlRowSet sqlRowSet = template.queryForRowSet(sql, new HashMap<>());
        SqlRowSetMetaData rowSetMetaData = sqlRowSet.getMetaData();
        int columnCount = rowSetMetaData.getColumnCount();
        for (int i = 1; i <= columnCount; i++) {
            String columnName = rowSetMetaData.getColumnName(i);
            int columnType = rowSetMetaData.getColumnType(i);
            SimpleDataType dataType = ColumnSqlGenerator.getDataType(columnType);
            int length = rowSetMetaData.getColumnDisplaySize(i);
            DBColumn dbColumn = new DBColumn();
            dbColumn.setName(columnName);
            dbColumn.setType(dataType == null ? null : dataType.name());
            dbColumn.setLength(length);
            dbColumn.setComment("");    // SQL查询的结果没有注释
            dbColumn.setIdKey(false);
            dbColumn.setDbGenerated(false);
            columns.add(dbColumn);
        }
        return columns;
    }


    /**
     * 创建数据库自建表索引
     *
     * @param sourceId     目标源ID
     * @param tableName    表名
     * @param indexName    索引名称
     * @param indexType    索引类型
     * @param indexColumns 索引字段名称
     * @return 是否成功
     */
    @Transactional
    public Boolean createTableIndex(String sourceId, String tableName, String indexName, String indexType, List<String> indexColumns) {
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(sourceId);
            DBDialect dbDialect = repositoryFactory.getDBDialect(sourceId);
            String schema = repositoryFactory.getDBSchema(sourceId);
            String sql = dbDialect.createTableIndexSql(schema, tableName, indexName, TableIndexType.valueOf(indexType), indexColumns);
            jdbcTemplate.update(sql, new HashMap<>());
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
        return true;
    }

    /**
     * 删除表索引
     *
     * @param sourceId  目标源ID
     * @param tableName 表名
     * @param indexName 索引名称
     * @return 是否成功
     */
    @Transactional
    public boolean deleteTableIndex(String sourceId, String tableName, String indexName) {
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(sourceId);
            DBDialect dbDialect = repositoryFactory.getDBDialect(sourceId);
            String schema = repositoryFactory.getDBSchema(sourceId);
            String sql = dbDialect.deleteTableIndexSql(schema, tableName, indexName);
            jdbcTemplate.update(sql, new HashMap<>());
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
        return true;
    }

    /**
     * 检查表列是否符合设置为唯一
     * @param tableDO
     * @param columnName
     * @return
     */
    public boolean checkColumnUnique(DataStoreTableDO tableDO, String columnName) {
        // 创建字段
        try {
            NamedParameterJdbcTemplate jdbcTemplate = repositoryFactory.getJdbcTemplate(tableDO.getSourceId());
            DBDialect dbDialect = repositoryFactory.getDBDialect(tableDO.getSourceId());
            String tablePart = DataStoreEntityType.SQL_BUILD.name().equals(tableDO.getType())?
                    "("+tableDO.getBuildSql()+")":dbDialect.backtick(tableDO.getTableName());
            String sql = "select count(distinct t."+dbDialect.backtick(columnName)+") AS un, count(t."+dbDialect.backtick(columnName)+") AS n "
                    +"from "+tablePart+" t ";
            Map<String, Object> result = jdbcTemplate.queryForMap(sql, new HashMap<>());
            return Objects.equals(result.get("un"), result.get("n"));
        } catch (Exception ex) {
            LogerHelper.errorLog(logger, ex);
            return false;
        }
    }
}
