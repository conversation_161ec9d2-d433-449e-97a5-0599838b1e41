package com.deeppaas.datastore.common.emnus;

/**
 * <AUTHOR>
 * @date 2022/1/19
 */
public enum DataStoreOperator {
    /**
     * 等于
     **/
    EQ("="),
    /**
     * 不等于
     **/
    NE("!="),
    /**
     * 大于
     **/
    GT(">"),
    /**
     * 大于等于
     **/
    GTE(">="),
    /**
     * 小于
     **/
    LT("<"),
    /**
     * 小于等于
     **/
    LTE("<="),
    /**
     * 包含
     **/
    IN("in"),
    /**
     * 不包含
     **/
    NOTIN("not in"),
    /**
     * 介于之间
     **/
    BETWEEN("between"),
    /**
     * 介于之间
     **/
    NOTBETWEEN("not between"),
    CONTAINS("like"),
    STARTWITH("like"),
    ENDWITH("like"),
    ISNULL("is null"),
    /** 不为空 */
    NOTNULL("is not null"),
    /**
     * 不是null
     **/
    AND("and"),
    /**
     * 不是null
     **/
    OR("or");

    private final String sqlOpt;

    DataStoreOperator(String sqlOpt) {
        this.sqlOpt = sqlOpt;
    }

    public String getSqlOpt() {
        return sqlOpt;
    }
}
