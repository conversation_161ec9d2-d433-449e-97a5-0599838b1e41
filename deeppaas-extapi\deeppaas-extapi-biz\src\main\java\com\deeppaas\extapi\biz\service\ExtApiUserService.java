package com.deeppaas.extapi.biz.service;

import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.extapi.biz.convert.ExtApiUserConvert;
import com.deeppaas.extapi.biz.dao.ExtApiDao;
import com.deeppaas.extapi.biz.dao.ExtApiUserDao;
import com.deeppaas.extapi.biz.dao.ExtApiUserRightDao;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiUserDO;
import com.deeppaas.extapi.biz.entity.ExtApiUserRightDO;
import com.deeppaas.extapi.biz.model.ExtApiUserRightAddParam;
import com.deeppaas.extapi.biz.model.ExtApiUserRightVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 接口授权用户Service
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Service
public class ExtApiUserService {
    private final ExtApiUserDao extApiUserDao;
    private final ExtApiUserRightDao extApiUserRightDao;
    private final ExtApiDao extApiDao;

    public ExtApiUserService(ExtApiUserDao extApiUserDao, ExtApiUserRightDao extApiUserRightDao, ExtApiDao extApiDao) {
        this.extApiUserDao = extApiUserDao;
        this.extApiUserRightDao = extApiUserRightDao;
        this.extApiDao = extApiDao;
    }


    public PageData<ExtApiUserDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<ExtApiUserDO> page = extApiUserDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    public ExtApiUserDO get(String id) {
        return extApiUserDao.getById(id);
    }

    @Transactional
    public ExtApiUserDO save(ExtApiUserDO param) {
        return extApiUserDao.save(param);
    }

    @Transactional
    public void delete(String id) {
        extApiUserDao.deleteById(id);
    }

    public Map<String, ExtApiUserDO> findMapByIds(Set<String> userIds) {
        List<ExtApiUserDO> userDOList = extApiUserDao.findByIdIn(userIds);
        return userDOList.stream().collect(Collectors.toMap(ExtApiUserDO::getId, Function.identity()));
    }

    @Transactional
    public void saveRight(ExtApiUserRightAddParam param) {
        List<ExtApiUserRightDO> userRightDOList = new ArrayList<>();
        for (String apiId : param.getApiIds()) {
            for (String userId : param.getUserIds()) {
                boolean exists = extApiUserRightDao.existsByApiIdAndUserId(apiId, userId);
                if (exists) {
                    continue;
                }
                ExtApiUserRightDO rightDO = new ExtApiUserRightDO();
                rightDO.setApiId(apiId);
                rightDO.setUserId(userId);
                userRightDOList.add(rightDO);
            }
        }
        if (!userRightDOList.isEmpty()) {
            extApiUserRightDao.saveAll(userRightDOList);
        }
    }

    @Transactional
    public void deleteRight(String id) {
        extApiUserRightDao.deleteById(id);
    }

    @Transactional
    public void usable(String id, Integer usable) {
        ExtApiUserDO apiUserDO = extApiUserDao.getById(id);
        if (Objects.equals(apiUserDO.getUsable(), usable)) {
            return;
        }
        // 修改可用标识
        apiUserDO.setUsable(usable);
        extApiUserDao.save(apiUserDO);
    }

    public List<ExtApiUserRightVO> listByApi(String apiId) {
        List<ExtApiUserRightDO> userRightDOList = extApiUserRightDao.findByApiId(apiId);
        if (CollectionUtils.isEmpty(userRightDOList)) {
            return Collections.emptyList();
        }
        Set<String> userIds = userRightDOList.stream().map(ExtApiUserRightDO::getUserId).collect(Collectors.toSet());
        Map<String, ExtApiUserDO> apiUserMap = findMapByIds(userIds);
        return ExtApiUserConvert.entityToVOSByApi(userRightDOList, apiUserMap);
    }

    public List<ExtApiUserRightVO> listByUser(String userId) {
        List<ExtApiUserRightDO> apiUserRightDOList = extApiUserRightDao.findByUserId(userId);
        if (CollectionUtils.isEmpty(apiUserRightDOList)) {
            return Collections.emptyList();
        }
        // API列表
        Set<String> apiIds = apiUserRightDOList.stream().map(ExtApiUserRightDO::getApiId).collect(Collectors.toSet());
        List<ExtApiDO> apiDOList = extApiDao.findByIdIn(apiIds);
        Map<String, ExtApiDO> apiDOMap = apiDOList.stream().collect(Collectors.toMap(ExtApiDO::getId, Function.identity()));
        return ExtApiUserConvert.entityToVOSByUser(apiUserRightDOList, apiDOMap);
    }

    public boolean existByApiIdAndUserId(String apiId, String userId) {
        return extApiUserRightDao.existsByApiIdAndUserId(apiId, userId);
    }
}
