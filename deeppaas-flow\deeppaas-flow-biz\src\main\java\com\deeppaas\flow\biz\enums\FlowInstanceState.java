package com.deeppaas.flow.biz.enums;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.flow.biz.FlowAction;

/**
 * 流程实例状态
 * <AUTHOR>
 * @date 2022/5/31
 */
public enum FlowInstanceState {
    /** 流程实例状态: 草稿 **/
    READY(0),
    /** 流程实例状态: 审批中 **/
    OPEN(1),
    /** 流程实例状态: 审批结束 **/
    FINISH(2),
    /** 流程实例状态: 审批中止 **/
    STOP(3),
    /** 流程实例状态: 驳回(到开始) **/
    BACK(4);

    /**
     * 数字值
     */
    private int intValue;

    FlowInstanceState(int intValue) {
        this.intValue = intValue;
    }

    public static FlowInstanceState closeState(FlowAction action) {
        return switch (action){
            case stop -> STOP;
            case back -> BACK;
            case cancel, takeBack -> READY;
            default -> throw RunException.error("本方法只适用于非正常结束动作");
        };
    }

    public int getIntValue() {
        return intValue;
    }

    public boolean intValueEquals(int intValueParam){
        return intValue == intValueParam;
    }
}
