package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.entity.FlowDefineDO;
import com.deeppaas.flow.biz.model.FlowDefineModel;
import com.deeppaas.flow.biz.model.param.FlowDefineParam;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName: FlowDefineService 
 * @Description: 流程定义服务接口
 * <AUTHOR>
 */
public interface FlowDefineService {
	
	/**
	 * @Title: get 
	 * @Description: 根据流程定义ID获取对应流程定义实体
	 * @param defineId
	 * @return
	 * <AUTHOR>
	 */
	FlowDefineDO get(String defineId);
	
	/**
	 * @Title: check 
	 * @Description: 检查流程定义
	 * @param define
	 * <AUTHOR>
	 */
	List<String> check(FlowDefineDO define);
	
	/**
	 * @Title: copy 
	 * @Description: 复制流程定义
	 * @param defineId
	 * @return
	 * <AUTHOR>
	 */
	FlowDefineDO copy(String defineId);

	/**
	 * 修改流程备注
	 *
	 * @param defineId  流程定义ID
	 * @param remark    备注
	 */
    void modifyRemark(String defineId, String remark);

    FlowDefineDO findByCodeAndState(String code, int state);

	List<FlowDefineDO> listByCode(String code);

    List<FlowDefineDO> listByEntity(String entity);

	void delete(String id);

	FlowDefineDO save(FlowDefineParam flowDefineParam);

	FlowDefineDO issue(FlowDefineParam flowDefineParam);

	List<FlowDefineDO> findByIds(Collection<String> flowDefineIds);

	/**
	 * 获取流程定义模型
	 * @param defineId
	 * @return
	 */
	FlowDefineModel getModelById(String defineId);

	/**
	 * 获取流程定义模型
	 * @param code
	 * @return
	 */
	FlowDefineModel getModelByCode(String code);

	/**
	 * 获取流程定义模型
	 * @param code
	 * @param version
	 * @return
	 */
	FlowDefineModel getModelByCode(String code, Integer version);

	/**
	 * 重载流程定义模型
	 * @param flowDefineDO
	 * @return
	 */
	FlowDefineModel reloadModel(FlowDefineDO flowDefineDO);

	/**
	 * 载入流程定义模型
	 * @param flowDefineDO
	 * @return
	 */
	FlowDefineModel loadModel(FlowDefineDO flowDefineDO);
}
