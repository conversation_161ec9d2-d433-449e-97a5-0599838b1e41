{"code": "XXX", "name": "XXX流程", "version": 1, "nodes": [{"code": "node1", "name": "开始", "type": "start", "remark": "备注信息", "page": "page_123456", "events": [{"event": "NODE_ENTER", "roleIds": ["role_123123", "role_123124"]}, {"event": "NODE_EXIT", "roleIds": ["role_123125"]}]}, {"code": "node2", "name": "人工1", "type": "task", "remark": "备注信息", "page": "iuasyifasfd8ad7f9a7ds", "taskType": "ANY", "actorAssign": true, "actorNullPass": true, "actorSamePass": true, "limitedTime": 0, "action": "TODO", "comment": false, "backTarget": "SOURCE", "backMsg": "TODO", "actorGroups": [{"condition": "动作规则", "actors": [{"type": "DEPT", "code": "IT"}, {"type": "DEPT", "code": "HR"}, {"type": "POST", "code": "BOSS", "deptLevel": 0, "deptFieldCode": "applyDept"}, {"type": "USER", "code": "liming"}, {"type": "ORG_USER", "code": "itJavaEngineer_liming"}, {"type": "DEPT_FIELD", "code": "fieldCodeX"}, {"type": "USER_FIELD", "code": "fieldCodeY"}, {"type": "ROLE", "code": "xxxRole"}]}], "events": [{"event": "NODE_ENTER", "roleIds": ["role_123123", "role_123124"]}, {"event": "NODE_EXIT", "roleIds": ["role_123125"]}]}], "lines": [{"code": "line1", "text": "", "from": "node1", "to": "node2", "condition": ""}], "events": [{"event": "INSTANCE_SUBMIT_BEFORE", "roleIds": ["role_123123", "role_123124"]}, {"event": "INSTANCE_FINISH", "roleIds": ["role_123125"]}]}