package com.deeppaas.common.context;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.Map;

/**
 * @ClassName: SpringContext
 * @Description: ApplicationContext存放器, 便于当容器启动以后，在任意位置获得ApplicationContext
 * <AUTHOR>
 * @date 2017年7月31日
 */
@Component
public class SpringContext implements ApplicationContextAware {
	private static ApplicationContext appContext;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		if(appContext == null){
			appContext = applicationContext;
		}
	}

	public static Object getBean(String name) {
		if (!StringUtils.hasText(name)) {
			return null;
		}
		return appContext.getBean(name);
	}

	public static <T> T getBean(Class<T> clazz) {
		return appContext.getBean(clazz);
	}

	public static <T> T getBean(String name, Class<T> clazz) {
		return appContext.getBean(name, clazz);
	}
	
	public static boolean containsBean(String beanName) {
		return appContext.containsBean(beanName);
	}

	public static Collection<Object> getBeanByAnnotation(Class<? extends Annotation> annotationType) {
		Map<String, Object> beanMap = appContext.getBeansWithAnnotation(annotationType);
		return beanMap.values();
	}
	
}
