package com.deeppaas.flow.workbench.model;

import lombok.Data;

/**
 * 工单处理权限
 *
 * <AUTHOR>
 * @date 2022/05/30
 */
@Data
public class WorkOrderAuth {

    /**
     * 审批
     */
    private Boolean audit;

    /**
     * 中止
     */
    private Boolean stop;

    /**
     * 撤回
     */
    private Boolean revoke;

    /**
     * 删除
     */
    private Boolean delete;

    public static WorkOrderAuth instanceOf(boolean audit, boolean stop, boolean revoke) {
        WorkOrderAuth auth = new WorkOrderAuth();
        auth.setAudit(audit);
        auth.setStop(stop);
        auth.setRevoke(revoke);
        auth.setDelete(false);
        return auth;
    }

    public static WorkOrderAuth instanceOf(boolean audit, boolean stop, boolean revoke, boolean delete) {
        WorkOrderAuth auth = new WorkOrderAuth();
        auth.setAudit(audit);
        auth.setStop(stop);
        auth.setRevoke(revoke);
        auth.setDelete(delete);
        return auth;
    }
}
