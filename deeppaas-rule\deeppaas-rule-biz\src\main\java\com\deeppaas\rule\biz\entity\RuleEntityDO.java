package com.deeppaas.rule.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 计算模型，用于变量定义，接口返回等场景定义的临时数据结构
 * <AUTHOR>
 * @date 2022/6/11
 */
@Data
@Table("rule_entity")
public class RuleEntityDO extends BaseUuidDO {

    public static final String API_PUBLIC = "PUBLIC";
    /**
     * 模型名称
     */
    @Column("name")
    private String name;

    /**
     * 规则备注
     */
    @Column("remark")
    private String remark;

    /**
     * 所属接口ID,如果是一个接口返回的结构定义那么有值，否则为PUBLIC
     */
    @Column("api_id")
    private String apiId;
}
