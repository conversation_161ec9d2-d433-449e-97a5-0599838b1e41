package com.deeppaas.file.biz.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 文件组
 * <AUTHOR>
 * @date 2022/10/19
 */
@Data
@Table("deeppaas_file_group")
public class FileGroupDO {
    /** 组名 */
    @Id
    @Column("id")
    private String id;
    /** 模式 本地/OSS */
    @Column("mode")
    private String mode;
    /** 访问控制类型 */
    @Column("type")
    private String type;
    /** 组指向(如OSS的bucket，本地硬盘文件夹的绝对路径) */
    @Column("bucket")
    private String bucket;
    /** 域名,本地默认/dss */
    @Column("domain")
    private String domain;
    /** 其他属性信息json形式健值对加密信息 */
    @Column("properties")
    private String properties;
}
