var f=typeof global=="object"&&global&&global.Object===Object&&global,l=f,s=typeof self=="object"&&self&&self.Object===Object&&self,g=l||s||Function("return this")(),j=g,y=j.Symbol,o=y,a=Object.prototype,S=a.hasOwnProperty,u=a.toString,e=o?o.toStringTag:void 0;function O(t){var r=S.call(t,e),b=t[e];try{t[e]=void 0;var i=!0}catch{}var c=u.call(t);return i&&(r?t[e]=b:delete t[e]),c}var T=Object.prototype,d=T.toString;function p(t){return d.call(t)}var m="[object Null]",v="[object Undefined]",n=o?o.toStringTag:void 0;function $(t){return t==null?t===void 0?v:m:n&&n in Object(t)?O(t):p(t)}function h(t){return t!=null&&typeof t=="object"}function G(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}var w="[object Symbol]";function k(t){return typeof t=="symbol"||h(t)&&$(t)==w}export{o as S,G as a,$ as b,k as c,l as f,h as i,j as r};
