package com.deeppaas.rule.biz.action;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.RuleContext;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
public abstract class RAction {
    /**
     * 动作ID
     */
    private String id;
    /**
     * 动作类型
     */
    private String type;
    /**
     * 动作名称
     */
    private String name;
    /**
     * 是否是开始动作
     */
    private boolean start;
    /**
     * 下一步
     */
    private String next;
    /**
     * 是否是返回结果动作，
     * 返回结果动作将把本动作的计算结果作为返回值返回，返回动作不在有next
     */
    private boolean returned;
    /**
     * 是否将返回结果同时定义为变量
     */
    private boolean returnVar;

    /**
     * 变量名称,是否将返回结果同时定义为变量名称
     */
    private String varName;

    /**
     * 变量类型
     */
    private SimpleDataType varType;

    /**
     * 变量对应模型
     */
    private String varEntity;


    /**
     * 执行
     * @param ruleContext
     * @return
     */
    public abstract Object execute(RuleContext ruleContext);

    protected Object returnOrNext(Object result, RuleContext ruleContext) {
        // 如果返回结果变量话，那么给增加变量
        if(returnVar){
            ruleContext.getVarMap().put(varName, result);
        }
        if(StringUtils.hasText(next)){
            if(returned){
                throw RunException.error("规则异常，动作["+id+"/"+name+"]不可以同时是返回动作又有下一个动作");
            }
            RAction nextAction = ruleContext.getAction(next);
            if(nextAction==null){
                throw RunException.error("规则异常，动作["+id+"/"+name+"]的下一个动作[id="+next+"]不存在");
            }
            return nextAction.execute(ruleContext);
        }
        return result;
    }
}
