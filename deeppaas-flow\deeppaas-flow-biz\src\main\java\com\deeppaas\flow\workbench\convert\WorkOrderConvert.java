package com.deeppaas.flow.workbench.convert;

import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.flow.workbench.model.WorkOrderParam;

/**
 * <AUTHOR>
 * @date 2022/3/12
 */
public class WorkOrderConvert {

    public static DataStoreData toDataStoreData(WorkOrderParam workOrderParam) {
        return DataStoreData.instance(workOrderParam.getEntity(), workOrderParam.getData());
    }

    public static WorkOrderParam toWorkOrderParam(String flowDefineCode, String startNode, DataStoreData orderData) {
        WorkOrderParam workOrderParam = new WorkOrderParam();
        workOrderParam.setFlowDefineCode(flowDefineCode);
        workOrderParam.setStartNode(startNode);
        workOrderParam.setData(orderData);
        return workOrderParam;
    }
}
