package com.deeppaas.rule.api.model;

import lombok.Data;

/**
 * 计算模型属性
 *
 * <AUTHOR>
 * @date 2022/6/11
 */
@Data
public class RuleEntityPropertyPO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 所属计算模型ID
     */
    private String entityId;

    /**
     * 属性名称，英文
     */
    private String name;

    /**
     * 模型备注，中文描述
     */
    private String remark;

    /**
     * 类型：文本、数字、布尔、数据模型对象、规则模型对象、文件
     * 文本数组、数字数组、数据模型对象数组、规则模型对象数组、文件数组
     * <p>
     * 参考
     *
     * @com.deeppaas.common.data.enums.SimpleDataType
     */
    private String type;

    /**
     * 对应模型：数据模型或数组为数据模型code,规则模型或数组为规则模型ID
     */
    private String typeEntity;
}
