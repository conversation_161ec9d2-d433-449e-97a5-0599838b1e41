package com.deeppaas.rule.api.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/6/17
 */
@Data
public class RulePO {
    private String id;
    /**
     * 规则名称
     */
    private String name;
    /**
     * 参数类型
     * @com.deeppaas.common.data.enums.SimpleDataType
     */
    private String paramType;

    /**
     * 参数如果是模型类对应的模型索引
     */
    private String paramEntity;
    /**
     * 返回值类型
     * @com.deeppaas.common.data.enums.SimpleDataType
     */
    private String returnType;

    /**
     * 返回模型或模型数组时对应的模型索引
     */
    private String returnEntity;

    /**
     * 后端规则，0是前端规则，1是后端规则
     */
    private Integer backEnd;

    /**
     * 定义来源：数据模型、流程等等
     */
    private String ownerType;

    /**
     * 定义来源ID：数据模型、流程等等ID
     */
    private String ownerId;

    /**
     * 规则内容
     */
    private String content;
}
