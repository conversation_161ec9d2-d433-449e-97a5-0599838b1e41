package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * @ClassName: FlowRecord 
 * @Description: 流程实例处理记录
 * <AUTHOR>
 */
@Data
@Table("flow_record")
public class FlowRecordDO implements BaseModel {
	private static final long serialVersionUID = -6859084734339858401L;
	@Id
	@Column("id")
	private Long id;
	/**
	 * 流程实例ID
	 */
	@Column("instance_id")
	private Long instanceId;
	/**
	 * 环节名称
	 */
	@Column("node_name")
	private String nodeName;
	/**
	 * 处理动作
	 */
	@Column("action")
	private String action;
	/**
	 * 处理人
	 */
	@Column("execute_user")
	private String executeUser;
	/**
	 * 处理时间
	 */
	@Column("execute_time")
	private LocalDateTime executeTime;
	/**
	 * 处理意见
	 */
	@Column("comment")
	private String comment;
	
}
