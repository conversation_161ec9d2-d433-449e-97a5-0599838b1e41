{"stepId": "step5", "type": "DATA_UPDATE", "name": "扣除剩余年假天数", "dataEntity": "ET_UserAnnualLeave", "condition": {"operator": "AND", "conditions": [{"field": "userCode", "operator": "EQ", "target": {"type": "VAR", "value": "varUserCode"}}, {"field": "year", "operator": "EQ", "target": {"type": "VAR", "value": "varNowYear"}}]}, "updateFields": [{"field": "remainDays", "target": {"type": "EXPR", "value": "effect.remainDays - data.applyDays"}}], "next": null}