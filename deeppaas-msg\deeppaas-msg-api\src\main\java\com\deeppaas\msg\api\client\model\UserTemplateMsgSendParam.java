package com.deeppaas.msg.api.client.model;

import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 模版消息发送参数
 * <AUTHOR>
 * @date 2022/9/3
 */
@Data
public class UserTemplateMsgSendParam {
    /**
     * 所属消息模块
     */
    private String boxId;

    /**
     * 消息标题
     */
    private String templateId;

    /**
     * 参数
     */
    private Map<String, Object> params;

    /**
     * 接收人
     */
    private Collection<String> toUser;
}
