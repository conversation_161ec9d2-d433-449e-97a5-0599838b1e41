package com.deeppaas.template.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 *
 * 模板类别
 * */
@Data
@Table("aidos_image_template_type")
public class AidosImageTemplateTypeDO extends BaseUuidDO {

    /**
     * 过程id
     * */
    @Column("process_id")
    private String processId;

    /**
     * 任务id
     * */
    @Column("process_task_id")
    private String processTaskId;

    /**
     * 类别名称
     * */
    @Column("name")
    private String name;

    /**
     * 模板别名
     * */
    @Column("alias")
    private String alias;

    /**
     * 创建人id
     * */
    @Column("create_user_id")
    private Integer createUserId;


    /**
     * 创建人名称
     * */
    @Column("create_user_name")
    private String createUserName;


}
