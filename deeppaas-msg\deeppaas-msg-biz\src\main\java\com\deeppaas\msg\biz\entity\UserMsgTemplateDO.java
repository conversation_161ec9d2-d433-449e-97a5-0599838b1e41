package com.deeppaas.msg.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 消息模版
 * <AUTHOR>
 * @date 2022/9/3
 */
@Data
@Table("deeppaas_user_msg_template")
public class UserMsgTemplateDO extends BaseUuidDO {
    /**
     * 消息标题
     */
    @Column("title")
    private String title;

    /**
     * 消息正文
     */
    @Column("content")
    private String content;

    /**
     * 链接地址
     */
    @Column("msg_url")
    private String msgUrl;

}
