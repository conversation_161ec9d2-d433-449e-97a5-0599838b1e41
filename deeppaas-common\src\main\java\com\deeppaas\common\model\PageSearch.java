package com.deeppaas.common.model;

import com.deeppaas.common.helper.StringHelper;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分页查询包装类
 *
 * <AUTHOR>
 * @date 2016年7月11日
 */
public class PageSearch {
    public static final String PARAM_PREFIX = "search_";
    private int pageNo = 1;
    private int pageSize;
    /**
     * ASC_id,DESC_createTime
     */
    private String sortInfo;
    private Map<String, String> param = new HashMap<>();

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getSortInfo() {
        return sortInfo;
    }

    public void setSortInfo(String sortInfo) {
        this.sortInfo = sortInfo;
    }

    public Map<String, String> getParam() {
        return param;
    }

    public void setParam(Map<String, String> param) {
        this.param = param;
    }

    public Map<String, Object> getSearchParams() {
        Map<String, Object> paramMap = new HashMap<>();
        param.forEach((key, obj) -> {
            if (key.startsWith(PARAM_PREFIX)) {
                paramMap.put(key.substring(PARAM_PREFIX.length()), obj);
            }
        });
        return paramMap;
    }

    public PageRequest getPageable() {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        if (StringHelper.isEmpty(sortInfo)) {
            return PageRequest.of(pageNo - 1, pageSize);
        } else {
            List<String> sortStrs = StringHelper.splitToList(sortInfo, ",");
            Sort mainSort = null;
            for (String sortStr : sortStrs) {
                Sort sort = null;
                if (sortStr.startsWith("ASC_")) {
                    sort = Sort.by(Direction.ASC, sortStr.substring(4));
                } else if (sortStr.startsWith("DESC_")) {
                    sort = Sort.by(Direction.DESC, sortStr.substring(5));
                }
                if (sort != null) {
                    if (mainSort == null) {
                        mainSort = sort;
                    } else {
                        mainSort = mainSort.and(sort);
                    }
                }
            }
            if (mainSort != null) {
                return PageRequest.of(pageNo - 1, pageSize, mainSort);
            } else {
                return PageRequest.of(pageNo - 1, pageSize);
            }
        }
    }
}
