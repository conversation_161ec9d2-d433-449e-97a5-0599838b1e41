package com.deeppaas.msg.biz.service;

import com.deeppaas.msg.biz.dao.UserMsgBoxDao;
import com.deeppaas.msg.biz.entity.UserMsgBoxDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@Service
public class UserMsgBoxService {
    private final UserMsgBoxDao userMsgBoxDao;

    public UserMsgBoxService(UserMsgBoxDao userMsgBoxDao) {
        this.userMsgBoxDao = userMsgBoxDao;
    }

    public UserMsgBoxDO getById(String id) {
        return userMsgBoxDao.getById(id);
    }

    public List<UserMsgBoxDO> findAllBoxes() {
        return userMsgBoxDao.findAll();
    }

    @Transactional
    public UserMsgBoxDO saveBox(UserMsgBoxDO boxDO) {
        return userMsgBoxDao.save(boxDO);
    }

    @Transactional
    public void deleteBox(String id) {
        userMsgBoxDao.deleteById(id);
    }
}
