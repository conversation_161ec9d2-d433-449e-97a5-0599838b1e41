package com.deeppaas.datastore.common.event;

/**
 * 数据仓库事件
 * <AUTHOR>
 * @date 2022/2/15
 */
public enum DataStoreEvent {
    /**
     * 新增数据前
     */
    BEFORE_INSERT,
    /**
     * 新增数据后
     */
    AFTER_INSERT,
    /**
     * 更新数据前
     */
    BEFORE_UPDATE,
    /**
     * 更新数据后
     */
    AFTER_UPDATE,
    /**
     * 删除数据前
     */
    BEFORE_DELETE,
    /**
     * 删除数据后
     */
    AFTER_DELETE,
    /**
     * 修改字段后
     */
    FIELD_CHANGED
}
