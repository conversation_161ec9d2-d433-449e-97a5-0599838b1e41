package com.deeppaas.datastore.common.web.req;

import com.deeppaas.common.helper.StringHelper;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据查询参数
 * <AUTHOR>
 * @date 2022/2/15
 */
@Data
public class DataStoreDataSearch {
    /**
     * 对应哪一个数据模型，必须
     */
    private String entity;
    /**
     * ASC_id,DESC_create_time
     */
    private String sortInfo;
    /**
     * 查询参数
     */
    private List<DataStoreDataSearchParam> params = new ArrayList<>();
    private int pageNo = 1;
    private int pageSize;

    @Data
    public static class DataStoreDataSearchParam{
        /**
         * 字段
         */
        private String fieldCode;
        /**
         * 比较运算符
         */
        private String operator;
        /**
         * 比较值
         */
        private Object value;
    }

    public PageRequest getPageable() {
        if (pageNo == 0) {
            pageNo = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        if (StringHelper.isEmpty(sortInfo)) {
            return PageRequest.of(pageNo - 1, pageSize);
        } else {
            List<String> sortStrs = StringHelper.splitToList(sortInfo, ",");
            Sort mainSort = null;
            for (String sortStr : sortStrs) {
                Sort sort = null;
                if (sortStr.startsWith("ASC_")) {
                    sort = Sort.by(Sort.Direction.ASC, sortStr.substring(4));
                } else if (sortStr.startsWith("DESC_")) {
                    sort = Sort.by(Sort.Direction.DESC, sortStr.substring(5));
                }
                if (sort != null) {
                    if (mainSort == null) {
                        mainSort = sort;
                    } else {
                        mainSort = mainSort.and(sort);
                    }
                }
            }
            if (mainSort != null) {
                return PageRequest.of(pageNo - 1, pageSize, mainSort);
            } else {
                return PageRequest.of(pageNo - 1, pageSize);
            }
        }
    }
}
