import{r}from"./index.689bcdcb.js";import{u as a}from"./useForceUpdate.a9f3ccc5.js";import{R as n}from"./responsiveObserve.da8166ca.js";function p(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=r.exports.useRef({}),s=a();return r.exports.useEffect(function(){var u=n.subscribe(function(o){e.current=o,t&&s()});return function(){return n.unsubscribe(u)}},[]),e.current}export{p as u};
