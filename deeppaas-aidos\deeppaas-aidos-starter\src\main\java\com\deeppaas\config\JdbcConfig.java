package com.deeppaas.config;

import com.deeppaas.common.data.converter.ArrayToJsonConverter;
import com.deeppaas.common.data.converter.JsonToListConverter;
import com.deeppaas.common.data.converter.ListToJsonConverter;
import com.deeppaas.common.data.jdbc.JRepositoryFactoryBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration;
import org.springframework.data.jdbc.repository.config.EnableJdbcRepositories;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionManager;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/28
 */
@Configuration
@EnableJdbcRepositories(value = "com.deeppaas", repositoryFactoryBeanClass= JRepositoryFactoryBean.class)
public class JdbcConfig extends AbstractJdbcConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource getDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    NamedParameterJdbcOperations namedParameterJdbcOperations(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean
    TransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Override
    public List<?> userConverters() {
        List<Converter> converters = new ArrayList<>();
        converters.add(new JsonToListConverter());
        converters.add(new ListToJsonConverter());
        converters.add(new ArrayToJsonConverter());
        return converters;
    }
}
