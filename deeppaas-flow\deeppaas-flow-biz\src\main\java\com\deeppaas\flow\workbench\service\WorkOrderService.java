package com.deeppaas.flow.workbench.service;

import com.deeppaas.account.api.client.UserClient;
import com.deeppaas.account.api.client.model.User;
import com.deeppaas.common.helper.AssertHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.datastore.api.client.DataStoreDataClient;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.datastore.common.mapping.DataStoreSelf;
import com.deeppaas.flow.biz.ExecuteResult;
import com.deeppaas.flow.biz.FlowEngine;
import com.deeppaas.flow.biz.FlowException;
import com.deeppaas.flow.biz.entity.FlowDefineDO;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.enums.FlowInstanceState;
import com.deeppaas.flow.biz.model.FlowDefineModel;
import com.deeppaas.flow.biz.model.vo.FlowInstanceVO;
import com.deeppaas.flow.workbench.convert.WorkOrderConvert;
import com.deeppaas.flow.workbench.dao.WorkOrderDao;
import com.deeppaas.flow.workbench.model.WorkOrder;
import com.deeppaas.flow.workbench.model.WorkOrderDO;
import com.deeppaas.flow.workbench.model.WorkOrderParam;
import com.deeppaas.flow.workbench.model.WorkOrderSearch;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 工单服务层
 *
 * <AUTHOR>
 * @date 2022/3/10
 */
@Service
public class WorkOrderService {
    private final WorkOrderDao orderDao;
    private final FlowEngine flowEngine;
    private final UserClient userClient;
    private final DataStoreDataClient dataStoreDataClient;

    public WorkOrderService(WorkOrderDao orderDao, FlowEngine flowEngine, UserClient userClient, DataStoreDataClient dataStoreDataClient) {
        this.orderDao = orderDao;
        this.flowEngine = flowEngine;
        this.userClient = userClient;
        this.dataStoreDataClient = dataStoreDataClient;
    }

    /**
     * 模拟提交工单
     */
    @Transactional(rollbackFor = Exception.class)
    public ExecuteResult mockSubmit(WorkOrderParam workOrderParam, String userCode) {
        DataStoreData orderData = WorkOrderConvert.toDataStoreData(workOrderParam);
        orderData = dataStoreDataClient.saveData(orderData);
        workOrderParam = WorkOrderConvert.toWorkOrderParam(workOrderParam.getFlowDefineCode(), workOrderParam.getStartNode(), orderData);
        FlowInstanceDO flowInstance = flowEngine.createInstance(workOrderParam.getFlowDefineCode(), workOrderParam.getStartNode(), orderData.get(DataStoreSelf.ID_SQL_PARAMETER).toString(), userCode);
        return flowEngine.mockStartInstance(flowInstance, userCode);
    }

    /**
     * 获取工单信息
     */
    public WorkOrder getWorkOrder(String defineCode, String orderId) {
        FlowInstanceDO flowInstanceDO = flowEngine.instance().getInstance(defineCode, orderId);
        AssertHelper.notNull(flowInstanceDO, "未找到流程实例信息，请联系系统管理员");
        FlowDefineModel defineModel = flowEngine.define().getModelByCode(flowInstanceDO.getDefineCode());
        DataStoreData data = dataStoreDataClient.getData(defineModel.getEntityCode(), orderId);
        AssertHelper.notNull(data, "未找到申请单信息，请联系系统管理员");
        // 创建人
        User creator = userClient.getUserByCode(flowInstanceDO.getCreator());
        FlowDefineDO defineDO = flowEngine.define().get(defineModel.getId());
        // 工单信息
        WorkOrder workOrder = new WorkOrder();
        workOrder.setDefine(defineDO);
        workOrder.setEntity(defineModel.getEntityCode());
        workOrder.setOrderId(orderId);
        workOrder.setInstance(FlowInstanceVO.instanceOf(flowInstanceDO, creator));
        workOrder.setData(data);
        return workOrder;
    }

    /**
     * 分页查询工单
     */
    public PageData<WorkOrder> findByPage(WorkOrderSearch orderSearch, String userCode) {
        PageData<WorkOrderDO> orderPage = orderDao.findByPage(orderSearch);

        List<WorkOrderDO> orderList = orderPage.getList();
        for (WorkOrderDO orderDO : orderList) {
            FlowDefineModel model = flowEngine.define().getModelByCode(orderDO.getDefineCode());
            DataStoreData data = dataStoreDataClient.getData(model.getEntityCode(), orderDO.getOrderId());
        }
        //return PageData.init(orderPage, detailVOS);
        return null;
    }

    /**
     * 保存工单
     *
     * @param param    订单参数
     * @param userCode 当前用户ID
     * @return 工单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public DataStoreData saveOrder(WorkOrderParam param, String userCode) {
        DataStoreData orderData = WorkOrderConvert.toDataStoreData(param);
        boolean isNew = orderData.get(DataStoreSelf.ID_SQL_PARAMETER) == null;
        if (isNew) {
            orderData = dataStoreDataClient.saveData(orderData);
            // 保存时立即创建一个在开始环节的关闭状态的流程实例
            flowEngine.createInstance(param.getFlowDefineCode(), param.getStartNode(), orderData.get(DataStoreSelf.ID_SQL_PARAMETER).toString(), userCode);
        } else {
            // todo 检查权限？
            orderData = dataStoreDataClient.saveData(orderData);
            isNew = false;
        }
        // TODO 发消息
//        orderMsgFacade.sendMsg(order, isNew, user);
//        fireButtonEvent(true, MdteButton.BUTTON_SAVE, order);
        return orderData;
    }


    /**
     * 删除工单
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeOrderAndFlowInstance(String entity, String orderId, String userCode) {
        DataStoreData orderData = dataStoreDataClient.getData(entity, orderId);
        AssertHelper.notNull(orderData, "未找到数据!");

        FlowInstanceDO instanceDO = flowEngine.instance().findByOrderId(orderId);
        if (!FlowInstanceState.READY.intValueEquals(instanceDO.getState())) {
            throw new FlowException("只能删除草稿数据");
        }

        dataStoreDataClient.removeData(entity, orderId, userCode);
        // TODO 先删除订单关联数据
        // TODO 删除事件？
        flowEngine.instance().delete(instanceDO);
    }
}
