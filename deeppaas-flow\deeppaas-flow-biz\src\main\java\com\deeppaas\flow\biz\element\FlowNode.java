package com.deeppaas.flow.biz.element;

import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.MockExecuteItem;
import com.deeppaas.flow.biz.enums.FlowEvent;
import com.deeppaas.flow.biz.enums.NodeType;
import com.deeppaas.flow.biz.model.AssignInfo;
import com.deeppaas.flow.biz.service.FlowTaskService;
import com.deeppaas.flow.biz.entity.FlowInstanceDO;
import com.deeppaas.flow.biz.entity.FlowTaskDO;
import com.deeppaas.flow.biz.service.FlowTokenService;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程环节
 * <AUTHOR>
 *
 */
public abstract class FlowNode {
	/**
	 * 编号
	 */
	protected final String code;
	/** 中文名 */
	protected final String name;
	/** 节点类型 */
	protected final NodeType type;
	/** 节点备注 */
	protected final String remark;
	protected List<FlowLine> inLines = new ArrayList<FlowLine>();	//进口
	protected List<FlowLine> outLines = new ArrayList<FlowLine>();	//出口

	/**
	 * 环节事件规则
	 */
	protected List<FlowEventRule> eventRules = new ArrayList<FlowEventRule>();

	public List<FlowLine> getInLines() {
		return inLines;
	}

	public List<FlowLine> getOutLines() {
		return outLines;
	}

	public List<FlowEventRule> getEventRules() {
		return eventRules;
	}

	public FlowNode(String code, String name, NodeType type, String remark, List<FlowEventRule> eventRules) {
		this.code = code;
		this.name = name;
		this.type = type;
		this.remark = remark;
		this.eventRules = eventRules;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public NodeType getType() {
		return type;
	}

	public String getRemark() {
		return remark;
	}

	/**
	 * 进入环节
	 */
	public void enter(ExecuteItem executeItem){
		FlowTokenService tokenService = executeItem.getEngine().token();
		executeItem.setToken(tokenService.passTokenAndRecord(executeItem.getToken(), this, executeItem.getOperator()));
		executeItem.setNode(this);
		executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_ENTER, executeItem);
		execute(executeItem);
	}
	/**
	 * 执行，传递令牌
	 */
	public void execute(ExecuteItem executeItem) {
		FlowTaskService taskService = executeItem.getEngine().task();
		executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_EXIT, executeItem);
		FlowNode jumpNode = executeItem.getJumpNode();
	
		if(jumpNode!=null){	//跳转
			executeItem.setJumpNode(null);
			List<FlowTaskDO> callbackTasks = taskService.findUnclosedTaskByInstanceId(executeItem.getInstance().getId());
			for(FlowTaskDO task : callbackTasks){
				executeItem.setTask(task);
				executeItem.getEngine().event().fireNodeEvent(FlowEvent.NODE_TASK_CLOSED, executeItem);
				task.setAction(executeItem.getAction().name());
				taskService.close(task, executeItem.getOperator(), executeItem.getAction(), "");
			}
			jumpNode.enter(executeItem);
		}else{
			runOutTransition(executeItem);
		}
	}

	/**
	 * 向前流转，根据流程线流转
	 * @param executeItem
	 */
	protected void runOutTransition(ExecuteItem executeItem) {
		for (FlowLine line : this.getOutLines()) {
			line.execute(executeItem);
		}
	}
	
	//模拟进入
	public List<AssignInfo> mockEnter(MockExecuteItem execution){
		FlowInstanceDO instance = execution.getInstance();
		execution.setInstance(instance);
		execution.setNode(this);
		return this.mockExecute(execution);
	}
	//模拟执行，传递令牌
	public List<AssignInfo> mockExecute(MockExecuteItem execution) {
		FlowNode jumpNode = execution.getJumpNode();
		if(jumpNode!=null){	//跳转
			return jumpNode.mockEnter(execution);
		}else{
			return mockRunOutTransition(execution);
		}
	}
	//模拟流转
	protected List<AssignInfo> mockRunOutTransition(MockExecuteItem execution) {
		for (FlowLine tm : this.getOutLines()) {
			List<AssignInfo> assignInfoList = tm.mockExecute(execution);
			if(!CollectionUtils.isEmpty(assignInfoList)){
				return assignInfoList;
			}
		}
		return List.of(AssignInfo.instanceAutoNode(this));
	}

	/**
	 * 检查
	 * @return
	 */
	public abstract List<String> check();
}
