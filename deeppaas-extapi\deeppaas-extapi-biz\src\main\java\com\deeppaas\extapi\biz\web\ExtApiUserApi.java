package com.deeppaas.extapi.biz.web;

import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.extapi.biz.entity.ExtApiUserDO;
import com.deeppaas.extapi.biz.model.ExtApiUserRightAddParam;
import com.deeppaas.extapi.biz.model.ExtApiUserRightVO;
import com.deeppaas.extapi.biz.service.ExtApiUserService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 接口授权用户接口
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@RequestAuth
@RestController
@RequestMapping("/api/extApi/user")
public class ExtApiUserApi {
    private final ExtApiUserService extApiUserService;

    public ExtApiUserApi(ExtApiUserService extApiUserService) {
        this.extApiUserService = extApiUserService;
    }

    /**
     * 列表
     */
    @GetMapping("/page")
    public RestModel<PageData<ExtApiUserDO>> page(PageSearch param) {
        PageData<ExtApiUserDO> pageData = extApiUserService.page(param);
        return RestModel.data(pageData);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public RestModel<ExtApiUserDO> get(@RequestParam String id) {
        ExtApiUserDO extApiUserDO = extApiUserService.get(id);
        return RestModel.data(extApiUserDO);
    }

    /**
     * 新增&修改
     *
     * @param param 参数
     * @return API主键ID
     */
    @PostMapping("/save")
    public RestModel<String> save(@RequestBody ExtApiUserDO param) {
        // 业务操作
        ExtApiUserDO extApiUserDO = extApiUserService.save(param);
        return RestModel.data(extApiUserDO.getId());
    }

    /**
     * 删除
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    public RestModel<String> delete(@BodyParam String id) {
        extApiUserService.delete(id);
        return RestModel.data("操作成功");
    }

    /**
     * 启用/禁用
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @PostMapping("/usable")
    public RestModel<String> usable(@BodyParam String id, @BodyParam Integer usable) {
        extApiUserService.usable(id, usable);
        return RestModel.data("操作成功");
    }

    /**
     * 新增接口权限
     *
     * @param param 添加参数
     * @return 操作结果
     */
    @PostMapping("/right/save")
    public RestModel<String> saveRight(@RequestBody ExtApiUserRightAddParam param) {
        // 参数检查
        param.check();
        // 业务操作
        extApiUserService.saveRight(param);
        return RestModel.data("操作成功");
    }

    /**
     * 删除接口权限
     *
     * @param id 接口权限主键ID
     * @return 操作结果
     */
    @PostMapping("/right/delete")
    public RestModel<String> deleteRight(@BodyParam String id) {
        // 业务操作
        extApiUserService.deleteRight(id);
        return RestModel.data("操作成功");
    }

    /**
     * API授权列表-获取接口授权用户列表
     */
    @GetMapping("/right/api")
    public RestModel<List<ExtApiUserRightVO>> listByApi(@RequestParam String apiId) {
        List<ExtApiUserRightVO> apiUserRightVOList = extApiUserService.listByApi(apiId);
        return RestModel.data(apiUserRightVOList);
    }

    /**
     * 用户被授权列表-获取用户被授权API列表
     */
    @GetMapping("/right/user")
    public RestModel<List<ExtApiUserRightVO>> listByUser(@RequestParam String userId) {
        List<ExtApiUserRightVO> apiUserRightVOList = extApiUserService.listByUser(userId);
        return RestModel.data(apiUserRightVOList);
    }
}
