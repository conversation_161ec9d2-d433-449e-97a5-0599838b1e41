package com.deeppaas.common.helper;

import com.deeppaas.common.exception.RunException;
import org.apache.commons.compress.utils.Sets;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.net.URLConnection;
import java.nio.file.Files;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

public class FileHelper {


    /**
     * 检查文件夹是否存在，如果不存在则创建文件夹
     *
     * @param dirFile
     */
    public static void existOrMkdir(File dirFile) {
        if (dirFile == null) {
            throw RunException.error("创建文件夹file不能为空");
        }
        if (!dirFile.exists()) {
            dirFile.mkdirs();
            dirFile.setReadable(true, false);
            dirFile.setExecutable(true, false);
            dirFile.setWritable(true, false);
        }
    }

    public static String buildFileSize(Long size) {
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";

        if (size < 1024) {
            fileSizeString = df.format((double) size) + "B";
        } else if (size < 1048576) {
            fileSizeString = df.format((double) size / 1024) + "KB";
        } else if (size < 1073741824) {
            fileSizeString = df.format((double) size / 1024 / 1024) + "MB";
        } else if (size < 1099511627776L) {
            fileSizeString = df.format((double) size / 1073741824) + "GB";
        } else if (size < 1125899906842624L) {
            fileSizeString = df.format((double) size / 1099511627776L) + "TB";
        } else {
            fileSizeString = df.format((double) size / 1125899906842624L) + "PB";
        }
        return fileSizeString;
    }

    public static boolean isImage(String name) {
        if (StringHelper.isEmpty(name)) {
            return false;
        }

        String contentType = URLConnection.guessContentTypeFromName(name);
        if (contentType != null) {
            return contentType.startsWith("image");
        }
        return false;
    }

    public static boolean isExcel(String name) {
        if (name.lastIndexOf(".") < 0)
            return false;
        name = name.toLowerCase();
        if (name.endsWith("xls") || name.endsWith("xlsx")) {
            return true;
        }
        return false;
    }

    public static boolean isPdf(String name) {
        if (name.lastIndexOf(".") < 0)
            return false;
        String suffix = name.substring(name.lastIndexOf(".") + 1);
        if (Objects.equals("pdf", suffix.toLowerCase())) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isOfd(String name) {
        if (name.lastIndexOf(".") < 0)
            return false;
        String suffix = name.substring(name.lastIndexOf(".") + 1);
        if (Objects.equals("ofd", suffix.toLowerCase())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断两个浮点数是否一致
     */
    public static boolean isEqualOfFloat(float a, float b) {
        if (Math.abs(Math.floor(a) - Math.floor(b)) <= 0) {
            return true;
        } else {
            return false;
        }
    }

    public static String getPosixPath(File file) {
        String posixPath = file.toURI().getPath();
        if (File.separator.equals("/")) {
            return posixPath;
        } else {
            return posixPath.replaceFirst("/", "");
        }
    }

    public static String getPosixPathBase64(File file) {
        return Base64Utils.encodeToUrlSafeString(FileHelper.getPosixPath(file).getBytes());
    }

    public static String getFileName(File file) {
        if (file.getName().indexOf(".") < 0)
            return file.getName();
        return file.getName().substring(0, file.getName().lastIndexOf("."));
    }

    public static String fileBase64(File file) {
        byte[] bytes = new byte[0];
        try {
            bytes = Files.readAllBytes(file.toPath());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return Base64Utils.encodeToString(bytes).trim();
    }

    /**
     * @Author：
     * @Description：获取文件夹下所有目录 ---一层级 不会向下递归
     * @Date：
     */
    public static List<File> getFiles(String path) {
        List<File> files = new ArrayList();
        File file = new File(path);
        File[] tempList = file.listFiles();
        Set<File> sets = Sets.newHashSet();
        for (int i = 0; i < tempList.length; i++) {

            if (tempList[i].isDirectory()) {
                sets.add(tempList[i]);
            }
        }
        files.addAll(sets);

        return files.stream().sorted(new AlphanumComparator()).collect(Collectors.toList());
    }


    /**
     * @Author：
     * @Description：获取某个目录下所有直接下级文件，包括目录下的子目录的下的文件
     * @Date：
     */
    public static List<File> getAllFiles(String path, Set<File> list) {
        if (CollectionUtils.isEmpty(list)) {
            list = Sets.newHashSet();
        }
        File file = new File(path);
        File[] files = file.listFiles();
        for (int i = 0; i < files.length; i++) {
            //判断是否是目录，是的话继续递归
            if (files[i].isDirectory()) {
                list.addAll(getAllFiles(files[i].getAbsolutePath(), list));
            } else {
                //否则添加到list
                //获取全部文件名
                //list.add(files[i].toString());
                //获取全部包+文件名
                list.add(files[i]);
            }
        }
        return list.stream().collect(Collectors.toList());
    }

    public static List<File> getFilesAndDirectory(String path, Set<File> list) {
        if (CollectionUtils.isEmpty(list)) {
            list = Sets.newHashSet();
        }
        File file = new File(path);
        File[] files = file.listFiles();
        if (file == null) {
            throw RunException.error("地址有误，请检查地址正确性：" + path);
        }
        for (int i = 0; i < files.length; i++) {
            //判断是否是目录，是的话继续递归
            File file1 = files[i];
            if (file1.isDirectory()) {
                if (file1.listFiles().length == 0) {
                    list.add(file1);
                }
                list.addAll(getFilesAndDirectory(file1.getAbsolutePath(), list));
            } else {
                //否则添加到list
                //获取全部文件名
                //list.add(files[i].toString());
                //获取全部包+文件名
                list.add(file1);
            }
        }
        return list.stream().collect(Collectors.toList());
    }


    public static List<File> getDirectory(String path, Set<File> list) {
        if (CollectionUtils.isEmpty(list)) {
            list = Sets.newHashSet();
        }
        File file = new File(path);

        File[] files = file.listFiles();
        List<File> files1 = Arrays.stream(files).filter(File::isDirectory).collect(Collectors.toList());
        list.addAll(files1);

        Set<File> finalList = list;
        files1.forEach(file1 -> {

            if (file1.isDirectory()) {
                if (file1.listFiles().length == 0) {
                    finalList.add(file1);
                }
                finalList.addAll(getDirectory(file1.getAbsolutePath(), finalList));
            }
        });

        return list.stream().collect(Collectors.toList());
    }

    public static String getSuffix(File file) {
        String sss = file.getName();
        if (sss.indexOf(".") > 0) {
            return sss.substring(sss.lastIndexOf("."));
        }
        return null;
    }



    public static String removeRootDirectory(String rootDirectory, String directoryPath) {
        if (StringHelper.isEmpty(rootDirectory) || StringHelper.isEmpty(directoryPath)) {
            return null;
        }
        String rootDirectoryFile = new File(rootDirectory).getAbsolutePath();
        String directoryPathFile = new File(directoryPath).getAbsolutePath();
        return directoryPathFile.substring(rootDirectoryFile.length()+1);

    }
}
