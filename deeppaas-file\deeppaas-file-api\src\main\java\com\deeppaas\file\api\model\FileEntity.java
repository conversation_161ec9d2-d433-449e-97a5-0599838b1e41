package com.deeppaas.file.api.model;

import java.io.InputStream;

/**
 * 文件实例
 * <AUTHOR>
 * @date 2020/7/15
 */
public abstract class FileEntity {
    /** 文件组 */
    protected String group;
    /** 文件KEY */
    protected String fileKey;

    public FileEntity(String group, String fileKey) {
        this.group = group;
        this.fileKey = fileKey;
    }

    public String getGroup() {
        return group;
    }

    public String getFileKey() {
        return fileKey;
    }

    public abstract InputStream getContent();

    public abstract void close();
}
