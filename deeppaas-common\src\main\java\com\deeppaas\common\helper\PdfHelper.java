package com.deeppaas.common.helper;

import com.itextpdf.text.Document;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/29
 */
public class PdfHelper {

    private static Logger logger = LoggerFactory.getLogger(PdfHelper.class);

    public static int getImageCount(File file) {
        PdfReader reader = null;
        try {
            reader = new PdfReader(file.getAbsolutePath());
            return reader.getNumberOfPages();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {

            if (reader != null)
                reader.close();
        }

    }


    public static void createPdfByTemplate(InputStream inputStream, Map<String, String> dataMap, String pdfFileName, Rectangle pageSize) {
        Document doc = new Document(pageSize, 0, 0, 0, 0);
        OutputStream os = null;
        // InputStream inputStream = null;
        PdfStamper ps = null;
        PdfReader reader = null;

        try {
            os = new FileOutputStream(pdfFileName);
            // inputStream = new FileInputStream(template);
            // 2 读入pdf表单
            reader = new PdfReader(inputStream);
            // 3 根据表单生成一个新的pdf
            ps = new PdfStamper(reader, os);
            // 4 获取pdf表单
            AcroFields form = ps.getAcroFields();
            // 5给表单添加中文字体
            BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
//            BaseFont bf =
            form.addSubstitutionFont(bf);
            // 6遍历data 给pdf表单表格赋值
            for (String key : dataMap.keySet()) {
                form.setField(key, dataMap.get(key));
            }
            ps.setFormFlattening(true);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                ps.close();
                reader.close();
                os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


}
