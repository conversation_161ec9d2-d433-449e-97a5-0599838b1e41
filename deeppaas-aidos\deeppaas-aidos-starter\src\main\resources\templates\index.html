<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <!-- <link rel="icon" type="image/svg+xml" href="/src/favicon.svg" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vite App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script>
    // 这一段修改需要同时替换java项目内代码
    (function () {
      var modelData = window.__INITIAL_MODEL_DATA || {}
      window.monitorCustom = {
        getUserUnique: () => modelData && modelData.user && modelData.user.code,
        catchResource: responseText => {
          try { var data = JSON.parse(responseText); return data && data.code && data.code !== 0; } catch (error) { return false }
        }
      };
    })()
  </script>
    <script type="module" crossorigin="" src="/static/assets/index.689bcdcb.js"></script>
    <link rel="stylesheet" href="/static/assets/index.9a7df853.css">
</body></html>