package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.dao.FlowTokenDao;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.entity.FlowTokenDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/22
 */
@Service
public class FlowTokenService {
    private final FlowTokenDao flowTokenDao;
    private final FlowPositionService flowPositionService;
    private final FlowTrackService flowTrackService;

    public FlowTokenService(FlowTokenDao flowTokenDao, FlowPositionService flowPositionService, FlowTrackService flowTrackService) {
        this.flowTokenDao = flowTokenDao;
        this.flowPositionService = flowPositionService;
        this.flowTrackService = flowTrackService;
    }

    /**
     * 传递令牌并且记录轨迹
     *
     * @param token
     * @param toNode
     * @param user
     * @return
     */
    @Transactional
    public FlowTokenDO passTokenAndRecord(FlowTokenDO token, FlowNode toNode, String user) {
        // todo 如果是撤回那么子流程需要处理
        String fromNodeCode = token.getNodeCode();
        String toNodeCode = toNode.getCode();
        if (Objects.equals(fromNodeCode, toNodeCode)) {
            token.setNodeCode(toNode.getCode());
            token.setOperator(user);
            token.setTaskId(null);
            token = flowTokenDao.save(token);
            /** 整理流程位置信息 */
            flowPositionService.updatePosition(token.getInstanceId(), toNode.getCode(), fromNodeCode);
            flowTrackService.saveTrack(token.getInstanceId(), fromNodeCode, toNode.getCode());
        }

        return token;
    }

    /**
     * 传递令牌不记录估计
     *
     * @param token
     * @param toNode
     * @param user
     * @return
     */
    @Transactional
    public FlowTokenDO passTokenNoRecord(FlowTokenDO token, FlowNode toNode, String user) {
        String fromNodeCode = token.getNodeCode();
        String toNodeCode = toNode.getCode();
        if (Objects.equals(fromNodeCode, toNodeCode)) {
            token.setNodeCode(toNode.getCode());
            token.setOperator(user);
            token.setTaskId(null);
            token = flowTokenDao.save(token);
        }
        return token;
    }

    public FlowTokenDO getTokenById(Long id) {
        return flowTokenDao.getById(id);
    }

    public List<FlowTokenDO> findChildToken(Long parentId) {
        return flowTokenDao.findByParentId(parentId);
    }

    @Transactional
    public void deleteByInstance(Long instanceId) {
        flowTokenDao.deleteByInstanceId(instanceId);
    }

    public List<FlowTokenDO> findByInstanceId(Long instanceId) {
        return flowTokenDao.findByInstanceId(instanceId);
    }

    @Transactional
    public void updateOperator(Long tokenId, String operator, Long taskId) {
        flowTokenDao.updateToken(tokenId, operator, taskId);
    }

    public List<FlowTokenDO> findAllChildToken(Long id) {
        List<FlowTokenDO> childTokens = flowTokenDao.findByParentId(id);
        for (FlowTokenDO childToken : childTokens) {
            childTokens.addAll(findAllChildToken(childToken.getId()));
        }
        return childTokens;
    }

    @Transactional
    public void deleteTokens(List<FlowTokenDO> childTokens) {
        if (!CollectionUtils.isEmpty(childTokens)) {
            flowTokenDao.deleteAll(childTokens);
        }
    }

    @Transactional
    public FlowTokenDO save(FlowTokenDO token) {
        return flowTokenDao.save(token);
    }
}
