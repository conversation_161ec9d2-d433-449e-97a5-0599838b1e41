package com.deeppaas.processConfiguration.domain.web;

import com.deeppaas.account.api.client.UserClient;
import com.deeppaas.account.api.client.model.User;
import com.deeppaas.process.biz.service.AidosProcessTaskService;
import com.deeppaas.processConfiguration.domain.aggregate.ProcessTaskConfigImpl;
import com.deeppaas.processConfiguration.domain.model.vo.ProcessConfigVO;
import com.deeppaas.taskInfo.biz.entity.TaskInfoImageDo;
import com.deeppaas.taskInfo.biz.service.TaskInfoImageService;
import com.deeppaas.template.biz.entity.AidosIntermediateTemplateTypeDO;
import com.deeppaas.template.biz.model.AidosImageTemplateVo;
import com.deeppaas.template.biz.model.ProcessTaskTemplateVo;
import com.deeppaas.template.biz.service.AidosImageTemplateTypeService;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.RequestAuth;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestAuth(auth = false)
@RequestMapping("/api/ai/process")
public class ProcessConfigApi {

    @Autowired
    private ProcessTaskConfigImpl processTaskConfigImpl;
    @Autowired
    private AidosProcessTaskService aidosProcessTaskService;

    private UserClient userClient;
    @Autowired
    private TaskInfoImageService taskInfoImageService;
    @Autowired
    private AidosImageTemplateTypeService aidosImageTemplateTypeService;

    ProcessConfigApi(UserClient userClient) {
        this.userClient = userClient;
    }

    @GetMapping("/config")
    public RestModel<List<ProcessConfigVO>> config(@RequestParam("name") String name) {
        //name
        if (StringHelper.isEmpty(name)) {
            name = "hide";
        }
        User userDO = userClient.getUserByCode(name);
        if (userDO == null) {
            List<User> userDOs = userClient.getAllUse(name);
            if (!CollectionUtils.isEmpty(userDOs))
                userDO = userDOs.get(0);
        }
        if (userDO == null)
            return RestModel.data(new ArrayList<>());
        List<ProcessConfigVO> list = processTaskConfigImpl.get(userDO.getId());

        return RestModel.data(list);
    }

    private static Integer ii = 1;

    @PostMapping("/taskCallBack")
    public RestModel taskCallBack(@RequestBody ProcessConfigVO processConfigVO) {

        aidosProcessTaskService.saveTaskCallBack(processConfigVO);

        return RestModel.success("接收成功");
    }
    @PostMapping("/callbackUpload")
    public RestModel callbackUpload(@RequestParam String id, @RequestParam("file") MultipartFile[] files) {
        if (StringHelper.isEmpty(id)) {
            return RestModel.error(400, "id不能为空");
        }
        String[] ids = id.split(",");
        if (!Objects.equals(ids.length, files.length)) {
            return RestModel.error(400, "id长度与图片数量长度不符");
        }

        List<TaskInfoImageDo> lists = taskInfoImageService.findAllBycallBackImageId(ids);
        Map<String, TaskInfoImageDo> taskInfoImageMap = lists.stream().collect(Collectors.toMap(TaskInfoImageDo::getCallBackImageId, Function.identity()));
        String path = System.getProperty("user.dir");
        try {
            List<TaskInfoImageDo> update = Lists.newArrayList();
            for (int i = 0; i < files.length; i++) {
                MultipartFile multipartFile = files[i];
                String idi = ids[i];
                //改名
                String paths = path + File.separator + "templates" + File.separator + multipartFile.getOriginalFilename();
                File desFile = new File(paths);
                if (!desFile.getParentFile().exists()) {
                    desFile.mkdirs();
                }
                multipartFile.transferTo(desFile);

                TaskInfoImageDo taskInfoImageDo = taskInfoImageMap.get(idi);
                if (taskInfoImageDo == null)
                    return RestModel.error(400, "id:" + idi + "。 未找到");
                byte[] bytes = paths.getBytes(StandardCharsets.UTF_8);
                taskInfoImageDo.setPath(Base64Utils.encodeToUrlSafeString(bytes));
                update.add(taskInfoImageDo);

            }
            if(!CollectionUtils.isEmpty(update))
                taskInfoImageService.save(update);

        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        return RestModel.success("新增成功");
    }


    @GetMapping("/getTemplate")
    public RestModel<List<ProcessTaskTemplateVo>> getTemplate(@RequestParam("templateId") String templateId,
                                                              @RequestParam("processTaskId") String processTaskId) {

        List<ProcessTaskTemplateVo> list = aidosProcessTaskService.getTemplateVo(processTaskId, templateId);
        return RestModel.data(list);
    }
    /**
     * 获取模板 根据业务类型id
     * */
    @GetMapping("/getTaskTemplate")
    public RestModel<List<AidosImageTemplateVo>> getTaskTemplate(@RequestParam("processTaskId") String processTaskId) {

        List<AidosImageTemplateVo> list = aidosProcessTaskService.getTemplateByProcessTaskId(processTaskId);
        return RestModel.data(list);
    }

    /**
     * 获取模板根据 模板类型
     * 根据  业务类型id   模板类型id
     * */
    @GetMapping("/getTemplateByType")
    public RestModel<List<AidosIntermediateTemplateTypeDO>> getTemplateByType(@RequestParam("processTaskId") String processTaskId,@RequestParam("id") String typeId) {

        List<AidosIntermediateTemplateTypeDO> list = aidosImageTemplateTypeService.findByProcessTaskId(processTaskId, typeId);
        return RestModel.data(list);
    }

    /**
     * 保存业务类型模板图片
     * */
    @PostMapping("/saveTaskTemplate")
    public RestModel saveTaskTemplate(@RequestBody List<AidosIntermediateTemplateTypeDO> aidosIntermediateTemplateTypeDO) {

        aidosImageTemplateTypeService.save(aidosIntermediateTemplateTypeDO);
        return RestModel.success("保存成功");
    }
    /*
    * 删除业务类型中模板图片
    * */
    @GetMapping("/delTaskTemplate")
    public RestModel saveTaskTemplate(@RequestParam("imageTemplateTypeId") String imageTemplateTypeId,@RequestParam("imageTemplateId") String imageTemplateId) {

        aidosImageTemplateTypeService.delAidosIntermediate(imageTemplateTypeId,imageTemplateId);
        return RestModel.success("保存成功");
    }

}
