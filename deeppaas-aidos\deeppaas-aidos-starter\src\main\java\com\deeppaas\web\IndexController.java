package com.deeppaas.web;

import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.account.api.client.model.LocalUser;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.common.web.handler.WebpImageHandler;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping
public class IndexController {

    /**
     * @return 登录页面
     */
    @GetMapping("/login")
    public String login(Model model, String originalUrl, HttpServletRequest request) {
        LocalUser localUser = LocalUserContext.getLocalUser();
        if (localUser != null) {
            if (StringHelper.isEmpty(originalUrl)) {
                return "redirect:/";
            } else {
                return "redirect:" + originalUrl;
            }
        }
        model.addAttribute("currentUser", null);
        // webpAccept
        model.addAttribute("webpAccept", WebpImageHandler.isWebpAccept(request));
        return "index";
    }

    /**
     * 首页
     */
    @RequestAuth
    @GetMapping(path = {"/", "/settings/**", "/addMould", "/process", "/dealFile","/eventCenter", "/plugins/**", "/dataCenter/**" , "/fileReporting/**"})
    public String index(Model model, HttpServletRequest request) {
        model.addAttribute("currentUser", LocalUserContext.getLocalUser());
        // webpAccept
        model.addAttribute("webpAccept", WebpImageHandler.isWebpAccept(request));
        return "index";
    }

    /**
     * 其他页
     */
    @RequestAuth(auth = false)
    @GetMapping(path = {"/error", "/403", "/404"})
    public String other(Model model, HttpServletRequest request) {
        model.addAttribute("currentUser", LocalUserContext.getLocalUser());
        model.addAttribute("webpAccept", WebpImageHandler.isWebpAccept(request));
        return "index";
    }

    /**
     * 前端发布系统页面
     */
    @RequestAuth
    @GetMapping("/release")
    public String release(Model model) {
        model.addAttribute("currentUser", LocalUserContext.getLocalUser());
        return "release";
    }




}
