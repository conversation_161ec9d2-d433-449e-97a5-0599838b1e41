package com.deeppaas.rule.biz.action;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.datastore.api.client.DataStoreDataClient;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.convert.ConditionConvert;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RConditionModel;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据同步动作
 * <AUTHOR>
 * @date 2022/9/28
 */
@Data
public class RDataEntitySyncAction  extends RAction{
    /**
     * 来源数据模型编号
     */
    private String sourceEntity;
    /**
     * 来源数据限制条件
     */
    private RConditionModel sourceCondition;
    /**
     * 本更新数据模型编号
     */
    private String targetEntity;
    /**
     * 本更新数据限制条件
     */
    private RConditionModel targetCondition;
    /**
     * 匹配字段
     */
    private MatchField[] matchFields;
    /**
     * 更新字段
     */
    private SyncField[] updateFields;

    @Override
    public Object execute(RuleContext ruleContext) {
        DataStoreDataClient dataStoreDataClient = ruleContext.getRuleEngine().dataStoreDataClient();
        DataStoreCondition sourceEntityCondition = ConditionConvert.buildDataStoreCondition(sourceCondition, ruleContext);
        DataStoreCondition targetEntityCondition = ConditionConvert.buildDataStoreCondition(targetCondition, ruleContext);

        List<DataStoreData> sourceDataList = dataStoreDataClient.query(sourceEntity, sourceEntityCondition);

        Set<Map<String, Object>> matchDataSet = new HashSet<>();
        for(DataStoreData sourceData : sourceDataList){
            Map<String, Object> matchData = getMatchData(sourceData);
            if(matchDataSet.contains(matchData)){
                throw RunException.optReject("同步数据匹配条件异常，来源数据中出现多条符合同一匹配条件数据,"+ JsonHelper.toJson(matchData));
            }
            matchDataSet.add(matchData);
        }
        DataStoreCondition[] matchConditions = new DataStoreCondition[matchFields.length+1];
        matchConditions[0] = targetEntityCondition;
        for(DataStoreData sourceData : sourceDataList) {
            Map<String, Object> matchData = getMatchData(sourceData);
            int i = 1;
            for(MatchField matchField : matchFields){
                if(matchData.get(matchField.sourceField)==null){
                    matchConditions[i++] = DataStoreCondition.isNull(matchField.targetField);
                }else {
                    matchConditions[i++] = DataStoreCondition.eq(matchField.targetField, matchData.get(matchField.sourceField), false);
                }
            }
            DataStoreCondition condition = DataStoreCondition.and(matchConditions);
            List<DataStoreData> updateDataList = dataStoreDataClient.query(targetEntity, condition);
            if(!CollectionUtils.isEmpty(updateDataList)){
                for(DataStoreData targetData : updateDataList){
                    ruleContext.setEffect(targetData);
                    for(SyncField updateField : updateFields){
                        if(StringUtils.hasText(updateField.getSourceField())){
                            targetData.put(updateField.targetField, sourceData.get(updateField.sourceField));
                        }else {
                            targetData.put(updateField.targetField, updateField.dataBind.getBindData(ruleContext));
                        }
                    }
                }
                dataStoreDataClient.update(targetEntity, updateDataList);
            }
        }
        return super.returnOrNext(null, ruleContext);
    }

    private Map<String, Object> getMatchData(DataStoreData sourceData) {
        Map<String, Object> matchMap = new HashMap<>();
        for(MatchField matchField : matchFields){
            matchMap.put(matchField.sourceField, sourceData.get(matchField.getSourceField()));
        }
        return matchMap;
    }

    /**
     * 匹配字段
     */
    public static class MatchField{
        /**
         * 更新数据模型字段
         */
        private String targetField;
        /**
         * 更新对应来源字段
         */
        private String sourceField;

        public String getTargetField() {
            return targetField;
        }

        public void setTargetField(String targetField) {
            this.targetField = targetField;
        }

        public String getSourceField() {
            return sourceField;
        }

        public void setSourceField(String sourceField) {
            this.sourceField = sourceField;
        }
    }
    /**
     * 更新字段
     */
    public static class SyncField{
        /**
         * 更新数据模型字段
         */
        private String targetField;
        /**
         * 更新值绑定
         */
        private RDataBind dataBind;
        /**
         * 更新对应来源字段
         */
        private String sourceField;

        public String getTargetField() {
            return targetField;
        }

        public void setTargetField(String targetField) {
            this.targetField = targetField;
        }

        public RDataBind getDataBind() {
            return dataBind;
        }

        public void setDataBind(RDataBind dataBind) {
            this.dataBind = dataBind;
        }

        public String getSourceField() {
            return sourceField;
        }

        public void setSourceField(String sourceField) {
            this.sourceField = sourceField;
        }
    }
}
