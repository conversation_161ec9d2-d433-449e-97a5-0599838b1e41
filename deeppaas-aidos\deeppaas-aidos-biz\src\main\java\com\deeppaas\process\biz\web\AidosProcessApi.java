package com.deeppaas.process.biz.web;

import com.deeppaas.process.biz.entity.AidosProcessDO;
import com.deeppaas.process.biz.service.AidosProcessService;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/process")
public class AidosProcessApi {


    @Autowired
    private AidosProcessService aidosProcessService;

    @PostMapping("/page")
    public RestModel<PageData<AidosProcessDO>> page(@RequestBody PageSearch pageSearch) {
        PageData<AidosProcessDO> list = aidosProcessService.page(pageSearch);
        return RestModel.data(list);
    }

    @GetMapping("/getAll")
    public RestModel<List<AidosProcessDO>> list() {
        List<AidosProcessDO> list = aidosProcessService.getAll();
        return RestModel.data(list);
    }

    @PostMapping("/save")
    public RestModel list(@RequestBody AidosProcessDO aidosProcessDO) {
        String message = aidosProcessService.save(aidosProcessDO);
        if(StringHelper.isNotEmpty(message)){
            return RestModel.error(500,message);
        }
        return RestModel.success("保存成功");
    }
    @GetMapping("/getById")
    public RestModel<AidosProcessDO> getById(@RequestParam String id) {
        AidosProcessDO model = aidosProcessService.get(id);
        return RestModel.data(model);
    }
    @GetMapping("/del")
    public RestModel list(@RequestParam String id) {
        aidosProcessService.del(id);
        return RestModel.success("删除成功");
    }

}
