package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.dao.FlowTrackDao;
import com.deeppaas.flow.biz.entity.FlowTrackDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class FlowTrackService {
	private final FlowTrackDao flowTrackDao;

	public FlowTrackService(FlowTrackDao flowTrackDao) {
		this.flowTrackDao = flowTrackDao;
	}

	@Transactional(rollbackFor = Exception.class)
	public void saveTrack(Long instanceId, String fromNode, String toNode) {
		FlowTrackDO track = new FlowTrackDO();
		track.setInstanceId(instanceId);
		track.setFormNode(fromNode);
		track.setToNode(toNode);
		track.setPassTime(LocalDateTime.now());
		flowTrackDao.save(track);
	}

	public List<FlowTrackDO> findTrack(Long instanceId) {
		return flowTrackDao.findByInstanceId(instanceId);
	}

    public void deleteByInstance(Long instanceId) {
        flowTrackDao.deleteAll(findTrack(instanceId));
    }
}
