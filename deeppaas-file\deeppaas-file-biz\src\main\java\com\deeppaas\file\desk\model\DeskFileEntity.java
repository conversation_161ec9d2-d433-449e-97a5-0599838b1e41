package com.deeppaas.file.desk.model;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.file.api.model.FileEntity;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/2/17
 */
public class DeskFileEntity extends FileEntity {
    /** 文件流 */
    private InputStream content;
    /** 文件流读取地址 */
    private final String filePath;

    public DeskFileEntity(String group, String fileKey, String filePath) {
        super(group, fileKey);
        this.filePath = filePath;
    }

    @Override
    public InputStream getContent() {
        try {
            this.content = new FileInputStream(filePath);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            throw RunException.error("get file input stream error");
        }
        return this.content;

    }

    @Override
    public void close() {
        if(this.content != null){
            try {
                this.content.close();
            } catch (IOException e) {
                e.printStackTrace();
                throw RunException.error("self file object close error");
            }
        }
    }
}
