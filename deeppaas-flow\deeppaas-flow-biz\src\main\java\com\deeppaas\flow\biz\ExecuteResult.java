package com.deeppaas.flow.biz;

import java.io.Serializable;
import java.util.List;

import com.deeppaas.flow.biz.model.AssignInfo;

/**
 * @ClassName: ExecuteResult 
 * @Description: 流程引擎执行结果
 * <AUTHOR>
 */
public class ExecuteResult implements Serializable{
	private static final long serialVersionUID = -7561439249582018619L;
	/** 是否成功流转 */
	private boolean success;
	/** 流程实例ID */
	private Long instanceId;
	/** 订单ID */
	private String orderId;
	/** 环节处理人信息 */
	private List<AssignInfo> assignInfos;

	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public Long getInstanceId() {
		return instanceId;
	}
	public void setInstanceId(Long instanceId) {
		this.instanceId = instanceId;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public List<AssignInfo> getAssignInfos() {
		return assignInfos;
	}

	public void setAssignInfos(List<AssignInfo> assignInfos) {
		this.assignInfos = assignInfos;
	}

	public static ExecuteResult successInstance(String orderId, Long instanceId) {
		ExecuteResult result = new ExecuteResult();
		result.setSuccess(true);
		result.setInstanceId(instanceId);
		result.setOrderId(orderId);
		return result;
	}
	public static ExecuteResult checkInstance(String orderId, Long instanceId) {
		ExecuteResult result = new ExecuteResult();
		result.setSuccess(false);
		result.setOrderId(orderId);
		result.setInstanceId(instanceId);
		return result;
	}
	public static ExecuteResult assignInstance(List<AssignInfo> assignInfos,String orderId, Long instanceId) {
		ExecuteResult result = new ExecuteResult();
		if(assignInfos==null){
			result.setSuccess(true);
		}else{
			result.setSuccess(false);
			result.setAssignInfos(assignInfos);
		}
		result.setOrderId(orderId);
		result.setInstanceId(instanceId);
		return result;
	}
}
