package com.deeppaas.rule.biz.action;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 判断动作
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@Data
public class RDecisionAction extends RAction {
    /**
     * 判断指向
     */
    private RDataBind dataBind;
    /**
     * 值为true时走向
     */
    String trueNext;
    /**
     * 值为false时走向
     */
    String falseNext;

    @Override
    public Object execute(RuleContext ruleContext) {
        boolean decision = (Boolean) dataBind.getBindData(ruleContext);
        // 如果返回结果变量话，那么给增加变量
        if(super.isReturnVar()){
            ruleContext.getVarMap().put(super.getVarName(), decision);
        }
        String nextActionId = decision ? trueNext : falseNext;
        if(StringUtils.hasText(nextActionId)){
            if(super.isReturned()){
                throw RunException.error("规则异常，动作["+super.getId()+"/"+super.getName()+"]不可以同时是返回动作又有下一个动作");
            }
            RAction nextAction = ruleContext.getAction(nextActionId);
            if(nextAction==null){
                throw RunException.error("规则异常，动作["+super.getId()+"/"+super.getNext()+"]的下一个动作[id="+nextActionId+"]不存在");
            }
            return nextAction.execute(ruleContext);
        }
        return decision;
    }

}
