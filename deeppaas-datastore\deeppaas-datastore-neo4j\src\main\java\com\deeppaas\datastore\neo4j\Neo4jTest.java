package com.deeppaas.datastore.neo4j;

import com.deeppaas.common.helper.JsonHelper;
import org.neo4j.driver.*;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
public class Neo4jTest {

    public static void main(String[] args){
        Driver driver = GraphDatabase.driver( "bolt://127.0.0.1:7687",
                AuthTokens.basic( "neo4j", "password" ) );
        //String cql = "MATCH (hero:Hero)-[r:JOIN_BATTLE]-> (battle:Battle) RETURN hero,r,battle";
        String cql = "MATCH (hero:Hero)-[r:JOIN_BATTLE]-> (battle:Battle) RETURN hero.name,r,battle.name";
        Object result = Neo4jUtils.query(driver, cql);
        System.out.println(JsonHelper.toJson(result));
        driver.close();
    }

}
