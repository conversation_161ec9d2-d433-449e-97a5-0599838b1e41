package com.deeppaas.quartz.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * API任务参数
 *
 * <AUTHOR>
 * @date 2022/3/29
 */
@Data
@Table("quartz_job_api_param")
public class JobApiParamDO{
    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 所属任务
     */
    @Column("job_id")
    private String jobId;

    /**
     * 参数code
     * <p>
     * ExtApiParamDO.code
     */
    @Column("code")
    private String code;

    /**
     * 参数值
     */
    @Column("value")
    private String value;

    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;
}
