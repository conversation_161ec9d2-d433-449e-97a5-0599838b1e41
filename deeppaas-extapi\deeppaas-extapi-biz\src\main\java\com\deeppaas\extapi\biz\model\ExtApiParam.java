package com.deeppaas.extapi.biz.model;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

/**
 * 接口
 *
 * <AUTHOR>
 * @date 2022/2/25
 */
@Data
public class ExtApiParam implements BaseModel {

    /**
     * 接口主键ID
     */
    private String id;

    /**
     * 接口名称
     */
    private String name;
    /**
     * 是否是平台已有接口
     */
    private Integer selfApi;
    /**
     * 接口方法
     * <p>
     * GET、POST
     */
    private String method;

    /**
     * 接口地址
     */
    private String url;

    /**
     * body类型
     * <p>
     * none、form-data、json、xml
     */
    private String reqBodyType;

    /**
     * 是否可用
     * 1=可用、0=不可用
     */
    private Integer usable;

    /**
     * 描述
     * <p>
     * 获取数据详情
     */
    private String remark;

    /**
     * 接口所属
     * <p>
     * 平台接口=SELF
     * 第三方接口=ExtApiUserDO.id
     */
    private String owner;

    /**
     * 返回体值类型
     */
    private String resBodyType;

    /**
     * 返回所属模型
     */
    private String resBodyEntity;
}
