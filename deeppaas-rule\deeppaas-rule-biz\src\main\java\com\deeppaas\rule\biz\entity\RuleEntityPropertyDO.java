package com.deeppaas.rule.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 计算模型属性
 *
 * <AUTHOR>
 * @date 2022/6/11
 */
@Data
@Table("rule_entity_property")
public class RuleEntityPropertyDO extends BaseUuidDO {
    /**
     * 所属计算模型ID
     */
    @Column("entity_id")
    private String entityId;
    /**
     * 属性名称，英文
     */
    @Column("name")
    private String name;
    /**
     * 模型备注，中文描述
     */
    @Column("remark")
    private String remark;

    /**
     * 类型：文本、数字、布尔、数据模型对象、规则模型对象、文件
     * 文本数组、数字数组、数据模型对象数组、规则模型对象数组、文件数组
     * <p>
     * 参考
     * @com.deeppaas.common.data.enums.SimpleDataType
     */
    @Column("type")
    private String type;

    /**
     * 对应模型：数据模型或数组为数据模型code,规则模型或数组为规则模型ID
     */
    @Column("type_entity")
    private String typeEntity;
}
