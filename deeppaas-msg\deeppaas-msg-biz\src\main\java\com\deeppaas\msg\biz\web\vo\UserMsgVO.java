package com.deeppaas.msg.biz.web.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息VO
 * <AUTHOR>
 * @date 2022/9/2
 */
@Data
public class UserMsgVO {
    private String id;
    /**
     * 所属消息模块
     */
    private String boxId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息正文
     */
    private String content;

    /**
     * 链接地址
     */
    private String msgUrl;

    /**
     * 接收人
     */
    private String toUser;

    /**
     * 1:已读/0:未读
     */
    private Integer readFlag;
    /**
     * 已读时间
     */
    private LocalDateTime readTime;
    private LocalDateTime createTime;
}
