package com.deeppaas.extapi.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 平台接口授权第三方信息
 *
 * <AUTHOR>
 * @date 2022/6/11
 */
@Data
@Table("ext_api_user_right")
@EqualsAndHashCode(callSuper = true)
public class ExtApiUserRightDO extends BaseUuidDO {
    /**
     * 平台接口ID
     */
    @Column("api_id")
    private String apiId;

    /**
     * 第三方用户ID
     */
    @Column("user_id")
    private String userId;
}
