package com.deeppaas.rule.api.client;

import com.deeppaas.rule.api.enums.RuleOwnerType;

import java.util.List;

/**
 * 事件规则客户端
 * <AUTHOR>
 * @date 2022/4/21
 */
public interface EventRuleClient {

    /**
     * 获取事件所属对象的指定事件规则
     * @param ownerType
     * @param ownerId
     * @param event
     * @return
     */
    List<String> findEventRules(RuleOwnerType ownerType, String ownerId, String event);
}
