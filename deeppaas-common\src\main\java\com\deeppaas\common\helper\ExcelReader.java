package com.deeppaas.common.helper;

import com.deeppaas.common.exception.RunException;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.hssf.usermodel.HSSFFormulaEvaluator;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFFormulaEvaluator;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * EXCEL简单读取器
 * <AUTHOR>
 * @date 2022/8/16
 */
public class ExcelReader {
    private static final DataFormatter formatter = new DataFormatter();
    private Workbook workbook;
    private FormulaEvaluator formulaEvaluator;

    private ExcelReader(Workbook workbook) {
        this.workbook = workbook;
        if (workbook instanceof HSSFWorkbook) {
            formulaEvaluator = new HSSFFormulaEvaluator((HSSFWorkbook) workbook);
        }
        if (workbook instanceof XSSFWorkbook) {
            formulaEvaluator = new XSSFFormulaEvaluator((XSSFWorkbook) workbook);
        }
        if (workbook instanceof SXSSFWorkbook) {
            formulaEvaluator = new SXSSFFormulaEvaluator((SXSSFWorkbook) workbook);
        }
    }
    public  Workbook getWorkbook(){
        return workbook;
    }
    public static ExcelReader of(InputStream inputStream) {
        try {
            return new ExcelReader(WorkbookFactory.create(inputStream));
        } catch (IOException e) {
            throw RunException.error("IO异常：" + e.getMessage());
        }
    }

    public static ExcelReader of(File file) {
        try {
            return new ExcelReader(WorkbookFactory.create(file));
        } catch (IOException e) {
            throw RunException.error("IO异常：" + e.getMessage());
        }
    }
    public Sheet getSheet(String sheetName) {
        Sheet sheet = workbook.getSheet(sheetName);
        return sheet;
    } public List<Sheet> getSheet() {
        Iterator<Sheet> iterator=workbook.sheetIterator();
        List<Sheet> list = new ArrayList<>();
        iterator.forEachRemaining(list::add);
        return  list;
    }

    public List<String> getSheetName() {
        Iterator<Sheet> iterator=workbook.sheetIterator();
        if(iterator==null){
            return Lists.newArrayList();
        }
        return IteratorUtils.toList(iterator).stream().map(Sheet::getSheetName).collect(Collectors.toList());
    }
    public List<String> getHeaders(String sheetName) {
        Sheet sheet = workbook.getSheet(sheetName);
        Row row=sheet.getRow(0);
        return Arrays.asList(readRow(sheet.getRow(0), row.getLastCellNum()));
    }

    public String[] getHeaders(String sheetName, int columnSize) {
        Sheet sheet = workbook.getSheet(sheetName);
        return readRow(sheet.getRow(0), columnSize);
    }
    public List<String> getHeaders(int sheetIndex) {
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        Row row=sheet.getRow(0);
        if(row==null)
            return null;
        return Arrays.asList(readRow(row, row.getLastCellNum()));
    }
    public String[] getHeaders(int sheetIndex, int columnSize) {
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        return readRow(sheet.getRow(0), columnSize);
    }

    public boolean checkHeaders(int sheetIndex, String[] headers) {
        if (headers == null) {
            return false;
        }
        String[] sheetHeaders = getHeaders(sheetIndex, headers.length);
        if (sheetHeaders.length != headers.length) {
            return false;
        }
        for (int i = 0; i < headers.length; i++) {
            if (!Objects.equals(sheetHeaders[i], headers[i])) {
                return false;
            }
        }
        return true;
    }

    public List<String[]> getData(int sheetIndex, int columnSize) {
        String[] headers = getHeaders(sheetIndex, columnSize);
        return getData(sheetIndex, 1, headers.length);
    }

    public List<String[]> getData(int sheetIndex, int rowStart, int columnSize) {
        List<String[]> data = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        int totalRow = sheet.getLastRowNum() + 1;
        for (int i = rowStart; i < totalRow; i++) {
            if(sheet.getRow(i)==null){
                return data;
            }
            String[] cellValues = readRow(sheet.getRow(i), columnSize);
            boolean hasValue = false;
            for(String cellValue : cellValues){
                if(StringUtils.hasText(cellValue)){
                    hasValue = true;
                    break;
                }
            }
            if(hasValue){
                data.add(cellValues);
            }
        }
        return data;
    }
    public List<String[]> getData(String sheetName, int columnSize) {
        String[] headers = getHeaders(sheetName, columnSize);
        return getData(sheetName, 1, headers.length);
    }

    public List<String[]> getData(String sheetName, int rowStart, int columnSize) {
        List<String[]> data = new ArrayList<>();
        Sheet sheet = workbook.getSheet(sheetName);
        int totalRow = sheet.getLastRowNum() + 1;
        for (int i = rowStart; i < totalRow; i++) {
            if(sheet.getRow(i)==null){
                return data;
            }
            String[] cellValues = readRow(sheet.getRow(i), columnSize);
            boolean hasValue = false;
            for(String cellValue : cellValues){
                if(StringUtils.hasText(cellValue)){
                    hasValue = true;
                    break;
                }
            }
            if(hasValue){
                data.add(cellValues);
            }
        }
        return data;
    }

    private String[] readRow(Row row, int columnSize) {

        if (row == null) {
            return new String[columnSize];
        }
        String[] cellValues = new String[columnSize];
        for(int cellNum=0; cellNum<columnSize; cellNum++){
            Cell cell = row.getCell(cellNum);
            if(cell!=null){
                cellValues[cellNum] = getStringValue(cell);
            }else {
                cellValues[cellNum] ="";
            }
        }
        return cellValues;
    }

    public String getStringValue(Cell cell) {
        return formatter.formatCellValue(cell, formulaEvaluator);
    }

    public void close() {
        workbook = null;
        formulaEvaluator = null;
    }
}
