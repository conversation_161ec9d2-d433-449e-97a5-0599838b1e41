package com.deeppaas.datastore.biz.convert;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.datastore.biz.entity.DataStoreTableFieldDO;
import com.deeppaas.datastore.common.mapping.DBColumn;

/**
 * 数据表字段转换器
 *
 * <AUTHOR>
 * @date 2022/3/30
 */
public class DataStoreTableFieldConvert {

    /**
     * 构建数据表字段DO
     *
     * @param tableId
     * @param column
     * @return
     */
    public static DataStoreTableFieldDO buildDO(String tableId, DBColumn column) {
        // 创建字段记录
        DataStoreTableFieldDO tableFieldDO = new DataStoreTableFieldDO();
        tableFieldDO.setTableId(tableId);
        tableFieldDO.setCode(column.getName());
        // 字段名称为空则给
        tableFieldDO.setName(StringHelper.isEmpty(column.getComment()) ? column.getName() : column.getComment());
        tableFieldDO.setColumnName(column.getName());

        tableFieldDO.setDataType(column.getType());
        tableFieldDO.setLength(column.getLength());
        tableFieldDO.setIdKey(BoolHelper.boolToInt(column.getIdKey()));
        tableFieldDO.setWebType("");
        tableFieldDO.setDbGenerated(BoolHelper.boolToInt(column.getDbGenerated()));
        return tableFieldDO;
    }
}
