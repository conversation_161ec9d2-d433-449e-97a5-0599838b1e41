package com.deeppaas.flow.biz.service;

import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.flow.biz.FlowConst;
import com.deeppaas.flow.biz.dao.FlowInstanceDao;
import com.deeppaas.flow.biz.dao.FlowInstanceUserDao;
import com.deeppaas.flow.biz.dao.FlowTaskDao;
import com.deeppaas.flow.biz.entity.*;
import com.deeppaas.flow.biz.enums.FlowInstanceState;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
@Transactional(readOnly = true)
public class FlowInstanceServiceImpl implements FlowInstanceService{
	private final FlowInstanceDao flowInstanceDao;
	private final FlowInstanceUserDao flowInstanceUserDao;
	private final FlowTaskDao flowTaskDao;
	private final FlowPositionService flowPositionService;
	private final FlowTrackService flowTrackService;

	public FlowInstanceServiceImpl(FlowInstanceDao flowInstanceDao, FlowInstanceUserDao flowInstanceUserDao, FlowTaskDao flowTaskDao, FlowPositionService flowPositionService, FlowTrackService flowTrackService) {
		this.flowInstanceDao = flowInstanceDao;
		this.flowInstanceUserDao = flowInstanceUserDao;
		this.flowTaskDao = flowTaskDao;
		this.flowPositionService = flowPositionService;
		this.flowTrackService = flowTrackService;
	}

	@Override
	public FlowInstanceDO getInstance(Long instanceId) {
		if(instanceId != null){
			return flowInstanceDao.getById(instanceId);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FlowInstanceDO close(FlowInstanceDO instance, FlowInstanceState state) {
		instance.setEndTime(LocalDateTime.now());
		instance.setState(state.getIntValue());
		return flowInstanceDao.save(instance);
	}
	
	@Override
	public FlowInstanceDO findByOrderId(String orderId) {
		return flowInstanceDao.findByOrderId(orderId);
	}

	@Override
	public FlowInstanceDO getInstance(String defineCode, String orderId) {
		return flowInstanceDao.findByDefineCodeAndOrderId(defineCode, orderId);
	}

	@Override
	public FlowInstanceDO getOpenInstance(String flowDefineCode, String orderId) {
		return flowInstanceDao.findByDefineCodeAndOrderIdAndState(flowDefineCode, orderId, FlowInstanceState.OPEN.getIntValue());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public FlowInstanceDO save(FlowInstanceDO instance) {
		return flowInstanceDao.save(instance);
	}

	@Override
	public FlowPositionDO findPrevPosition(FlowInstanceDO instance, String nodeCode) {
		FlowPositionDO position = flowPositionService.findByInstanceIdAndNodeCode(instance.getId(), nodeCode);
		if(position.getPrevId() != null){
			return flowPositionService.findById(position.getPrevId());
		}
		return null;
	}

	@Override
	public List<FlowTrackDO> findTrack(Long instanceId) {
		return flowTrackService.findTrack(instanceId);
	}

	@Override
	public void updatePriority(Long instanceId, Integer priority) {
		flowInstanceDao.updatePriority(instanceId, priority);
	}

    @Override
	@Transactional(rollbackFor = Exception.class)
    public void delete(FlowInstanceDO instance) {
        flowPositionService.deleteByInstance(instance.getId());
        flowTrackService.deleteByInstance(instance.getId());
		flowInstanceUserDao.deleteByInstance(instance.getId());
        flowInstanceDao.delete(instance);
    }

	@Override
	public List<FlowPositionDO> findAllPosition(Long instanceId) {
		return flowPositionService.findByInstanceId(instanceId);
	}

	@Override
	public void reloadInstanceUsers(Long instanceId) {
		List<FlowTaskDO> tasks = flowTaskDao.findByInstanceId(instanceId);
		Set<String> todoUserSet = new HashSet<>();
		Set<String> doneUserSet = new HashSet<>();
		for(FlowTaskDO task : tasks){
			if(FlowConst.TASK_STATE_OPEN==task.getState()){
				todoUserSet.addAll(task.getActors());
			}
			if(FlowConst.TASK_STATE_CLOSE==task.getState()
					&& task.getOperator()!=null){
				doneUserSet.add(task.getOperator());
			}
		}
		FlowInstanceUserDO usersDO = flowInstanceUserDao.getById(instanceId);
		if(usersDO==null){
			usersDO = new FlowInstanceUserDO();
			usersDO.setInstanceId(instanceId);
			usersDO.setTodoUsers(List.copyOf(todoUserSet));
			usersDO.setDoneUsers(List.copyOf(doneUserSet));
			flowInstanceUserDao.insert(usersDO);
		}else {
			usersDO.setTodoUsers(List.copyOf(todoUserSet));
			usersDO.setDoneUsers(List.copyOf(doneUserSet));
			flowInstanceUserDao.save(usersDO);
		}
	}

	@Override
	public Page<FlowInstanceDO> findForPage(PageSearch pageSearch) {
		JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
		return flowInstanceDao.queryForPage(condition, pageSearch.getPageable());
	}

}
