package com.deeppaas.msg.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.msg.biz.entity.UserMsgTemplateParamDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
public interface UserMsgTemplateParamDao extends BaseJdbcDao<UserMsgTemplateParamDO, String> {
    List<UserMsgTemplateParamDO> findByTemplateId(String templateId);

    @Modifying
    @Query("delete from deeppaas_user_msg_template_param where template_id=:templateId")
    void deleteByTemplateId(String templateId);
}
