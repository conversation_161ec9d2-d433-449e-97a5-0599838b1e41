//package com.deeppaas.file.api.config;
//
//import com.deeppaas.file.api.client.FileClient;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// * @date 2022/2/21
// */
//@Configuration//(proxyBeanMethods = false)
//public class FileConfiguration {
//
//    public String getFileMode(){
//        return null;
//    }
//
//    public FileClient getFileClient(){
//        return null;
//    }
//}
