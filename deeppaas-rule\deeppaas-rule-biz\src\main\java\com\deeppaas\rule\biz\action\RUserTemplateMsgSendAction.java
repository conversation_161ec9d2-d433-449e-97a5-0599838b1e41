package com.deeppaas.rule.biz.action;

import com.deeppaas.msg.api.client.model.UserTemplateMsgSendParam;
import com.deeppaas.rule.biz.RuleContext;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
@Data
public class RUserTemplateMsgSendAction extends RMsgSendAction{
    /**
     * 消息盒子
     */
    private String boxId;
    /**
     * 模版I
     */
    private String templateId;

    @Override
    public Object execute(RuleContext ruleContext) {
        UserTemplateMsgSendParam param = new UserTemplateMsgSendParam();
        param.setBoxId(boxId);
        param.setTemplateId(templateId);
        param.setParams(getParamMap(ruleContext));
        param.setToUser(getToUsers(ruleContext));
        ruleContext.getRuleEngine().msgClient().sendUserTemplateMsg(param);
        return null;
    }
}
