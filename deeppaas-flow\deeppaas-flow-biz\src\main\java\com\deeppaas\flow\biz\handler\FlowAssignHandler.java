//package com.deeppaas.flow.biz.handler;
//
//import java.util.Collection;
//
//import com.deeppaas.flow.biz.ExecuteItem;
//import com.deeppaas.flow.biz.element.TaskNode;
//import com.deeppaas.flow.biz.model.AssignInfo;
//
///**
// * 节点指派器
// * <AUTHOR>
// */
//public interface FlowAssignHandler {
//
//	/**
//	 * 指派生成任务
//	 * @param executeItem
//	 * @param node
//	 * @return
//	 */
//	boolean doAssign(ExecuteItem executeItem, TaskNode node);
//
//	/**
//	 * 模拟获取审批人
//	 * @param executeItem
//	 * @param node
//	 * @return
//	 */
//	Collection<String> mockAssignUser(ExecuteItem executeItem, TaskNode node);
//
//	/**
//	 * 模拟指派
//	 * @param executeItem
//	 * @param node
//	 * @return
//	 */
//	AssignInfo mockAssign(ExecuteItem executeItem, TaskNode node);
//
//}
