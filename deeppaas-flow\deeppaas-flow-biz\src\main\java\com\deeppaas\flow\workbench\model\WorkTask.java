package com.deeppaas.flow.workbench.model;

import com.deeppaas.account.api.client.model.User;
import com.deeppaas.common.model.BaseModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单任务
 *
 * <AUTHOR>
 * @date 2022/05/28
 */
@Data
public class WorkTask implements BaseModel {
    /**
     * 任务ID
     */
    private Long id;
    /**
     * 流程实例ID
     */
    private Long instanceId;
    /**
     * 令牌ID
     */
    private Long tokenId;
    /**
     * 环节编号
     */
    private String nodeCode;
    /**
     * 环节名称
     */
    private String nodeName;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 状态
     */
    private Integer state;
    /**
     * 处理类型
     */
    private String action;
    /**
     * 限定时间
     */
    private LocalDateTime limitedTime;
    /**
     * 顺签顺序，非顺签为0
     */
    private Integer sortNo;
    /**
     * 等待任务ID，用于加签记录发起加签的任务ID,非加签为0
     */
    private Long waitTaskId;

    /**
     * 处理人
     */
    private User operator;

    /**
     * 参与人
     */
    private List<User> actors;

    /**
     * 权限
     */
    private WorkOrderAuth orderAuth;
}
