package com.deeppaas.quartz.service;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.quartz.convert.QuartzJobConvert;
import com.deeppaas.quartz.dao.QuartzJobDao;
import com.deeppaas.quartz.dao.QuartzJobLogDao;
import com.deeppaas.quartz.entity.QuartzJobDO;
import com.deeppaas.quartz.model.QuartzJobParam;
import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 任务服务
 */
@Service
public class QuartzJobService {
    private static final Logger logger = LoggerFactory.getLogger(QuartzJobService.class);
    private final SchedulerFactoryBean schedulerFactoryBean;
    private final QuartzJobDao quartzJobDao;
    private final QuartzJobLogDao quartzJobLogDao;

    public QuartzJobService(SchedulerFactoryBean schedulerFactoryBean,
                            QuartzJobDao quartzJobDao,
                            QuartzJobLogDao quartzJobLogDao) {
        this.schedulerFactoryBean = schedulerFactoryBean;
        this.quartzJobDao = quartzJobDao;
        this.quartzJobLogDao = quartzJobLogDao;
    }

    public QuartzJobDO findByJobId(String jobId) {
        return quartzJobDao.getById(jobId);
    }

    public List<QuartzJobDO> findAll() {
        return quartzJobDao.findAll();
    }

    public List<QuartzJobDO> findByApp(String appCode) {
        return quartzJobDao.findByAppCode(appCode);
    }

    @Transactional
    public void updateFireInfo(String appCode, String jobId, LocalDateTime fireTime, LocalDateTime nextTime, String fireResult) {
        quartzJobDao.updateFireInfo(fireTime, nextTime, fireResult, jobId);
    }

    @Transactional
    public QuartzJobDO add(QuartzJobParam param) {
        QuartzJobDO jobDO = QuartzJobConvert.paramToEntity(param);
        jobDO = quartzJobDao.save(jobDO);
        // 创建任务
        boolean success = createScheduler(jobDO);
        if (success) {
            return jobDO;
        }
        throw RunException.error("新增定时任务失败，请稍后再试");
    }

    @Transactional
    public QuartzJobDO modify(QuartzJobParam param) {
        // 先保存记录信息
        QuartzJobDO jobDO = quartzJobDao.getById(param.getId());
        QuartzJobConvert.paramToEntity(jobDO, param);
        jobDO = quartzJobDao.save(jobDO);
        // 更新定时执行器
        boolean success = createScheduler(jobDO);
        if (success) {
            return jobDO;
        }
        throw RunException.error("修改定时任务失败，请稍后再试");
    }

    @Transactional
    public void start(String id) {
        QuartzJobDO jobDO = quartzJobDao.getById(id);
        jobDO.setUsed(BoolHelper.INT_TRUE);
        jobDO.setUpdateTime(LocalDateTime.now());
        jobDO = quartzJobDao.save(jobDO);
        // 更新定时执行器
        boolean success = createScheduler(jobDO);
        if (success) {
            return;
        }
        throw RunException.error("开启定时任务失败，请稍后再试");
    }

    @Transactional
    public void stop(String id) {
        QuartzJobDO jobDO = quartzJobDao.getById(id);
        jobDO.setUsed(BoolHelper.INT_FALSE);
        jobDO.setUpdateTime(LocalDateTime.now());
        quartzJobDao.save(jobDO);

        boolean success = deleteScheduler(jobDO.getId(), jobDO.getAppCode());
        if (success) {
            return;
        }
        throw RunException.error("开启定时任务失败，请稍后再试");
    }

    @Transactional
    public void delete(String jobId) {
        // 删除记录
        quartzJobDao.deleteById(jobId);
    }

    @Transactional
    public void run(String id) {
        QuartzJobDO jobDO = findByJobId(id);
        if (jobDO == null) {
            throw RunException.dataNotFound("任务不存在");
        }
        boolean success = runScheduler(jobDO);
        if (success) {
            return;
        }
        throw RunException.error("运行任务失败，请稍后再试");
    }

    public void loadAll() {
        List<QuartzJobDO> jobDOS = findAll();
        for (QuartzJobDO jobDO : jobDOS) {
            this.createScheduler(jobDO);
        }
    }

    /**
     * 创建定时任务实例
     */
    private boolean runScheduler(QuartzJobDO jobDO) {
        try {
            Scheduler scheduler = schedulerFactoryBean.getScheduler();
            //创建任务
            Class jobClass = Class.forName(jobDO.getJobClass());
            JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(jobDO.getId(), "TEMP_RUN").withDescription(jobDO.getJobName()).build();
            // 表达式调度构建器
            SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule();
            scheduleBuilder.withIntervalInSeconds(1);
            scheduleBuilder.withRepeatCount(2);
            // 构建一个新的trigger
            SimpleTrigger trigger = TriggerBuilder.newTrigger().withIdentity(jobDO.getId(), "TEMP_RUN").withSchedule(scheduleBuilder).startNow().build();
            scheduler.scheduleJob(jobDetail, trigger);

        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("运行实例任务[" + jobDO.getAppCode() + "-" + jobDO.getId() + "]异常|runScheduler");
            return false;
        }
        return true;
    }

    /**
     * 创建定时任务实例
     */
    private boolean createScheduler(QuartzJobDO jobDO) {
        if (BoolHelper.intToBool(jobDO.getUsed())) {
            try {
                Scheduler scheduler = schedulerFactoryBean.getScheduler();
                TriggerKey triggerKey = TriggerKey.triggerKey(jobDO.getId(), jobDO.getAppCode());
                CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
                if (trigger != null) {
                    String cronExpression = trigger.getCronExpression();
                    if (!Objects.equals(cronExpression, jobDO.getCron())) {
                        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(jobDO.getCron());
                        trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
                        scheduler.rescheduleJob(triggerKey, trigger);
                    }
                    logger.info("实例[" + scheduler.getSchedulerInstanceId() + "]任务[" + jobDO.getAppCode() + "-" + jobDO.getId() + "]载入成功");
                } else {
                    //创建任务
                    Class jobClass = Class.forName(jobDO.getJobClass());
                    JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(jobDO.getId()).withDescription(jobDO.getJobName()).build();
                    // 表达式调度构建器
                    CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(jobDO.getCron());
                    // 按新的cronExpression表达式构建一个新的trigger
                    trigger = TriggerBuilder.newTrigger().withIdentity(jobDO.getId(), jobDO.getAppCode()).withSchedule(scheduleBuilder).build();
                    scheduler.scheduleJob(jobDetail, trigger);
                    logger.info("实例[" + scheduler.getSchedulerInstanceId() + "]任务[" + jobDO.getAppCode() + "-" + jobDO.getId() + "]创建成功");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                logger.error("创建实例任务[" + jobDO.getAppCode() + "-" + jobDO.getId() + "]异常|createScheduler");
                return false;
            }
        }
        return true;
    }

    /**
     * 删除定时任务实例
     */
    public boolean deleteScheduler(String jobId, String appCode) {
        try {
            Scheduler scheduler = schedulerFactoryBean.getScheduler();
            TriggerKey triggerKey = TriggerKey.triggerKey(jobId, appCode);
            Trigger trigger = scheduler.getTrigger(triggerKey);
            if (trigger != null) {
                //获取触发器标识
                scheduler.pauseTrigger(triggerKey);
                scheduler.unscheduleJob(triggerKey);
                return scheduler.deleteJob(JobKey.jobKey(jobId, appCode));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("删除实例任务[" + appCode + "-" + jobId + "]异常|deleteScheduler");
        }
        return false;
    }
}
