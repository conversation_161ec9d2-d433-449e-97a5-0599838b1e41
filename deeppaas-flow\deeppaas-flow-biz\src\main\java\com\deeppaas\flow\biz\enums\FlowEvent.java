package com.deeppaas.flow.biz.enums;

/**
 * 流程事件
 * <AUTHOR>
 * @date 2022/2/22
 */
public enum FlowEvent {
    /**
     * 环节进入
     */
    NODE_ENTER,
    /**
     * 环节离开
     */
    NODE_EXIT,
    /**
     * 任务打开
     */
    NODE_TASK_OPEN,
    /**
     * 任务关闭
     */
    NODE_TASK_CLOSED,
    /**
     * 环节任务同意前
     */
    NODE_TASK_EXECUTE_BEFORE,
    /**
     * 环节任务同意后
     */
    NODE_TASK_EXECUTE_AFTER,
    /**
     * 环节任务驳回前
     */
    NODE_TASK_BACK_BEFORE,
    /**
     * 环节任务驳回后
     */
    NODE_TASK_BACK_AFTER,
    /**
     * 流程完成
     */
    INSTANCE_FINISH,
    /**
     * 提交前
     */
    INSTANCE_SUBMIT_BEFORE,
    /**
     * 提交后
     */
    INSTANCE_SUBMIT_AFTER,

    /**
     * 撤回前
     */
    INSTANCE_CANCEL_BEFORE,
    /**
     * 撤回后
     */
    INSTANCE_CANCEL_AFTER,
    /**
     * 中止前
     */
    INSTANCE_STOP_BEFORE,
    /**
     * 中止后
     */
    INSTANCE_STOP_AFTER;

}
