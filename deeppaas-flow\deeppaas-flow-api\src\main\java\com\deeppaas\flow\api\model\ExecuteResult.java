package com.deeppaas.flow.api.model;

import java.io.Serializable;

/**
 * @ClassName: ExecuteResult 
 * @Description: 流程引擎执行结果
 * <AUTHOR>
 * @date 2017年9月4日
 */
public class ExecuteResult implements Serializable{
	private static final long serialVersionUID = -7561439249582018619L;
	/** 是否成功流转 */
	private boolean success;
	/** 流程实例ID */
	private Long instanceId;
	/** 订单ID */
	private String orderId;
	/** 环节处理人信息 */
	private AssignInfo assignInfo;

	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public Long getInstanceId() {
		return instanceId;
	}
	public void setInstanceId(Long instanceId) {
		this.instanceId = instanceId;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public AssignInfo getAssignInfo() {
		return assignInfo;
	}
	public void setAssignInfo(AssignInfo assignInfo) {
		this.assignInfo = assignInfo;
	}

	public static ExecuteResult successInstance(String orderId, Long instanceId) {
		ExecuteResult result = new ExecuteResult();
		result.setSuccess(true);
		result.setInstanceId(instanceId);
		result.setOrderId(orderId);
		return result;
	}
	public static ExecuteResult checkInstance(String orderId, Long instanceId) {
		ExecuteResult result = new ExecuteResult();
		result.setSuccess(false);
		result.setOrderId(orderId);
		result.setInstanceId(instanceId);
		return result;
	}
	public static ExecuteResult assignInstance(AssignInfo assignInfo,String orderId, Long instanceId) {
		ExecuteResult result = new ExecuteResult();
		if(assignInfo==null){
			result.setSuccess(true);
		}else{
			result.setSuccess(false);
			result.setAssignInfo(assignInfo);
		}
		result.setOrderId(orderId);
		result.setInstanceId(instanceId);
		return result;
	}
}
