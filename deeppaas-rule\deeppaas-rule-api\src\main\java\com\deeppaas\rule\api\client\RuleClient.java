package com.deeppaas.rule.api.client;

import com.deeppaas.rule.api.model.RuleEntityPropertyPO;
import com.deeppaas.rule.api.model.RulePO;

import java.util.List;

/**
 * 规则相关客户端
 *
 * <AUTHOR>
 * @date 2022/2/17
 */
public interface RuleClient {

    /**
     * 执行规则
     *
     * @param ruleId 规则ID
     * @param data   当前处理数据
     */
    Object executeById(String ruleId, Object data);

    /**
     * 保存owner唯一的规则
     *
     * @param rulePO
     * @return
     */
    RulePO saveOwnerUniqueRule(RulePO rulePO);

    RulePO getRuleById(String ruleId);

    /**
     * 获取计算模型属性列表
     *
     * @param entityId 计算模型ID
     * @return 计算模型属性列表
     */
    List<RuleEntityPropertyPO> listEntityProperty(String entityId);
}
