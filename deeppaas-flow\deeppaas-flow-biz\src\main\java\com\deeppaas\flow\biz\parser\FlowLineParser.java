package com.deeppaas.flow.biz.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.deeppaas.flow.biz.element.FlowLine;
import com.deeppaas.flow.biz.element.FlowNode;

import java.util.Map;

/**
 * 流程线解析器
 * <AUTHOR>
 * @date 2021/12/28
 */
public class FlowLineParser {
    private static final String KEY_CODE = "code";
    private static final String KEY_TEXT = "text";
    private static final String KEY_FROM = "from";
    private static final String KEY_TO = "to";
    private static final String KEY_CONDITION = "condition";

    static void loadLine(JsonNode lineJson, Map<String, FlowNode> nodeMap){
        String code = lineJson.get(KEY_CODE).textValue();
        String text = lineJson.get(KEY_TEXT).textValue();
        String fromNodeCode = lineJson.get(KEY_FROM).textValue();
        String toNodeCode = lineJson.get(KEY_TO).textValue();
        String condition = lineJson.get(KEY_CONDITION).textValue();

        FlowNode fromNode = nodeMap.get(fromNodeCode);
        FlowNode toNode = nodeMap.get(toNodeCode);

        FlowLine flowLine = new FlowLine(code, text, fromNode, toNode, condition);
        fromNode.getOutLines().add(flowLine);
    }
}
