package com.deeppaas.taskInfo.biz.service;

import com.deeppaas.taskInfo.biz.dao.TaskInfoDao;
import com.deeppaas.taskInfo.biz.entity.TaskInfoDO;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Service
public class TaskInfoService {

    @Autowired
    private TaskInfoDao taskInfoDao;

    public List<TaskInfoDO> getAll() {
        return taskInfoDao.findAll();
    }

    public void insert(TaskInfoDO taskInfoDO) {
        String id = taskInfoDO.getId();
        if (StringHelper.isEmpty(id)) {
            taskInfoDO.setCreateTime(LocalDateTime.now());
            taskInfoDao.insert(taskInfoDO);
        } else {
            taskInfoDao.save(taskInfoDO);
        }
    }

    public void insert(List<TaskInfoDO> taskInfoDos) {
        taskInfoDao.insertAll(taskInfoDos);
    }

    public void del(String id) {
        taskInfoDao.deleteById(id);
    }

    public PageData<TaskInfoDO> page(PageSearch pageSearch) {
        pageSearch.setSortInfo("DESC_createTime");
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<TaskInfoDO> page = taskInfoDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }

    public List<TaskInfoDO> findAllById(String... id) {
        return (List<TaskInfoDO>) taskInfoDao.findAllById(Arrays.asList(id));
    }

    public TaskInfoDO get(String taskId) {
        return taskInfoDao.getById(taskId);
    }
}
