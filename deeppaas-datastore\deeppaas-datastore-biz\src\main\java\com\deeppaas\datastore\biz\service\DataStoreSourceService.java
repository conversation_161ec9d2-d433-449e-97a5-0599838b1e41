package com.deeppaas.datastore.biz.service;

import com.deeppaas.datastore.biz.dao.DataStoreSourceDao;
import com.deeppaas.datastore.biz.entity.DataStoreSourceDO;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@Service
public class DataStoreSourceService {
    private final DataStoreSourceDao dataStoreSourceDao;
    private final String defaultDbType;
    private final String defaultSchema;
    private final DataSource defaultDataSource;

    public DataStoreSourceService(DataStoreSourceDao dataStoreSourceDao,
                                  @Value("${dp.datasource.db-type}") String defaultDbType,
                                  @Value("${dp.datasource.default-schema}") String defaultSchema, DataSource dataSource) {
        this.dataStoreSourceDao = dataStoreSourceDao;
        this.defaultDbType = defaultDbType;
        this.defaultSchema = defaultSchema;
        this.defaultDataSource = dataSource;
    }

    public String getDefaultDbType() {
        return defaultDbType;
    }

    public List<DataStoreSourceDO> list() {
        return dataStoreSourceDao.findAll();
    }

    @Transactional
    public DataStoreSourceDO save(DataStoreSourceDO dataStoreSource) {
        return dataStoreSourceDao.save(dataStoreSource);
    }

    @Transactional
    public void deleteById(String id) {
        DataStoreSourceDO dataStoreSourceDO = dataStoreSourceDao.getById(id);
        if (dataStoreSourceDO != null) {
            dataStoreSourceDao.delete(dataStoreSourceDO);
        }
    }

    public DataStoreSourceDO findById(String id) {
        return dataStoreSourceDao.getById(id);
    }

    /**
     * 初始化默认数据源
     */
    @Transactional
    public void defaultInit(){
        DataStoreSourceDO defaultDS = dataStoreSourceDao.getById(DataStoreSourceDO.ID_DEFAULT);
        if(defaultDS==null){
            defaultDS = new DataStoreSourceDO();
            defaultDS.setId(DataStoreSourceDO.ID_DEFAULT);
            defaultDS.setName(DataStoreSourceDO.ID_DEFAULT);
            defaultDS.setDbType(defaultDbType);
            defaultDS.setDefaultSchema(defaultSchema);
            if(defaultDataSource instanceof HikariDataSource hds){
                defaultDS.setJdbcUrl(hds.getJdbcUrl());
                defaultDS.setUser(hds.getUsername());
                defaultDS.setPwd(hds.getPassword());
                defaultDS.setDriverClass(hds.getDriverClassName());
            }
            defaultDS.setCreateTime(LocalDateTime.now());
            dataStoreSourceDao.insert(defaultDS);
        }else {
            defaultDS.setName(DataStoreSourceDO.ID_DEFAULT);
            defaultDS.setDbType(defaultDbType);
            defaultDS.setDefaultSchema(defaultSchema);
            if(defaultDataSource instanceof HikariDataSource hds){
                defaultDS.setJdbcUrl(hds.getJdbcUrl());
                defaultDS.setUser(hds.getUsername());
                defaultDS.setPwd(hds.getPassword());
                defaultDS.setDriverClass(hds.getDriverClassName());
            }
            dataStoreSourceDao.save(defaultDS);
        }
    }

    /**
     * 数据源链接测试
     *
     * @param sourceDO 参数
     * @return 是否成功连接
     */
    public Boolean testConnection(DataStoreSourceDO sourceDO) {
        boolean success = true;
        Connection connection = null;
        try {
            SingleConnectionDataSource dataSource = new SingleConnectionDataSource();
            dataSource.setUrl(sourceDO.getJdbcUrl());
            dataSource.setUsername(sourceDO.getUser());
            dataSource.setPassword(sourceDO.getPwd());
            dataSource.setDriverClassName(sourceDO.getDriverClass());
            connection = dataSource.getConnection();
        } catch (Exception e) {
            e.printStackTrace();
            success = false;
        } finally {
            try {
                if (connection != null && !connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return success;
    }

//    public static void main(String[] args) {
//        DataStoreSourceDO sourceDO = new DataStoreSourceDO();
//        sourceDO.setName("TestConnection");
//        sourceDO.setJdbcUrl("**********************************************************************************************************************************");
//        sourceDO.setUser("java_dev");
//        sourceDO.setPwd("yLmFkvy66l");
//        sourceDO.setDriverClass("com.mysql.cj.jdbc.Driver");
//
//        boolean success = true;
//        Connection connection = null;
//        try {
//            SingleConnectionDataSource dataSource = new SingleConnectionDataSource();
//            dataSource.setUrl(sourceDO.getJdbcUrl());
//            dataSource.setUsername(sourceDO.getUser());
//            dataSource.setPassword(sourceDO.getPwd());
//            dataSource.setDriverClassName(sourceDO.getDriverClass());
//            connection = dataSource.getConnection();
//        } catch (Exception e) {
//            e.printStackTrace();
//            success = false;
//        } finally {
//            try {
//                if (connection != null && !connection.isClosed()) {
//                    connection.close();
//                }
//            } catch (SQLException e) {
//                e.printStackTrace();
//            }
//        }
//
//        if (success) {
//            System.out.println("连接成功");
//        } else {
//            System.out.println("连接失败");
//        }
//    }
}
