package com.deeppaas.common.web.handler;

import com.deeppaas.common.model.PageSearch;
import org.springframework.core.MethodParameter;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.ServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * PageSearch参数匹配器
 *
 * <AUTHOR>
 * @date 2016年7月14日
 */
public class PageParamHandler implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return PageSearch.class.isAssignableFrom(parameter.getParameterType());
    }

    @Override
    public Object resolveArgument(MethodParameter parameter,
                                  ModelAndViewContainer mavContainer, NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) {
        final int defaultPageNo = 1;
        final int defaultPageSize = 10;
        PageSearch pageSearch = new PageSearch();
        ServletRequestDataBinder dataBinder = new ServletRequestDataBinder(pageSearch, parameter.getParameterName());
        dataBinder.bind(webRequest.getNativeRequest(ServletRequest.class));
        if (pageSearch.getPageNo() == 0) {
            pageSearch.setPageNo(defaultPageNo);
        }
        if (pageSearch.getPageSize() == 0) {
            pageSearch.setPageSize(defaultPageSize);
        }
        Map<String, String> paramMap = new HashMap<>();
        webRequest.getParameterMap().forEach((key, value) -> {
            if (value.length == 1 && StringUtils.hasText(value[0])) {
                paramMap.put(key, value[0]);
            }
            if (value.length > 1) {
                paramMap.put(key, StringUtils.arrayToDelimitedString(value, ","));
            }
        });
        pageSearch.setParam(paramMap);
        return pageSearch;
    }

}
