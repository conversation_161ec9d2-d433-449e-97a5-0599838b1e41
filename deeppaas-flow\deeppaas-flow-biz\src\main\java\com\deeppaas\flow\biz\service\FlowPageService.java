package com.deeppaas.flow.biz.service;

import com.deeppaas.flow.biz.dao.FlowPageDao;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.element.FlowPageNode;
import com.deeppaas.flow.biz.entity.FlowDefineDO;
import com.deeppaas.flow.biz.entity.FlowPageDO;
import com.deeppaas.flow.biz.model.FlowDefineModel;
import com.deeppaas.flow.biz.parser.FlowDefineParser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
@Service
public class FlowPageService {
    private final FlowPageDao flowPageDao;

    public FlowPageService(FlowPageDao flowPageDao) {
        this.flowPageDao = flowPageDao;
    }

    public FlowPageDO findById(String id) {
        return flowPageDao.getById(id);
    }

    public FlowPageDO findByNode(String defineId, String nodeId) {
        return flowPageDao.findByDefineIdAndNodeId(defineId, nodeId);
    }

    public List<FlowPageDO> findByPageId(String page) {
        return flowPageDao.findByPageId(page);
    }

    @Transactional
    public void updateFlowPage(FlowDefineDO flowDefineDO) {
        FlowDefineModel defineModel = FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
        Map<String, FlowNode> nodeMap = defineModel.getNodes();
        flowPageDao.deleteByDefineId(flowDefineDO.getId());
        for(FlowNode flowNode : nodeMap.values()){
            if(flowNode instanceof FlowPageNode pageNode && StringUtils.hasText(pageNode.getPage())){
                FlowPageDO flowPageDO = new FlowPageDO();
                flowPageDO.setDefineId(flowDefineDO.getId());
                flowPageDO.setNodeId(pageNode.getCode());
                flowPageDO.setNodeName(pageNode.getName());
                flowPageDO.setPageId(pageNode.getPage());
                flowPageDO.setCreateTime(LocalDateTime.now());
                flowPageDao.save(flowPageDO);
            }
        }
    }

    @Transactional
    public void deleteFlowPage(String flowDefineId) {
        flowPageDao.deleteByDefineId(flowDefineId);
    }
}
