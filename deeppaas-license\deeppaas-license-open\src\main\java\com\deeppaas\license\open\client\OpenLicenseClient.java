package com.deeppaas.license.open.client;

import com.deeppaas.license.api.client.LicenseClient;
import com.deeppaas.license.api.model.License;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2022/11/2
 */
@Component
public class OpenLicenseClient implements LicenseClient {
    private static final License openLicense;
    static {
        openLicense = new License();
        openLicense.setExpiration("2222-01-01");
        openLicense.setUserLimit(License.INT_NO_LIMIT);
        openLicense.setOnlineUserLimit(License.INT_NO_LIMIT);
        openLicense.setTableLimit(License.INT_NO_LIMIT);
    }
    @Override
    public License getLicense() {
        return openLicense;
    }

    @Override
    public boolean addOnlineUser(String userId, String clientId, long expireTime) {
        return true;
    }

    @Override
    public void removeOnlineUser(String userId) {

    }

    @Override
    public boolean refreshOnlineUser(String userId, String clientId, long expireTime) {
        return true;
    }
}
