package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 流程处理人，用于离职时转任务
 * 设置之后所有任务自动转给代理人
 * <AUTHOR>
 * @date 2020/11/5
 */
@Data
@Table("flow_user_proxy")
public class FlowUserProxyDO implements BaseModel {
    @Id
    @Column("id")
    private Long id;
    /**
     * 授权用户
     */
    @Column("flow_user")
    private String flowUser;
    /**
     * 代理用户
     */
    @Column("proxy_user")
    private String proxyUser;
}
