package com.deeppaas.common.data.converter;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.WritingConverter;

/**
 * 数据转换器-布尔转字节
 * <AUTHOR>
 * @date 2022/7/21
 */
@WritingConverter
public class Boolean2NumberConverter implements Converter<Boolean, Byte> {

    public Byte convert(Boolean bool) {
        return bool ? Byte.valueOf("1") : Byte.valueOf("0");
    }

}
