package com.deeppaas.extapi.biz.enums;

/**
 * 请求消息体类型
 *
 * <AUTHOR>
 * @date 2022/06/16
 */
public enum ApiReqBodyType {
    /**
     * 无请求体
     */
    NONE("none", ""),

    /**
     * Form表单
     * <p>
     * 将表单的数据处理为一条消息，以标签为单元，用分隔符（这就是boundary的作用）分开
     */
    FORM_DATA("form-data", "multipart/form-data; boundary=----footfoodapplicationrequestnetwork"),

    /**
     * 原生Form表单
     * <p>
     * key1=val1&key2=val2
     */
    FORM_URLENCODED("x-www-form-urlencoded", "application/x-www-form-urlencoded"),

    /**
     * JSON数据
     */
    JSON("json", "application/json"),

    /**
     * 二进制流数据
     * <p>
     * 文件
     */
    BINARY("binary", "application/octet-stream");

    ApiReqBodyType(String type, String contentType) {
        this.type = type;
        this.contentType = contentType;
    }

    private String type;
    private String contentType;

    public String getType() {
        return type;
    }

    public String getContentType() {
        return contentType;
    }
}
