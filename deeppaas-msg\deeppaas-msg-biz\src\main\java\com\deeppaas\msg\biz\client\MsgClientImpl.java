package com.deeppaas.msg.biz.client;

import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.msg.api.client.MsgClient;
import com.deeppaas.msg.api.client.MsgFormat;
import com.deeppaas.msg.api.client.model.UserMsgSendParam;
import com.deeppaas.msg.api.client.model.UserTemplateMsgSendParam;
import com.deeppaas.msg.biz.entity.UserMsgDO;
import com.deeppaas.msg.biz.entity.UserMsgTemplateDO;
import com.deeppaas.msg.biz.service.UserMsgService;
import com.deeppaas.msg.biz.service.UserMsgTemplateService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
@Component
public class MsgClientImpl implements MsgClient {
    private final UserMsgService userMsgService;
    private final UserMsgTemplateService templateService;

    public MsgClientImpl(UserMsgService userMsgService, UserMsgTemplateService templateService) {
        this.userMsgService = userMsgService;
        this.templateService = templateService;
    }

    @Override
    public void sendUserMsg(UserMsgSendParam param) {
        if(!CollectionUtils.isEmpty(param.getToUser())){
            List<UserMsgDO> msgList = param.getToUser().stream().map(toUser -> {
                UserMsgDO userMsgDO = new UserMsgDO();
                userMsgDO.setBoxId(param.getBoxId());
                userMsgDO.setTitle(param.getTitle());
                userMsgDO.setContent(param.getContent());
                userMsgDO.setMsgUrl(param.getMsgUrl());
                userMsgDO.setToUser(toUser);
                userMsgDO.setReadFlag(BoolHelper.INT_FALSE);
                userMsgDO.setDel(BoolHelper.INT_FALSE);
                userMsgDO.setCreateTime(LocalDateTime.now());
                return userMsgDO;
            }).toList();
            userMsgService.saveAll(msgList);
        }
    }

    @Override
    public void sendUserTemplateMsg(UserTemplateMsgSendParam param) {
        UserMsgTemplateDO template = templateService.getById(param.getTemplateId());
        if(!CollectionUtils.isEmpty(param.getToUser())){
            List<UserMsgDO> msgList = param.getToUser().stream().map(toUser -> {
                UserMsgDO userMsgDO = new UserMsgDO();
                userMsgDO.setBoxId(param.getBoxId());
                userMsgDO.setTitle(MsgFormat.format(template.getTitle(), param.getParams()));
                userMsgDO.setContent(MsgFormat.format(template.getContent(), param.getParams()));
                userMsgDO.setMsgUrl(MsgFormat.format(template.getMsgUrl(), param.getParams()));
                userMsgDO.setToUser(toUser);
                userMsgDO.setReadFlag(BoolHelper.INT_FALSE);
                userMsgDO.setDel(BoolHelper.INT_FALSE);
                userMsgDO.setCreateTime(LocalDateTime.now());
                return userMsgDO;
            }).toList();
            userMsgService.saveAll(msgList);
        }
    }
}
