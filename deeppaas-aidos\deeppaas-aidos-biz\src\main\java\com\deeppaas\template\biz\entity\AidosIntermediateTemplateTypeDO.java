package com.deeppaas.template.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Data
@Table("aidos_intermediate_template_type")
public class AidosIntermediateTemplateTypeDO extends BaseUuidDO {

    /**
     * 过程id
     */
    @Column("process_id")
    private String processId;

    /**
     * 任务id
     */
    @Column("process_task_id")
    private String processTaskId;

    /**
     * 模板类别id
     */
    @Column("image_template_type_id")
    private String imageTemplateTypeId;

    /**
     * 模板id
     */
    @Column("image_template_id")
    private String imageTemplateId;
}
