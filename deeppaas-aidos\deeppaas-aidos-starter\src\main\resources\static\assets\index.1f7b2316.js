import{X as Gr,r as g,E as H,Q as nt,f as $,b as re,Y as we,_ as q,R as E,c as fr,a as pr,W as Ur,P as oe,Z as Yr,$ as ht,F as vr,J as qr,K as Kr,L as Jr,U as Zr,V as Qr,a0 as en,M as tn,T as rn,C as nn,e as Le}from"./index.689bcdcb.js";import{b as on,d as Oe,e as he,P as an,t as dr,u as sn,i as un,g as ln,c as gr}from"./reactNode.50ea6579.js";var cn=function(){if(typeof navigator=="undefined"||typeof window=="undefined")return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return!!(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(e==null?void 0:e.substr(0,4)))},fn=Gr()?g.exports.useLayoutEffect:g.exports.useEffect;function pn(e,t,r){return r?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function vn(e,t,r){var o=e[t]||{};return H(H({},o),r)}function dn(e,t,r,o){for(var a=r.points,i=Object.keys(e),n=0;n<i.length;n+=1){var u=i[n];if(pn(e[u].points,a,o))return"".concat(t,"-placement-").concat(u)}return""}function hr(e){var t=e.prefixCls,r=e.motion,o=e.animation,a=e.transitionName;return r||(o?{motionName:"".concat(t,"-").concat(o)}:a?{motionName:a}:null)}function gn(e){var t=e.prefixCls,r=e.visible,o=e.zIndex,a=e.mask,i=e.maskMotion,n=e.maskAnimation,u=e.maskTransitionName;if(!a)return null;var s={};return(i||u||n)&&(s=H({motionAppear:!0},hr({motion:i,prefixCls:t,transitionName:u,animation:n}))),g.exports.createElement(nt,$({},s,{visible:r,removeOnLeave:!0}),function(l){var v=l.className;return g.exports.createElement("div",{style:{zIndex:o},className:re("".concat(t,"-mask"),v)})})}var hn=function(e){if(!e)return!1;if(e.offsetParent)return!0;if(e.getBBox){var t=e.getBBox();if(t.width||t.height)return!0}if(e.getBoundingClientRect){var r=e.getBoundingClientRect();if(r.width||r.height)return!0}return!1};function mt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,o)}return r}function yt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mt(Object(r),!0).forEach(function(o){mn(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mt(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Se(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Se=function(t){return typeof t}:Se=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Se(e)}function mn(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var de,yn={Webkit:"-webkit-",Moz:"-moz-",ms:"-ms-",O:"-o-"};function $e(){if(de!==void 0)return de;de="";var e=document.createElement("p").style,t="Transform";for(var r in yn)r+t in e&&(de=r);return de}function mr(){return $e()?"".concat($e(),"TransitionProperty"):"transitionProperty"}function Ee(){return $e()?"".concat($e(),"Transform"):"transform"}function bt(e,t){var r=mr();r&&(e.style[r]=t,r!=="transitionProperty"&&(e.style.transitionProperty=t))}function Ve(e,t){var r=Ee();r&&(e.style[r]=t,r!=="transform"&&(e.style.transform=t))}function bn(e){return e.style.transitionProperty||e.style[mr()]}function wn(e){var t=window.getComputedStyle(e,null),r=t.getPropertyValue("transform")||t.getPropertyValue(Ee());if(r&&r!=="none"){var o=r.replace(/[^0-9\-.,]/g,"").split(",");return{x:parseFloat(o[12]||o[4],0),y:parseFloat(o[13]||o[5],0)}}return{x:0,y:0}}var Tn=/matrix\((.*)\)/,Cn=/matrix3d\((.*)\)/;function _n(e,t){var r=window.getComputedStyle(e,null),o=r.getPropertyValue("transform")||r.getPropertyValue(Ee());if(o&&o!=="none"){var a,i=o.match(Tn);if(i)i=i[1],a=i.split(",").map(function(u){return parseFloat(u,10)}),a[4]=t.x,a[5]=t.y,Ve(e,"matrix(".concat(a.join(","),")"));else{var n=o.match(Cn)[1];a=n.split(",").map(function(u){return parseFloat(u,10)}),a[12]=t.x,a[13]=t.y,Ve(e,"matrix3d(".concat(a.join(","),")"))}}else Ve(e,"translateX(".concat(t.x,"px) translateY(").concat(t.y,"px) translateZ(0)"))}var xn=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,be;function wt(e){var t=e.style.display;e.style.display="none",e.offsetHeight,e.style.display=t}function ae(e,t,r){var o=r;if(Se(t)==="object"){for(var a in t)t.hasOwnProperty(a)&&ae(e,a,t[a]);return}if(typeof o!="undefined"){typeof o=="number"&&(o="".concat(o,"px")),e.style[t]=o;return}return be(e,t)}function Pn(e){var t,r,o,a=e.ownerDocument,i=a.body,n=a&&a.documentElement;return t=e.getBoundingClientRect(),r=t.left,o=t.top,r-=n.clientLeft||i.clientLeft||0,o-=n.clientTop||i.clientTop||0,{left:r,top:o}}function yr(e,t){var r=e["page".concat(t?"Y":"X","Offset")],o="scroll".concat(t?"Top":"Left");if(typeof r!="number"){var a=e.document;r=a.documentElement[o],typeof r!="number"&&(r=a.body[o])}return r}function br(e){return yr(e)}function wr(e){return yr(e,!0)}function ye(e){var t=Pn(e),r=e.ownerDocument,o=r.defaultView||r.parentWindow;return t.left+=br(o),t.top+=wr(o),t}function ot(e){return e!=null&&e==e.window}function Tr(e){return ot(e)?e.document:e.nodeType===9?e:e.ownerDocument}function On(e,t,r){var o=r,a="",i=Tr(e);return o=o||i.defaultView.getComputedStyle(e,null),o&&(a=o.getPropertyValue(t)||o[t]),a}var Sn=new RegExp("^(".concat(xn,")(?!px)[a-z%]+$"),"i"),$n=/^(top|right|bottom|left)$/,Be="currentStyle",ze="runtimeStyle",ee="left",An="px";function Mn(e,t){var r=e[Be]&&e[Be][t];if(Sn.test(r)&&!$n.test(t)){var o=e.style,a=o[ee],i=e[ze][ee];e[ze][ee]=e[Be][ee],o[ee]=t==="fontSize"?"1em":r||0,r=o.pixelLeft+An,o[ee]=a,e[ze][ee]=i}return r===""?"auto":r}typeof window!="undefined"&&(be=window.getComputedStyle?On:Mn);function Te(e,t){return e==="left"?t.useCssRight?"right":e:t.useCssBottom?"bottom":e}function Tt(e){if(e==="left")return"right";if(e==="right")return"left";if(e==="top")return"bottom";if(e==="bottom")return"top"}function Ct(e,t,r){ae(e,"position")==="static"&&(e.style.position="relative");var o=-999,a=-999,i=Te("left",r),n=Te("top",r),u=Tt(i),s=Tt(n);i!=="left"&&(o=999),n!=="top"&&(a=999);var l="",v=ye(e);("left"in t||"top"in t)&&(l=bn(e)||"",bt(e,"none")),"left"in t&&(e.style[u]="",e.style[i]="".concat(o,"px")),"top"in t&&(e.style[s]="",e.style[n]="".concat(a,"px")),wt(e);var d=ye(e),f={};for(var c in t)if(t.hasOwnProperty(c)){var p=Te(c,r),h=c==="left"?o:a,w=v[c]-d[c];p===c?f[p]=h+w:f[p]=h-w}ae(e,f),wt(e),("left"in t||"top"in t)&&bt(e,l);var y={};for(var b in t)if(t.hasOwnProperty(b)){var C=Te(b,r),_=t[b]-v[b];b===C?y[C]=f[C]+_:y[C]=f[C]-_}ae(e,y)}function En(e,t){var r=ye(e),o=wn(e),a={x:o.x,y:o.y};"left"in t&&(a.x=o.x+t.left-r.left),"top"in t&&(a.y=o.y+t.top-r.top),_n(e,a)}function Dn(e,t,r){if(r.ignoreShake){var o=ye(e),a=o.left.toFixed(0),i=o.top.toFixed(0),n=t.left.toFixed(0),u=t.top.toFixed(0);if(a===n&&i===u)return}r.useCssRight||r.useCssBottom?Ct(e,t,r):r.useCssTransform&&Ee()in document.body.style?En(e,t):Ct(e,t,r)}function at(e,t){for(var r=0;r<e.length;r++)t(e[r])}function Cr(e){return be(e,"boxSizing")==="border-box"}var Nn=["margin","border","padding"],Ye=-1,Rn=2,qe=1,jn=0;function Hn(e,t,r){var o={},a=e.style,i;for(i in t)t.hasOwnProperty(i)&&(o[i]=a[i],a[i]=t[i]);r.call(e);for(i in t)t.hasOwnProperty(i)&&(a[i]=o[i])}function me(e,t,r){var o=0,a,i,n;for(i=0;i<t.length;i++)if(a=t[i],a)for(n=0;n<r.length;n++){var u=void 0;a==="border"?u="".concat(a).concat(r[n],"Width"):u=a+r[n],o+=parseFloat(be(e,u))||0}return o}var K={getParent:function(t){var r=t;do r.nodeType===11&&r.host?r=r.host:r=r.parentNode;while(r&&r.nodeType!==1&&r.nodeType!==9);return r}};at(["Width","Height"],function(e){K["doc".concat(e)]=function(t){var r=t.document;return Math.max(r.documentElement["scroll".concat(e)],r.body["scroll".concat(e)],K["viewport".concat(e)](r))},K["viewport".concat(e)]=function(t){var r="client".concat(e),o=t.document,a=o.body,i=o.documentElement,n=i[r];return o.compatMode==="CSS1Compat"&&n||a&&a[r]||n}});function _t(e,t,r){var o=r;if(ot(e))return t==="width"?K.viewportWidth(e):K.viewportHeight(e);if(e.nodeType===9)return t==="width"?K.docWidth(e):K.docHeight(e);var a=t==="width"?["Left","Right"]:["Top","Bottom"],i=t==="width"?e.getBoundingClientRect().width:e.getBoundingClientRect().height,n=Cr(e),u=0;(i==null||i<=0)&&(i=void 0,u=be(e,t),(u==null||Number(u)<0)&&(u=e.style[t]||0),u=parseFloat(u)||0),o===void 0&&(o=n?qe:Ye);var s=i!==void 0||n,l=i||u;return o===Ye?s?l-me(e,["border","padding"],a):u:s?o===qe?l:l+(o===Rn?-me(e,["border"],a):me(e,["margin"],a)):u+me(e,Nn.slice(o),a)}var kn={position:"absolute",visibility:"hidden",display:"block"};function xt(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var o,a=t[0];return a.offsetWidth!==0?o=_t.apply(void 0,t):Hn(a,kn,function(){o=_t.apply(void 0,t)}),o}at(["width","height"],function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);K["outer".concat(t)]=function(o,a){return o&&xt(o,e,a?jn:qe)};var r=e==="width"?["Left","Right"]:["Top","Bottom"];K[e]=function(o,a){var i=a;if(i!==void 0){if(o){var n=Cr(o);return n&&(i+=me(o,["padding","border"],r)),ae(o,e,i)}return}return o&&xt(o,e,Ye)}});function _r(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);return e}var m={getWindow:function(t){if(t&&t.document&&t.setTimeout)return t;var r=t.ownerDocument||t;return r.defaultView||r.parentWindow},getDocument:Tr,offset:function(t,r,o){if(typeof r!="undefined")Dn(t,r,o||{});else return ye(t)},isWindow:ot,each:at,css:ae,clone:function(t){var r,o={};for(r in t)t.hasOwnProperty(r)&&(o[r]=t[r]);var a=t.overflow;if(a)for(r in t)t.hasOwnProperty(r)&&(o.overflow[r]=t.overflow[r]);return o},mix:_r,getWindowScrollLeft:function(t){return br(t)},getWindowScrollTop:function(t){return wr(t)},merge:function(){for(var t={},r=0;r<arguments.length;r++)m.mix(t,r<0||arguments.length<=r?void 0:arguments[r]);return t},viewportWidth:0,viewportHeight:0};_r(m,K);var We=m.getParent;function Ke(e){if(m.isWindow(e)||e.nodeType===9)return null;var t=m.getDocument(e),r=t.body,o,a=m.css(e,"position"),i=a==="fixed"||a==="absolute";if(!i)return e.nodeName.toLowerCase()==="html"?null:We(e);for(o=We(e);o&&o!==r&&o.nodeType!==9;o=We(o))if(a=m.css(o,"position"),a!=="static")return o;return null}var Pt=m.getParent;function In(e){if(m.isWindow(e)||e.nodeType===9)return!1;var t=m.getDocument(e),r=t.body,o=null;for(o=Pt(e);o&&o!==r&&o!==t;o=Pt(o)){var a=m.css(o,"position");if(a==="fixed")return!0}return!1}function it(e,t){for(var r={left:0,right:1/0,top:0,bottom:1/0},o=Ke(e),a=m.getDocument(e),i=a.defaultView||a.parentWindow,n=a.body,u=a.documentElement;o;){if((navigator.userAgent.indexOf("MSIE")===-1||o.clientWidth!==0)&&o!==n&&o!==u&&m.css(o,"overflow")!=="visible"){var s=m.offset(o);s.left+=o.clientLeft,s.top+=o.clientTop,r.top=Math.max(r.top,s.top),r.right=Math.min(r.right,s.left+o.clientWidth),r.bottom=Math.min(r.bottom,s.top+o.clientHeight),r.left=Math.max(r.left,s.left)}else if(o===n||o===u)break;o=Ke(o)}var l=null;if(!m.isWindow(e)&&e.nodeType!==9){l=e.style.position;var v=m.css(e,"position");v==="absolute"&&(e.style.position="fixed")}var d=m.getWindowScrollLeft(i),f=m.getWindowScrollTop(i),c=m.viewportWidth(i),p=m.viewportHeight(i),h=u.scrollWidth,w=u.scrollHeight,y=window.getComputedStyle(n);if(y.overflowX==="hidden"&&(h=i.innerWidth),y.overflowY==="hidden"&&(w=i.innerHeight),e.style&&(e.style.position=l),t||In(e))r.left=Math.max(r.left,d),r.top=Math.max(r.top,f),r.right=Math.min(r.right,d+c),r.bottom=Math.min(r.bottom,f+p);else{var b=Math.max(h,d+c);r.right=Math.min(r.right,b);var C=Math.max(w,f+p);r.bottom=Math.min(r.bottom,C)}return r.top>=0&&r.left>=0&&r.bottom>r.top&&r.right>r.left?r:null}function Ln(e,t,r,o){var a=m.clone(e),i={width:t.width,height:t.height};return o.adjustX&&a.left<r.left&&(a.left=r.left),o.resizeWidth&&a.left>=r.left&&a.left+i.width>r.right&&(i.width-=a.left+i.width-r.right),o.adjustX&&a.left+i.width>r.right&&(a.left=Math.max(r.right-i.width,r.left)),o.adjustY&&a.top<r.top&&(a.top=r.top),o.resizeHeight&&a.top>=r.top&&a.top+i.height>r.bottom&&(i.height-=a.top+i.height-r.bottom),o.adjustY&&a.top+i.height>r.bottom&&(a.top=Math.max(r.bottom-i.height,r.top)),m.mix(a,i)}function st(e){var t,r,o;if(!m.isWindow(e)&&e.nodeType!==9)t=m.offset(e),r=m.outerWidth(e),o=m.outerHeight(e);else{var a=m.getWindow(e);t={left:m.getWindowScrollLeft(a),top:m.getWindowScrollTop(a)},r=m.viewportWidth(a),o=m.viewportHeight(a)}return t.width=r,t.height=o,t}function Ot(e,t){var r=t.charAt(0),o=t.charAt(1),a=e.width,i=e.height,n=e.left,u=e.top;return r==="c"?u+=i/2:r==="b"&&(u+=i),o==="c"?n+=a/2:o==="r"&&(n+=a),{left:n,top:u}}function Ce(e,t,r,o,a){var i=Ot(t,r[1]),n=Ot(e,r[0]),u=[n.left-i.left,n.top-i.top];return{left:Math.round(e.left-u[0]+o[0]-a[0]),top:Math.round(e.top-u[1]+o[1]-a[1])}}function St(e,t,r){return e.left<r.left||e.left+t.width>r.right}function $t(e,t,r){return e.top<r.top||e.top+t.height>r.bottom}function Vn(e,t,r){return e.left>r.right||e.left+t.width<r.left}function Bn(e,t,r){return e.top>r.bottom||e.top+t.height<r.top}function _e(e,t,r){var o=[];return m.each(e,function(a){o.push(a.replace(t,function(i){return r[i]}))}),o}function xe(e,t){return e[t]=-e[t],e}function At(e,t){var r;return/%$/.test(e)?r=parseInt(e.substring(0,e.length-1),10)/100*t:r=parseInt(e,10),r||0}function Mt(e,t){e[0]=At(e[0],t.width),e[1]=At(e[1],t.height)}function xr(e,t,r,o){var a=r.points,i=r.offset||[0,0],n=r.targetOffset||[0,0],u=r.overflow,s=r.source||e;i=[].concat(i),n=[].concat(n),u=u||{};var l={},v=0,d=!!(u&&u.alwaysByViewport),f=it(s,d),c=st(s);Mt(i,c),Mt(n,t);var p=Ce(c,t,a,i,n),h=m.merge(c,p);if(f&&(u.adjustX||u.adjustY)&&o){if(u.adjustX&&St(p,c,f)){var w=_e(a,/[lr]/gi,{l:"r",r:"l"}),y=xe(i,0),b=xe(n,0),C=Ce(c,t,w,y,b);Vn(C,c,f)||(v=1,a=w,i=y,n=b)}if(u.adjustY&&$t(p,c,f)){var _=_e(a,/[tb]/gi,{t:"b",b:"t"}),x=xe(i,1),T=xe(n,1),D=Ce(c,t,_,x,T);Bn(D,c,f)||(v=1,a=_,i=x,n=T)}v&&(p=Ce(c,t,a,i,n),m.mix(h,p));var O=St(p,c,f),A=$t(p,c,f);if(O||A){var k=a;O&&(k=_e(a,/[lr]/gi,{l:"r",r:"l"})),A&&(k=_e(a,/[tb]/gi,{t:"b",b:"t"})),a=k,i=r.offset||[0,0],n=r.targetOffset||[0,0]}l.adjustX=u.adjustX&&O,l.adjustY=u.adjustY&&A,(l.adjustX||l.adjustY)&&(h=Ln(p,c,f,l))}return h.width!==c.width&&m.css(s,"width",m.width(s)+h.width-c.width),h.height!==c.height&&m.css(s,"height",m.height(s)+h.height-c.height),m.offset(s,{left:h.left,top:h.top},{useCssRight:r.useCssRight,useCssBottom:r.useCssBottom,useCssTransform:r.useCssTransform,ignoreShake:r.ignoreShake}),{points:a,offset:i,targetOffset:n,overflow:l}}function zn(e,t){var r=it(e,t),o=st(e);return!r||o.left+o.width<=r.left||o.top+o.height<=r.top||o.left>=r.right||o.top>=r.bottom}function ut(e,t,r){var o=r.target||t,a=st(o),i=!zn(o,r.overflow&&r.overflow.alwaysByViewport);return xr(e,a,r,i)}ut.__getOffsetParent=Ke;ut.__getVisibleRectForElement=it;function Wn(e,t,r){var o,a,i=m.getDocument(e),n=i.defaultView||i.parentWindow,u=m.getWindowScrollLeft(n),s=m.getWindowScrollTop(n),l=m.viewportWidth(n),v=m.viewportHeight(n);"pageX"in t?o=t.pageX:o=u+t.clientX,"pageY"in t?a=t.pageY:a=s+t.clientY;var d={left:o,top:a,width:0,height:0},f=o>=0&&o<=u+l&&a>=0&&a<=s+v,c=[r.points[0],"cc"];return xr(e,d,yt(yt({},r),{},{points:c}),f)}function Fn(){this.__data__=[],this.size=0}var Xn=Fn;function Gn(e,t){return e===t||e!==e&&t!==t}var Pr=Gn,Un=Pr;function Yn(e,t){for(var r=e.length;r--;)if(Un(e[r][0],t))return r;return-1}var De=Yn,qn=De,Kn=Array.prototype,Jn=Kn.splice;function Zn(e){var t=this.__data__,r=qn(t,e);if(r<0)return!1;var o=t.length-1;return r==o?t.pop():Jn.call(t,r,1),--this.size,!0}var Qn=Zn,eo=De;function to(e){var t=this.__data__,r=eo(t,e);return r<0?void 0:t[r][1]}var ro=to,no=De;function oo(e){return no(this.__data__,e)>-1}var ao=oo,io=De;function so(e,t){var r=this.__data__,o=io(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}var uo=so,lo=Xn,co=Qn,fo=ro,po=ao,vo=uo;function ie(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}ie.prototype.clear=lo;ie.prototype.delete=co;ie.prototype.get=fo;ie.prototype.has=po;ie.prototype.set=vo;var Ne=ie,go=Ne;function ho(){this.__data__=new go,this.size=0}var mo=ho;function yo(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var bo=yo;function wo(e){return this.__data__.get(e)}var To=wo;function Co(e){return this.__data__.has(e)}var _o=Co,xo=typeof we=="object"&&we&&we.Object===Object&&we,Or=xo,Po=Or,Oo=typeof self=="object"&&self&&self.Object===Object&&self,So=Po||Oo||Function("return this")(),J=So,$o=J,Ao=$o.Symbol,lt=Ao,Et=lt,Sr=Object.prototype,Mo=Sr.hasOwnProperty,Eo=Sr.toString,ge=Et?Et.toStringTag:void 0;function Do(e){var t=Mo.call(e,ge),r=e[ge];try{e[ge]=void 0;var o=!0}catch{}var a=Eo.call(e);return o&&(t?e[ge]=r:delete e[ge]),a}var No=Do,Ro=Object.prototype,jo=Ro.toString;function Ho(e){return jo.call(e)}var ko=Ho,Dt=lt,Io=No,Lo=ko,Vo="[object Null]",Bo="[object Undefined]",Nt=Dt?Dt.toStringTag:void 0;function zo(e){return e==null?e===void 0?Bo:Vo:Nt&&Nt in Object(e)?Io(e):Lo(e)}var Re=zo;function Wo(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var $r=Wo,Fo=Re,Xo=$r,Go="[object AsyncFunction]",Uo="[object Function]",Yo="[object GeneratorFunction]",qo="[object Proxy]";function Ko(e){if(!Xo(e))return!1;var t=Fo(e);return t==Uo||t==Yo||t==Go||t==qo}var Ar=Ko,Jo=J,Zo=Jo["__core-js_shared__"],Qo=Zo,Fe=Qo,Rt=function(){var e=/[^.]+$/.exec(Fe&&Fe.keys&&Fe.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function ea(e){return!!Rt&&Rt in e}var ta=ea,ra=Function.prototype,na=ra.toString;function oa(e){if(e!=null){try{return na.call(e)}catch{}try{return e+""}catch{}}return""}var Mr=oa,aa=Ar,ia=ta,sa=$r,ua=Mr,la=/[\\^$.*+?()[\]{}|]/g,ca=/^\[object .+?Constructor\]$/,fa=Function.prototype,pa=Object.prototype,va=fa.toString,da=pa.hasOwnProperty,ga=RegExp("^"+va.call(da).replace(la,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function ha(e){if(!sa(e)||ia(e))return!1;var t=aa(e)?ga:ca;return t.test(ua(e))}var ma=ha;function ya(e,t){return e==null?void 0:e[t]}var ba=ya,wa=ma,Ta=ba;function Ca(e,t){var r=Ta(e,t);return wa(r)?r:void 0}var se=Ca,_a=se,xa=J,Pa=_a(xa,"Map"),ct=Pa,Oa=se,Sa=Oa(Object,"create"),je=Sa,jt=je;function $a(){this.__data__=jt?jt(null):{},this.size=0}var Aa=$a;function Ma(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Ea=Ma,Da=je,Na="__lodash_hash_undefined__",Ra=Object.prototype,ja=Ra.hasOwnProperty;function Ha(e){var t=this.__data__;if(Da){var r=t[e];return r===Na?void 0:r}return ja.call(t,e)?t[e]:void 0}var ka=Ha,Ia=je,La=Object.prototype,Va=La.hasOwnProperty;function Ba(e){var t=this.__data__;return Ia?t[e]!==void 0:Va.call(t,e)}var za=Ba,Wa=je,Fa="__lodash_hash_undefined__";function Xa(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Wa&&t===void 0?Fa:t,this}var Ga=Xa,Ua=Aa,Ya=Ea,qa=ka,Ka=za,Ja=Ga;function ue(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}ue.prototype.clear=Ua;ue.prototype.delete=Ya;ue.prototype.get=qa;ue.prototype.has=Ka;ue.prototype.set=Ja;var Za=ue,Ht=Za,Qa=Ne,ei=ct;function ti(){this.size=0,this.__data__={hash:new Ht,map:new(ei||Qa),string:new Ht}}var ri=ti;function ni(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var oi=ni,ai=oi;function ii(e,t){var r=e.__data__;return ai(t)?r[typeof t=="string"?"string":"hash"]:r.map}var He=ii,si=He;function ui(e){var t=si(this,e).delete(e);return this.size-=t?1:0,t}var li=ui,ci=He;function fi(e){return ci(this,e).get(e)}var pi=fi,vi=He;function di(e){return vi(this,e).has(e)}var gi=di,hi=He;function mi(e,t){var r=hi(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}var yi=mi,bi=ri,wi=li,Ti=pi,Ci=gi,_i=yi;function le(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}le.prototype.clear=bi;le.prototype.delete=wi;le.prototype.get=Ti;le.prototype.has=Ci;le.prototype.set=_i;var Er=le,xi=Ne,Pi=ct,Oi=Er,Si=200;function $i(e,t){var r=this.__data__;if(r instanceof xi){var o=r.__data__;if(!Pi||o.length<Si-1)return o.push([e,t]),this.size=++r.size,this;r=this.__data__=new Oi(o)}return r.set(e,t),this.size=r.size,this}var Ai=$i,Mi=Ne,Ei=mo,Di=bo,Ni=To,Ri=_o,ji=Ai;function ce(e){var t=this.__data__=new Mi(e);this.size=t.size}ce.prototype.clear=Ei;ce.prototype.delete=Di;ce.prototype.get=Ni;ce.prototype.has=Ri;ce.prototype.set=ji;var Hi=ce,ki="__lodash_hash_undefined__";function Ii(e){return this.__data__.set(e,ki),this}var Li=Ii;function Vi(e){return this.__data__.has(e)}var Bi=Vi,zi=Er,Wi=Li,Fi=Bi;function Ae(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new zi;++t<r;)this.add(e[t])}Ae.prototype.add=Ae.prototype.push=Wi;Ae.prototype.has=Fi;var Xi=Ae;function Gi(e,t){for(var r=-1,o=e==null?0:e.length;++r<o;)if(t(e[r],r,e))return!0;return!1}var Ui=Gi;function Yi(e,t){return e.has(t)}var qi=Yi,Ki=Xi,Ji=Ui,Zi=qi,Qi=1,es=2;function ts(e,t,r,o,a,i){var n=r&Qi,u=e.length,s=t.length;if(u!=s&&!(n&&s>u))return!1;var l=i.get(e),v=i.get(t);if(l&&v)return l==t&&v==e;var d=-1,f=!0,c=r&es?new Ki:void 0;for(i.set(e,t),i.set(t,e);++d<u;){var p=e[d],h=t[d];if(o)var w=n?o(h,p,d,t,e,i):o(p,h,d,e,t,i);if(w!==void 0){if(w)continue;f=!1;break}if(c){if(!Ji(t,function(y,b){if(!Zi(c,b)&&(p===y||a(p,y,r,o,i)))return c.push(b)})){f=!1;break}}else if(!(p===h||a(p,h,r,o,i))){f=!1;break}}return i.delete(e),i.delete(t),f}var Dr=ts,rs=J,ns=rs.Uint8Array,os=ns;function as(e){var t=-1,r=Array(e.size);return e.forEach(function(o,a){r[++t]=[a,o]}),r}var is=as;function ss(e){var t=-1,r=Array(e.size);return e.forEach(function(o){r[++t]=o}),r}var us=ss,kt=lt,It=os,ls=Pr,cs=Dr,fs=is,ps=us,vs=1,ds=2,gs="[object Boolean]",hs="[object Date]",ms="[object Error]",ys="[object Map]",bs="[object Number]",ws="[object RegExp]",Ts="[object Set]",Cs="[object String]",_s="[object Symbol]",xs="[object ArrayBuffer]",Ps="[object DataView]",Lt=kt?kt.prototype:void 0,Xe=Lt?Lt.valueOf:void 0;function Os(e,t,r,o,a,i,n){switch(r){case Ps:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case xs:return!(e.byteLength!=t.byteLength||!i(new It(e),new It(t)));case gs:case hs:case bs:return ls(+e,+t);case ms:return e.name==t.name&&e.message==t.message;case ws:case Cs:return e==t+"";case ys:var u=fs;case Ts:var s=o&vs;if(u||(u=ps),e.size!=t.size&&!s)return!1;var l=n.get(e);if(l)return l==t;o|=ds,n.set(e,t);var v=cs(u(e),u(t),o,a,i,n);return n.delete(e),v;case _s:if(Xe)return Xe.call(e)==Xe.call(t)}return!1}var Ss=Os;function $s(e,t){for(var r=-1,o=t.length,a=e.length;++r<o;)e[a+r]=t[r];return e}var As=$s,Ms=Array.isArray,ft=Ms,Es=As,Ds=ft;function Ns(e,t,r){var o=t(e);return Ds(e)?o:Es(o,r(e))}var Rs=Ns;function js(e,t){for(var r=-1,o=e==null?0:e.length,a=0,i=[];++r<o;){var n=e[r];t(n,r,e)&&(i[a++]=n)}return i}var Hs=js;function ks(){return[]}var Is=ks,Ls=Hs,Vs=Is,Bs=Object.prototype,zs=Bs.propertyIsEnumerable,Vt=Object.getOwnPropertySymbols,Ws=Vt?function(e){return e==null?[]:(e=Object(e),Ls(Vt(e),function(t){return zs.call(e,t)}))}:Vs,Fs=Ws;function Xs(e,t){for(var r=-1,o=Array(e);++r<e;)o[r]=t(r);return o}var Gs=Xs;function Us(e){return e!=null&&typeof e=="object"}var ke=Us,Ys=Re,qs=ke,Ks="[object Arguments]";function Js(e){return qs(e)&&Ys(e)==Ks}var Zs=Js,Bt=Zs,Qs=ke,Nr=Object.prototype,eu=Nr.hasOwnProperty,tu=Nr.propertyIsEnumerable,ru=Bt(function(){return arguments}())?Bt:function(e){return Qs(e)&&eu.call(e,"callee")&&!tu.call(e,"callee")},nu=ru,Me={exports:{}};function ou(){return!1}var au=ou;(function(e,t){var r=J,o=au,a=t&&!t.nodeType&&t,i=a&&!0&&e&&!e.nodeType&&e,n=i&&i.exports===a,u=n?r.Buffer:void 0,s=u?u.isBuffer:void 0,l=s||o;e.exports=l})(Me,Me.exports);var iu=9007199254740991,su=/^(?:0|[1-9]\d*)$/;function uu(e,t){var r=typeof e;return t=t==null?iu:t,!!t&&(r=="number"||r!="symbol"&&su.test(e))&&e>-1&&e%1==0&&e<t}var lu=uu,cu=9007199254740991;function fu(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=cu}var Rr=fu,pu=Re,vu=Rr,du=ke,gu="[object Arguments]",hu="[object Array]",mu="[object Boolean]",yu="[object Date]",bu="[object Error]",wu="[object Function]",Tu="[object Map]",Cu="[object Number]",_u="[object Object]",xu="[object RegExp]",Pu="[object Set]",Ou="[object String]",Su="[object WeakMap]",$u="[object ArrayBuffer]",Au="[object DataView]",Mu="[object Float32Array]",Eu="[object Float64Array]",Du="[object Int8Array]",Nu="[object Int16Array]",Ru="[object Int32Array]",ju="[object Uint8Array]",Hu="[object Uint8ClampedArray]",ku="[object Uint16Array]",Iu="[object Uint32Array]",P={};P[Mu]=P[Eu]=P[Du]=P[Nu]=P[Ru]=P[ju]=P[Hu]=P[ku]=P[Iu]=!0;P[gu]=P[hu]=P[$u]=P[mu]=P[Au]=P[yu]=P[bu]=P[wu]=P[Tu]=P[Cu]=P[_u]=P[xu]=P[Pu]=P[Ou]=P[Su]=!1;function Lu(e){return du(e)&&vu(e.length)&&!!P[pu(e)]}var Vu=Lu;function Bu(e){return function(t){return e(t)}}var zu=Bu,Je={exports:{}};(function(e,t){var r=Or,o=t&&!t.nodeType&&t,a=o&&!0&&e&&!e.nodeType&&e,i=a&&a.exports===o,n=i&&r.process,u=function(){try{var s=a&&a.require&&a.require("util").types;return s||n&&n.binding&&n.binding("util")}catch{}}();e.exports=u})(Je,Je.exports);var Wu=Vu,Fu=zu,zt=Je.exports,Wt=zt&&zt.isTypedArray,Xu=Wt?Fu(Wt):Wu,jr=Xu,Gu=Gs,Uu=nu,Yu=ft,qu=Me.exports,Ku=lu,Ju=jr,Zu=Object.prototype,Qu=Zu.hasOwnProperty;function el(e,t){var r=Yu(e),o=!r&&Uu(e),a=!r&&!o&&qu(e),i=!r&&!o&&!a&&Ju(e),n=r||o||a||i,u=n?Gu(e.length,String):[],s=u.length;for(var l in e)(t||Qu.call(e,l))&&!(n&&(l=="length"||a&&(l=="offset"||l=="parent")||i&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||Ku(l,s)))&&u.push(l);return u}var tl=el,rl=Object.prototype;function nl(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||rl;return e===r}var ol=nl;function al(e,t){return function(r){return e(t(r))}}var il=al,sl=il,ul=sl(Object.keys,Object),ll=ul,cl=ol,fl=ll,pl=Object.prototype,vl=pl.hasOwnProperty;function dl(e){if(!cl(e))return fl(e);var t=[];for(var r in Object(e))vl.call(e,r)&&r!="constructor"&&t.push(r);return t}var gl=dl,hl=Ar,ml=Rr;function yl(e){return e!=null&&ml(e.length)&&!hl(e)}var bl=yl,wl=tl,Tl=gl,Cl=bl;function _l(e){return Cl(e)?wl(e):Tl(e)}var xl=_l,Pl=Rs,Ol=Fs,Sl=xl;function $l(e){return Pl(e,Sl,Ol)}var Al=$l,Ft=Al,Ml=1,El=Object.prototype,Dl=El.hasOwnProperty;function Nl(e,t,r,o,a,i){var n=r&Ml,u=Ft(e),s=u.length,l=Ft(t),v=l.length;if(s!=v&&!n)return!1;for(var d=s;d--;){var f=u[d];if(!(n?f in t:Dl.call(t,f)))return!1}var c=i.get(e),p=i.get(t);if(c&&p)return c==t&&p==e;var h=!0;i.set(e,t),i.set(t,e);for(var w=n;++d<s;){f=u[d];var y=e[f],b=t[f];if(o)var C=n?o(b,y,f,t,e,i):o(y,b,f,e,t,i);if(!(C===void 0?y===b||a(y,b,r,o,i):C)){h=!1;break}w||(w=f=="constructor")}if(h&&!w){var _=e.constructor,x=t.constructor;_!=x&&"constructor"in e&&"constructor"in t&&!(typeof _=="function"&&_ instanceof _&&typeof x=="function"&&x instanceof x)&&(h=!1)}return i.delete(e),i.delete(t),h}var Rl=Nl,jl=se,Hl=J,kl=jl(Hl,"DataView"),Il=kl,Ll=se,Vl=J,Bl=Ll(Vl,"Promise"),zl=Bl,Wl=se,Fl=J,Xl=Wl(Fl,"Set"),Gl=Xl,Ul=se,Yl=J,ql=Ul(Yl,"WeakMap"),Kl=ql,Ze=Il,Qe=ct,et=zl,tt=Gl,rt=Kl,Hr=Re,fe=Mr,Xt="[object Map]",Jl="[object Object]",Gt="[object Promise]",Ut="[object Set]",Yt="[object WeakMap]",qt="[object DataView]",Zl=fe(Ze),Ql=fe(Qe),ec=fe(et),tc=fe(tt),rc=fe(rt),te=Hr;(Ze&&te(new Ze(new ArrayBuffer(1)))!=qt||Qe&&te(new Qe)!=Xt||et&&te(et.resolve())!=Gt||tt&&te(new tt)!=Ut||rt&&te(new rt)!=Yt)&&(te=function(e){var t=Hr(e),r=t==Jl?e.constructor:void 0,o=r?fe(r):"";if(o)switch(o){case Zl:return qt;case Ql:return Xt;case ec:return Gt;case tc:return Ut;case rc:return Yt}return t});var nc=te,Ge=Hi,oc=Dr,ac=Ss,ic=Rl,Kt=nc,Jt=ft,Zt=Me.exports,sc=jr,uc=1,Qt="[object Arguments]",er="[object Array]",Pe="[object Object]",lc=Object.prototype,tr=lc.hasOwnProperty;function cc(e,t,r,o,a,i){var n=Jt(e),u=Jt(t),s=n?er:Kt(e),l=u?er:Kt(t);s=s==Qt?Pe:s,l=l==Qt?Pe:l;var v=s==Pe,d=l==Pe,f=s==l;if(f&&Zt(e)){if(!Zt(t))return!1;n=!0,v=!1}if(f&&!v)return i||(i=new Ge),n||sc(e)?oc(e,t,r,o,a,i):ac(e,t,s,r,o,a,i);if(!(r&uc)){var c=v&&tr.call(e,"__wrapped__"),p=d&&tr.call(t,"__wrapped__");if(c||p){var h=c?e.value():e,w=p?t.value():t;return i||(i=new Ge),a(h,w,r,o,i)}}return f?(i||(i=new Ge),ic(e,t,r,o,a,i)):!1}var fc=cc,pc=fc,rr=ke;function kr(e,t,r,o,a){return e===t?!0:e==null||t==null||!rr(e)&&!rr(t)?e!==e&&t!==t:pc(e,t,r,o,kr,a)}var vc=kr,dc=vc;function gc(e,t){return dc(e,t)}var hc=gc;function mc(e,t){return e===t?!0:!e||!t?!1:"pageX"in t&&"pageY"in t?e.pageX===t.pageX&&e.pageY===t.pageY:"clientX"in t&&"clientY"in t?e.clientX===t.clientX&&e.clientY===t.clientY:!1}function yc(e,t){e!==document.activeElement&&Oe(t,e)&&typeof e.focus=="function"&&e.focus()}function nr(e,t){var r=null,o=null;function a(n){var u=q(n,1),s=u[0].target;if(!!document.documentElement.contains(s)){var l=s.getBoundingClientRect(),v=l.width,d=l.height,f=Math.floor(v),c=Math.floor(d);(r!==f||o!==c)&&Promise.resolve().then(function(){t({width:f,height:c})}),r=f,o=c}}var i=new on(a);return e&&i.observe(e),function(){i.disconnect()}}var bc=function(e,t){var r=E.useRef(!1),o=E.useRef(null);function a(){window.clearTimeout(o.current)}function i(n){if(!r.current||n===!0){if(e()===!1)return;r.current=!0,a(),o.current=window.setTimeout(function(){r.current=!1},t)}else a(),o.current=window.setTimeout(function(){r.current=!1,i()},t)}return[i,function(){r.current=!1,a()}]};function or(e){return typeof e!="function"?null:e()}function ar(e){return pr(e)!=="object"||!e?null:e}var wc=function(t,r){var o=t.children,a=t.disabled,i=t.target,n=t.align,u=t.onAlign,s=t.monitorWindowResize,l=t.monitorBufferTime,v=l===void 0?0:l,d=E.useRef({}),f=E.useRef(),c=E.Children.only(o),p=E.useRef({});p.current.disabled=a,p.current.target=i,p.current.align=n,p.current.onAlign=u;var h=bc(function(){var T=p.current,D=T.disabled,O=T.target,A=T.align,k=T.onAlign;if(!D&&O){var N=f.current,I,L=or(O),W=ar(O);d.current.element=L,d.current.point=W,d.current.align=A;var V=document,G=V.activeElement;return L&&hn(L)?I=ut(N,L,A):W&&(I=Wn(N,W,A)),yc(G,N),k&&I&&k(N,I),!0}return!1},v),w=q(h,2),y=w[0],b=w[1],C=E.useRef({cancel:function(){}}),_=E.useRef({cancel:function(){}});E.useEffect(function(){var T=or(i),D=ar(i);f.current!==_.current.element&&(_.current.cancel(),_.current.element=f.current,_.current.cancel=nr(f.current,y)),(d.current.element!==T||!mc(d.current.point,D)||!hc(d.current.align,n))&&(y(),C.current.element!==T&&(C.current.cancel(),C.current.element=T,C.current.cancel=nr(T,y)))}),E.useEffect(function(){a?b():y()},[a]);var x=E.useRef(null);return E.useEffect(function(){s?x.current||(x.current=he(window,"resize",y)):x.current&&(x.current.remove(),x.current=null)},[s]),E.useEffect(function(){return function(){C.current.cancel(),_.current.cancel(),x.current&&x.current.remove(),b()}},[]),E.useImperativeHandle(r,function(){return{forceAlign:function(){return y(!0)}}}),E.isValidElement(c)&&(c=E.cloneElement(c,{ref:fr(c.ref,f)})),c},Ir=E.forwardRef(wc);Ir.displayName="Align";var ir=["measure","align",null,"motion"],Tc=function(e,t){var r=Ur(null),o=q(r,2),a=o[0],i=o[1],n=g.exports.useRef();function u(v){i(v,!0)}function s(){oe.cancel(n.current)}function l(v){s(),n.current=oe(function(){u(function(d){switch(a){case"align":return"motion";case"motion":return"stable"}return d}),v==null||v()})}return g.exports.useEffect(function(){u("measure")},[e]),g.exports.useEffect(function(){switch(a){case"measure":t();break}a&&(n.current=oe(Yr(ht.mark(function v(){var d,f;return ht.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:d=ir.indexOf(a),f=ir[d+1],f&&d!==-1&&u(f);case 3:case"end":return p.stop()}},v)}))))},[a]),g.exports.useEffect(function(){return function(){s()}},[]),[a,l]},Cc=function(e){var t=g.exports.useState({width:0,height:0}),r=q(t,2),o=r[0],a=r[1];function i(u){a({width:u.offsetWidth,height:u.offsetHeight})}var n=g.exports.useMemo(function(){var u={};if(e){var s=o.width,l=o.height;e.indexOf("height")!==-1&&l?u.height=l:e.indexOf("minHeight")!==-1&&l&&(u.minHeight=l),e.indexOf("width")!==-1&&s?u.width=s:e.indexOf("minWidth")!==-1&&s&&(u.minWidth=s)}return u},[e,o]);return[n,i]},Lr=g.exports.forwardRef(function(e,t){var r=e.visible,o=e.prefixCls,a=e.className,i=e.style,n=e.children,u=e.zIndex,s=e.stretch,l=e.destroyPopupOnHide,v=e.forceRender,d=e.align,f=e.point,c=e.getRootDomNode,p=e.getClassNameFromAlign,h=e.onAlign,w=e.onMouseEnter,y=e.onMouseLeave,b=e.onMouseDown,C=e.onTouchStart,_=g.exports.useRef(),x=g.exports.useRef(),T=g.exports.useState(),D=q(T,2),O=D[0],A=D[1],k=Cc(s),N=q(k,2),I=N[0],L=N[1];function W(){s&&L(c())}var V=Tc(r,W),G=q(V,2),j=G[0],U=G[1],M=g.exports.useState(null),S=q(M,2),Y=S[0],B=S[1],Z=g.exports.useRef();function Q(){return f||c}function pe(){var R;(R=_.current)===null||R===void 0||R.forceAlign()}function Wr(R,z){var ne=p(z);O!==ne&&A(ne),B(z),j==="align"&&(h==null||h(R,z))}fn(function(){if(Y&&j==="align"){var R=p(Y);O!==R?pe():U(function(){var z;(z=Z.current)===null||z===void 0||z.call(Z)})}},[Y]);var ve=H({},hr(e));["onAppearEnd","onEnterEnd","onLeaveEnd"].forEach(function(R){var z=ve[R];ve[R]=function(ne,Ie){return U(),z==null?void 0:z(ne,Ie)}});function vt(){return new Promise(function(R){Z.current=R})}g.exports.useEffect(function(){!ve.motionName&&j==="motion"&&U()},[ve.motionName,j]),g.exports.useImperativeHandle(t,function(){return{forceAlign:pe,getElement:function(){return x.current}}});var Fr=H(H({},I),{},{zIndex:u,opacity:j==="motion"||j==="stable"||!r?void 0:0,pointerEvents:j==="stable"?void 0:"none"},i),dt=!0;(d==null?void 0:d.points)&&(j==="align"||j==="stable")&&(dt=!1);var gt=n;return g.exports.Children.count(n)>1&&(gt=g.exports.createElement("div",{className:"".concat(o,"-content")},n)),g.exports.createElement(nt,$({visible:r,ref:x,leavedClassName:"".concat(o,"-hidden")},ve,{onAppearPrepare:vt,onEnterPrepare:vt,removeOnLeave:l,forceRender:v}),function(R,z){var ne=R.className,Ie=R.style,Xr=re(o,a,O,ne);return g.exports.createElement(Ir,{target:Q(),key:"popup",ref:_,monitorWindowResize:!0,disabled:dt,align:d,onAlign:Wr},g.exports.createElement("div",{ref:z,className:Xr,onMouseEnter:w,onMouseLeave:y,onMouseDownCapture:b,onTouchStartCapture:C,style:H(H({},Ie),Fr)},gt))})});Lr.displayName="PopupInner";var Vr=g.exports.forwardRef(function(e,t){var r=e.prefixCls,o=e.visible,a=e.zIndex,i=e.children,n=e.mobile;n=n===void 0?{}:n;var u=n.popupClassName,s=n.popupStyle,l=n.popupMotion,v=l===void 0?{}:l,d=n.popupRender,f=g.exports.useRef();g.exports.useImperativeHandle(t,function(){return{forceAlign:function(){},getElement:function(){return f.current}}});var c=H({zIndex:a},s),p=i;return g.exports.Children.count(i)>1&&(p=g.exports.createElement("div",{className:"".concat(r,"-content")},i)),d&&(p=d(p)),g.exports.createElement(nt,$({visible:o,ref:f,removeOnLeave:!0},v),function(h,w){var y=h.className,b=h.style,C=re(r,u,y);return g.exports.createElement("div",{ref:w,className:C,style:H(H({},b),c)},p)})});Vr.displayName="MobilePopupInner";var _c=["visible","mobile"],Br=g.exports.forwardRef(function(e,t){var r=e.visible,o=e.mobile,a=vr(e,_c),i=g.exports.useState(r),n=q(i,2),u=n[0],s=n[1],l=g.exports.useState(!1),v=q(l,2),d=v[0],f=v[1],c=H(H({},a),{},{visible:u});g.exports.useEffect(function(){s(r),r&&o&&f(cn())},[r,o]);var p=d?g.exports.createElement(Vr,$({},c,{mobile:o,ref:t})):g.exports.createElement(Lr,$({},c,{ref:t}));return g.exports.createElement("div",null,g.exports.createElement(gn,c),p)});Br.displayName="Popup";var sr=g.exports.createContext(null);function Ue(){}function xc(){return""}function Pc(e){return e?e.ownerDocument:window.document}var Oc=["onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur","onContextMenu"];function Sc(e){var t=function(r){qr(a,r);var o=Kr(a);function a(i){var n;Jr(this,a),n=o.call(this,i),n.popupRef=g.exports.createRef(),n.triggerRef=g.exports.createRef(),n.attachId=void 0,n.clickOutsideHandler=void 0,n.touchOutsideHandler=void 0,n.contextMenuOutsideHandler1=void 0,n.contextMenuOutsideHandler2=void 0,n.mouseDownTimeout=void 0,n.focusTime=void 0,n.preClickTime=void 0,n.preTouchTime=void 0,n.delayTimer=void 0,n.hasPopupMouseDown=void 0,n.onMouseEnter=function(s){var l=n.props.mouseEnterDelay;n.fireEvents("onMouseEnter",s),n.delaySetPopupVisible(!0,l,l?null:s)},n.onMouseMove=function(s){n.fireEvents("onMouseMove",s),n.setPoint(s)},n.onMouseLeave=function(s){n.fireEvents("onMouseLeave",s),n.delaySetPopupVisible(!1,n.props.mouseLeaveDelay)},n.onPopupMouseEnter=function(){n.clearDelayTimer()},n.onPopupMouseLeave=function(s){var l;s.relatedTarget&&!s.relatedTarget.setTimeout&&Oe((l=n.popupRef.current)===null||l===void 0?void 0:l.getElement(),s.relatedTarget)||n.delaySetPopupVisible(!1,n.props.mouseLeaveDelay)},n.onFocus=function(s){n.fireEvents("onFocus",s),n.clearDelayTimer(),n.isFocusToShow()&&(n.focusTime=Date.now(),n.delaySetPopupVisible(!0,n.props.focusDelay))},n.onMouseDown=function(s){n.fireEvents("onMouseDown",s),n.preClickTime=Date.now()},n.onTouchStart=function(s){n.fireEvents("onTouchStart",s),n.preTouchTime=Date.now()},n.onBlur=function(s){n.fireEvents("onBlur",s),n.clearDelayTimer(),n.isBlurToHide()&&n.delaySetPopupVisible(!1,n.props.blurDelay)},n.onContextMenu=function(s){s.preventDefault(),n.fireEvents("onContextMenu",s),n.setPopupVisible(!0,s)},n.onContextMenuClose=function(){n.isContextMenuToShow()&&n.close()},n.onClick=function(s){if(n.fireEvents("onClick",s),n.focusTime){var l;if(n.preClickTime&&n.preTouchTime?l=Math.min(n.preClickTime,n.preTouchTime):n.preClickTime?l=n.preClickTime:n.preTouchTime&&(l=n.preTouchTime),Math.abs(l-n.focusTime)<20)return;n.focusTime=0}n.preClickTime=0,n.preTouchTime=0,n.isClickToShow()&&(n.isClickToHide()||n.isBlurToHide())&&s&&s.preventDefault&&s.preventDefault();var v=!n.state.popupVisible;(n.isClickToHide()&&!v||v&&n.isClickToShow())&&n.setPopupVisible(!n.state.popupVisible,s)},n.onPopupMouseDown=function(){if(n.hasPopupMouseDown=!0,clearTimeout(n.mouseDownTimeout),n.mouseDownTimeout=window.setTimeout(function(){n.hasPopupMouseDown=!1},0),n.context){var s;(s=n.context).onPopupMouseDown.apply(s,arguments)}},n.onDocumentClick=function(s){if(!(n.props.mask&&!n.props.maskClosable)){var l=s.target,v=n.getRootDomNode(),d=n.getPopupDomNode();(!Oe(v,l)||n.isContextMenuOnly())&&!Oe(d,l)&&!n.hasPopupMouseDown&&n.close()}},n.getRootDomNode=function(){var s=n.props.getTriggerDOMNode;if(s)return s(n.triggerRef.current);try{var l=Zr(n.triggerRef.current);if(l)return l}catch{}return Qr.findDOMNode(en(n))},n.getPopupClassNameFromAlign=function(s){var l=[],v=n.props,d=v.popupPlacement,f=v.builtinPlacements,c=v.prefixCls,p=v.alignPoint,h=v.getPopupClassNameFromAlign;return d&&f&&l.push(dn(f,c,s,p)),h&&l.push(h(s)),l.join(" ")},n.getComponent=function(){var s=n.props,l=s.prefixCls,v=s.destroyPopupOnHide,d=s.popupClassName,f=s.onPopupAlign,c=s.popupMotion,p=s.popupAnimation,h=s.popupTransitionName,w=s.popupStyle,y=s.mask,b=s.maskAnimation,C=s.maskTransitionName,_=s.maskMotion,x=s.zIndex,T=s.popup,D=s.stretch,O=s.alignPoint,A=s.mobile,k=s.forceRender,N=n.state,I=N.popupVisible,L=N.point,W=n.getPopupAlign(),V={};return n.isMouseEnterToShow()&&(V.onMouseEnter=n.onPopupMouseEnter),n.isMouseLeaveToHide()&&(V.onMouseLeave=n.onPopupMouseLeave),V.onMouseDown=n.onPopupMouseDown,V.onTouchStart=n.onPopupMouseDown,g.exports.createElement(Br,$({prefixCls:l,destroyPopupOnHide:v,visible:I,point:O&&L,className:d,align:W,onAlign:f,animation:p,getClassNameFromAlign:n.getPopupClassNameFromAlign},V,{stretch:D,getRootDomNode:n.getRootDomNode,style:w,mask:y,zIndex:x,transitionName:h,maskAnimation:b,maskTransitionName:C,maskMotion:_,ref:n.popupRef,motion:c,mobile:A,forceRender:k}),typeof T=="function"?T():T)},n.attachParent=function(s){oe.cancel(n.attachId);var l=n.props,v=l.getPopupContainer,d=l.getDocument,f=n.getRootDomNode(),c;v?(f||v.length===0)&&(c=v(f)):c=d(n.getRootDomNode()).body,c?c.appendChild(s):n.attachId=oe(function(){n.attachParent(s)})},n.getContainer=function(){var s=n.props.getDocument,l=s(n.getRootDomNode()).createElement("div");return l.style.position="absolute",l.style.top="0",l.style.left="0",l.style.width="100%",n.attachParent(l),l},n.setPoint=function(s){var l=n.props.alignPoint;!l||!s||n.setState({point:{pageX:s.pageX,pageY:s.pageY}})},n.handlePortalUpdate=function(){n.state.prevPopupVisible!==n.state.popupVisible&&n.props.afterPopupVisibleChange(n.state.popupVisible)},n.triggerContextValue={onPopupMouseDown:n.onPopupMouseDown};var u;return"popupVisible"in i?u=!!i.popupVisible:u=!!i.defaultPopupVisible,n.state={prevPopupVisible:u,popupVisible:u},Oc.forEach(function(s){n["fire".concat(s)]=function(l){n.fireEvents(s,l)}}),n}return tn(a,[{key:"componentDidMount",value:function(){this.componentDidUpdate()}},{key:"componentDidUpdate",value:function(){var n=this.props,u=this.state;if(u.popupVisible){var s;!this.clickOutsideHandler&&(this.isClickToHide()||this.isContextMenuToShow())&&(s=n.getDocument(this.getRootDomNode()),this.clickOutsideHandler=he(s,"mousedown",this.onDocumentClick)),this.touchOutsideHandler||(s=s||n.getDocument(this.getRootDomNode()),this.touchOutsideHandler=he(s,"touchstart",this.onDocumentClick)),!this.contextMenuOutsideHandler1&&this.isContextMenuToShow()&&(s=s||n.getDocument(this.getRootDomNode()),this.contextMenuOutsideHandler1=he(s,"scroll",this.onContextMenuClose)),!this.contextMenuOutsideHandler2&&this.isContextMenuToShow()&&(this.contextMenuOutsideHandler2=he(window,"blur",this.onContextMenuClose));return}this.clearOutsideHandler()}},{key:"componentWillUnmount",value:function(){this.clearDelayTimer(),this.clearOutsideHandler(),clearTimeout(this.mouseDownTimeout),oe.cancel(this.attachId)}},{key:"getPopupDomNode",value:function(){var n;return((n=this.popupRef.current)===null||n===void 0?void 0:n.getElement())||null}},{key:"getPopupAlign",value:function(){var n=this.props,u=n.popupPlacement,s=n.popupAlign,l=n.builtinPlacements;return u&&l?vn(l,u,s):s}},{key:"setPopupVisible",value:function(n,u){var s=this.props.alignPoint,l=this.state.popupVisible;this.clearDelayTimer(),l!==n&&("popupVisible"in this.props||this.setState({popupVisible:n,prevPopupVisible:l}),this.props.onPopupVisibleChange(n)),s&&u&&n&&this.setPoint(u)}},{key:"delaySetPopupVisible",value:function(n,u,s){var l=this,v=u*1e3;if(this.clearDelayTimer(),v){var d=s?{pageX:s.pageX,pageY:s.pageY}:null;this.delayTimer=window.setTimeout(function(){l.setPopupVisible(n,d),l.clearDelayTimer()},v)}else this.setPopupVisible(n,s)}},{key:"clearDelayTimer",value:function(){this.delayTimer&&(clearTimeout(this.delayTimer),this.delayTimer=null)}},{key:"clearOutsideHandler",value:function(){this.clickOutsideHandler&&(this.clickOutsideHandler.remove(),this.clickOutsideHandler=null),this.contextMenuOutsideHandler1&&(this.contextMenuOutsideHandler1.remove(),this.contextMenuOutsideHandler1=null),this.contextMenuOutsideHandler2&&(this.contextMenuOutsideHandler2.remove(),this.contextMenuOutsideHandler2=null),this.touchOutsideHandler&&(this.touchOutsideHandler.remove(),this.touchOutsideHandler=null)}},{key:"createTwoChains",value:function(n){var u=this.props.children.props,s=this.props;return u[n]&&s[n]?this["fire".concat(n)]:u[n]||s[n]}},{key:"isClickToShow",value:function(){var n=this.props,u=n.action,s=n.showAction;return u.indexOf("click")!==-1||s.indexOf("click")!==-1}},{key:"isContextMenuOnly",value:function(){var n=this.props.action;return n==="contextMenu"||n.length===1&&n[0]==="contextMenu"}},{key:"isContextMenuToShow",value:function(){var n=this.props,u=n.action,s=n.showAction;return u.indexOf("contextMenu")!==-1||s.indexOf("contextMenu")!==-1}},{key:"isClickToHide",value:function(){var n=this.props,u=n.action,s=n.hideAction;return u.indexOf("click")!==-1||s.indexOf("click")!==-1}},{key:"isMouseEnterToShow",value:function(){var n=this.props,u=n.action,s=n.showAction;return u.indexOf("hover")!==-1||s.indexOf("mouseEnter")!==-1}},{key:"isMouseLeaveToHide",value:function(){var n=this.props,u=n.action,s=n.hideAction;return u.indexOf("hover")!==-1||s.indexOf("mouseLeave")!==-1}},{key:"isFocusToShow",value:function(){var n=this.props,u=n.action,s=n.showAction;return u.indexOf("focus")!==-1||s.indexOf("focus")!==-1}},{key:"isBlurToHide",value:function(){var n=this.props,u=n.action,s=n.hideAction;return u.indexOf("focus")!==-1||s.indexOf("blur")!==-1}},{key:"forcePopupAlign",value:function(){if(this.state.popupVisible){var n;(n=this.popupRef.current)===null||n===void 0||n.forceAlign()}}},{key:"fireEvents",value:function(n,u){var s=this.props.children.props[n];s&&s(u);var l=this.props[n];l&&l(u)}},{key:"close",value:function(){this.setPopupVisible(!1)}},{key:"render",value:function(){var n=this.state.popupVisible,u=this.props,s=u.children,l=u.forceRender,v=u.alignPoint,d=u.className,f=u.autoDestroy,c=g.exports.Children.only(s),p={key:"trigger"};this.isContextMenuToShow()?p.onContextMenu=this.onContextMenu:p.onContextMenu=this.createTwoChains("onContextMenu"),this.isClickToHide()||this.isClickToShow()?(p.onClick=this.onClick,p.onMouseDown=this.onMouseDown,p.onTouchStart=this.onTouchStart):(p.onClick=this.createTwoChains("onClick"),p.onMouseDown=this.createTwoChains("onMouseDown"),p.onTouchStart=this.createTwoChains("onTouchStart")),this.isMouseEnterToShow()?(p.onMouseEnter=this.onMouseEnter,v&&(p.onMouseMove=this.onMouseMove)):p.onMouseEnter=this.createTwoChains("onMouseEnter"),this.isMouseLeaveToHide()?p.onMouseLeave=this.onMouseLeave:p.onMouseLeave=this.createTwoChains("onMouseLeave"),this.isFocusToShow()||this.isBlurToHide()?(p.onFocus=this.onFocus,p.onBlur=this.onBlur):(p.onFocus=this.createTwoChains("onFocus"),p.onBlur=this.createTwoChains("onBlur"));var h=re(c&&c.props&&c.props.className,d);h&&(p.className=h);var w=H({},p);rn(c)&&(w.ref=fr(this.triggerRef,c.ref));var y=g.exports.cloneElement(c,w),b;return(n||this.popupRef.current||l)&&(b=g.exports.createElement(e,{key:"portal",getContainer:this.getContainer,didUpdate:this.handlePortalUpdate},this.getComponent())),!n&&f&&(b=null),g.exports.createElement(sr.Provider,{value:this.triggerContextValue},y,b)}}],[{key:"getDerivedStateFromProps",value:function(n,u){var s=n.popupVisible,l={};return s!==void 0&&u.popupVisible!==s&&(l.popupVisible=s,l.prevPopupVisible=u.popupVisible),l}}]),a}(g.exports.Component);return t.contextType=sr,t.defaultProps={prefixCls:"rc-trigger-popup",getPopupClassNameFromAlign:xc,getDocument:Pc,onPopupVisibleChange:Ue,afterPopupVisibleChange:Ue,onPopupAlign:Ue,popupClassName:"",mouseEnterDelay:0,mouseLeaveDelay:.1,focusDelay:0,blurDelay:.15,popupStyle:{},destroyPopupOnHide:!1,popupAlign:{},defaultPopupVisible:!1,mask:!1,maskClosable:!0,action:[],showAction:[],hideAction:[],autoDestroy:!1},t}var $c=Sc(an),F={adjustX:1,adjustY:1},X=[0,0],zr={left:{points:["cr","cl"],overflow:F,offset:[-4,0],targetOffset:X},right:{points:["cl","cr"],overflow:F,offset:[4,0],targetOffset:X},top:{points:["bc","tc"],overflow:F,offset:[0,-4],targetOffset:X},bottom:{points:["tc","bc"],overflow:F,offset:[0,4],targetOffset:X},topLeft:{points:["bl","tl"],overflow:F,offset:[0,-4],targetOffset:X},leftTop:{points:["tr","tl"],overflow:F,offset:[-4,0],targetOffset:X},topRight:{points:["br","tr"],overflow:F,offset:[0,-4],targetOffset:X},rightTop:{points:["tl","tr"],overflow:F,offset:[4,0],targetOffset:X},bottomRight:{points:["tr","br"],overflow:F,offset:[0,4],targetOffset:X},rightBottom:{points:["bl","br"],overflow:F,offset:[4,0],targetOffset:X},bottomLeft:{points:["tl","bl"],overflow:F,offset:[0,4],targetOffset:X},leftBottom:{points:["br","bl"],overflow:F,offset:[-4,0],targetOffset:X}},Ac=function(t){var r=t.overlay,o=t.prefixCls,a=t.id,i=t.overlayInnerStyle;return g.exports.createElement("div",{className:"".concat(o,"-inner"),id:a,role:"tooltip",style:i},typeof r=="function"?r():r)},Mc=function(t,r){var o=t.overlayClassName,a=t.trigger,i=a===void 0?["hover"]:a,n=t.mouseEnterDelay,u=n===void 0?0:n,s=t.mouseLeaveDelay,l=s===void 0?.1:s,v=t.overlayStyle,d=t.prefixCls,f=d===void 0?"rc-tooltip":d,c=t.children,p=t.onVisibleChange,h=t.afterVisibleChange,w=t.transitionName,y=t.animation,b=t.motion,C=t.placement,_=C===void 0?"right":C,x=t.align,T=x===void 0?{}:x,D=t.destroyTooltipOnHide,O=D===void 0?!1:D,A=t.defaultVisible,k=t.getTooltipContainer,N=t.overlayInnerStyle,I=vr(t,["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle"]),L=g.exports.useRef(null);g.exports.useImperativeHandle(r,function(){return L.current});var W=H({},I);"visible"in t&&(W.popupVisible=t.visible);var V=function(){var S=t.arrowContent,Y=S===void 0?null:S,B=t.overlay,Z=t.id;return[g.exports.createElement("div",{className:"".concat(f,"-arrow"),key:"arrow"},Y),g.exports.createElement(Ac,{key:"content",prefixCls:f,id:Z,overlay:B,overlayInnerStyle:N})]},G=!1,j=!1;if(typeof O=="boolean")G=O;else if(O&&pr(O)==="object"){var U=O.keepParent;G=U===!0,j=U===!1}return g.exports.createElement($c,$({popupClassName:o,prefixCls:f,popup:V,action:i,builtinPlacements:zr,popupPlacement:_,ref:L,popupAlign:T,getPopupContainer:k,onPopupVisibleChange:p,afterPopupVisibleChange:h,popupTransitionName:w,popupAnimation:y,popupMotion:b,defaultPopupVisible:A,destroyPopupOnHide:G,autoDestroy:j,mouseLeaveDelay:l,popupStyle:v,mouseEnterDelay:u},W),c)},Ec=g.exports.forwardRef(Mc),Dc={adjustX:1,adjustY:1},ur={adjustX:0,adjustY:0},Nc=[0,0];function lr(e){return typeof e=="boolean"?e?Dc:ur:$($({},ur),e)}function Rc(e){var t=e.arrowWidth,r=t===void 0?4:t,o=e.horizontalArrowShift,a=o===void 0?16:o,i=e.verticalArrowShift,n=i===void 0?8:i,u=e.autoAdjustOverflow,s=e.arrowPointAtCenter,l={left:{points:["cr","cl"],offset:[-4,0]},right:{points:["cl","cr"],offset:[4,0]},top:{points:["bc","tc"],offset:[0,-4]},bottom:{points:["tc","bc"],offset:[0,4]},topLeft:{points:["bl","tc"],offset:[-(a+r),-4]},leftTop:{points:["tr","cl"],offset:[-4,-(n+r)]},topRight:{points:["br","tc"],offset:[a+r,-4]},rightTop:{points:["tl","cr"],offset:[4,-(n+r)]},bottomRight:{points:["tr","bc"],offset:[a+r,4]},rightBottom:{points:["bl","cr"],offset:[4,n+r]},bottomLeft:{points:["tl","bc"],offset:[-(a+r),4]},leftBottom:{points:["br","cl"],offset:[-4,n+r]}};return Object.keys(l).forEach(function(v){l[v]=s?$($({},l[v]),{overflow:lr(u),targetOffset:Nc}):$($({},zr[v]),{overflow:lr(u)}),l[v].ignoreShake=!0}),l}var Bc=dr("success","processing","error","default","warning"),jc=dr("pink","red","yellow","orange","cyan","green","blue","purple","geekblue","magenta","volcano","gold","lime"),Hc=globalThis&&globalThis.__rest||function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(r[o[a]]=e[o[a]]);return r},kc=function(t,r){var o={},a=$({},t);return r.forEach(function(i){t&&i in t&&(o[i]=t[i],delete a[i])}),{picked:o,omitted:a}},cr=new RegExp("^(".concat(jc.join("|"),")(-inverse)?$"));function Ic(e,t){var r=e.type;if((r.__ANT_BUTTON===!0||e.type==="button")&&e.props.disabled||r.__ANT_SWITCH===!0&&(e.props.disabled||e.props.loading)){var o=kc(e.props.style,["position","left","right","top","bottom","float","display","zIndex"]),a=o.picked,i=o.omitted,n=$($({display:"inline-block"},a),{cursor:"not-allowed",width:e.props.block?"100%":null}),u=$($({},i),{pointerEvents:"none"}),s=gr(e,{style:u,className:null});return g.exports.createElement("span",{style:n,className:re(e.props.className,"".concat(t,"-disabled-compatible-wrapper"))},s)}return e}var pt=g.exports.forwardRef(function(e,t){var r,o=g.exports.useContext(nn),a=o.getPopupContainer,i=o.getPrefixCls,n=o.direction,u=sn(!1,{value:e.visible,defaultValue:e.defaultVisible}),s=q(u,2),l=s[0],v=s[1],d=function(){var M=e.title,S=e.overlay;return!M&&!S&&M!==0},f=function(M){var S;v(d()?!1:M),d()||(S=e.onVisibleChange)===null||S===void 0||S.call(e,M)},c=function(){var M=e.builtinPlacements,S=e.arrowPointAtCenter,Y=e.autoAdjustOverflow;return M||Rc({arrowPointAtCenter:S,autoAdjustOverflow:Y})},p=function(M,S){var Y=c(),B=Object.keys(Y).find(function(pe){return Y[pe].points[0]===S.points[0]&&Y[pe].points[1]===S.points[1]});if(!!B){var Z=M.getBoundingClientRect(),Q={top:"50%",left:"50%"};B.indexOf("top")>=0||B.indexOf("Bottom")>=0?Q.top="".concat(Z.height-S.offset[1],"px"):(B.indexOf("Top")>=0||B.indexOf("bottom")>=0)&&(Q.top="".concat(-S.offset[1],"px")),B.indexOf("left")>=0||B.indexOf("Right")>=0?Q.left="".concat(Z.width-S.offset[0],"px"):(B.indexOf("right")>=0||B.indexOf("Left")>=0)&&(Q.left="".concat(-S.offset[0],"px")),M.style.transformOrigin="".concat(Q.left," ").concat(Q.top)}},h=function(){var M=e.title,S=e.overlay;return M===0?M:S||M||""},w=e.getPopupContainer,y=Hc(e,["getPopupContainer"]),b=e.prefixCls,C=e.openClassName,_=e.getTooltipContainer,x=e.overlayClassName,T=e.color,D=e.overlayInnerStyle,O=e.children,A=i("tooltip",b),k=i(),N=l;!("visible"in e)&&d()&&(N=!1);var I=Ic(un(O)?O:g.exports.createElement("span",null,O),A),L=I.props,W=re(L.className,Le({},C||"".concat(A,"-open"),!0)),V=re(x,(r={},Le(r,"".concat(A,"-rtl"),n==="rtl"),Le(r,"".concat(A,"-").concat(T),T&&cr.test(T)),r)),G=D,j;return T&&!cr.test(T)&&(G=$($({},D),{background:T}),j={"--antd-arrow-background-color":T}),g.exports.createElement(Ec,$({},y,{prefixCls:A,overlayClassName:V,getTooltipContainer:w||_||a,ref:t,builtinPlacements:c(),overlay:h(),visible:N,onVisibleChange:f,onPopupAlign:p,overlayInnerStyle:G,arrowContent:g.exports.createElement("span",{className:"".concat(A,"-arrow-content"),style:j}),motion:{motionName:ln(k,"zoom-big-fast",e.transitionName),motionDeadline:1e3}}),N?gr(I,{className:W}):I)});pt.displayName="Tooltip";pt.defaultProps={placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0};var zc=pt;export{jc as P,zc as T,Re as _,$c as a,cn as b,Bc as c,ke as d,$r as e,J as f,Rc as g,hc as h,hn as i,fn as u};
