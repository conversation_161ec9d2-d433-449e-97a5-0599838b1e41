package com.deeppaas.extapi.biz.model;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 接口VO
 *
 * <AUTHOR>
 * @date 2022/06/13
 */
@Data
public class ExtApiBaseVO implements BaseModel {
    /**
     * 接口主键ID
     */
    private String id;

    /**
     * 接口名称
     */
    private String name;
    /**
     * 是否是平台自有接口
     */
    private Integer selfApi;

    /**
     * 接口方法
     * <p>
     * GET、POST
     */
    private String method;

    /**
     * 接口地址
     * <p>
     * http://domain.cn
     */
    private String url;

    /**
     * body类型
     * <p>
     * none、form-data、json、xml
     */
    private String bodyType;

    /**
     * 是否可用
     * 1=可用、0=不可用
     */
    private Integer usable;

    /**
     * 描述
     * <p>
     * 获取数据详情
     */
    private String remark;

    /**
     * 如果是对外提供的，是第三方提供的接口所属那个用户ID
     */
    private String owner;

    /**
     * 返回值类型
     */
    private String returnType;

    /**
     * 返回所属模型
     */
    private String returnEntity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否已经配置规则
     */
    private Boolean hasRule;
}
