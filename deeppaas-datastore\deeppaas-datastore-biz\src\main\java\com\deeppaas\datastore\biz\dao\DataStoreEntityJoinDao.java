package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityJoinDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
public interface DataStoreEntityJoinDao extends BaseJdbcDao<DataStoreEntityJoinDO, String> {
    List<DataStoreEntityJoinDO> findByEntityCodeOrderByIdAsc(String entity);

    @Modifying
    @Query("delete from datastore_entity_join where entity_code=:entityCode and code=:code")
    void deleteByEntityCodeAndJoinCode(@Param("entityCode") String entityCode, @Param("code") String code);

    List<DataStoreEntityJoinDO> findByEntityCodeAndParentJoinCodeOrderByIdAsc(String entityCode, String code);

    DataStoreEntityJoinDO findByEntityCodeAndCode(String entityCode, String code);
}
