package com.deeppaas.extapi.biz.test;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.extapi.biz.enums.ApiReqBodyType;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import com.deeppaas.extapi.biz.enums.ApiMethod;
import com.deeppaas.extapi.biz.enums.ApiPositionType;
import com.deeppaas.extapi.biz.enums.ApiResBodyType;
import com.deeppaas.extapi.biz.service.ThirdApiClient;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/22
 */
public class ThirdApiTest {

    public static void main(String[] args){
        ExtApiDO testApi = initTestApi();
        List<ExtApiParamDO> testApiParams = initTestApiParams();
        Map<String, Object> reqParams = initTestReqParams();
        ThirdApiClient client = ThirdApiClient.of(testApi, testApiParams, null);
        Object result = client.execute(reqParams);
        System.out.println(JsonHelper.toJson(result));
    }

    private static ExtApiDO initTestApi() {
        ExtApiDO extApiDO = new ExtApiDO();
        extApiDO.setMethod(ApiMethod.POST.name());
        //extApiDO.setUrl("http://localhost:8080/api/extApiAction/execute/form/06p1vy0u00317hyf87");
        extApiDO.setUrl("http://localhost:8080/openApi/06p1vy0u00317hyf87/06j0n2vg00010isumd");
        extApiDO.setReqBodyType(ApiReqBodyType.FORM_DATA.name());
        extApiDO.setResBodyType(ApiResBodyType.JSON_DATA_ENTITIES.name());
        return extApiDO;
    }


    private static List<ExtApiParamDO> initTestApiParams() {
        List<ExtApiParamDO> apiParams = new ArrayList<>();
        ExtApiParamDO param1 = new ExtApiParamDO();
        param1.setCode("code");
        param1.setType(SimpleDataType.TEXT.name());
        param1.setPosition(ApiPositionType.BODY.name());
        apiParams.add(param1);
        ExtApiParamDO param2 = new ExtApiParamDO();
        param2.setCode("name");
        param2.setType(SimpleDataType.TEXT.name());
        param2.setPosition(ApiPositionType.BODY.name());
        apiParams.add(param2);
        ExtApiParamDO param3 = new ExtApiParamDO();
        param3.setCode("file");
        param3.setType(SimpleDataType.FILE.name());
        param3.setPosition(ApiPositionType.BODY.name());
        apiParams.add(param3);
        return apiParams;
    }

    private static Map<String, Object> initTestReqParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("code", "ABC");
        params.put("name", "liming");
        params.put("file", new File("/Users/<USER>/Downloads/test.jpg"));
        return params;
    }


}
