package com.deeppaas.rule.biz.enums;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
public enum ActionType {
    /**
     * 定义变量
     */
    VARIABLE,
    /**
     * 判断
     */
    DECISION,
    /**
     * 设置/赋值动作
     */
    SETTER,
    /**
     * 数据模型查询单条动作
     */
    DATA_GET,
    /**
     * 数据模型查询多条动作
     */
    DATA_LIST,
    /**
     * 数据模型分页查询动作
     */
    DATA_PAGE,
    /**
     * 数据保存
     */
    DATA_SAVE,
    /**
     * 指定模型数据更新
     */
    DATA_UPDATE,
    /**
     * 数据同步动作
     */
    DATA_SYNC,
    /**
     * 指定模型数据删除
     */
    DATA_DELETE,
    /**
     * 接口调用动作
     */
    EXT_API,
    /**
     * 错误抛出
     */
    ERROR,
    /**
     * 规则动作
     */
    RULE,
    /**
     * 消息发送动作
     */
    USER_MSG_SEND,
    /**
     * 模版消息发送动作
     */
    USER_TEMPLATE_MSG_SEND;
}
