package com.deeppaas.rule.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.rule.biz.entity.RuleEntityDO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/2/25
 */
public interface RuleEntityDao extends BaseJdbcDao<RuleEntityDO, String> {

    RuleEntityDO findByApiId(String apiId);

    List<RuleEntityDO> findByIdIn(Set<String> ids);

    List<RuleEntityDO> findByApiIdOrderByIdAsc(String publicApi);
}
