import{u as n,g as s,j as t,N as p,O as r}from"./index.689bcdcb.js";import{M as a}from"./index.41297259.js";import"./index.71d34973.js";import"./reactNode.50ea6579.js";import"./Overflow.1478d911.js";import"./index.1f7b2316.js";import"./RightOutlined.ed31d8e3.js";import"./LeftOutlined.6bce5536.js";function k(){const i=n().pathname.split("/"),o=[{path:"/settings",key:"settings",title:"\u7528\u6237\u5217\u8868"},{path:"template",key:"template",title:"\u6A21\u677F\u7BA1\u7406"}];return s("div",{className:"w-[1200px] bg-white mx-auto mt-6 min-h-[713px] pb-10",children:[t(a,{mode:"horizontal",selectedKeys:[i[i.length-1]],children:o.map(e=>t(a.Item,{children:t("nav",{children:t(p,{to:e.path,children:e.title})})},e.key))}),t(r,{})]})}export{k as default};
