package com.deeppaas.flow.biz.element;

import com.deeppaas.flow.biz.enums.NodeType;

import java.util.ArrayList;
import java.util.List;

/**
 * 子流程环节
 * <AUTHOR>
 * @date 2021/12/27
 */
public class SubFlowNode extends FlowNode {
    public SubFlowNode(String code, String name, NodeType type, String remark, List<FlowEventRule> eventRules) {
        super(code, name, type, remark, eventRules);
    }

    @Override
    public List<String> check() {
        List<String> errors = new ArrayList<>();
        if(inLines.isEmpty()){
            errors.add("环节["+getCode()+"]没有进入连接线");
        }
        if(outLines.isEmpty()){
            errors.add("环节["+getCode()+"]没有流转连接线");
        }
        return errors;
    }
}
