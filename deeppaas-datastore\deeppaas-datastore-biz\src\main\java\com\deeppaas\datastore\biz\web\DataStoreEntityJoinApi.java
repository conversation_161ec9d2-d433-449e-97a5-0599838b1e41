package com.deeppaas.datastore.biz.web;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.datastore.biz.entity.DataStoreEntityJoinDO;
import com.deeppaas.datastore.biz.entity.DataStoreEntityJoinMatchDO;
import com.deeppaas.datastore.biz.service.DataStoreEntityJoinService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/04/08
 */
@RestController
@RequestMapping("/api/datastore/entity/join")
public class DataStoreEntityJoinApi {
    private DataStoreEntityJoinService dataStoreEntityJoinService;

    public DataStoreEntityJoinApi(DataStoreEntityJoinService dataStoreEntityJoinService) {
        this.dataStoreEntityJoinService = dataStoreEntityJoinService;
    }

    /**
     * 数据模型关联
     *
     * @param id 主键ID
     * @return 关联信息
     */
    @GetMapping("/get")
    public RestModel<DataStoreEntityJoinDO> get(@RequestParam String id) {
        DataStoreEntityJoinDO joinDO = dataStoreEntityJoinService.get(id);
        return RestModel.data(joinDO);
    }

    /**
     * 数据模型关联
     *
     * @param entityCode 模型编号
     * @param code       关联编号
     * @return 关联信息
     */
    @GetMapping("/getByCode")
    public RestModel<DataStoreEntityJoinDO> getByCode(@RequestParam String entityCode, @RequestParam String code) {
        DataStoreEntityJoinDO joinDO = dataStoreEntityJoinService.findByCode(entityCode, code);
        return RestModel.data(joinDO);
    }

    /**
     * 数据模型关联-列表
     *
     * @param entityCode 数据模型编号
     * @return 关联列表
     */
    @GetMapping("/list")
    public RestModel<List<DataStoreEntityJoinDO>> listJoin(@RequestParam String entityCode) {
        List<DataStoreEntityJoinDO> list = dataStoreEntityJoinService.findByEntityCode(entityCode);
        return RestModel.data(list);
    }

    /**
     * 数据模型关联-列表
     *
     * @param entityCode 数据模型编号
     * @return 关联列表
     */
    @GetMapping("/child/list")
    public RestModel<List<DataStoreEntityJoinDO>> childListJoin(@RequestParam String entityCode, @RequestParam String parentJoinCode) {
        List<DataStoreEntityJoinDO> list = dataStoreEntityJoinService.findByEntityCodeAndParentJoinCode(entityCode, parentJoinCode);
        return RestModel.data(list);
    }

    /**
     * 数据模型关联-新增
     *
     * @param param 参数
     * @return 关联信息
     */
    @PostMapping("/add")
    public RestModel<DataStoreEntityJoinDO> addJoin(@RequestBody DataStoreEntityJoinDO param) {
        DataStoreEntityJoinDO joinDO = dataStoreEntityJoinService.addJoin(param);
        return RestModel.data(joinDO);
    }

    /**
     * 数据模型关联-删除
     *
     * @param entityCode 模型编号
     * @param code       关联编号
     * @return 操作结果
     */
    @PostMapping("/delete")
    public RestModel<Boolean> deleteJoin(@BodyParam String entityCode, @BodyParam String code) {
        dataStoreEntityJoinService.deleteJoin(entityCode, code);
        return RestModel.data(true);
    }

    /**
     * 数据模型匹配关系-列表
     *
     * @param id 匹配关系主键ID
     * @return 关联列表
     */
    @GetMapping("/match/get")
    public RestModel<DataStoreEntityJoinMatchDO> getMatch(@RequestParam String id) {
        DataStoreEntityJoinMatchDO joinMatchDO = dataStoreEntityJoinService.getMatch(id);
        return RestModel.data(joinMatchDO);
    }

    /**
     * 数据模型匹配关系-列表
     *
     * @param entityCode 数据模型编号
     * @return 关联列表
     */
    @GetMapping("/match/listByEntity")
    public RestModel<List<DataStoreEntityJoinMatchDO>> listJoinMatch(@RequestParam String entityCode) {
        List<DataStoreEntityJoinMatchDO> list = dataStoreEntityJoinService.findMatchByEntityCode(entityCode);
        return RestModel.data(list);
    }

    /**
     * 数据模型匹配关系-列表
     *
     * @param entityCode 数据模型编号
     * @return 关联列表
     */
    @GetMapping("/match/listByJoin")
    public RestModel<List<DataStoreEntityJoinMatchDO>> listByJoin(@RequestParam String entityCode, @RequestParam String joinCode) {
        List<DataStoreEntityJoinMatchDO> list = dataStoreEntityJoinService.findMatchByJoinCode(entityCode, joinCode);
        return RestModel.data(list);
    }

    /**
     * 数据模型匹配关系-新增
     *
     * @param param 参数
     * @return 关联信息
     */
    @PostMapping("/match/add")
    public RestModel<DataStoreEntityJoinMatchDO> addJoinMatch(@RequestBody DataStoreEntityJoinMatchDO param) {
        DataStoreEntityJoinMatchDO joinMatchDO = dataStoreEntityJoinService.addJoinMatch(param);
        return RestModel.data(joinMatchDO);
    }

    /**
     * 数据模型匹配关系-删除
     *
     * @param id 匹配关系主键ID
     * @return 操作结果
     */
    @PostMapping("/match/delete")
    public RestModel<Boolean> deleteJoinMatch(@BodyParam String id) {
        dataStoreEntityJoinService.deleteJoinMatch(id);
        return RestModel.data(true);
    }
}
