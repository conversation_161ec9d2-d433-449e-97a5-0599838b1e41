package com.deeppaas.common.data.converter;

import com.deeppaas.common.helper.JsonHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.WritingConverter;

import java.util.List;

/**
 * List集合转JSON转换器
 * <AUTHOR>
 * @date 2021/12/24
 */
@WritingConverter
public class ListToJsonConverter<E> implements Converter<List<E>, String> {

    @Override
    public String convert(List<E> source) {
        if(source==null){
            return "[]";
        }
        try {
            return JsonHelper.writeValueAsString(source);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "[]";
    }
}
