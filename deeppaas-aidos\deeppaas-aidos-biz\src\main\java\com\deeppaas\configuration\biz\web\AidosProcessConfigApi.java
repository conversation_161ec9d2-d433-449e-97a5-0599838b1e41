package com.deeppaas.configuration.biz.web;

import com.deeppaas.configuration.biz.entity.AidosProcessConfigDO;
import com.deeppaas.configuration.biz.service.AidosProcessConfigService;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/processTask/config")
public class AidosProcessConfigApi {


    @Autowired
    private AidosProcessConfigService aidosProcessService;

    @PostMapping("/page")
    public RestModel<PageData<AidosProcessConfigDO>> page(@RequestBody PageSearch pageSearch) {
        PageData<AidosProcessConfigDO> list = aidosProcessService.page(pageSearch);
        return RestModel.data(list);
    }

    @GetMapping("/getAll")
    public RestModel<List<AidosProcessConfigDO>> list() {
        List<AidosProcessConfigDO> list = aidosProcessService.getAll();
        return RestModel.data(list);
    }

    @PostMapping("/save")
    public RestModel list(@RequestBody AidosProcessConfigDO aidosProcessConfigDO) {
       String message = aidosProcessService.insert(aidosProcessConfigDO);
       if (StringHelper.isNotEmpty(message))
           return RestModel.success(message);
        return RestModel.success("保存成功");
    }
    @PostMapping("/saves")
    public RestModel list(@RequestBody List<AidosProcessConfigDO> aidosProcessConfigDO) {
        for (int i = 0; i < aidosProcessConfigDO.size(); i++) {
            aidosProcessConfigDO.get(i).setSort(i);
        }
        aidosProcessService.insert(aidosProcessConfigDO);
        return RestModel.success("保存成功");
    }
    @GetMapping("/del")
    public RestModel list(@RequestParam String id) {
        aidosProcessService.del(id);
        return RestModel.success("删除成功");
    }


}
