package com.deeppaas.common.helper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 时间日期处理工具
 *
 * <AUTHOR>
 */
public final class DateHelper {
    public static final String DATE_PATTERN = "yyyy-MM-dd";    //日期格式
    public static final String DATE_PATTERN1 = "yyyyMMdd";    //日期格式
    public static final String TIME_PATTERN = DATE_PATTERN + " HH:mm:ss";    //时间格式
    public static final String MINUTE_PATTERN = DATE_PATTERN + " HH:mm";    //精确到分格式
    public static final String LONGMINUTE_PATTERN = "yyyyMMddHHmm";    //精确到分格式
    private static final Map<String, String> patternMap;

    static {
        patternMap = new HashMap<>();
        patternMap.put("yyyyMMdd", "yyyy-MM-dd");
        patternMap.put("yyyy/MM/dd", "yyyy-MM-dd");
        patternMap.put("yyyy-MM-dd", "yyyy-MM-dd");
        patternMap.put("yyyy年MM月dd日", "yyyy-MM-dd");

        patternMap.put("yyyyMMddHHmm", "yyyy-MM-dd HH:mm:ss");
        patternMap.put("yyyy/MM/dd HH:mm", "yyyy-MM-dd HH:mm:ss");
        patternMap.put("yyyy-MM-dd HH:mm", "yyyy-MM-dd HH:mm:ss");
        patternMap.put("yyyyMMddHHmmss", "yyyy-MM-dd HH:mm:ss");
        patternMap.put("yyyy/MM/dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss");
        patternMap.put("yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss");

    }

    /**
     * 将日期类型转为时间字符串
     *
     * @param date
     * @param pattern 时间格式
     * @return
     */
    public static final String date2str(Date date, String pattern) {
        SimpleDateFormat df = null;
        String returnValue = "";

        if (date != null) {
            df = new SimpleDateFormat(pattern);
            returnValue = df.format(date);
        }
        return (returnValue);
    }

    /**
     * 将LocalDateTime类型转为时间字符串
     *
     * @param dateTime LocalDateTime
     * @return 日期时间字符串
     */
    public static final String localDateTime2str(LocalDateTime dateTime) {
        if (null == dateTime) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_PATTERN);
        return dateTime.format(formatter);
    }

    public static final String localDateTime2str(LocalDateTime dateTime, String pattern) {
        if (null == dateTime) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return dateTime.format(formatter);
    }

    /**
     * 将时间字符串转为LocalDateTime类型
     *
     * @param timeStr 时间字符串
     * @return LocalDateTime
     */
    public static LocalDateTime str2LocalDateTime(String timeStr) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(TIME_PATTERN);
        return LocalDateTime.parse(timeStr, df);
    }

    /**
     * 将时间戳型转为时间字符串
     *
     * @param timeMillis
     * @param pattern    时间格式
     * @return
     */
    public static final String timeMillis2str(Long timeMillis, String pattern) {
        SimpleDateFormat df = null;
        String returnValue = "";
        if (timeMillis != null) {
            Date date = new Date(timeMillis);
            df = new SimpleDateFormat(pattern);
            returnValue = df.format(date);
        }
        return (returnValue);
    }

    /**
     * 将日期时间字符串转回时间
     *
     * @param dateStr
     * @param pattern
     * @return
     */
    public static final Date str2date(String dateStr, String pattern) {
        SimpleDateFormat df = null;
        Date date = null;
        df = new SimpleDateFormat(pattern);
        try {
            date = df.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 获取系统日期格式的日期字符串
     *
     * @return java.util.Date
     * @Methods Name getNowDateStr
     * @Create In Jun 16, 2011 By lee
     */
    public static String getNowDateStr() {
        return DateHelper.date2str(new Date(), DateHelper.DATE_PATTERN);
    }

    /**
     * 获取系统时间格式的日期字符串
     *
     * @return String
     * @Methods Name getNowTimeStr
     * @Create In Jun 16, 2011 By lee
     */
    public static String getNowTimeStr() {
        return DateHelper.date2str(new Date(), DateHelper.TIME_PATTERN);
    }

    /**
     * 提供日期自处理方法，
     *
     * @param date
     * @param amount 需要增加或减少的量
     * @param field  需要增加或减少的参数（年/月/日）
     * @return Date
     * @Methods Name add
     * @Create In Jun 16, 2011 By lee
     */
    public static Date add(Date date, int amount, int field) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, amount);
        return calendar.getTime();
    }

    public static int getYearInt() {
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        int year = calendar.get(Calendar.YEAR);
        return year;
    }

    /**
     * 获得参数时间小时的开始和结束时间
     *
     * @param date
     * @return
     */
    public static long[] getHourBeginAndEndTime(Date date) {
        long[] beginAndEndTime = new long[2];
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        beginAndEndTime[0] = calendar.getTimeInMillis();
        calendar.add(Calendar.HOUR_OF_DAY, 1);
        beginAndEndTime[1] = calendar.getTimeInMillis();
        return beginAndEndTime;
    }

    /**
     * 得到参数时间一天的开始和结束时间
     *
     * @param date
     * @return
     * <AUTHOR>
     * @date 2016年7月19日
     */
    public static long[] getDayBeginAndEndTime(Date date) {
        long[] beginAndEndTime = new long[2];
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        beginAndEndTime[0] = calendar.getTimeInMillis();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        beginAndEndTime[1] = calendar.getTimeInMillis();
        return beginAndEndTime;
    }

    /**
     * 得到参数时间所在月的开始和结束时间戳
     *
     * @param date
     * @return
     */
    public static long[] getMonthBeginAndEndTime(Date date) {
        long[] beginAndEndTime = new long[2];
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);    //目标月份第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        beginAndEndTime[0] = calendar.getTimeInMillis();
        calendar.add(Calendar.MONTH, 1);
        beginAndEndTime[1] = calendar.getTimeInMillis();
        return beginAndEndTime;
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        if(date == null){
            return null;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
        return localDateTime;
    }
    public static LocalDateTime toLocalDateTime(Long datetime) {
        if(datetime == null){
            return null;
        }
        LocalDateTime localDateTime = new Date(datetime).toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
        return localDateTime;
    }

    public static String formatDataTime(String str) {
        for(Map.Entry<String, String> entry : patternMap.entrySet()) {
            String result = formatDateTime(str, entry.getKey(), entry.getValue());
            if(result!=null) {
                return result;
            }
        }
        return null;
    }

    public static String formatDateTime(String str, String fromPattern, String toPattern){
        SimpleDateFormat fromFormat = new SimpleDateFormat(fromPattern);
        try {
            Date date = fromFormat.parse(str);
            SimpleDateFormat toFormat = new SimpleDateFormat(toPattern);
            return toFormat.format(date);
        } catch (ParseException e) {
            return null;
        }
    }
}
