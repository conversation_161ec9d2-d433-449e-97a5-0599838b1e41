package com.deeppaas.process.biz.service;

import com.deeppaas.process.biz.dao.AidosProcessDao;
import com.deeppaas.process.biz.entity.AidosProcessDO;
import com.deeppaas.common.data.jdbc.JCondition;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class AidosProcessService {

    @Autowired
    private AidosProcessDao aidosProcessDao;

    public List<AidosProcessDO> getAll() {
        return aidosProcessDao.findAll();
    }

    public List<AidosProcessDO> getAll(List<String> processId) {
        if (CollectionUtils.isEmpty(processId))
            return Lists.newArrayList();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("IN_id", processId);
        JCondition condition = JCondition.ofPageParam(paramMap);

        return aidosProcessDao.queryForList(condition);
    }

    private boolean isIdenticalName(AidosProcessDO source) {
        String id = source.getId();
        AidosProcessDO name = aidosProcessDao.findByName(source.getName());
        if (name == null)
            return false;
        if (Objects.equals(name.getId(), id)) {
            return false;
        }
        return true;
    }

    public String save(AidosProcessDO aidosProcessDO) {
        String id = aidosProcessDO.getId();
        if (isIdenticalName(aidosProcessDO))
            return "已存在相同名称【" + aidosProcessDO.getName() + "】";
        if (StringHelper.isEmpty(id)) {
            aidosProcessDO.setCreateTime(LocalDateTime.now());
            aidosProcessDO = aidosProcessDao.insert(aidosProcessDO);
        } else {
            aidosProcessDao.save(aidosProcessDO);
        }
        return "";
    }

    public void insert(List<AidosProcessDO> aidosProcessDO) {
        aidosProcessDao.insertAll(aidosProcessDO);
    }

    public void del(String id) {
        aidosProcessDao.deleteById(id);
    }

    public PageData<AidosProcessDO> page(PageSearch pageSearch) {
        JCondition condition = JCondition.ofPageParam(pageSearch.getSearchParams());
        Page<AidosProcessDO> page = aidosProcessDao.queryForPage(condition, pageSearch.getPageable());
        return PageData.init(page);
    }
    public AidosProcessDO get(String id) {
        return   aidosProcessDao.getById(id);
    }
}
