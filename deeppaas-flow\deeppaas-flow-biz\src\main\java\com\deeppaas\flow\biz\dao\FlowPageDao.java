package com.deeppaas.flow.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowPageDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
public interface FlowPageDao extends BaseJdbcDao<FlowPageDO, String> {
    FlowPageDO findByDefineIdAndNodeId(String defineId, String nodeId);

    List<FlowPageDO> findByPageId(String pageId);

    List<FlowPageDO> findByDefineId(String defineId);

    @Modifying
    @Query("delete from flow_page where define_id = :defineId ")
    void deleteByDefineId(@Param("defineId") String defineId);
}
