package com.deeppaas.rule.biz.client;

import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.rule.api.client.RuleClient;
import com.deeppaas.rule.api.model.RuleEntityPropertyPO;
import com.deeppaas.rule.api.model.RulePO;
import com.deeppaas.rule.biz.RuleEngine;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.convert.RuleConvert;
import com.deeppaas.rule.biz.convert.RuleEntityConvert;
import com.deeppaas.rule.biz.entity.RuleDO;
import com.deeppaas.rule.biz.entity.RuleEntityPropertyDO;
import com.deeppaas.rule.biz.service.RuleEntityService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/2
 */
@Component
public class RuleClientImpl implements RuleClient {
    private final RuleEngine ruleEngine;
    private final RuleEntityService ruleEntityService;

    public RuleClientImpl(RuleEngine ruleEngine, RuleEntityService ruleEntityService) {
        this.ruleEngine = ruleEngine;
        this.ruleEntityService = ruleEntityService;
    }

    /**
     * 规则执行
     */
    @Override
    public Object executeById(String ruleId, Object param) {
        RuleContext ruleContext = ruleEngine.initContextById(ruleId);
        if (param != null && param instanceof DataStoreData storeData) {
            ruleContext.setParam(storeData);
        } else {
            ruleContext.setParam(param);
        }

        Object result = ruleContext.getFirstAction().execute(ruleContext);
        return result;
    }

    @Override
    public RulePO saveOwnerUniqueRule(RulePO rulePO) {
        List<RuleDO> ruleDOS = ruleEngine.ruleService().findByOwnerTypeAndOwnerId(rulePO.getOwnerType(), rulePO.getOwnerId());
        RuleDO ruleDO;
        if (CollectionUtils.isEmpty(ruleDOS)) {
            ruleDO = new RuleDO();
            ruleDO.setOwnerType(rulePO.getOwnerType());
            ruleDO.setOwnerId(rulePO.getOwnerId());
            ruleDO.setCreateTime(LocalDateTime.now());
        } else {
            ruleDO = ruleDOS.get(0);
        }
        ruleDO.setName(rulePO.getName());
        ruleDO.setParamType(rulePO.getParamType());
        ruleDO.setParamAppCode(rulePO.getParamType());
        ruleDO.setParamEntity(rulePO.getParamEntity());
        ruleDO.setParamRemark(rulePO.getParamEntity());
        ruleDO.setReturnType(rulePO.getReturnType());
        ruleDO.setReturnEntity(rulePO.getReturnEntity());
        //ruleDO.setReturnAppCode(rulePO.getReturnEntity());
        ruleDO.setBackEnd(rulePO.getBackEnd());
        ruleDO.setContent(rulePO.getContent());
        ruleDO = ruleEngine.ruleService().save(ruleDO);
        rulePO.setId(ruleDO.getId());
        return rulePO;
    }

    @Override
    public RulePO getRuleById(String ruleId) {
        RuleDO ruleDO = ruleEngine.ruleService().getById(ruleId);
        return RuleConvert.ruleDOToPO(ruleDO);
    }

    /**
     * 获取计算模型属性列表
     *
     * @param entityId 计算模型ID
     * @return 计算模型属性列表
     */
    @Override
    public List<RuleEntityPropertyPO> listEntityProperty(String entityId) {
        List<RuleEntityPropertyDO> propertyDOList = ruleEntityService.listEntityProperty(entityId);
        return RuleEntityConvert.entityToPropertyPOS(propertyDOList);
    }
}
