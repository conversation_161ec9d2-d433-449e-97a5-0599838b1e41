//package com.deeppaas.datastore.biz.service;
//
//import com.deeppaas.datastore.biz.dao.DataStoreEntityDao;
//import com.deeppaas.datastore.biz.dao.DataStoreEntityFieldDao;
//import com.deeppaas.datastore.biz.dao.DataStoreTableFieldDao;
//import com.deeppaas.datastore.biz.entity.DataStoreEntityFieldDO;
//import com.deeppaas.datastore.biz.model.DataStoreEntityFieldDTO;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2022/2/15
// */
//@Service
//public class DataStoreEntityFieldService {
//    private final DataStoreEntityFieldDao dataStoreEntityFieldDao;
//    private final DataStoreEntityDao dataStoreEntityDao;
//    private final DataStoreTableFieldDao dataStoreTableFieldDao;
//
//    public DataStoreEntityFieldService(DataStoreEntityFieldDao dataStoreEntityFieldDao, DataStoreEntityDao dataStoreEntityDao, DataStoreTableFieldDao dataStoreTableFieldDao) {
//        this.dataStoreEntityFieldDao = dataStoreEntityFieldDao;
//        this.dataStoreEntityDao = dataStoreEntityDao;
//        this.dataStoreTableFieldDao = dataStoreTableFieldDao;
//    }
//
//    public List<DataStoreEntityFieldDO> findByEntityCode(String entityCode) {
//        return dataStoreEntityFieldDao.findByEntityCodeOrderByIdAsc(entityCode);
//    }
//
//    public List<DataStoreEntityFieldDTO> findDTOByEntityCode(String code) {
//
//    }
//}
