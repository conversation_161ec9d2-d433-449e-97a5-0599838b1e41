package com.deeppaas.common.exception;

/**
 * @Description 基础异常
 * <AUTHOR>
 * @Date 2019-03-21
 */
public abstract class BaseException extends RuntimeException {
    /** 异常码 */
    private int code;

    @Override
    public String toString() {
        String message = getLocalizedMessage();
        return message;
    }

    public int getCode() {
        return code;
    }

    public BaseException(int code, String message){
        super(message);
        this.code = code;
    }
}
