package com.deeppaas.common.data.ddl;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
public interface DDLGenerator {

    String createTableSql(String schema, String tableName, String owner, List<ColumnData> columns);

    //String createTableColumnSql(String schema, String tableName, String columnName, String dataType, int length, int decimalDigits);

    //String modifyTableColumnSql(String schema, String tableName, String columnName, String dataType, int length, int decimalDigits);

}
