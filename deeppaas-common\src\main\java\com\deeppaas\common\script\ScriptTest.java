package com.deeppaas.common.script;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-05-29
 */
public class ScriptTest {

    public static void main(String[] args) {
//        test1();
//        test2();
//        test3();
        test4();
    }

    private static void test1() {
        String expression = "a1+(b2-c3)*d4/e5";
        Map<String, Object> vars = Map.of();
        vars.put("a1", 1);
        vars.put("b2", 2);
        vars.put("c3", 3);
        vars.put("d4", 4);
        vars.put("e5", 5);
        Object result = ScriptUtils.eval(expression, vars);
        System.out.println("result type is " + result.getClass().getSimpleName());
        System.out.println("result value is " + result);
    }

    private static void test2() {
        Object result = 1 + (2.0 - 3) * 4 / 5;
        System.out.println("result value is " + (1 + (2 - 3) * 4 / 5));
        System.out.println("result type is " + result.getClass().getSimpleName());
        System.out.println("result value is " + result);
    }

    private static void test3() {
        String expression = "a1+(b2-c3)*d4/e5";
        List<String> varNames = ScriptUtils.getMathVarNames(expression);

        for (int i = 0; i < varNames.size(); i++) {
            System.out.println("var" + i + " name is " + varNames.get(i));
        }

    }

    private static void test4() {
        String expression = "ZF+PL";
        Map<String, Object> vars = new HashMap<>();
        vars.put("ZF", 1);
        vars.put("DZ", 2);
        vars.put("PL", 3);

        Object result = ScriptUtils.eval(expression, vars);
        System.out.println("result type is " + result.getClass().getSimpleName());
        System.out.println("result value is " + result);
    }
}
