package com.deeppaas.extapi.biz.model;

import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 接口授权参数
 * <p>
 * 批量
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
@Data
public class ExtApiUserRightAddParam implements BaseModel {

    /**
     * 接口ID列表
     */
    private List<String> apiIds;

    /**
     * 第三方服务ID列表
     */
    private List<String> userIds;


    public void check() {
        if (CollectionUtils.isEmpty(apiIds)) {
            throw ReqException.paramNotFound("接口ID不允许为空");
        }
        if (CollectionUtils.isEmpty(userIds)) {
            throw ReqException.paramNotFound("服务ID不允许为空");
        }
    }
}
