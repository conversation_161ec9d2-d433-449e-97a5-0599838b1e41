package com.deeppaas.common.data.ddl;

import org.springframework.data.mapping.PersistentProperty;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.RelationalPersistentEntity;
import org.springframework.data.relational.core.mapping.RelationalPersistentProperty;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
public class JdbcUtils {
    private static final Map<Class<?>, JDBCType> sqlTypeMappings = new HashMap<>();

    static {

        sqlTypeMappings.put(String.class, JDBCType.VARCHAR);
        sqlTypeMappings.put(BigInteger.class, JDBCType.BIGINT);
        sqlTypeMappings.put(BigDecimal.class, JDBCType.DECIMAL);
        sqlTypeMappings.put(Byte.class, JDBCType.TINYINT);
        sqlTypeMappings.put(byte.class, JDBCType.TINYINT);
        sqlTypeMappings.put(Short.class, JDBCType.SMALLINT);
        sqlTypeMappings.put(short.class, JDBCType.SMALLINT);
        sqlTypeMappings.put(Integer.class, JDBCType.INTEGER);
        sqlTypeMappings.put(int.class, JDBCType.INTEGER);
        sqlTypeMappings.put(Long.class, JDBCType.BIGINT);
        sqlTypeMappings.put(long.class, JDBCType.BIGINT);
        sqlTypeMappings.put(Double.class, JDBCType.DOUBLE);
        sqlTypeMappings.put(double.class, JDBCType.DOUBLE);
        sqlTypeMappings.put(Float.class, JDBCType.REAL);
        sqlTypeMappings.put(float.class, JDBCType.REAL);
        sqlTypeMappings.put(Boolean.class, JDBCType.BIT);
        sqlTypeMappings.put(boolean.class, JDBCType.BIT);
        sqlTypeMappings.put(byte[].class, JDBCType.VARBINARY);
        sqlTypeMappings.put(java.util.Date.class, JDBCType.DATE);
        sqlTypeMappings.put(LocalDate.class, JDBCType.DATE);
        sqlTypeMappings.put(LocalDateTime.class, JDBCType.DATE);
        sqlTypeMappings.put(LocalTime.class, JDBCType.TIMESTAMP);
        sqlTypeMappings.put(OffsetDateTime.class, JDBCType.TIMESTAMP_WITH_TIMEZONE);
    }

    public static JDBCType jdbcTypeFor(Class<?> type){
        Assert.notNull(type, "Type must not be null.");

        return sqlTypeMappings.keySet().stream()
                .filter(k -> k.isAssignableFrom(type))
                .findFirst()
                .map(sqlTypeMappings::get)
                .orElse(JDBCType.NULL);
    }

    public static List<ColumnData> getEntityColumns(RelationalPersistentEntity entity) {
        Iterable<PersistentProperty> properties = entity.getPersistentProperties(Column.class);
        List<ColumnData> columns = new ArrayList<>();
        for(PersistentProperty persistentProperty : properties){
            if(persistentProperty instanceof RelationalPersistentProperty property){
                String columnName = property.getColumnName().getReference();
                ColumnDDL columnDDL = property.findAnnotation(ColumnDDL.class);
                if(columnDDL==null){
                    columns.add(ColumnData.of(columnName, property.isIdProperty(), JdbcUtils.jdbcTypeFor(property.getType())));
                }else {
                    columns.add(ColumnData.of(columnName, property.isIdProperty(), columnDDL));
                }
            }
        }
        return columns;
    }
}
