package com.deeppaas.common.data.jdbc;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */
@NoRepositoryBean
public interface BaseJdbcDao<T, ID> extends PagingAndSortingRepository<T, ID> {

    <S extends T> S insert(S entity);

    <S extends T> Iterable<S> insertAll(Iterable<S> entities);

    T getById(ID id);

    List<T> findAll();

    List<T> findAll(Sort sort);

    List<T> findByIds(Collection<ID> ids);

    List<T> queryForList(JCondition jCondition);

    List<T> queryForList(JCondition jCondition, Sort sort);

    Page<T> queryForPage(JCondition jCondition, Pageable pageable);

    <D> D queryForObject(String sql, Map<String, ?> param, Class<D> resultClass);

    Map<String, Object> queryForMap(String sql, Map<String, ?> param);

    <D> List<D> queryForList(String sql, Map<String, ?> param, Class<D> resultClass);

    List<Map<String, Object>> queryForMaps(String sql, Map<String, ?> param);

    int update(String sql, Map<String, ?> param);

    int[] batchUpdate(String sql, Map<String, ?>[] paramArray);

    /**
     * 保存，通用数据库ID回查是insert还是update
     * @param entity
     * @param id
     * @return
     * @param <S>
     */
    <S extends T> S save(S entity, ID id);
}
