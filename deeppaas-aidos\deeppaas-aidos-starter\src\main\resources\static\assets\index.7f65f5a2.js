var oe=Object.defineProperty;var L=Object.getOwnPropertySymbols;var se=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var q=(r,t,a)=>t in r?oe(r,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[t]=a,A=(r,t)=>{for(var a in t||(t={}))se.call(t,a)&&q(r,a,t[a]);if(L)for(var a of L(t))le.call(t,a)&&q(r,a,t[a]);return r};import{J as ce,K as ue,L as ie,E as F,M as de,F as fe,b as D,e as g,g as pe,j as z,r as u,C as J,_ as W,f as B,I as H,d as ve}from"./index.689bcdcb.js";import{o as he}from"./index.71d34973.js";var X=function(r){ce(a,r);var t=ue(a);function a(e){var n;ie(this,a),n=t.call(this,e),n.handleChange=function(l){var h=n.props,m=h.disabled,b=h.onChange;m||("checked"in n.props||n.setState({checked:l.target.checked}),b&&b({target:F(F({},n.props),{},{checked:l.target.checked}),stopPropagation:function(){l.stopPropagation()},preventDefault:function(){l.preventDefault()},nativeEvent:l.nativeEvent}))},n.saveInput=function(l){n.input=l};var o="checked"in e?e.checked:e.defaultChecked;return n.state={checked:o},n}return de(a,[{key:"focus",value:function(){this.input.focus()}},{key:"blur",value:function(){this.input.blur()}},{key:"render",value:function(){var n,o=this.props,l=o.prefixCls,h=o.className,m=o.style,b=o.name,x=o.id,i=o.type,C=o.disabled,P=o.readOnly,c=o.tabIndex,N=o.onClick,_=o.onFocus,y=o.onBlur,s=o.onKeyDown,k=o.onKeyPress,p=o.onKeyUp,v=o.autoFocus,K=o.value,E=o.required,I=fe(o,["prefixCls","className","style","name","id","type","disabled","readOnly","tabIndex","onClick","onFocus","onBlur","onKeyDown","onKeyPress","onKeyUp","autoFocus","value","required"]),G=Object.keys(I).reduce(function(S,O){return(O.substr(0,5)==="aria-"||O.substr(0,5)==="data-"||O==="role")&&(S[O]=I[O]),S},{}),$=this.state.checked,j=D(l,h,(n={},g(n,"".concat(l,"-checked"),$),g(n,"".concat(l,"-disabled"),C),n));return pe("span",{className:j,style:m,children:[z("input",A({name:b,id:x,type:i,required:E,readOnly:P,disabled:C,tabIndex:c,className:"".concat(l,"-input"),checked:!!$,onClick:N,onFocus:_,onBlur:y,onKeyUp:p,onKeyDown:s,onKeyPress:k,onChange:this.handleChange,autoFocus:v,ref:this.saveInput,value:K},G)),z("span",{className:"".concat(l,"-inner")})]})}}],[{key:"getDerivedStateFromProps",value:function(n,o){return"checked"in n?F(F({},o),{},{checked:n.checked}):null}}]),a}(u.exports.Component);X.defaultProps={prefixCls:"rc-checkbox",className:"",style:{},type:"checkbox",defaultChecked:!1,onFocus:function(){},onBlur:function(){},onChange:function(){},onKeyDown:function(){},onKeyPress:function(){},onKeyUp:function(){}};var xe=globalThis&&globalThis.__rest||function(r,t){var a={};for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&t.indexOf(e)<0&&(a[e]=r[e]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,e=Object.getOwnPropertySymbols(r);n<e.length;n++)t.indexOf(e[n])<0&&Object.prototype.propertyIsEnumerable.call(r,e[n])&&(a[e[n]]=r[e[n]]);return a},Q=u.exports.createContext(null),be=function(t,a){var e=t.defaultValue,n=t.children,o=t.options,l=o===void 0?[]:o,h=t.prefixCls,m=t.className,b=t.style,x=t.onChange,i=xe(t,["defaultValue","children","options","prefixCls","className","style","onChange"]),C=u.exports.useContext(J),P=C.getPrefixCls,c=C.direction,N=u.exports.useState(i.value||e||[]),_=W(N,2),y=_[0],s=_[1],k=u.exports.useState([]),p=W(k,2),v=p[0],K=p[1];u.exports.useEffect(function(){"value"in i&&s(i.value||[])},[i.value]);var E=function(){return l.map(function(f){return typeof f=="string"||typeof f=="number"?{label:f,value:f}:f})},I=function(f){K(function(w){return w.filter(function(V){return V!==f})})},G=function(f){K(function(w){return[].concat(H(w),[f])})},$=function(f){var w=y.indexOf(f.value),V=H(y);w===-1?V.push(f.value):V.splice(w,1),"value"in i||s(V);var U=E();x==null||x(V.filter(function(R){return v.indexOf(R)!==-1}).sort(function(R,ne){var re=U.findIndex(function(M){return M.value===R}),ae=U.findIndex(function(M){return M.value===ne});return re-ae}))},j=P("checkbox",h),S="".concat(j,"-group"),O=he(i,["value","disabled"]);l&&l.length>0&&(n=E().map(function(d){return u.exports.createElement(Z,{prefixCls:j,key:d.value.toString(),disabled:"disabled"in d?d.disabled:i.disabled,value:d.value,checked:y.indexOf(d.value)!==-1,onChange:d.onChange,className:"".concat(S,"-item"),style:d.style},d.label)}));var ee={toggleOption:$,value:y,disabled:i.disabled,name:i.name,registerValue:G,cancelValue:I},te=D(S,g({},"".concat(S,"-rtl"),c==="rtl"),m);return u.exports.createElement("div",B({className:te,style:b},O,{ref:a}),u.exports.createElement(Q.Provider,{value:ee},n))},Ce=u.exports.forwardRef(be),ye=u.exports.memo(Ce),ge=globalThis&&globalThis.__rest||function(r,t){var a={};for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&t.indexOf(e)<0&&(a[e]=r[e]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,e=Object.getOwnPropertySymbols(r);n<e.length;n++)t.indexOf(e[n])<0&&Object.prototype.propertyIsEnumerable.call(r,e[n])&&(a[e[n]]=r[e[n]]);return a},me=function(t,a){var e,n=t.prefixCls,o=t.className,l=t.children,h=t.indeterminate,m=h===void 0?!1:h,b=t.style,x=t.onMouseEnter,i=t.onMouseLeave,C=t.skipGroup,P=C===void 0?!1:C,c=ge(t,["prefixCls","className","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup"]),N=u.exports.useContext(J),_=N.getPrefixCls,y=N.direction,s=u.exports.useContext(Q),k=u.exports.useRef(c.value);u.exports.useEffect(function(){s==null||s.registerValue(c.value),ve("checked"in c||!!s||!("value"in c),"Checkbox","`value` is not a valid prop, do you mean `checked`?")},[]),u.exports.useEffect(function(){if(!P)return c.value!==k.current&&(s==null||s.cancelValue(k.current),s==null||s.registerValue(c.value),k.current=c.value),function(){return s==null?void 0:s.cancelValue(c.value)}},[c.value]);var p=_("checkbox",n),v=B({},c);s&&!P&&(v.onChange=function(){c.onChange&&c.onChange.apply(c,arguments),s.toggleOption&&s.toggleOption({label:l,value:c.value})},v.name=s.name,v.checked=s.value.indexOf(c.value)!==-1,v.disabled=c.disabled||s.disabled);var K=D((e={},g(e,"".concat(p,"-wrapper"),!0),g(e,"".concat(p,"-rtl"),y==="rtl"),g(e,"".concat(p,"-wrapper-checked"),v.checked),g(e,"".concat(p,"-wrapper-disabled"),v.disabled),e),o),E=D(g({},"".concat(p,"-indeterminate"),m));return u.exports.createElement("label",{className:K,style:b,onMouseEnter:x,onMouseLeave:i},u.exports.createElement(X,B({},v,{prefixCls:p,className:E,ref:a})),l!==void 0&&u.exports.createElement("span",null,l))},Y=u.exports.forwardRef(me);Y.displayName="Checkbox";var Z=Y,T=Z;T.Group=ye;T.__ANT_CHECKBOX=!0;var Ne=T;export{Ne as C,X as a};
