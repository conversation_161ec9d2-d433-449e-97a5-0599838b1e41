package com.deeppaas.common.web.view;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.web.servlet.view.document.AbstractXlsView;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title: AppletXlsxView
 * @Description: excel导出工具
 * @date 2019/12/9下午8:32
 */
public abstract class BaseXlsxView extends AbstractXlsView {

    public BaseXlsxView() {
        this.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    @Override
    protected Workbook createWorkbook(Map<String, Object> model, HttpServletRequest request) {
        Workbook workbook = new SXSSFWorkbook(5000);
        return workbook;
    }
}
