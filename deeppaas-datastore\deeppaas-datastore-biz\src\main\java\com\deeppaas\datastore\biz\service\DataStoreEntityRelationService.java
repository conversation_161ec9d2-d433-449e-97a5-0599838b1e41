package com.deeppaas.datastore.biz.service;

import com.deeppaas.datastore.biz.dao.DataStoreEntityDao;
import com.deeppaas.datastore.biz.dao.DataStoreEntityRelationDao;
import com.deeppaas.datastore.biz.dao.DataStoreEntityRelationMatchDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityDO;
import com.deeppaas.datastore.biz.entity.DataStoreEntityRelationDO;
import com.deeppaas.datastore.biz.entity.DataStoreEntityRelationMatchDO;
import com.deeppaas.datastore.common.mapping.DataStoreEntityRelation;
import com.deeppaas.datastore.common.mapping.DataStoreEntityRelationMatch;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
@Service
public class DataStoreEntityRelationService {
    private final DataStoreEntityRelationDao entityRelationDao;
    private final DataStoreEntityRelationMatchDao entityRelationMatchDao;
    private final DataStoreEntityDao dataStoreEntityDao;

    public DataStoreEntityRelationService(DataStoreEntityRelationDao entityRelationDao, DataStoreEntityRelationMatchDao entityRelationMatchDao, DataStoreEntityDao dataStoreEntityDao) {
        this.entityRelationDao = entityRelationDao;
        this.entityRelationMatchDao = entityRelationMatchDao;
        this.dataStoreEntityDao = dataStoreEntityDao;
    }

    @Transactional
    public boolean saveRelation(DataStoreEntityRelation relation) {
        DataStoreEntityRelationDO relationDO = entityRelationDao.findRelation(relation.getEntityCode(), relation.getRelationEntityCode());
        if(relationDO==null){
            relationDO = new DataStoreEntityRelationDO();
            relationDO.setEntity1Code(relation.getEntityCode());
            relationDO.setEntity2Code(relation.getRelationEntityCode());
            entityRelationDao.save(relationDO);
        }else {
            entityRelationMatchDao.deleteByEntity1CodeAndEntity2Code(relationDO.getEntity1Code(), relationDO.getEntity2Code());
        }

        if(relation.getEntityCode().equals(relationDO.getEntity1Code())){
            for(DataStoreEntityRelationMatch relationMatch : relation.getMatches()){
                DataStoreEntityRelationMatchDO matchDO = new DataStoreEntityRelationMatchDO();
                matchDO.setEntity1Code(relation.getEntityCode());
                matchDO.setEntity1FieldCode(relationMatch.getEntityFieldCode());
                matchDO.setEntity2Code(relation.getRelationEntityCode());
                matchDO.setEntity2FieldCode(relation.getRelationEntityCode());
                entityRelationMatchDao.save(matchDO);
            }
        }else {
            for(DataStoreEntityRelationMatch relationMatch : relation.getMatches()){
                DataStoreEntityRelationMatchDO matchDO = new DataStoreEntityRelationMatchDO();
                matchDO.setEntity1Code(relation.getRelationEntityCode());
                matchDO.setEntity1FieldCode(relation.getRelationEntityCode());
                matchDO.setEntity2Code(relation.getEntityCode());
                matchDO.setEntity2FieldCode(relationMatch.getEntityFieldCode());
                entityRelationMatchDao.save(matchDO);
            }
        }
        return true;
    }

    public List<DataStoreEntityDO> findRelationEntity(String entityCode) {
        List<DataStoreEntityRelationDO> relationDOS = entityRelationDao.findByEntityCode(entityCode);
        List<String> relationEntityCodes = new ArrayList<>();
        for(DataStoreEntityRelationDO relationDO: relationDOS){
            if(relationDO.getEntity1Code().equals(entityCode)){
                relationEntityCodes.add(relationDO.getEntity2Code());
            }else {
                relationEntityCodes.add(relationDO.getEntity1Code());
            }
        }
        if(relationEntityCodes.isEmpty()){
            return Collections.emptyList();
        }
        return dataStoreEntityDao.findByCodeIn(relationEntityCodes);
    }
}
