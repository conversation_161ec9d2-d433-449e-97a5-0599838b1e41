package com.deeppaas.datastore.common.dialect;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.datastore.common.emnus.TableIndexType;
import com.deeppaas.datastore.common.mapping.DataStoreSelf;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31
 */
public class PostgresqlDialect extends AbstractDialect {
    private static final String BACKTICK_SEPARATOR = "\"";

    @Override
    public String limitOffset(Pageable pageable) {
        int offset = pageable.getPageNumber() * pageable.getPageSize();
        int limit = pageable.getPageSize();
        return String.format(" LIMIT %s, %s", offset, limit);
    }

    @Override
    public String backtick(String name) {
        return new StringBuffer(BACKTICK_SEPARATOR).append(name).append(BACKTICK_SEPARATOR).toString();
    }

    @Override
    public String createSelfTableSql(String schema, String tableName, String owner) {
        return "CREATE TABLE "+backtick(schema)+"." + backtick(tableName) + " ("
                + backtick(DataStoreSelf.ID_SQL_PARAMETER) + " varchar(32) NOT NULL,"
                + backtick(DataStoreSelf.CREATE_TIME_SQL_PARAMETER) + " timestamp(6) NOT NULL, "
                + backtick(DataStoreSelf.DATA_SQL_PARAMETER) + " json NOT NULL);"
                + "\n"
                + "ALTER TABLE "+backtick(schema)+"." + backtick(tableName) +" OWNER TO \""+owner+"\";"
                + "\n"
                + "ALTER TABLE " + backtick(schema) +"." + backtick(tableName)+" ADD CONSTRAINT "+backtick(tableName+"_pk")+" PRIMARY KEY ("+backtick(DataStoreSelf.ID_SQL_PARAMETER)+");";

    }

    @Override
    public String createSelfTableColumnSql(String schema, String tableName, String fieldCode, String columnName, String dataType, Integer length, Integer decimalDigits) {
        SimpleDataType simpleDataType = SimpleDataType.valueOf(dataType);
        return "ALTER TABLE "+backtick(schema)+"." + backtick(tableName)
                + " ADD COLUMN " + backtick(columnName) + " "
                + columnInitSql(simpleDataType, length, decimalDigits)
                + " GENERATED ALWAYS AS (data->>'" + fieldCode + "') STORED";
    }

    @Override
    public String modifySelfTableColumnSql(String schema, String tableName, String fieldCode, String columnName, String dataType, Integer length, Integer decimalDigits) {
        SimpleDataType simpleDataType = SimpleDataType.valueOf(dataType);
        return "ALTER TABLE "+schema+"." + backtick(tableName)
                + " MODIFY COLUMN " + backtick(columnName) + " "
                + columnInitSql(simpleDataType, length, decimalDigits)
                + " GENERATED ALWAYS AS (data->>'" + fieldCode + "') STORED";
    }

    @Override
    public String createTableIndexSql(String schema, String tableName, String indexName, TableIndexType indexType, List<String> indexColumns) {
        if (TableIndexType.UNIQUE==indexType) {
            return  "CREATE UNIQUE INDEX " + indexName + " ON "+backtick(schema)+"." + backtick(tableName) + " (" + StringHelper.join(indexColumns, ",") + ")";
        } else {
            return  "CREATE INDEX " + indexName + " ON "+backtick(schema)+"." + backtick(tableName) + " (" + StringHelper.join(indexColumns, ",") + ")";
        }
    }

    @Override
    public String deleteTableIndexSql(String schema, String tableName, String indexName) {
        return "DROP INDEX "+indexName;
    }

    public static String columnInitSql(SimpleDataType simpleDataType, Integer length, Integer decimalDigits){
        switch (simpleDataType){
            case TEXT: {
                if(length>2000){
                    return "text";
                }
                return "varchar(" + length + ")";
            }
            case NUMBER: {
                if(decimalDigits == null){
                    decimalDigits = 0;
                }
                if(decimalDigits!=0){
                    return "decimal("+length+","+decimalDigits+")";
                }
                if(length<10){// 如果整数不超过10位，那么用int
                    return "int";
                }else if(length<19){// 如果整数不超过19位，那么用bigint
                    return "bigint";
                }else {
                    return "decimal("+length+",0)";
                }
            }
            case BOOLEAN:
                return "tinyint(1)";
            case DATE_TIME: {
                return "timestamp without time zone";
            }
            // 对象数组等统一保存为JSON
            default:
                return "json";
        }

    }
}
