package com.deeppaas.quartz.api;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.quartz.entity.QuartzJobDO;
import com.deeppaas.quartz.model.QuartzJobParam;
import com.deeppaas.quartz.service.QuartzJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 定时任务管理器接口层
 *
 * <AUTHOR>
 * @date 2022/03/22
 */
@RestController
@RequestMapping("/api/quartz")
public class QuartzJobApi {
    private final QuartzJobService quartzJobService;

    @Autowired
    public QuartzJobApi(QuartzJobService quartzJobService) {
        this.quartzJobService = quartzJobService;
    }

    /**
     * 获取定时任务
     */
    @GetMapping("/get")
    public RestModel<QuartzJobDO> get(@RequestParam String id) {
        return RestModel.data(quartzJobService.findByJobId(id));
    }

    /**
     * 获取定时任务列表
     */
    @GetMapping("/all")
    public RestModel<List<QuartzJobDO>> listAll() {
        return RestModel.data(quartzJobService.findAll());
    }

    /**
     * 获取定时任务列表
     */
    @GetMapping("/list")
    public RestModel<List<QuartzJobDO>> listByApp(@RequestParam String appCode) {
        return RestModel.data(quartzJobService.findByApp(appCode));
    }

    /**
     * 新建
     */
    @PostMapping("/add")
    public RestModel<QuartzJobDO> add(@RequestBody QuartzJobParam param) {
        QuartzJobDO jobDO = quartzJobService.add(param);
        return RestModel.data(jobDO);
    }

    /**
     * 修改
     */
    @PostMapping("/modify")
    public RestModel<QuartzJobDO> modify(@RequestBody QuartzJobParam param) {
        QuartzJobDO jobDO = quartzJobService.modify(param);
        return RestModel.data(jobDO);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    public RestModel<Boolean> delete(@BodyParam String id) {
        quartzJobService.delete(id);
        return RestModel.data(true);
    }

    /**
     * 停止任务
     */
    @PostMapping("/stop")
    public RestModel<Boolean> stop(@BodyParam String id) {
        quartzJobService.stop(id);
        return RestModel.data(true);
    }

    /**
     * 开启任务
     */
    @PostMapping("/start")
    public RestModel<Boolean> start(@BodyParam String id) {
        quartzJobService.start(id);
        return RestModel.data(true);
    }

    /**
     * 立即执行一次
     */
    @PostMapping("/run")
    public RestModel<Boolean> run(@BodyParam String id) {
        quartzJobService.run(id);
        return RestModel.data(true);
    }
}
