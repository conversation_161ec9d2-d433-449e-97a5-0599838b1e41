package com.deeppaas.msg.api.client;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
public class MsgFormat {

    public static String format(final String template, Map<String, Object> params){
        String msg = new String(template);
        for(Map.Entry<String, Object> entry : params.entrySet()){
            Object value = entry.getValue();
            if(value!=null){
                msg = msg.replaceAll("{"+entry.getKey()+"}", value.toString());
            }
        }
        return msg;
    }
}
