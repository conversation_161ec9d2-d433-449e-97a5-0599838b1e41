package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 流程页面设置
 * <AUTHOR>
 * @date 2022/5/17
 */
@Data
@Table("flow_page")
public class FlowPageDO extends BaseUuidDO {
    /**
     * 流程定义Id
     */
    @Column("define_id")
    private String defineId;
    /**
     * 流程定义环节ID
     */
    @Column("node_id")
    private String nodeId;
    /**
     * 流程定义环节名称
     */
    @Column("node_name")
    private String nodeName;
    /**
     * 流程环节页面组件ID
     */
    @Column("page_id")
    private String pageId;
}
