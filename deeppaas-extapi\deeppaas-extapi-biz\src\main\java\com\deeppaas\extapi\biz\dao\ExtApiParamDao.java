package com.deeppaas.extapi.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.extapi.biz.entity.ExtApiParamDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/25
 */
public interface ExtApiParamDao extends BaseJdbcDao<ExtApiParamDO, String> {

    @Modifying
    @Query("delete from ext_api_param where api_id=:apiId")
    void deleteByApiId(@Param("apiId") String apiId);

    List<ExtApiParamDO> findByApiIdOrderByIdAsc(String apiId);
}
