package com.deeppaas.quartz.service;

import com.deeppaas.common.exception.ReqException;
import com.deeppaas.quartz.dao.JobApiParamDao;
import com.deeppaas.quartz.entity.JobApiParamDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务服务
 */
@Service
public class QuartzJobParamService {
    private final JobApiParamDao apiParamDao;

    public QuartzJobParamService(JobApiParamDao apiParamDao) {
        this.apiParamDao = apiParamDao;
    }

    /**
     * 获取API定时任务参数列表
     *
     * @param jobId 任务ID
     * @return 定时任务参数列表
     */
    public List<JobApiParamDO> listApiParam(String jobId) {
        return apiParamDao.findByJobId(jobId);
    }

    /**
     * 保存API参数
     *
     * @param param 参数
     * @return 参数信息
     */
    @Transactional
    public JobApiParamDO saveApiParam(JobApiParamDO param) {
        JobApiParamDO apiParamDO;
        if (param.getId() == null) {
            apiParamDO = apiParamDao.findByJobIdAndCode(param.getJobId(), param.getCode());
            if (apiParamDO != null) {
                throw ReqException.paramValidate("参数编号已经存在");
            }
            apiParamDO = new JobApiParamDO();
            apiParamDO.setJobId(param.getJobId());
            apiParamDO.setCode(param.getCode());
            apiParamDO.setValue(param.getValue());
            apiParamDO.setCreateTime(LocalDateTime.now());
        } else {
            apiParamDO = apiParamDao.getById(param.getId());
            apiParamDO.setCode(param.getCode());
            apiParamDO.setValue(param.getValue());
        }
        return apiParamDao.save(apiParamDO);
    }

    /**
     * 删除API相关参数
     *
     * @param id 参数主键ID
     */
    @Transactional
    public void deleteApiParam(Long id) {
        apiParamDao.deleteById(id);
    }
}
