package com.deeppaas.configuration.biz.dao;

import com.deeppaas.configuration.biz.entity.AidosConfigVariableDO;
import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AidosConfigVariableDao extends BaseJdbcDao<AidosConfigVariableDO, String> {

    public List<AidosConfigVariableDO> findByAidosProcessConfigIdAndKeyIn(String aidosProcessConfigId,List<String> key);
    public List<AidosConfigVariableDO> findByAidosProcessConfigId(String aidosProcessConfigId);

    List<AidosConfigVariableDO> findByProcessTaskId(String processTaskId);
}
