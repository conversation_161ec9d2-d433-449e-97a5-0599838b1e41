package com.deeppaas.quartz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 任务执行日志
 *
 * <AUTHOR>
 * @date 2022/03/23
 */
@Data
@Table("quartz_job_log")
@EqualsAndHashCode(callSuper = true)
public class QuartzJobLogDO extends BaseUuidDO {

    /**
     * 所属任务
     */
    @Column("job_id")
    private String jobId;

    /**
     * 执行结果
     */
    @Column("result")
    private String result;

    /**
     * 执行结果
     */
    @Column("content")
    private String content;

}
