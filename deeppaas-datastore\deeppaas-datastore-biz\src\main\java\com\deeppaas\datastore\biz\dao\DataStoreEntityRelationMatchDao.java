package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreEntityRelationMatchDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6
 */
public interface DataStoreEntityRelationMatchDao extends BaseJdbcDao<DataStoreEntityRelationMatchDO, String> {

    List<DataStoreEntityRelationMatchDO> findByEntity1CodeAndEntity2Code(String entity1Code, String entity2Code);

    @Modifying
    @Query("delete from datastore_entity_relation_match where entity1_code=:entity1Code and entity2_code=:entity2Code")
    void deleteByEntity1CodeAndEntity2Code(String entity1Code, String entity2Code);
}
