package com.deeppaas.template.biz.web;

import com.deeppaas.template.biz.entity.AidosImageTemplateTypeDO;
import com.deeppaas.template.biz.service.AidosImageTemplateTypeService;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/imagetemplateType")
public class AidosImageTemplateTypeApi {

    private static final Logger logger = LoggerFactory.getLogger(AidosImageTemplateTypeApi.class);
    @Autowired
    private AidosImageTemplateTypeService aidosImageTemplateTypeService;

    @PostMapping("/page")
    public RestModel<PageData<AidosImageTemplateTypeDO>> page(@RequestBody PageSearch pageSearch) {
        PageData<AidosImageTemplateTypeDO> list = aidosImageTemplateTypeService.page(pageSearch);
        return RestModel.data(list);
    }

    @GetMapping("/getAll")
    public RestModel<List<AidosImageTemplateTypeDO>> list() {
        List<AidosImageTemplateTypeDO> list = aidosImageTemplateTypeService.getAll();
        return RestModel.data(list);
    }
    @GetMapping("/getById")
    public RestModel<AidosImageTemplateTypeDO> getById(@RequestParam String id) {
        AidosImageTemplateTypeDO model = aidosImageTemplateTypeService.get(id);
        return RestModel.data(model);
    }

    @PostMapping("/save")
    public RestModel list(@RequestBody AidosImageTemplateTypeDO aidosImageTemplateTypeDO) {
        String message = aidosImageTemplateTypeService.save(aidosImageTemplateTypeDO);
        if (StringHelper.isNotEmpty(message)) {
            return RestModel.error(500, message);
        }
        return RestModel.success("保存成功");
    }

    @GetMapping("/del")
    public RestModel list(@RequestParam String id) {
        aidosImageTemplateTypeService.del(id);
        return RestModel.success("删除成功");
    }


}
