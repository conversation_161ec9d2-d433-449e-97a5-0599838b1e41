package com.deeppaas.datastore.biz.service;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.BoolHelper;
import com.deeppaas.datastore.biz.convert.DataStoreFolderConvert;
import com.deeppaas.datastore.biz.dao.DataStoreFolderDao;
import com.deeppaas.datastore.biz.entity.DataStoreFolderDO;
import com.deeppaas.datastore.biz.web.req.DataStoreFolderParam;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderBaseVO;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderNode;
import com.deeppaas.datastore.biz.web.vo.DataStoreFolderVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 数据表文件夹服务层
 *
 * <AUTHOR>
 * @date 2022/07/30
 */
@Service
public class DataStoreFolderService {
    private final DataStoreFolderDao folderDao;

    public DataStoreFolderService(DataStoreFolderDao folderDao) {
        this.folderDao = folderDao;
    }

    public DataStoreFolderDO get(String id) {
        return folderDao.getById(id);
    }

    public List<DataStoreFolderDO> gets(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return folderDao.findByIdIn(ids);
    }

    public List<DataStoreFolderNode> getDescendants(String parentId) {
        List<DataStoreFolderNode> folderNodeList = Lists.newArrayList();
        // 获取子文件夹
        List<DataStoreFolderDO> folderDOList = folderDao.findByParentIdAndUsableOrderBySortNoAsc(parentId, BoolHelper.INT_TRUE);
        for (DataStoreFolderDO folderDO : folderDOList) {
            DataStoreFolderNode folderNode = new DataStoreFolderNode();
            folderNode.setId(folderDO.getId());
            folderNode.setName(folderDO.getName());
            folderNode.setSortNo(folderDO.getSortNo());
            // 获取后代列表
            folderNode.setChildrenNodes(getDescendants(folderDO.getId()));
            // 放入集合中
            folderNodeList.add(folderNode);
        }
        return folderNodeList;

    }

    public List<DataStoreFolderNode> getChildren(String parentId) {
        List<DataStoreFolderNode> folderNodeList = Lists.newArrayList();
        // 获取子文件夹
        List<DataStoreFolderDO> folderDOList = folderDao.findByParentIdAndUsableOrderBySortNoAsc(parentId, BoolHelper.INT_TRUE);
        for (DataStoreFolderDO folderDO : folderDOList) {
            DataStoreFolderNode folderNode = new DataStoreFolderNode();
            folderNode.setId(folderDO.getId());
            folderNode.setName(folderDO.getName());
            folderNode.setSortNo(folderDO.getSortNo());
            // 放入集合中
            folderNodeList.add(folderNode);
        }
        return folderNodeList;
    }

    @Transactional
    public DataStoreFolderDO add(DataStoreFolderParam param) {
//        boolean existName = folderDao.existsByParentIdAndName(param.getParentId(), param.getName());
//        if (existName) {
//            throw RunException.optReject("操作失败，名称已经存在");
//        }
        DataStoreFolderDO folderDO = new DataStoreFolderDO();
        folderDO.setName(param.getName());
        folderDO.setParentId(param.getParentId());
        folderDO.setUsable(BoolHelper.INT_TRUE);
        folderDO.setSortNo(param.getSortNo());
        return folderDao.save(folderDO);
    }

    @Transactional
    public void rename(String id, String name) {
        DataStoreFolderDO folderDO = folderDao.getById(id);
        if (folderDO == null) {
            throw RunException.dataNotFound("操作失败，未找到有效数据");
        }
        folderDO.setName(name);
        folderDao.save(folderDO);
    }

    @Transactional
    public void remove(String id) {
        DataStoreFolderDO folderDO = folderDao.getById(id);
        if (folderDO == null) {
            throw RunException.dataNotFound("操作失败，未找到有效数据");
        }
        folderDO.setUsable(BoolHelper.INT_FALSE);
        folderDao.save(folderDO);
    }

    @Transactional
    public void sort(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        int sortNo = 1;
        for (String id : ids) {
            DataStoreFolderDO folderDO = folderDao.getById(id);
            if (folderDO == null) {
                continue;
            }
            folderDO.setSortNo(sortNo++);
            folderDao.save(folderDO);
        }
    }

    public List<DataStoreFolderVO> recycles() {
        List<DataStoreFolderDO> folderDOList = folderDao.findByUsable(BoolHelper.INT_FALSE);
        if (CollectionUtils.isEmpty(folderDOList)) {
            return Collections.emptyList();
        }
        // 父部门编号列表
        Set<String> parentIds = folderDOList.stream().map(DataStoreFolderDO::getParentId).collect(Collectors.toSet());
        List<DataStoreFolderDO> parentFolderDOList = folderDao.findByIdIn(parentIds);
        // 组装并返回
        return DataStoreFolderConvert.entityToVOS(folderDOList, parentFolderDOList);
    }

    @Transactional
    public void regain(String id) {
        DataStoreFolderDO folderDO = folderDao.getById(id);
        if (folderDO == null) {
            throw RunException.dataNotFound("操作失败，未找到有效数据");
        }
        folderDO.setUsable(BoolHelper.INT_TRUE);
        folderDao.save(folderDO);
    }

    @Transactional
    public void delete(String id) {
        List<DataStoreFolderDO> folderDOList = folderDao.findByParentId(id);
        for (DataStoreFolderDO childDO : folderDOList) {
            delete(childDO.getId());
        }
        // 删除本身
        folderDao.deleteById(id);
    }

    @Transactional
    public void modifyParent(String id, String parentId) {
        DataStoreFolderDO folderDO = folderDao.getById(id);
        if (folderDO == null) {
            throw RunException.dataNotFound("操作失败，未找到有效数据");
        }
        // 修改父文件夹ID
        folderDO.setParentId(parentId);
        folderDao.save(folderDO);
    }

    public List<DataStoreFolderBaseVO> getParents(String id) {
        List<DataStoreFolderDO> folderDOList = new ArrayList<>();
        // 将文件夹放入列表
        addFolderToRoot(id, folderDOList);
        // 组装返回
        return DataStoreFolderConvert.entityToBaseVOS(folderDOList);
    }

    private void addFolderToRoot(String id, List<DataStoreFolderDO> folderDOList) {
        DataStoreFolderDO folderDO = folderDao.getById(id);
        if (folderDO == null) {
            return;
        }
        folderDOList.add(folderDO);
        // 再次获取父文件夹信息
        addFolderToRoot(folderDO.getParentId(), folderDOList);
    }
}
