package com.deeppaas.datastore.common.dialect;

import com.deeppaas.datastore.common.emnus.TableIndexType;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
public abstract class AbstractDialect {

    public abstract String limitOffset(Pageable pageable);

    public abstract String backtick(String name);

    public abstract String createSelfTableSql(String schema, String tableName, String owner);

    public abstract String createSelfTableColumnSql(String schema, String tableName, String fieldCode, String columnName, String dataType, Integer length, Integer decimalDigits);

    public abstract String modifySelfTableColumnSql(String schema, String tableName, String fieldCode, String columnName, String dataType, Integer length, Integer decimalDigits);

    public abstract String createTableIndexSql(String schema, String tableName, String indexName, TableIndexType indexType, List<String> indexColumns);

    public abstract String deleteTableIndexSql(String schema, String tableName, String indexName);
}
