package com.deeppaas.datastore.common.mapping;

import lombok.Data;

/**
 * 数据仓库属性
 * <AUTHOR>
 * @date 2021/12/16
 */
@Data
public class DataStoreProperty {
    /**
     * 属性名
     */
    private String name;
    /**
     * 数据库列名
     */
    private String columnName;
    /**
     * 数据类型
     */
    private String dataType;
    /**
     * 是否是主键
     */
    private boolean primary;
    /**
     * 是否是数据库生成字段，不如自增ID
     */
    private boolean dbGenerated;
    /**
     * 是否是鉴别器字段
     */
    private boolean discriminator;
    /**
     * 是否是唯一
     */
    private boolean unique;
    /**
     * 鉴别器鉴别值
     */
    private String discriminatorValue;
    /**
     * 是否是虚列
     */
    private boolean virtual;
    /**
     * 所属关联
     */
    private DataStoreTableJoin tableJoin;
}
