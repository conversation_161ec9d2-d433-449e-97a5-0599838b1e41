var q=Object.defineProperty,V=Object.defineProperties;var $=Object.getOwnPropertyDescriptors;var v=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var y=(u,r,t)=>r in u?q(u,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[r]=t,F=(u,r)=>{for(var t in r||(r={}))L.call(r,t)&&y(u,t,r[t]);if(v)for(var t of v(r))G.call(r,t)&&y(u,t,r[t]);return u},B=(u,r)=>V(u,$(r));import{r as p,j as e,g as c,B as N,am as R,az as H}from"./index.689bcdcb.js";import{u as D}from"./useMutation.b5bc9a93.js";import{u as J}from"./useQuery.d623ee6b.js";import{s as U,m as K,b as Z}from"./user.32d1e8da.js";import{F as n}from"./index.6f21ddaa.js";import{M,P as W}from"./index.a066d314.js";import{I as E}from"./index.f2cb64d4.js";import{R as x}from"./FolderOutlined.015b2d42.js";import{T as X}from"./index.1f7b2316.js";import{Q as Y}from"./QuestionCircleOutlined.bc2cf384.js";import{e as ee}from"./util.bf0e86fd.js";import{u as ue}from"./useUser.01c90442.js";import{S as I}from"./index.1f87c7c0.js";import{B as k}from"./button.34f11f85.js";import{T as se}from"./Table.0c868d2f.js";import"./index.71d34973.js";import"./reactNode.50ea6579.js";import"./responsiveObserve.da8166ca.js";import"./DialogWrap.9951837c.js";import"./getScrollBarSize.001053fa.js";import"./RightOutlined.ed31d8e3.js";import"./pickAttrs.04174e9b.js";import"./getRenderPropValue.1abeb751.js";import"./EyeOutlined.70f6da72.js";import"./index.7f65f5a2.js";import"./Overflow.1478d911.js";import"./DownOutlined.cc0b2189.js";import"./CheckOutlined.11997747.js";import"./css.032fc6ca.js";import"./LeftOutlined.6bce5536.js";import"./useBreakpoint.ddfb7c4a.js";import"./useForceUpdate.a9f3ccc5.js";import"./dropdown.cc3aab1b.js";import"./index.41297259.js";import"./scrollTo.edb0bd08.js";const T={id:"",name:"",code:"",aidosRole:"admin",usable:1},re=/^[0-9a-zA-Z_]{1,}$/;function te({data:u,isOpen:r,onClose:t,refetch:m}){const i=n.useForm()[0];p.exports.useEffect(()=>{r&&(u?i.setFieldsValue(u):i.setFieldsValue(T))},[u,i,r]);const h=D(U,{onSuccess(a){N.success(a.data.data),m()}});function o(a){console.log(a),u?h.mutate(B(F({},a),{id:u.id})):h.mutate(a),t()}return e(M,{title:u?"\u7F16\u8F91\u7528\u6237":"\u521B\u5EFA\u7528\u6237",visible:r,onCancel:()=>t(),onOk:()=>i.submit(),style:{width:"480px"},okText:u?"\u786E\u5B9A":"\u521B\u5EFA",cancelText:"\u53D6\u6D88",forceRender:!0,children:c(n,{layout:"vertical",onFinish:a=>o(a),form:i,initialValues:T,children:[e(n.Item,{name:"name",label:"\u7528\u6237\u540D",className:"text-sm font-bold",rules:[{whitespace:!0,message:"\u8BF7\u586B\u5199\u7528\u6237\u540D"},{required:!0,message:"\u8BF7\u586B\u5199\u7528\u6237\u540D"}],children:e(E,{type:"text"})}),e(n.Item,{name:"code",label:"\u7528\u6237\u7F16\u53F7",className:"text-sm font-bold",rules:[{pattern:re,message:"\u53EA\u80FD\u8F93\u5165\u6570\u5B57\u548C\u5B57\u6BCD"},{required:!0,message:"\u8BF7\u586B\u5199\u7528\u6237\u7F16\u53F7"}],children:e(E,{})}),e(n.Item,{name:"aidosRole",label:"\u7528\u6237\u7C7B\u578B",className:"text-sm font-bold",children:c(x.Group,{children:[e(x,{value:"admin",children:"\u7BA1\u7406\u5458"}),e(x,{value:"user",style:{marginLeft:"22px"},children:"\u666E\u901A\u7528\u6237"})]})}),e(n.Item,{name:"usable",label:"\u7528\u6237\u72B6\u6001",className:"text-sm font-bold",children:c(x.Group,{disabled:(u==null?void 0:u.aidosRole)==="superAdmin",children:[e(x,{value:1,children:"\u542F\u7528"}),e(x,{value:0,style:{marginLeft:"22px"},children:"\u7981\u7528"})]})})]})})}const _=[{required:!0,message:"\u5FC5\u586B"}];function oe({isOpen:u,onClose:r,code:t}){const m=n.useForm()[0];p.exports.useEffect(()=>{u&&m.setFieldsValue({newPassword:"",confirmPassword:"",oldPassword:""})},[m,u]);const i=D(K,{onSuccess(o){N.success(o.data.data),r(!1)}});function h(o){if(!o.newPassword.trim()){N.warning("\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A\u683C");return}const a={oldPwd:o.oldPassword,pwd:o.newPassword,confirmPwd:o.confirmPassword,code:t};i.mutate(a)}return e(M,{title:c("div",{children:["\u5BC6\u7801\u4FEE\u6539",e(X,{title:"\u521D\u59CB\u5BC6\u7801\uFF1A666666",className:"text-black",color:"#ffffff",overlayInnerStyle:{color:"black"},placement:"right",children:e(Y,{className:"w-4 h-4 ml-2 cursor-pointer"})})]}),visible:u,onCancel:()=>r(!1),onOk:()=>m.submit(),style:{width:"480px"},okText:"\u786E\u5B9A",cancelText:"\u53D6\u6D88",forceRender:!0,children:c(n,{layout:"vertical",form:m,onFinish:o=>{h(o)},children:[e(n.Item,{label:"\u65E7\u5BC6\u7801",name:"oldPassword",className:"text-sm font-bold",rules:_,children:e(E,{name:"oldPassword",placeholder:"\u8BF7\u8F93\u5165"})}),e(n.Item,{label:"\u65B0\u5BC6\u7801",name:"newPassword",className:"text-sm font-bold",rules:_,children:e(E,{name:"newPassword",placeholder:"\u8BF7\u8F93\u5165"})}),e(n.Item,{label:"\u786E\u8BA4\u5BC6\u7801",name:"confirmPassword",className:"text-sm font-bold",rules:[({getFieldValue:o})=>({validator(a,f){return!f||o("newPassword")===f?Promise.resolve():Promise.reject(new Error("\u5E94\u4E0E\u786E\u8BA4\u5BC6\u7801\u4FDD\u6301\u4E00\u81F4"))}}),{required:!0,message:"\u5FC5\u586B"}],children:e(E,{name:"confirmPassword",placeholder:"\u8BF7\u8F93\u5165"})})]})})}const g={ADMIN:"admin",USER:"user",SUPERADMIN:"superAdmin",HIDE:"hide"},ae={[g.ADMIN]:"\u7BA1\u7406\u5458",[g.USER]:"\u666E\u901A\u7528\u6237",[g.SUPERADMIN]:"\u8D85\u7EA7\u7BA1\u7406\u5458",[g.SUPERADMIN]:"\u6E38\u5BA2"};function Ve(){const[u,r]=p.exports.useState(!1),[t,m]=p.exports.useState(!1),[i,h]=p.exports.useState({pageNo:1,search_EQ_aidosRole:""}),[o,a]=p.exports.useState(""),{Option:f}=I,[O,P]=p.exports.useState(),[S,C]=p.exports.useState({pageNo:1,pageSize:10,search_CONTAINS_name:"",search_EQ_aidosRole:""}),{user:w}=ue();console.log(w);const{data:d,refetch:A}=J(["account-page"],()=>ee(()=>Z(S))()),j=D(U,{onSuccess(){console.log(1),A()}});if(p.exports.useEffect(()=>{A()},[S]),!w)return null;const Q={total:d==null?void 0:d.total,showTotal:(s,l)=>`\u7B2C ${l[0]}-${l[1]} \u6761/\u5171 ${s} \u6761`,defaultCurrent:1,defaultPageSize:10,onChange:s=>{C(l=>B(F({},l),{pageNo:s}))},pageSizeOptions:["5","10","50"],onShowSizeChange:(s,l)=>{C(b=>B(F({},b),{pageSize:l}))},showQuickJumper:!0,current:d==null?void 0:d.pageNo},z=[{title:"\u5E8F\u53F7",render:(s,l,b)=>e("div",{children:b+1}),key:"id"},{title:"\u59D3\u540D",dataIndex:"name",key:"name"},{title:"\u7F16\u53F7",dataIndex:"code",key:"code"},{title:"\u7C7B\u578B",render:s=>e("div",{children:ae[s.aidosRole]}),key:"aidosRole"},{title:"\u72B6\u6001",render:s=>e("div",{children:s.usable===1?"\u542F\u7528":"\u7981\u7528"}),key:"usable"},{title:"\u64CD\u4F5C",dataIndex:"",key:"x",render:s=>c("div",{className:"flex",children:[s.aidosRole!=="hide"?e(R,{children:(w.aidosRole==="superAdmin"||w.code===s.code)&&s.aidosRole!=="superAdmin"?c(R,{children:[e("div",{className:"mr-4 cursor-pointer hover:text-primary-default",onClick:()=>{P(s),r(!0)},children:"\u7F16\u8F91"}),e("div",{className:"mr-4  cursor-pointer hover:text-primary-default",onClick:()=>{m(!0),a(s.code)},children:"\u5BC6\u7801"})]}):null}):null,s.aidosRole!=="superAdmin"&&s.usable===1||w.aidosRole==="user"?e(W,{placement:"bottomRight",title:"\u4F60\u786E\u5B9A\u8981\u5220\u9664\u5417\uFF1F",onConfirm:()=>j.mutate(B(F({},s),{usable:0})),okText:"\u786E\u5B9A",cancelText:"\u53D6\u6D88",okType:"danger",icon:e(H,{style:{color:"#FF4D4F"}}),children:e("div",{className:"cursor-pointer hover:text-primary-default",children:"\u5220\u9664"})}):null]})}];return c("div",{className:" px-6",children:[c("div",{className:"h-16 mt-2 flex justify-end items-center",children:[e(E,{className:"h-8 ",style:{width:"160px",height:"32px",marginRight:"14px"},placeholder:"\u8BF7\u8F93\u5165\u59D3\u540D",value:i.search_CONTAINS_name,onChange:s=>{h(l=>B(F({},l),{search_CONTAINS_name:s.target.value}))}}),c(I,{className:"w-[108px] h-8",value:i.search_EQ_aidosRole,onChange:s=>{h(l=>B(F({},l),{search_EQ_aidosRole:s}))},getPopupContainer:s=>s.parentElement,children:[e(f,{value:"superAdmin",children:"\u8D85\u7EA7\u7BA1\u7406\u5458"}),e(f,{value:"",children:"\u5168\u90E8\u7528\u6237"}),e(f,{value:"user",children:"\u666E\u901A\u7528\u6237"}),e(f,{value:"admin",children:"\u7BA1\u7406\u5458"})]}),e(k,{type:"primary",className:"ml-[14px]",onClick:()=>C(i),children:"\u641C\u7D22"}),e(k,{className:"ml-6",onClick:()=>{r(!0)},children:"\u521B\u5EFA\u7528\u6237"})]}),e(se,{dataSource:d==null?void 0:d.list,columns:z,pagination:Q}),e(te,{data:O,isOpen:u,onClose:()=>{r(!1),P(void 0)},refetch:A}),e(oe,{isOpen:t,onClose:m,code:o})]})}export{Ve as default};
