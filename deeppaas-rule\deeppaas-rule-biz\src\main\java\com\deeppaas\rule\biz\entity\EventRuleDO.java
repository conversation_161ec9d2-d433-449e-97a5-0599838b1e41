package com.deeppaas.rule.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
@Data
@Table("rule_event_rule")
public class EventRuleDO extends BaseUuidDO {

    /**
     * 事件来源：数据模型、流程等等
     */
    @Column("owner_type")
    private String ownerType;

    /**
     * 事件来源ID：数据模型、流程等等ID
     */
    @Column("owner_id")
    private String ownerId;

    /**
     * 事件@DataStoreEvent
     */
    @Column("event")
    private String event;

    /**
     * 执行规则ID
     */
    @Column("rule_id")
    private String ruleId;

    /**
     * 排序号
     */
    @Column("sort_no")
    private Integer sortNo;
}
