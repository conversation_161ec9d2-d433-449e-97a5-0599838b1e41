package com.deeppaas.flow.workbench.model;

import com.deeppaas.flow.biz.entity.FlowDefineDO;
import com.deeppaas.flow.biz.model.vo.FlowInstanceVO;
import lombok.Data;

import java.util.Map;

/**
 * 工单
 *
 * <AUTHOR>
 * @date 2022/3/11
 */
@Data
public class WorkOrder {
    /**
     * 数据模型
     */
    private String entity;

    /**
     * 工单ID
     */
    private String orderId;

    /**
     * 流程信息
     */
    private FlowDefineDO define;

    /**
     * 流程实例信息
     */
    private FlowInstanceVO instance;

    /**
     * 申请单数据
     */
    private Map<String, Object> data;
}
