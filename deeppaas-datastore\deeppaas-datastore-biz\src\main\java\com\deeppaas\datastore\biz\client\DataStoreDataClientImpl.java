package com.deeppaas.datastore.biz.client;

import com.deeppaas.common.model.PageData;
import com.deeppaas.datastore.api.client.DataStoreDataClient;
import com.deeppaas.datastore.biz.factory.DataStoreJdbcRepositoryFactory;
import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.DataStoreRepository;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/5
 */
@Component
public class DataStoreDataClientImpl implements DataStoreDataClient {
    private DataStoreJdbcRepositoryFactory repositoryFactory;

    public DataStoreDataClientImpl(DataStoreJdbcRepositoryFactory repositoryFactory) {
        this.repositoryFactory = repositoryFactory;
    }

    @Override
    public DataStoreData getData(String entityCode, String id) {
        DataStoreRepository repository = repositoryFactory.getRepository(entityCode);
        return repository.getById(id);
    }

    @Override
    public DataStoreData saveData(DataStoreData dataStoreData) {
        DataStoreRepository repository = repositoryFactory.getRepository(dataStoreData.getEntity());
        return repository.save(dataStoreData);
    }

    @Override
    public void removeData(String entity, String id, String userCode) {
        DataStoreRepository repository = repositoryFactory.getRepository(entity);
        repository.deleteById(id);
    }
    @Override
    public List<DataStoreData> query(String entityCode, DataStoreCondition condition) {
        DataStoreRepository repository = repositoryFactory.getRepository(entityCode);
        return repository.findForList(condition);
    }
    @Override
    public List<DataStoreData> query(String entityCode, DataStoreCondition condition, Sort sort) {
        DataStoreRepository repository = repositoryFactory.getRepository(entityCode);
        if(sort==null){
            return repository.findForList(condition);
        }
        return repository.findForList(condition, sort);
    }
    @Override
    public List<DataStoreData> query(String entityCode, DataStoreCondition condition, Pageable pageable) {
        DataStoreRepository repository = repositoryFactory.getRepository(entityCode);
        return repository.findForList(condition, pageable);
    }

    @Override
    public PageData<DataStoreData> queryForPage(String entityCode, DataStoreCondition condition, Pageable pageable) {
        DataStoreRepository repository = repositoryFactory.getRepository(entityCode);
        return repository.findForPage(condition, pageable);
    }

    @Override
    public int delete(String entityCode, DataStoreCondition condition) {
        DataStoreRepository repository = repositoryFactory.getRepository(entityCode);
        List<DataStoreData> list = repository.findForList(condition);
        for(DataStoreData data : list){
            repository.delete(data);
        }
        return list.size();
    }

    @Override
    public void update(String entityCode, List<DataStoreData> dataList) {
        DataStoreRepository repository = repositoryFactory.getRepository(entityCode);
        for(DataStoreData data : dataList){
            repository.save(data);
        }
    }


}
