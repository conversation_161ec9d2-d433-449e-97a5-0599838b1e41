package com.deeppaas.extapi.biz.convert;

import com.deeppaas.extapi.biz.entity.ExtApiDO;
import com.deeppaas.extapi.biz.entity.ExtApiUserDO;
import com.deeppaas.extapi.biz.entity.ExtApiUserRightDO;
import com.deeppaas.extapi.biz.model.ExtApiUserRightVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ExtApiUserConvert {

    public static List<ExtApiUserRightVO> entityToVOSByApi(List<ExtApiUserRightDO> apiUserRightDOList,
                                                           Map<String, ExtApiUserDO> apiUserMap) {

        List<ExtApiUserRightVO> voList = new ArrayList<>(apiUserRightDOList.size());
        for (ExtApiUserRightDO userRightDO : apiUserRightDOList) {
            ExtApiUserRightVO vo = new ExtApiUserRightVO();
            vo.setId(userRightDO.getId());
            vo.setUser(apiUserMap.get(vo.getId()));
            voList.add(vo);
        }
        return voList;
    }

    public static List<ExtApiUserRightVO> entityToVOSByUser(List<ExtApiUserRightDO> apiUserRightDOList,
                                                            Map<String, ExtApiDO> apiDOMap) {
        List<ExtApiUserRightVO> voList = new ArrayList<>(apiUserRightDOList.size());
        for (ExtApiUserRightDO userRightDO : apiUserRightDOList) {
            ExtApiUserRightVO vo = new ExtApiUserRightVO();
            vo.setId(userRightDO.getId());
            vo.setApi(ExtApiConvert.entityToVO(apiDOMap.get(userRightDO.getApiId())));
            voList.add(vo);
        }
        return voList;
    }
}
