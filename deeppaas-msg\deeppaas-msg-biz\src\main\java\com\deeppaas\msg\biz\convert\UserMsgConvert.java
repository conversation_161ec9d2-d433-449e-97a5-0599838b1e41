package com.deeppaas.msg.biz.convert;

import com.deeppaas.msg.biz.entity.UserMsgDO;
import com.deeppaas.msg.biz.web.vo.UserMsgVO;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class UserMsgConvert {
    public static UserMsgVO doToVo(UserMsgDO msgDO) {
        if(msgDO==null){
            return null;
        }
        UserMsgVO vo = new UserMsgVO();
        vo.setId(msgDO.getId());
        vo.setBoxId(msgDO.getBoxId());
        vo.setTitle(msgDO.getTitle());
        vo.setContent(msgDO.getContent());
        vo.setMsgUrl(msgDO.getMsgUrl());
        vo.setToUser(msgDO.getToUser());
        vo.setReadFlag(msgDO.getReadFlag());
        vo.setReadTime(msgDO.getReadTime());
        vo.setCreateTime(msgDO.getCreateTime());
        return vo;
    }
}
