package com.deeppaas.datastore.biz.dao;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.datastore.biz.entity.DataStoreFolderDO;

import java.util.List;
import java.util.Set;

/**
 * 数据表文件夹DAO
 *
 * <AUTHOR>
 * @date 2022/07/30
 */
public interface DataStoreFolderDao extends BaseJdbcDao<DataStoreFolderDO, String> {

    List<DataStoreFolderDO> findByParentId(String parentId);

    List<DataStoreFolderDO> findByParentIdAndUsableOrderBySortNoAsc(String parentId, int usable);

    boolean existsByParentIdAndName(String parentId, String name);

    List<DataStoreFolderDO> findByUsable(int usable);

    List<DataStoreFolderDO> findByIdIn(Set<String> ids);
}
