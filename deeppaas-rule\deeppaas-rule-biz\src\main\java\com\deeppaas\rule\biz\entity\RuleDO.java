package com.deeppaas.rule.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 动作规则
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@Data
@Table("rule")
public class RuleDO extends BaseUuidDO {
    /**
     * 规则名称
     */
    @Column("name")
    private String name;
    /**
     * 参数类型
     * @com.deeppaas.common.data.enums.SimpleDataType
     */
    @Column("param_type")
    private String paramType;

    /**
     * 参数应用编号
     */
    @Column("param_app_code")
    private String paramAppCode;

    /**
     * 参数如果是模型类对应的模型索引
     */
    @Column("param_entity")
    private String paramEntity;

    /**
     * 参数备注说明
     */
    @Column("param_remark")
    private String paramRemark;
    /**
     * 返回值类型
     * @com.deeppaas.common.data.enums.SimpleDataType
     */
    @Column("return_type")
    private String returnType;

    /**
     * 返回模型或模型数组时对应的模型索引
     */
    @Column("return_entity")
    private String returnEntity;

    /**
     * 返回应用编号
     */
    @Column("return_app_code")
    private String returnAppCode;

    /**
     * 后端规则，0是前端规则，1是后端规则
     */
    @Column("back_end")
    private Integer backEnd;

    /**
     * 定义来源：数据模型、流程等等
     */
    @Column("owner_type")
    private String ownerType;

    /**
     * 定义来源ID：数据模型、流程等等ID
     */
    @Column("owner_id")
    private String ownerId;

    /**
     * 规则内容
     */
    @Column("content")
    private String content;
}
