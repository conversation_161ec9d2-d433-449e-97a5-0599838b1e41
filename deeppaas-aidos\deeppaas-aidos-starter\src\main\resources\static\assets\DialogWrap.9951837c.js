import{M as ve,L as pe,I as ie,J as xe,K as Ne,r as a,P as ae,X as we,a as Ee,Q as he,f as E,E as y,b as ne,_ as oe}from"./index.689bcdcb.js";import{P as Se,d as Re}from"./reactNode.50ea6579.js";import{g as ge}from"./getScrollBarSize.001053fa.js";import{K as se}from"./RightOutlined.ed31d8e3.js";import{p as _e}from"./pickAttrs.04174e9b.js";function P(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!t)return{};var n=i.element,o=n===void 0?document.body:n,e={},r=Object.keys(t);return r.forEach(function(s){e[s]=o.style[s]}),r.forEach(function(s){o.style[s]=t[s]}),e}function $e(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var Z={},ce=function(t){if(!(!$e()&&!t)){var i="ant-scrolling-effect",n=new RegExp("".concat(i),"g"),o=document.body.className;if(t){if(!n.test(o))return;P(Z),Z={},document.body.className=o.replace(n,"").trim();return}var e=ge();if(e&&(Z=P({position:"relative",width:"calc(100% - ".concat(e,"px)")}),!n.test(o))){var r="".concat(o," ").concat(i);document.body.className=r.trim()}}},p=[],Ce="ant-scrolling-effect",ee=new RegExp("".concat(Ce),"g"),Te=0,te=new Map,Pe=ve(function t(i){var n=this;pe(this,t),this.lockTarget=void 0,this.options=void 0,this.getContainer=function(){var o;return(o=n.options)===null||o===void 0?void 0:o.container},this.reLock=function(o){var e=p.find(function(r){var s=r.target;return s===n.lockTarget});e&&n.unLock(),n.options=o,e&&(e.options=o,n.lock())},this.lock=function(){var o;if(!p.some(function(c){var l=c.target;return l===n.lockTarget})){if(p.some(function(c){var l,u=c.options;return(u==null?void 0:u.container)===((l=n.options)===null||l===void 0?void 0:l.container)})){p=[].concat(ie(p),[{target:n.lockTarget,options:n.options}]);return}var e=0,r=((o=n.options)===null||o===void 0?void 0:o.container)||document.body;(r===document.body&&window.innerWidth-document.documentElement.clientWidth>0||r.scrollHeight>r.clientHeight)&&(e=ge());var s=r.className;if(p.filter(function(c){var l,u=c.options;return(u==null?void 0:u.container)===((l=n.options)===null||l===void 0?void 0:l.container)}).length===0&&te.set(r,P({width:e!==0?"calc(100% - ".concat(e,"px)"):void 0,overflow:"hidden",overflowX:"hidden",overflowY:"hidden"},{element:r})),!ee.test(s)){var f="".concat(s," ").concat(Ce);r.className=f.trim()}p=[].concat(ie(p),[{target:n.lockTarget,options:n.options}])}},this.unLock=function(){var o,e=p.find(function(f){var c=f.target;return c===n.lockTarget});if(p=p.filter(function(f){var c=f.target;return c!==n.lockTarget}),!(!e||p.some(function(f){var c,l=f.options;return(l==null?void 0:l.container)===((c=e.options)===null||c===void 0?void 0:c.container)}))){var r=((o=n.options)===null||o===void 0?void 0:o.container)||document.body,s=r.className;!ee.test(s)||(P(te.get(r),{element:r}),te.delete(r),r.className=r.className.replace(ee,"").trim())}},this.lockTarget=Te++,this.options=i}),C=0,W=we(),Y={},T=function(i){if(!W)return null;if(i){if(typeof i=="string")return document.querySelectorAll(i)[0];if(typeof i=="function")return i();if(Ee(i)==="object"&&i instanceof window.HTMLElement)return i}return document.body},Le=function(t){xe(n,t);var i=Ne(n);function n(o){var e;return pe(this,n),e=i.call(this,o),e.container=void 0,e.componentRef=a.exports.createRef(),e.rafId=void 0,e.scrollLocker=void 0,e.renderComponent=void 0,e.updateScrollLocker=function(r){var s=r||{},f=s.visible,c=e.props,l=c.getContainer,u=c.visible;u&&u!==f&&W&&T(l)!==e.scrollLocker.getContainer()&&e.scrollLocker.reLock({container:T(l)})},e.updateOpenCount=function(r){var s=r||{},f=s.visible,c=s.getContainer,l=e.props,u=l.visible,m=l.getContainer;u!==f&&W&&T(m)===document.body&&(u&&!f?C+=1:r&&(C-=1));var g=typeof m=="function"&&typeof c=="function";(g?m.toString()!==c.toString():m!==c)&&e.removeCurrentContainer()},e.attachToParent=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(r||e.container&&!e.container.parentNode){var s=T(e.props.getContainer);return s?(s.appendChild(e.container),!0):!1}return!0},e.getContainer=function(){return W?(e.container||(e.container=document.createElement("div"),e.attachToParent(!0)),e.setWrapperClassName(),e.container):null},e.setWrapperClassName=function(){var r=e.props.wrapperClassName;e.container&&r&&r!==e.container.className&&(e.container.className=r)},e.removeCurrentContainer=function(){var r,s;(r=e.container)===null||r===void 0||(s=r.parentNode)===null||s===void 0||s.removeChild(e.container)},e.switchScrollingEffect=function(){C===1&&!Object.keys(Y).length?(ce(),Y=P({overflow:"hidden",overflowX:"hidden",overflowY:"hidden"})):C||(P(Y),Y={},ce(!0))},e.scrollLocker=new Pe({container:T(o.getContainer)}),e}return ve(n,[{key:"componentDidMount",value:function(){var e=this;this.updateOpenCount(),this.attachToParent()||(this.rafId=ae(function(){e.forceUpdate()}))}},{key:"componentDidUpdate",value:function(e){this.updateOpenCount(e),this.updateScrollLocker(e),this.setWrapperClassName(),this.attachToParent()}},{key:"componentWillUnmount",value:function(){var e=this.props,r=e.visible,s=e.getContainer;W&&T(s)===document.body&&(C=r&&C?C-1:C),this.removeCurrentContainer(),ae.cancel(this.rafId)}},{key:"render",value:function(){var e=this.props,r=e.children,s=e.forceRender,f=e.visible,c=null,l={getOpenCount:function(){return C},getContainer:this.getContainer,switchScrollingEffect:this.switchScrollingEffect,scrollLocker:this.scrollLocker};return(s||f||this.componentRef.current)&&(c=a.exports.createElement(Se,{getContainer:this.getContainer,ref:this.componentRef},r(l))),c}}]),n}(a.exports.Component);function Oe(t){var i=t.prefixCls,n=t.style,o=t.visible,e=t.maskProps,r=t.motionName;return a.exports.createElement(he,{key:"mask",visible:o,motionName:r,leavedClassName:"".concat(i,"-mask-hidden")},function(s){var f=s.className,c=s.style;return a.exports.createElement("div",E({style:y(y({},c),n),className:ne("".concat(i,"-mask"),f)},e))})}function le(t,i,n){var o=i;return!o&&n&&(o="".concat(t,"-").concat(n)),o}var ue=-1;function De(){return ue+=1,ue}function fe(t,i){var n=t["page".concat(i?"Y":"X","Offset")],o="scroll".concat(i?"Top":"Left");if(typeof n!="number"){var e=t.document;n=e.documentElement[o],typeof n!="number"&&(n=e.body[o])}return n}function Ie(t){var i=t.getBoundingClientRect(),n={left:i.left,top:i.top},o=t.ownerDocument,e=o.defaultView||o.parentWindow;return n.left+=fe(e),n.top+=fe(e,!0),n}var Me=a.exports.memo(function(t){var i=t.children;return i},function(t,i){var n=i.shouldUpdate;return!n}),de={width:0,height:0,overflow:"hidden",outline:"none"},ye=a.exports.forwardRef(function(t,i){var n=t.closable,o=t.prefixCls,e=t.width,r=t.height,s=t.footer,f=t.title,c=t.closeIcon,l=t.style,u=t.className,m=t.visible,g=t.forceRender,V=t.bodyStyle,q=t.bodyProps,L=t.children,O=t.destroyOnClose,z=t.modalRender,F=t.motionName,H=t.ariaId,G=t.onClose,K=t.onVisibleChanged,B=t.onMouseDown,J=t.onMouseUp,D=t.mousePosition,b=a.exports.useRef(),I=a.exports.useRef(),j=a.exports.useRef();a.exports.useImperativeHandle(i,function(){return{focus:function(){var d;(d=b.current)===null||d===void 0||d.focus()},changeActive:function(d){var w=document,_=w.activeElement;d&&_===I.current?b.current.focus():!d&&_===b.current&&I.current.focus()}}});var Q=a.exports.useState(),k=oe(Q,2),S=k[0],M=k[1],R={};e!==void 0&&(R.width=e),r!==void 0&&(R.height=r),S&&(R.transformOrigin=S);function A(){var h=Ie(j.current);M(D?"".concat(D.x-h.left,"px ").concat(D.y-h.top,"px"):"")}var x;s&&(x=a.exports.createElement("div",{className:"".concat(o,"-footer")},s));var U;f&&(U=a.exports.createElement("div",{className:"".concat(o,"-header")},a.exports.createElement("div",{className:"".concat(o,"-title"),id:H},f)));var N;n&&(N=a.exports.createElement("button",{type:"button",onClick:G,"aria-label":"Close",className:"".concat(o,"-close")},c||a.exports.createElement("span",{className:"".concat(o,"-close-x")})));var X=a.exports.createElement("div",{className:"".concat(o,"-content")},N,U,a.exports.createElement("div",E({className:"".concat(o,"-body"),style:V},q),L),x);return a.exports.createElement(he,{visible:m,onVisibleChanged:K,onAppearPrepare:A,onEnterPrepare:A,forceRender:g,motionName:F,removeOnLeave:O,ref:j},function(h,d){var w=h.className,_=h.style;return a.exports.createElement("div",{key:"dialog-element",role:"document",ref:d,style:y(y(y({},_),l),R),className:ne(o,u,w),onMouseDown:B,onMouseUp:J},a.exports.createElement("div",{tabIndex:0,ref:b,style:de,"aria-hidden":"true"}),a.exports.createElement(Me,{shouldUpdate:m||g},z?z(X):X),a.exports.createElement("div",{tabIndex:0,ref:I,style:de,"aria-hidden":"true"}))})});ye.displayName="Content";function me(t){var i=t.prefixCls,n=i===void 0?"rc-dialog":i,o=t.zIndex,e=t.visible,r=e===void 0?!1:e,s=t.keyboard,f=s===void 0?!0:s,c=t.focusTriggerAfterClose,l=c===void 0?!0:c,u=t.scrollLocker,m=t.title,g=t.wrapStyle,V=t.wrapClassName,q=t.wrapProps,L=t.onClose,O=t.afterClose,z=t.transitionName,F=t.animation,H=t.closable,G=H===void 0?!0:H,K=t.mask,B=K===void 0?!0:K,J=t.maskTransitionName,D=t.maskAnimation,b=t.maskClosable,I=b===void 0?!0:b,j=t.maskStyle,Q=t.maskProps,k=a.exports.useRef(),S=a.exports.useRef(),M=a.exports.useRef(),R=a.exports.useState(r),A=oe(R,2),x=A[0],U=A[1],N=a.exports.useRef();N.current||(N.current="rcDialogTitle".concat(De()));function X(v){if(v){if(!Re(S.current,document.activeElement)){var $;k.current=document.activeElement,($=M.current)===null||$===void 0||$.focus()}}else{if(U(!1),B&&k.current&&l){try{k.current.focus({preventScroll:!0})}catch{}k.current=null}x&&(O==null||O())}}function h(v){L==null||L(v)}var d=a.exports.useRef(!1),w=a.exports.useRef(),_=function(){clearTimeout(w.current),d.current=!0},be=function(){w.current=setTimeout(function(){d.current=!1})},re=null;I&&(re=function($){d.current?d.current=!1:S.current===$.target&&h($)});function ke(v){if(f&&v.keyCode===se.ESC){v.stopPropagation(),h(v);return}r&&v.keyCode===se.TAB&&M.current.changeActive(!v.shiftKey)}return a.exports.useEffect(function(){return r&&U(!0),function(){}},[r]),a.exports.useEffect(function(){return function(){clearTimeout(w.current)}},[]),a.exports.useEffect(function(){return x?(u==null||u.lock(),u==null?void 0:u.unLock):function(){}},[x,u]),a.exports.createElement("div",E({className:"".concat(n,"-root")},_e(t,{data:!0})),a.exports.createElement(Oe,{prefixCls:n,visible:B&&r,motionName:le(n,J,D),style:y({zIndex:o},j),maskProps:Q}),a.exports.createElement("div",E({tabIndex:-1,onKeyDown:ke,className:ne("".concat(n,"-wrap"),V),ref:S,onClick:re,role:"dialog","aria-labelledby":m?N.current:null,style:y(y({zIndex:o},g),{},{display:x?null:"none"})},q),a.exports.createElement(ye,E({},t,{onMouseDown:_,onMouseUp:be,ref:M,closable:G,ariaId:N.current,prefixCls:n,visible:r,onClose:h,onVisibleChanged:X,motionName:le(n,z,F)}))))}var Ae=function(i){var n=i.visible,o=i.getContainer,e=i.forceRender,r=i.destroyOnClose,s=r===void 0?!1:r,f=i.afterClose,c=a.exports.useState(n),l=oe(c,2),u=l[0],m=l[1];return a.exports.useEffect(function(){n&&m(!0)},[n]),o===!1?a.exports.createElement(me,E({},i,{getOpenCount:function(){return 2}})):!e&&s&&!u?null:a.exports.createElement(Le,{visible:n,forceRender:e,getContainer:o},function(g){return a.exports.createElement(me,E({},i,{destroyOnClose:s,afterClose:function(){f==null||f(),m(!1)}},g))})};Ae.displayName="Dialog";export{Ae as D};
