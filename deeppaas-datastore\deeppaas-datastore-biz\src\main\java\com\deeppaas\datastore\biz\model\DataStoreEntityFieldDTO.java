package com.deeppaas.datastore.biz.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
@Data
public class DataStoreEntityFieldDTO {
    private String id;

    /**
     * 所属模型
     **/
    private String entityCode;
    /**
     * 所属表
     **/
    private String tableId;

    /**
     * 字段编号
     **/
    private String code;

    /**
     * 字段名称
     **/
    private String name;
    /**
     * 数据库列名
     */
    private String columnName;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 字段长度(字符串类型为总长度，数字类型为包含小数点前后的总位数)
     */
    private Integer length;

    /**
     * 小数点后多少位，除数字其他类型均为0
     */
    private Integer decimalDigits;

    /**
     * 是否是主键
     */
    private Integer idKey;

    /**
     * 是否唯一
     */
    private Integer unique;

    /**
     * 是否是数据库生成的数据
     */
    private Integer dbGenerated;

    /**
     * 页面表单控件类型
     */
    private String webType;

    /**
     * 是否是读虚列
     */
    private Integer readOnly;
    /**
     * 是否是鉴别器字段
     */
    private Integer discriminator;
    /**
     * 鉴别器鉴别值
     */
    private String discriminatorValue;
    /**
     * 所属关联
     */
    private String joinCode;
    /**
     * 创建时间
     **/
    private LocalDateTime createTime;
}
