package com.deeppaas.common.web.context;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.JsonHelper;
import com.fasterxml.jackson.databind.JsonNode;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * 当前POST请求提参数上线文
 * <AUTHOR>
 * @date 2016年8月26日
 */
public class BodyParamContext {
	private static ThreadLocal<JsonNode> localJsonNode = new ThreadLocal<JsonNode>();
	
	/**
	 * 从当前线程中获取已参数
	 * @return
	 * <AUTHOR>
	 * @date 2016年8月26日
	 */
	public static JsonNode getJsonParam(){
		return localJsonNode.get();
	}
	
	/**
	 * 向当前线程中放入参数
	 * @param jsonNode
	 * <AUTHOR>
	 * @date 2016年8月26日
	 */
	public static void setJsonParam(JsonNode jsonNode) {
		localJsonNode.set(jsonNode);
	}
	
	/**
	 * 清除当前线程参数 
	 * <AUTHOR>
	 * @date 2016年8月26日
	 */
	public static void remove() {
		localJsonNode.remove();
	}


	public static void loadJsonParam(HttpServletRequest request){
		if(localJsonNode.get()==null){
			try {
				request.setCharacterEncoding("UTF-8");
			} catch (UnsupportedEncodingException e) {
				throw new RuntimeException(e);
			}
			try{
				JsonNode jsonNode = JsonHelper.readTree(request.getInputStream());
				BodyParamContext.setJsonParam(jsonNode);
			}catch(Exception e){
				throw new RuntimeException("@RequestBoyd或@Body参数必须为标准JSON");
			}
		} else {
			throw RunException.error("local json params has been loaded");
		}
	}

	public static Map<String, Object> getBodyParameterMap() {
		JsonNode bodyNode = getJsonParam();
		if(bodyNode==null){
			throw RunException.error("没有载入当前body信息");
		}
		try {
			Map<String, Object> map = JsonHelper.readValue(bodyNode, Map.class);
			return map;
		} catch (Exception e) {
			throw RunException.error("获取参数Map失败，e="+e.getMessage());
		}
    }
}
