package com.deeppaas.license.single;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.license.single.model.SingleLicense;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/2
 */
public class SingleLicenseHelper {
    private static final String ALGORITHM_RSA = "RSA";
    private static final String ALGORITHM_MD5_RSA = "MD5withRSA";
    private static final String ALGORITHM_AES = "AES";
    private static final String ALGORITHM_CIPHER_AES = "AES/CBC/PKCS5Padding";
    private static final String LICENSE_FILE_NAME = "license.lic";

    public static SingleLicense getLocalLicense(){
        ApplicationHome h = new ApplicationHome(new SingleLicenseHelper().getClass());
        File jarDir = h.getDir();
        String clientSource = ClientMonitor.getClientSource();
        File licenseFile = new File(jarDir.getPath() + File.separator + LICENSE_FILE_NAME);
        if(licenseFile.exists()){
            try {
                byte[] licenseData = FileCopyUtils.copyToByteArray(licenseFile);
                byte[] key = clientSource.substring(0,16).getBytes(Charset.forName("UTF-8"));
                byte[] iv = clientSource.substring(16,32).getBytes(Charset.forName("UTF-8"));
                String licenseJson = decrypt(licenseData, key, iv);
                SingleLicense context = JsonHelper.fromJson(licenseJson, SingleLicense.class);
                return context;
            } catch (IOException e) {
                return null;
            }
        }
        return null;
    }
    public static SingleLicense getLicense(byte[] licenseData){
        String clientSource = ClientMonitor.getClientSource();
        try {
            byte[] key = clientSource.substring(0,16).getBytes(Charset.forName("UTF-8"));
            byte[] iv = clientSource.substring(16,32).getBytes(Charset.forName("UTF-8"));
            String licenseJson = decrypt(licenseData, key, iv);
            SingleLicense context = JsonHelper.fromJson(licenseJson, SingleLicense.class);
            return context;
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean localCheck() {
        return check(getLocalLicense());
    }

    public static boolean check(SingleLicense context){
        if(context==null){
            return false;
        }
        if(StringUtils.hasText(context.getExpiration())){
            Date expireDate = DateHelper.str2date(context.getExpiration(), DateHelper.DATE_PATTERN);
            if(expireDate.before(new Date())){
                return false;
            }
        }
        String plainText = ClientMonitor.getClientSource();
        String pubKeyText = context.getPublicKey();
        String signText = context.getSign();
        try {
            // 解密由base64编码的公钥,并构造X509EncodedKeySpec对象
            X509EncodedKeySpec bobPubKeySpec = new X509EncodedKeySpec(Base64.getUrlDecoder().decode(pubKeyText));
            // RSA 对称加密算法
            KeyFactory factory = KeyFactory.getInstance(ALGORITHM_RSA);
            // 取出公钥对象
            PublicKey pubKey = factory.generatePublic(bobPubKeySpec);
            // 解密base64 编码的数字签名
            byte[] signed = Base64.getUrlDecoder().decode(signText);
            Signature signature = Signature.getInstance(ALGORITHM_MD5_RSA);
            signature.initVerify(pubKey);
            signature.update(plainText.getBytes());
            // 验证签名是否正常
            if(signature.verify(signed)){

                return true;
            }else{
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 解密
     * @param licenseData
     * @param key
     * @param iv
     * @return
     */
    public static String decrypt(byte[] licenseData, byte[] key, byte[] iv){
        String result = null;
        try {
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            Key keySpec = new SecretKeySpec(key, ALGORITHM_AES);
            Cipher cipher = Cipher.getInstance(ALGORITHM_CIPHER_AES);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);               //与加密时不同MODE:Cipher.DECRYPT_MODE
            byte[] ret = cipher.doFinal(licenseData);
            result = new String(ret, "utf-8");
        }catch(Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static void updateLicense(byte[] licenseData) {
        ApplicationHome home = new ApplicationHome(new SingleLicense().getClass());
        File jarDir = home.getDir();
        File licenseFile = new File(jarDir.getPath() + File.separator + LICENSE_FILE_NAME);
        try {
            FileCopyUtils.copy(licenseData, licenseFile);
        } catch (IOException e) {
            throw RunException.error(e.getMessage());
        }
    }
}
