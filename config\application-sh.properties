dp.datasource.db-type=MYSQL
spring.datasource.jdbc-url=************************************************************************************************
spring.datasource.username=nocode
spring.datasource.password=nocode123456
spring.datasource.hikari.connectionInitSql=SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
kubao.ai.server=http://127.0.0.1:7999



#\u6587\u4EF6\u7CFB\u7EDF
dp.file.groups=
##flyway
spring.flyway.locations= db/commondb,db/mysqldb
