package com.deeppaas.process.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Data
@Table("aidos_process")
public class AidosProcessDO  extends BaseUuidDO {

    /**
     * 事项名称
     */
    @Column("name")
    private String name;
    /**
     * 事项别名
     */
    @Column("alias")
    private String alias;

    /**
     * 上传人
     * */
    @Column("upload_user_id")
    private Integer uploadUserId;

    /**
     * 上传人
     * */
    @Column("upload_user_name")
    private String uploadUserName;
}
