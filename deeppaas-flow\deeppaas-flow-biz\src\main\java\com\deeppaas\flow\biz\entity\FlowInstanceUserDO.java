package com.deeppaas.flow.biz.entity;

import com.deeppaas.common.model.BaseModel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.util.List;

/**
 * 流程实例相关用户，查询用
 * <AUTHOR>
 * @date 2022/9/7
 */
@Data
@Table("flow_instance_user")
public class FlowInstanceUserDO implements BaseModel {
    @Id
    @Column("instance_id")
    private Long instanceId;
    /**
     * 代办人
     */
    @Column("todo_users")
    private List<String> todoUsers;

    /**
     * 已办人
     */
    @Column("done_users")
    private List<String> doneUsers;

}
