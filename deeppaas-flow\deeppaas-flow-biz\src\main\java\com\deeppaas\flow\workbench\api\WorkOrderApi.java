package com.deeppaas.flow.workbench.api;

import com.deeppaas.account.api.client.context.LocalUserContext;
import com.deeppaas.common.model.PageData;
import com.deeppaas.common.model.PageSearch;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.common.web.annontation.BodyParam;
import com.deeppaas.common.web.annontation.RequestAuth;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.flow.biz.ExecuteResult;
import com.deeppaas.flow.biz.model.PastNodeInfo;
import com.deeppaas.flow.biz.model.vo.FlowRecordVO;
import com.deeppaas.flow.workbench.model.*;
import com.deeppaas.flow.workbench.service.WorkOrderService;
import com.deeppaas.flow.workbench.service.WorkTaskService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工单接口
 *
 * <AUTHOR>
 * @date 2022/3/10
 */
@RestController
@RequestMapping("/api/flow/order")
@RequestAuth
public class WorkOrderApi {
    private final WorkOrderService workOrderService;
    private final WorkTaskService workTaskService;

    public WorkOrderApi(WorkOrderService workOrderService, WorkTaskService workTaskService) {
        this.workOrderService = workOrderService;
        this.workTaskService = workTaskService;
    }

    /**
     * 预提交工单
     */
    @PostMapping("/submitCheck")
    public RestModel<ExecuteResult> mockSubmit(@RequestBody WorkOrderParam workOrderParam) {
        String userCode = LocalUserContext.getCurUserCode();
        ExecuteResult result = workOrderService.mockSubmit(workOrderParam, userCode);
        return RestModel.data(result);
    }

    /**
     * 表单提交
     */
    @PostMapping("/submit")
    public RestModel<ExecuteResult> submit(@BodyParam String flowDefineCode,
                                           @BodyParam String startNode,
                                           @BodyParam String orderId) {
        String userCode = LocalUserContext.getCurUserCode();
        ExecuteResult result = workTaskService.submit(flowDefineCode, startNode, orderId, userCode);
        return RestModel.data(result);
    }

    /**
     * 指派审批人并流转工单
     *
     * @param assignParams 如果下个环节是分支节点，那么就有多个指派
     * @return 操作结果
     */
    @PostMapping("/assignSubmit")
    public RestModel<ExecuteResult> assignSubmit(@RequestBody AssignParam[] assignParams) {
        String userCode = LocalUserContext.getCurUserCode();
        ExecuteResult result = workTaskService.assignSubmit(assignParams, userCode);
        return RestModel.data(result);
    }

    /**
     * 获取工单信息
     *
     * @param defineCode 流程定义编号
     * @param orderId    工单ID
     * @return 工单信息
     */
    @GetMapping("/get")
    public RestModel<WorkOrder> get(String defineCode, String orderId) {
        String userCode = LocalUserContext.getCurUserCode();
        WorkOrder workOrder = workOrderService.getWorkOrder(defineCode, orderId);
        return RestModel.data(workOrder);
    }

    /**
     * 分页查询工单列表
     *
     * @param workOrderSearch 具体参数为pageNo=1&pageSize=10&tableId=203&code=123&creatorId=111&showDeptId=101&startTime=12345678901234
     *                        &endTime=2234567890123&nodeName=部门经理审批&status=1&createTime=32345678901234
     *                        &f123_EQ=111&f223_LIKE=111
     *                        其中f123_EQ为配置字段参数
     */
    @GetMapping("/page")
    public RestModel<PageData<WorkOrder>> page(WorkOrderSearch workOrderSearch) {
        String userCode = LocalUserContext.getCurUserCode();
        PageData<WorkOrder> pageInfo = workOrderService.findByPage(workOrderSearch, userCode);
        return RestModel.data(pageInfo);
    }

    /**
     * 保存工单信息
     *
     * @param workOrderParam 工单参数
     * @return 工单信息
     */
    @PostMapping("/save")
    public RestModel<DataStoreData> save(@RequestBody WorkOrderParam workOrderParam) {
        String userCode = LocalUserContext.getCurUserCode();
        DataStoreData o = workOrderService.saveOrder(workOrderParam, userCode);
        return RestModel.data(o);
    }

    /**
     * 删除数据
     *
     * @param entity  模型编号
     * @param orderId 工单ID
     * @return 操作结果
     */
    @PostMapping("/remove")
    public RestModel<String> remove(@BodyParam String entity, @BodyParam String orderId) {
        String userCode = LocalUserContext.getCurUserCode();
        workOrderService.removeOrderAndFlowInstance(entity, orderId, userCode);
        return RestModel.success("删除成功");

    }

    /**
     * 审批检查，是否需要指派，是否需要必填审批意见
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PostMapping("/auditCheck")
    public RestModel<ExecuteResult> auditCheck(@BodyParam Long taskId) {
        String userCode = LocalUserContext.getCurUserCode();
        return RestModel.data(workTaskService.auditCheck(taskId, userCode));
    }

    /**
     * 审批
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PostMapping("/audit")
    public RestModel<ExecuteResult> audit(@BodyParam Long taskId, @BodyParam String comment) {
        String userCode = LocalUserContext.getCurUserCode();
        return RestModel.data(workTaskService.audit(taskId, userCode, comment));
    }

    /**
     * 指派环节处理人并审核通过任务
     *
     * @param assignParams 指派信息列表
     * @param taskId       任务ID
     * @param comment      审核信息
     * @return 操作结果
     */
    @PostMapping("/assignAudit")
    public RestModel<ExecuteResult> assignAudit(@BodyParam AssignParam[] assignParams,
                                                @BodyParam Long taskId, @BodyParam String comment) {
        String userCode = LocalUserContext.getCurUserCode();
        ExecuteResult executeResult = workTaskService.assignAudit(taskId, comment, assignParams, userCode);
        return RestModel.data(executeResult);
    }

    /**
     * 获取驳回环节
     *
     * @param taskId 任务ID
     * @return 可驳回的环节列表
     */
    @GetMapping("/backTarget")
    public RestModel<List<PastNodeInfo>> backTarget(Long taskId) {
        String userCode = LocalUserContext.getCurUserCode();
        List<PastNodeInfo> backNodes = workTaskService.getBackInfo(taskId, userCode);
        return RestModel.data(backNodes);
    }

    /**
     * 驳回处理任务
     *
     * @param taskId       任务ID
     * @param backNodeCode 目标环节编号
     * @param comment      评论
     * @return 操作结果
     */
    @PostMapping("/back")
    public RestModel<ExecuteResult> back(@BodyParam Long taskId,
                                         @BodyParam String backNodeCode,
                                         @BodyParam String comment) {
        String userCode = LocalUserContext.getCurUserCode();
        ExecuteResult executeResult = workTaskService.back(taskId, backNodeCode, comment, userCode);
        return RestModel.data(executeResult);
    }

    /**
     * 可撤回环节
     *
     * @param taskId 任务ID
     * @return 环节列表
     */
    @GetMapping("/takeBackCheck")
    public RestModel<List<PastNodeInfo>> takeBackCheck(Long taskId) {
        String userCode = LocalUserContext.getCurUserCode();
        return RestModel.data(workTaskService.takeBackCheck(taskId, userCode));
    }

    /**
     * 撤回
     *
     * @param instanceId 实例ID
     * @param nodeCode   环节编号
     * @param comment    评论
     * @return 操作结果
     */
    @PostMapping("/takeBack")
    public RestModel<ExecuteResult> takeBack(@BodyParam Long instanceId, @BodyParam String nodeCode,
                                             @BodyParam(required = false) String comment) {
        String userCode = LocalUserContext.getCurUserCode();
        if (comment == null) {
            comment = "";
        }
        return RestModel.data(workTaskService.takeBack(instanceId, nodeCode, userCode, comment));
    }

    /**
     * 撤销流程
     *
     * @param instanceId 实例ID
     * @param comment    评论
     * @return 操作结果
     */
    @PostMapping("/cancel")
    public RestModel<String> cancel(@BodyParam Long instanceId, @BodyParam String comment) {
        String userCode = LocalUserContext.getCurUserCode();
        workTaskService.cancel(instanceId, userCode, comment);
        return RestModel.success("撤销成功");
    }

    /**
     * 中止检查
     *
     * @param instanceId 实例ID
     * @return 操作结果
     */
    @PostMapping("/stopCheck")
    public RestModel<Boolean> stopCheck(@BodyParam Long instanceId) {
        String userCode = LocalUserContext.getCurUserCode();
        Boolean result = workTaskService.stopCheck(instanceId, userCode);
        return RestModel.data(result);
    }

    /**
     * 中止流程
     *
     * @param instanceId 实例ID
     * @param comment    评论
     * @return 操作结果
     */
    @PostMapping("/stop")
    public RestModel<?> stop(@BodyParam Long instanceId, @BodyParam String comment) {
        String userCode = LocalUserContext.getCurUserCode();
        workTaskService.stop(instanceId, userCode, comment);
        return RestModel.success("中止成功");
    }

    /**
     * 获取环节对应页面
     *
     * @param defineCode 流程定义编号
     * @param node       流程定义环节ID
     * @return 环节对应页面
     */
    @GetMapping("/nodePage")
    public RestModel<String> nodePage(@RequestParam String defineCode, @RequestParam String node) {
        String pageId = workTaskService.getFlowNodePage(defineCode, node);
        return RestModel.data(pageId);
    }


    /**
     * 当前工单待处理任务列表
     *
     * @param defineCode 流程定义编号
     * @param orderId    工单ID
     * @return 待处理任务列表
     */
    @GetMapping("/tasks/open")
    public RestModel<List<WorkTask>> openTask(@RequestParam String defineCode, @RequestParam String orderId) {
        String userCode = LocalUserContext.getCurUserCode();
        List<WorkTask> workTaskList = workTaskService.openTask(defineCode, orderId, userCode);
        return RestModel.data(workTaskList);
    }

    /**
     * 分页查询任务列表
     */
    @GetMapping("/tasks/all")
    public RestModel<PageData<WorkTask>> allTask(@RequestParam String defineCode, PageSearch pageSearch) {
        String curUserCode = LocalUserContext.getCurUserCode();
        return RestModel.data(workTaskService.allTask(pageSearch, defineCode, curUserCode));
    }

    /**
     * 工单的流程操作记录
     *
     * @param defineCode 流程定义编号
     * @param orderId    工单ID
     * @return 流程操作记录列表
     */
    @GetMapping("/records")
    public RestModel<List<FlowRecordVO>> listFLowRecord(@RequestParam String defineCode, @RequestParam String orderId) {
        List<FlowRecordVO> flowRecordVOList = workTaskService.findFlowRecord(defineCode, orderId);
        return RestModel.data(flowRecordVOList);
    }
}
