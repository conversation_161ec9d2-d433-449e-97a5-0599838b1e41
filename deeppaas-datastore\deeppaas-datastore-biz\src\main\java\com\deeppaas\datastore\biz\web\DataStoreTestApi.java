//package com.deeppaas.datastore.biz.web;
//
//import com.deeppaas.common.model.RestModel;
//import com.deeppaas.common.web.annontation.RequestAuth;
//import com.deeppaas.datastore.biz.entity.DataStoreTableDO;
//import com.deeppaas.datastore.biz.entity.DataStoreTableFieldDO;
//import com.deeppaas.datastore.biz.service.DataStoreDBService;
//import com.deeppaas.datastore.biz.service.DataStoreTableService;
//import com.deeppaas.datastore.common.emnus.ColumnDataType;
//import com.deeppaas.datastore.common.mapping.DBColumn;
//import com.deeppaas.datastore.common.mapping.DBTable;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 数据仓库测试API
// *
// * <AUTHOR>
// * @date 2022/03/05
// */
//@RestController
//@RequestMapping("/api/test")
//@RequestAuth(auth = false)
//public class DataStoreTestApi {
//    private final DataStoreTableService dataStoreTableService;
//    private final DataStoreDBService dataStoreDBService;
//
//    public DataStoreTestApi(DataStoreTableService dataStoreTableService, DataStoreDBService dataStoreDBService) {
//        this.dataStoreTableService = dataStoreTableService;
//        this.dataStoreDBService = dataStoreDBService;
//    }
//
//    /**
//     * 测试1
//     */
//    @GetMapping("/test1")
//    public RestModel<List<DBColumn>> test1(String sql) {
//        List<DBColumn> list = dataStoreDBService.sqlColumns("DEEPPAAS", sql);
//        return RestModel.data(list);
//    }
//
//    @GetMapping("/test2")
//    public RestModel<List<DBTable>> test2() {
//        List<DBTable> list = dataStoreDBService.tables("DEEPPAAS");
//        return RestModel.data(list);
//    }
//
//    @GetMapping("/test3")
//    public RestModel<List<DBColumn>> test3(String tableId) {
//        DataStoreTableDO table = dataStoreTableService.get(tableId);
//        List<DBColumn> list = dataStoreDBService.tableColumns(table.getSourceId(), table.getTableName());
//        return RestModel.data(list);
//    }
//
//    @GetMapping("/test4")
//    public RestModel<String> test4(String tableId) {
//        List<String> sqlList = new ArrayList<>();
//        List<DataStoreTableDO> tables = dataStoreTableService.list("DEEPPAAS");
//        for(DataStoreTableDO tableDO : tables){
//            List<DataStoreTableFieldDO> fieldDOS = dataStoreTableService.listField(tableDO.getId());
//            for(DataStoreTableFieldDO fieldDO : fieldDOS){
//                if(!fieldDO.getIdKey()){
//                    ColumnDataType columnDataType = ColumnDataType.valueOf(fieldDO.getDataType());
//                    String sql = "ALTER TABLE " + tableDO.getTableName() + " MODIFY COLUMN `" + fieldDO.getColumnName() +"` "
//                            + columnDataType.getDbTypeSql(fieldDO.getLength())
//                            + " GENERATED ALWAYS AS (data->>'$." + fieldDO.getCode() + "') VIRTUAL;";
//                    sqlList.add(sql);
//                }
//            }
//        }
//        System.out.println("========================================");
//        for(String sql : sqlList){
//            System.out.println(sql);
//        }
//        System.out.println("========================================");
//        return RestModel.success("");
//    }
//}
