package com.deeppaas.datastore.common.jdbc;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.UidGenerator;
import com.deeppaas.datastore.common.DataStoreException;
import com.deeppaas.datastore.common.mapping.DataStoreData;
import com.deeppaas.datastore.common.mapping.DataStoreEntity;
import com.deeppaas.datastore.common.mapping.DataStoreProperty;
import com.deeppaas.datastore.common.mapping.DataStoreSelf;
import com.deeppaas.datastore.common.utils.DataConvert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据仓库属性映射器
 * <AUTHOR>
 * @date 2022/1/19
 */
public class DataStorePropertyMapper {
    final private DataStoreEntity entity;

    DataStorePropertyMapper(DataStoreEntity entity) {
        this.entity = entity;
    }

    public static DataStorePropertyMapper of(DataStoreEntity entity) {
        return new DataStorePropertyMapper(entity);
    }

    public Map<String, Object> getStoreParam(final DataStoreData data) {
        return switch (entity.getType()){
            case SELF_BUILD -> selfTableStoreParam(data);
            case LOAD_BUILD -> loadTableStoreParam(data);
            case SQL_BUILD -> throw DataStoreException.sqlBuildStoreError();
            default -> throw DataStoreException.error("实体类型异常");
        };
    }

    private Map<String, Object> loadTableStoreParam(DataStoreData data) {
        List<DataStoreProperty> properties = entity.getProperties();
        Map<String, Object> paramMap = new HashMap<>();
        for(DataStoreProperty property : properties){
            if(!property.isVirtual()){
                paramMap.put(property.getName(), data.get(property.getName()));
            }
        }
        return paramMap;
    }

    private Map<String, Object> selfTableStoreParam(DataStoreData data) {
        Map<String, Object> dataMap = new HashMap<>();
        List<DataStoreProperty> properties = entity.getProperties();
        for(DataStoreProperty property : properties){
            if(property.getTableJoin()== null && !property.isPrimary()){
                Object propertyValue = data.get(property.getName());
                SimpleDataType propertyDataType = SimpleDataType.valueOf(property.getDataType());
                propertyValue = switch (propertyDataType){
                    case NUMBER -> DataConvert.ofNumber(propertyValue);
                    case BOOLEAN -> DataConvert.ofBool(propertyValue);
                    case DATE_TIME -> DataConvert.ofDateText(propertyValue);
                    default -> propertyValue;
                };
                if(propertyValue!=null){
                    dataMap.put(property.getName(), propertyValue);
                }
            }
        }
        Map<String, Object> paramMap = new HashMap<>();
        if(data.get(DataStoreSelf.ID_SQL_PARAMETER)==null){
            String uuid = UidGenerator.nextId();
            paramMap.put(DataStoreSelf.ID_SQL_PARAMETER, uuid);
        }else {
            paramMap.put(DataStoreSelf.ID_SQL_PARAMETER, data.get(DataStoreSelf.ID_SQL_PARAMETER));
        }
        if(data.get(DataStoreSelf.CREATE_TIME_SQL_PARAMETER)==null){
            paramMap.put(DataStoreSelf.CREATE_TIME_SQL_PARAMETER, LocalDateTime.now());
        }
        paramMap.put(DataStoreSelf.DATA_SQL_PARAMETER, JsonHelper.toJson(dataMap));
        return paramMap;
    }
}
