# 三方授权登陆
## 授权跳转

    由deeppaas平台向第三方跳转，跳转时附带临时授权令牌。

- 授权跳转GET请求：`/openApi/authorize/redirect?appId=xxx&redirectUrl=yyy`
- 参数
   - `appId`：第三方授权用户ID，从deeppaas接口管理第三方服务配置获取；
   - `redirectUrl`：要跳转的地址
- 流程：接到请求后回重定向到`redirectUrl`对应地址，并在地址中直接增加临时授权令牌参数`dp_auth_code`，如`redirectUrl`为`http://xxx.com`，那么重定向地址为`http://xxx.com?dp_auth_code=dc09036d3afb48d8b98db82decd99859`

## 第三方获取用户信息
    第三方接到请求后使用临时授权令牌调用deeppaas接口获取用户信息。
- 获取用户GET接口：`/openApi/authorize/getUserInfo?appId=xxx&authCode=yyy&sign=zzz`
- 参数
   - `appId`：第三方授权用户ID，从deeppaas接口管理第三方服务配置获取；
   - `authCode`：临时授权令牌;
   - `sign`：请求签名，`sign=md5(authCode+”&”+appSecret)`，`appSecret`从deeppaas接口管理第三方服务配置获取；
   - 返回信息：
   结构如下
```
   {
      "code":0, //返回码，0为正常，非0则为异常
      "message":"success",  //返回信息，code非0是返回异常信息
      "data":{
         "id":"b3728b1d3457427ca6fc55bff031bc02", //用户ID
         "code":"admin",   //用户编号
         "name":"管理员",   //用户名
         "usable":1,       //是否可用，1是0否
         "avatar":null,    //头像地址
         "phoneNumber":null,  //电话号码
         "email":null      //电子邮箱
      }
   }
```