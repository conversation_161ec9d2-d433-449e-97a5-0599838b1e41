package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据模型字段备选
 *
 * <AUTHOR>
 * @date 2022/2/19
 */
@Data
@Table("datastore_entity_field_option")
@EqualsAndHashCode(callSuper = true)
public class DataStoreEntityFieldOptionDO extends BaseUuidDO {
    /**
     * 数据模型编号
     **/
    @Column("entity_code")
    private String entityCode;
    /**
     * 所属字段
     **/
    @Column("field_id")
    private String fieldId;

    /**
     * 数据源ID
     **/
    @Column("source_id")
    private String sourceId;

    /**
     * 备选数据模型
     **/
    @Column("option_entity_code")
    private String optionEntityCode;
    /**
     * 备选值字段
     */
    @Column("value_field")
    private String valueField;
    /**
     * 备选显示字段
     */
    @Column("display_field")
    private String displayField;
    /**
     * 过滤条件
     */
    @Column("condition")
    private String condition;
    /**
     * 是否已修改覆盖，用于判定继承数据表情况，1为已修改，0为未修改，默认为0
     */
    @Column("overwrote")
    private Integer overwrote;
}
