package com.deeppaas.flow.biz.service;

import com.deeppaas.common.script.ScriptUtils;
import com.deeppaas.flow.biz.ExecuteItem;
import com.deeppaas.flow.biz.element.FlowNode;
import com.deeppaas.flow.biz.element.FlowEventRule;
import com.deeppaas.flow.biz.enums.FlowEvent;
import com.deeppaas.rule.api.client.RuleClient;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 流程事件服务
 * <AUTHOR>
 * @date 2022/2/21
 */
@Service
public class FlowEventService {
    private final RuleClient ruleClient;

    public FlowEventService(RuleClient ruleClient) {
        this.ruleClient = ruleClient;
    }

    /**
     * 执行事件
     * @param flowEvent
     * @param executeItem
     */
    public void fireInsEvent(FlowEvent flowEvent, final ExecuteItem executeItem){
        List<FlowEventRule> eventRules = executeItem.getDefine().getEventRules();
        for(FlowEventRule eventRule : eventRules){
            if(eventRule.getEvent()==flowEvent){
                List<String> ruleList = eventRule.getRuleIds();
                for(String ruleId : ruleList){
                    ruleClient.executeById(ruleId, executeItem.getOrderData());
                }
            }
        }
    }

    public void fireNodeEvent(FlowEvent flowEvent, final ExecuteItem executeItem){
        FlowNode flowNode = executeItem.getNode();
        List<FlowEventRule> eventRules = flowNode.getEventRules();
        for(FlowEventRule eventRule : eventRules){
            if(eventRule.getEvent()==flowEvent){
                List<String> ruleList = eventRule.getRuleIds();
                for(String ruleId : ruleList){
                    ruleClient.executeById(ruleId, executeItem.getOrderData());
                }
            }
        }
    }

    /**
     * 执行判断逻辑
     * @param condition
     * @param executeItem
     */
    public boolean decided(final String condition, final ExecuteItem executeItem){
        Map<String, Object> params = executeItem.getOrderData();
        Object decideResult = ScriptUtils.eval(condition, params);
        if(decideResult instanceof Boolean result){
            return result;
        }
        return false;
    }
}
