package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import com.deeppaas.common.data.ddl.ColumnDDL;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.sql.JDBCType;
import java.time.LocalDateTime;

/**
 * 数据模型记录实体
 *
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@Table("datastore_entity")
@EqualsAndHashCode(callSuper = true)
public class DataStoreEntityDO extends BaseUuidDO {
    /**
     * 数据源
     **/
    @Column("source_id")
    @ColumnDDL(type = JDBCType.VARCHAR, length = 32, comment = "数据源")
    private String sourceId;
    /**
     * 数据模型编号手动，全局唯一
     **/
    @Column("code")
    @ColumnDDL(type = JDBCType.VARCHAR, length = 32, comment = "编号")
    private String code;
    /**
     * 数据模型名称
     **/
    @Column("name")
    @ColumnDDL(type = JDBCType.VARCHAR, length = 50, comment = "名称")
    private String name;
    /**
     * 类型 @DataStoreEntityType
     */
    @Column("type")
    @ColumnDDL(type = JDBCType.VARCHAR, length = 32, comment = "类型")
    private String type;
    /**
     * 所属应用
     */
    @Column("app_code")
    @ColumnDDL(type = JDBCType.VARCHAR, length = 32, comment = "所属应用")
    private String appCode;

    /**
     * 数据模型对应主表ID
     **/
    @Column("table_id")
    @ColumnDDL(type = JDBCType.VARCHAR, length = 32, comment = "对应表ID")
    private String tableId;

    /**
     * 数据模型对应主表
     **/
    @Column("table_name")
    @ColumnDDL(type = JDBCType.VARCHAR, length = 50, comment = "对应表名")
    private String tableName;

    /**
     * 更新时间
     **/
    @Column("update_time")
    @ColumnDDL(type = JDBCType.DATE, comment = "更新日期")
    private LocalDateTime updateTime;

    @JsonIgnore
    public void check() {

    }
}
