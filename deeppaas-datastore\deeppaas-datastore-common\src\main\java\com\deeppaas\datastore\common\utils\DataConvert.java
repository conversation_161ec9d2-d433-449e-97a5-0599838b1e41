package com.deeppaas.datastore.common.utils;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.DateHelper;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.Temporal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
public class DataConvert {

    public static Number ofNumber(Object value){
        if(value==null){
            return null;
        }
        if(value instanceof Number number){
            return number;
        }
        if(value instanceof String strValue){
            if(StringUtils.hasText(strValue)){
                try{
                    return new BigDecimal(strValue.trim());
                }catch (Exception e){
                    throw RunException.error(value + "转化为数字异常");
                }
            }
            return null;
        }
        throw RunException.error(value + "转化为数字异常");
    }

    public static Boolean ofBool(Object value) {
        if(value==null){
            return null;
        }
        if(value instanceof Boolean b){
            return b;
        }
        if(value instanceof String strValue){
            if(StringUtils.hasText(strValue)){
                return Boolean.valueOf(strValue.trim());
            }
            return null;
        }
        throw RunException.error(value + "转化为BOOL异常");
    }

    public static String ofDateText(Object value) {
        if(value==null){
            return null;
        }
        if(value instanceof String strValue){
            if(StringUtils.hasText(strValue)){
                return strValue;
            }
            return null;
        }
        if(value instanceof Date dateValue){
            return DateHelper.date2str(dateValue, DateHelper.TIME_PATTERN);
        }
        if(value instanceof LocalDate localDate){
            return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        if(value instanceof LocalTime localTime){
            return localTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        }
        if(value instanceof LocalDateTime localDateTime){
            return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        throw RunException.error(value + "转化为日期时间异常");
    }
}
