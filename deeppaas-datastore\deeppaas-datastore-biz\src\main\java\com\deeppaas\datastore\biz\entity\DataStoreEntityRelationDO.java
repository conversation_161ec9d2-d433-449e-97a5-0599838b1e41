package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据模型关系记录
 * <AUTHOR>
 * @date 2022/4/6
 */
@Data
@Table("datastore_entity_relation")
public class DataStoreEntityRelationDO  extends BaseUuidDO {
    /**
     * 数据模型1编号
     **/
    @Column("entity1_code")
    private String entity1Code;
    /**
     * 数据模型2编号
     **/
    @Column("entity2_code")
    private String entity2Code;
}
