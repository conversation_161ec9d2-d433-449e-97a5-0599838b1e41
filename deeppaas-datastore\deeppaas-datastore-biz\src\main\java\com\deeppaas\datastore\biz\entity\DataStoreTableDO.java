package com.deeppaas.datastore.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import com.deeppaas.common.exception.ReqException;
import com.deeppaas.common.helper.StringHelper;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 数据仓库自建表记录
 *
 * <AUTHOR>
 * @date 2022/2/15
 */
@Data
@Table("datastore_table")
@EqualsAndHashCode(callSuper = true)
public class DataStoreTableDO extends BaseUuidDO {
    /**
     * 目标数据源
     **/
    @Column("source_id")
    private String sourceId;

    /**
     * 数据库表名
     **/
    @Column("table_name")
    private String tableName;
    /**
     * 表中文名
     **/
    @Column("name")
    private String name;
    /**
     * 类型 @DataStoreEntityType
     **/
    @Column("type")
    private String type;

    /**
     * 构建SQL
     **/
    @Column("build_sql")
    private String buildSql;

    /**
     * 所属文件夹（默认为ROOT）
     **/
    @Column("folder_id")
    private String folderId;

    /**
     * 文件夹内排序号
     **/
    @Column("sort_no")
    private Integer sortNo;

    @JsonIgnore
    public void check() {
        if (StringHelper.isEmpty(this.sourceId)) {
            throw ReqException.paramNotFound("数据源ID不能为空");
        }
        if (StringHelper.isEmpty(this.tableName)) {
            throw ReqException.paramNotFound("表名称不能为空");
        }
    }
}
