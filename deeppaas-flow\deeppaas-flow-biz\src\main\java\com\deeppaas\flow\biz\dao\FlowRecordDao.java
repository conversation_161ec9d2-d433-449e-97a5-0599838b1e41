package com.deeppaas.flow.biz.dao;

import java.util.List;

import com.deeppaas.common.data.jdbc.BaseJdbcDao;
import com.deeppaas.flow.biz.entity.FlowRecordDO;
import org.springframework.data.jdbc.repository.query.Modifying;
import org.springframework.data.jdbc.repository.query.Query;

public interface FlowRecordDao extends BaseJdbcDao<FlowRecordDO, Long> {

    /**
     * 更新审批历史的流程ID为最新流程ID
     *
     * @param newFlowId 新的流程ID
     * @param oldFlowId 原流程ID
     */
    @Modifying
    @Query(value = "update flow_record set flow_id = ?1 where flow_id = ?2")
    void updateFlowId(Long newFlowId, Long oldFlowId);

    List<FlowRecordDO> findByInstanceIdOrderByExecuteTime(Long instanceId);
}
