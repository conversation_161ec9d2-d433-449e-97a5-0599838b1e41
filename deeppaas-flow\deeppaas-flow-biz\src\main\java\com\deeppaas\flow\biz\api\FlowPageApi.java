package com.deeppaas.flow.biz.api;

import com.deeppaas.common.model.RestModel;
import com.deeppaas.flow.biz.entity.FlowDefineDO;
import com.deeppaas.flow.biz.entity.FlowPageDO;
import com.deeppaas.flow.biz.model.vo.FlowPageVO;
import com.deeppaas.flow.biz.service.FlowDefineService;
import com.deeppaas.flow.biz.service.FlowPageService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程页面相关接口
 * <AUTHOR>
 * @date 2022/5/17
 */
@RestController
@RequestMapping("/api/flow/page")
public class FlowPageApi {
    private final FlowPageService flowPageService;
    private final FlowDefineService flowDefineService;

    public FlowPageApi(FlowPageService flowPageService, FlowDefineService flowDefineService) {
        this.flowPageService = flowPageService;
        this.flowDefineService = flowDefineService;
    }

    /**
     * 根据ID获取流程页面设置
     * @param id
     * @return
     */
    @GetMapping("/getById")
    public RestModel<FlowPageDO> getById(String id){
        FlowPageDO flowPageDO = flowPageService.findById(id);
        return RestModel.data(flowPageDO);
    }

    /**
     * 获取指定流程环节页面设置
     * @param defineId
     * @param node
     * @return
     */
    @GetMapping("/get")
    public RestModel<FlowPageDO> get(String defineId, String node){
        FlowPageDO flowPageDO = flowPageService.findByNode(defineId, node);
        return RestModel.data(flowPageDO);
    }

    /**
     * 获取页面组件对应的流程环节页面设置
     * @param page
     * @return
     */
    @GetMapping("/list")
    public RestModel<List<FlowPageVO>> list(String page){
        List<FlowPageDO> flowPageDOS = flowPageService.findByPageId(page);
        List<FlowPageVO> flowPageVOS = buildVOS(flowPageDOS);
        return RestModel.data(flowPageVOS);
    }

    private List<FlowPageVO> buildVOS(List<FlowPageDO> flowPageList) {
        Set<String> flowDefineIds = flowPageList.stream().map(FlowPageDO::getDefineId).collect(Collectors.toSet());
        List<FlowDefineDO> flowDefineDOS = flowDefineService.findByIds(flowDefineIds);
        Map<String, FlowDefineDO> flowDefineMap = flowDefineDOS.stream().collect(Collectors.toMap(FlowDefineDO::getId, Function.identity()));
        List<FlowPageVO> vos = flowPageList.stream().map(flowPage -> {
            FlowPageVO vo = new FlowPageVO();
            vo.setFlowDefine(flowDefineMap.get(flowPage.getDefineId()));
            vo.setNodeId(flowPage.getNodeId());
            vo.setNodeName(flowPage.getNodeName());
            vo.setPageId(flowPage.getPageId());
            return vo;
        }).toList();
        return vos;
    }
}
