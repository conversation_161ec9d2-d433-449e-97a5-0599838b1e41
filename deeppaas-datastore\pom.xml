<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>

            </plugin>

        </plugins>
    </build>

    <parent>
        <artifactId>deeppaas2</artifactId>
        <groupId>com.deeppaas</groupId>
        <version>2.0</version>
    </parent>

    <artifactId>deeppaas-datastore</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <modules>
        <module>deeppaas-datastore-common</module>
        <module>deeppaas-datastore-event</module>
        <module>deeppaas-datastore-api</module>
        <module>deeppaas-datastore-biz</module>
        <module>deeppaas-datastore-neo4j</module>
    </modules>
</project>