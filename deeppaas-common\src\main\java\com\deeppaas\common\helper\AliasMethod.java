package com.deeppaas.common.helper;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.List;
import java.util.Random;

/**
 * 抽奖别名算法工具
 * 注意该实现只支持概率集合相加等于1的情况，
 * 该算法假设概率集合大小为N，视每个概率为一个颜色柱子，算法将长柱子截断成短柱子，与本来就是短柱子的概率柱子组成N个长短相同的柱子
 *         |   |
 *  ___    |   |
 * |   |   |   |				 _______________
 * |0.3|___|0.4|				|   |_A_|	|	|
 * |   |   |   |		====>   |   |	|	|.15|
 * |   |0.2|   |___				|.25|0.2|.25|_C_|
 * |   |   |   |0.1|			|   |   |	|0.1|
 * |_A_|_B_|_C_|_D_|			|_A_|_B_|_C_|_D_|
 * 
 * <AUTHOR>
 * @date 2016年7月19日
 */
public final class AliasMethod {
    /** 简单随机. */
    private final Random random;
    /** 别名组 */
    private final int[] alias;
    /** 概率组 */
    private final double[] probability;
    /** 概率组长度 */
    private final int length; 
 
    /**
     * 构造
     * <AUTHOR>
     * @date 2016年7月19日
     */
    public AliasMethod(List<Double> probabilities) {
        this(probabilities, new Random());
    }
 
    /**
     * 构造
     * <AUTHOR>
     * @date 2016年7月19日
     */
    public AliasMethod(List<Double> prob, Random rand){
        if (prob == null || rand == null) {
            throw new NullPointerException();
        }
        if (prob.size() == 0) {
            throw new IllegalArgumentException("Probability vector must be nonempty.");
        }
          
        this.random = rand;  
        this.length = prob.size();  
        this.probability = new double[length];  
        this.alias = new int[length];        
 
        double[] probtemp = new double[length];  
        
        Deque<Integer> small = new ArrayDeque<Integer>();  //短柱子
        Deque<Integer> large = new ArrayDeque<Integer>();  //长柱子
          
        /* divide elements into 2 groups by probability */  
        for(int i=0;i<length;i++){  
            probtemp[i] = prob.get(i)*length;  //将柱子等比放大
            if(probtemp[i]<1.0) {
                small.add(i);
            } else {
                large.add(i);
            }
        }  
        //循环将长柱子截断并入短柱子
        while(!small.isEmpty() && !large.isEmpty()){
            int less = small.pop();  //取出一个短柱子
            int more = large.pop();  //取出一个长柱子
            probability[less] = probtemp[less];  //短柱子站位
            alias[less] = more;  //记录短柱子并入的截断长柱子
            /*
             * 将下标为more的长柱子截断（截断部分补齐到短柱子，此时得到一根长度为1的组合柱子如原柱子下标为less，那么现在下标为less的柱子上接的是下标为more的柱子。）
             * 同时原less的柱子补齐了，从需要处理的柱子中去掉
             */
            probtemp[more] = probtemp[more]-(1.0-probability[less]);  
            if(probtemp[more]<1.0)  //如果截断后剩余为长还是短
            {
                small.add(more);
            } else {
                large.add(more);
            }
        }  
        
        //最后剩下的未补齐柱子一定是原来一种颜色的柱子而不是组合柱子，视为完整
        while (!small.isEmpty()) {
            probability[small.pop()] = 1.0;
        }
        while (!large.isEmpty()) {
            probability[large.pop()] = 1.0;
        }
    }
 
    /**
     * 获取根据概率组合获取概率集合下标值
     * @return
     * <AUTHOR>
     * @date 2016年7月19日
     */
    public int next() {
        /* 扔骰子到柱子 */
        int column = random.nextInt(length);
        /* 随机柱子中的颜色 */
        boolean coinToss = random.nextDouble() < probability[column];
        return coinToss ? column : alias[column];
    }
 
//    public static void main(String[] args) {
//    	int[] pnum = {0,0,0,0,0,0,0};
//    	// 概率集合
//    	List<Double> probabilityList = Lists.newArrayList();
//    	// 临时概率集合
//    	List<Double> tempProbList = Lists.newArrayList(0.00001,0.0001,0.0025,0.005,0.005,0.05,0.95);
//    	// 初始化奖品信息
//    	List<String> prizeList = Lists.newArrayList();
//    	prizeList.add("苹果6 plus(64G)");
//    	prizeList.add("小米手机5");
//    	prizeList.add("保温杯");
//    	prizeList.add("50人民币");
//    	prizeList.add("龙猫可爱暖手枕");
//    	prizeList.add("10人民币");
//    	prizeList.add("188火星券");
//    	// 中奖总概率
//    	Double prizeprobabilitySum = 0.0D;
//        for(int i = 0; i < (tempProbList.size() - 1); i++){
//        		Double value = tempProbList.get(i);
//            	probabilityList.add(value);
//            	prizeprobabilitySum += value;
//        }
//
//        if(1.0 > prizeprobabilitySum){
//			// 这个是火星券的中奖概率
//			probabilityList.add(1.0 - prizeprobabilitySum);
//		}
//
//        for(int i=0; i<7; i++){
//        	String prizeName = prizeList.get(i);
//        	Double p = probabilityList.get(i);
//        	System.out.println("奖品：【"+prizeName+"】的P"+(i+1)+"概率为：" + p +"%");
//        }
//        System.out.println("\n");
//        AliasMethod method = new AliasMethod(probabilityList);
//        for(int i=0; i<1000000 ; i++){
//        	int p = method.next();
//        	pnum[p] = pnum[p]+1;
//        }
//        for(int i=0; i<7; i++){
//        	String prizeName = prizeList.get(i);
//        	System.out.println("奖品：【"+prizeName+"】的概率为："+pnum[i]+"/1000000");
//        }
//    }
}

