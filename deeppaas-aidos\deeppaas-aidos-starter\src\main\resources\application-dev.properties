spring.datasource.jdbc-url=************************************************************************************************************************************
spring.datasource.username=kubao
spring.datasource.password=FgjC7DJdxJ7h1zkeo
spring.datasource.hikari.connectionInitSql=SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.schema=classpath:schema-mysql.sql
spring.datasource.initialization-mode=always

#\u9ED8\u8BA4\u6570\u636E\u5E93\u4FE1\u606F
dp.datasource.db-type=MYSQL
dp.datasource.default-schema=kubao_aidos_dev

#\u6587\u4EF6\u7CFB\u7EDF
dp.file.groups=

