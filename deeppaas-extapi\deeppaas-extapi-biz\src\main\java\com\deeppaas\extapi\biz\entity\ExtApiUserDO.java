package com.deeppaas.extapi.biz.entity;

import com.deeppaas.common.data.BaseUuidDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 第三方服务实体
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Data
@Table("ext_api_user")
@EqualsAndHashCode(callSuper = true)
public class ExtApiUserDO extends BaseUuidDO {
    /**
     * 用户中文名
     */
    @Column("name")
    private String name;

    /**
     * 是否可用
     * 1=可用、0=不可用
     */
    @Column("usable")
    private Integer usable;

    /**
     * 描述
     */
    @Column("remark")
    private String remark;

    /**
     * 第三方提供接口签权方式
     * @com.deeppaas.extapi.biz.enums.ApiAuthType
     */
    @Column("user_auth_type")
    private String userAuthType;

    /**
     * 第三方调用DP平台时的签权方式
     * @com.deeppaas.extapi.biz.enums.ApiAuthType
     * 值提供IP和SECRET
     */
    @Column("call_auth_type")
    private String callAuthType;

    /**
     * 第三方提供接口签权所需用户授权信息
     */
    @Column("user_auth_info")
    private String userAuthInfo;

    /**
     * 第三方调用DP平台时的签权所需秘钥
     * IP：记录鉴权用ip地址 多个ip用","分隔 不填表示不限制
     * SECRET：记录鉴权秘钥
     */
    @Column("call_auth_info")
    private String callAuthInfo;

}
