package com.deeppaas.common.helper;

import java.net.InetAddress;

/**
 * ID生成器
 * <AUTHOR>
 * @date 2022/6/9
 */
public class UidGenerator {
    /**
     * 截止开始时间(2022-01-01 00:00:00,不可修改)
     */
    private static final long startTimeMill = 1640966400000l;
    /**
     * 实例IP哈希
     */
    private static final int instanceIp;

    /**
     * 实例加载时间
     */
    private static final long instanceTimeMill = System.currentTimeMillis();
    /**
     * 当前实例标识
     */
    private static final String instanceKey;

    /**
     * 循环计数器
     */
    private static short counter = (short) 0;


    static {
        int ipHash;
        try {
            ipHash = InetAddress.getLocalHost().hashCode();
        } catch (Exception e) {
            ipHash = 0;
        }
        instanceIp = ipHash;
        int instanceHash = (instanceTimeMill+""+instanceIp).hashCode();
        String formatted = Integer.toUnsignedString(instanceHash, 36);
        StringBuilder instanceBuilder = new StringBuilder( "0000000" );
        instanceBuilder.replace( 7 - formatted.length(), 7, formatted );
        instanceKey = instanceBuilder.toString();
    }

    /**
     * 生成下一个ID
     * @return
     */
    public static String nextId() {
        StringBuilder idBuilder = new StringBuilder();
        idBuilder.append(getTimePart()).append(getCountPart()).append(instanceKey);
        return idBuilder.toString();
    }

    /**
     * 时间戳部分，8位字符串，将当前毫秒时间戳减[截止开始时间]后进行36进制序取值，8位36进制保障89年不重复
     * @return
     */
    private static String getTimePart() {
        long timeMill = System.currentTimeMillis()-startTimeMill;
        String time36 = Long.toUnsignedString(timeMill, 36);
        if(time36.length()>8){
            time36 = time36.substring(time36.length()-8);
        }
        StringBuilder buf = new StringBuilder( "00000000" );
        buf.replace( 8 - time36.length(), 8, time36 );
        return buf.toString();
    }

    /**
     * 获取计数器部分,3位
     * @return
     */
    private static String getCountPart() {
        short count = getCount();
        String formatted = Integer.toUnsignedString(count, 36);
        StringBuilder buf = new StringBuilder( "000" );
        buf.replace( 3 - formatted.length(), 3, formatted );
        return buf.toString();
    }

    /**
     * 获取循环计数器，最大值Short.MAX_VALUE，到最大之后重新计数0，1毫秒内如果超过一次循环则会出现重复ID
     */
    protected static short getCount() {
        synchronized(UidGenerator.class) {
            if (counter<0) counter=0;
            return counter++;
        }
    }

//    public static void main(String[] args) throws Exception {
//        for(int i=0;i<100;i++){
//            System.out.println(nextId());
//        }
//        System.out.println("=================================================");
//    }
}
