package com.deeppaas.rule.biz.convert;

import com.deeppaas.datastore.common.DataStoreCondition;
import com.deeppaas.datastore.common.DataStoreConditionL;
import com.deeppaas.datastore.common.DataStoreConditionS;
import com.deeppaas.datastore.common.emnus.DataStoreOperator;
import com.deeppaas.rule.biz.RuleContext;
import com.deeppaas.rule.biz.model.RConditionModel;

import java.util.List;

/**
 * 条件转换器
 * <AUTHOR>
 * @date 2022/3/5
 */
public class ConditionConvert {

    /**
     * 构建数据模型条件
     * @param rConditionModel
     * @param ruleContext
     * @return
     */
    public static DataStoreCondition buildDataStoreCondition(RConditionModel rConditionModel, RuleContext ruleContext) {
        if(rConditionModel instanceof RConditionModel.Simple cs){
            DataStoreCondition condition = DataStoreConditionS.of(
                    cs.getFieldName(), cs.getDataBind().getBindData(ruleContext), DataStoreOperator.valueOf(cs.getOperator())
            );
            return condition;
        }
        if(rConditionModel instanceof RConditionModel.Logic cl){
            List<DataStoreCondition> children = cl.getConditions().stream().map(c -> buildDataStoreCondition(c, ruleContext)).toList();
            DataStoreCondition condition = new DataStoreConditionL(
                    children.toArray(new DataStoreCondition[children.size()]), DataStoreOperator.valueOf(cl.getOperator())
            );
            return condition;
        }
        return null;
    }
}
