//package com.deeppaas.flow.biz.impl;
//
//import com.deeppaas.flow.biz.FlowConst;
//import com.deeppaas.flow.biz.cache.FlowCacheManager;
//import com.deeppaas.flow.biz.dao.FlowDefineDao;
//import com.deeppaas.flow.biz.parser.FlowDefineParser;
//import com.deeppaas.flow.biz.entity.FlowDefineDO;
//import com.deeppaas.flow.biz.model.FlowDefineModel;
//import org.springframework.cache.Cache;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//@Component
//@Transactional(readOnly = true)
//public class FlowDefineModelManager {
//	private final FlowDefineDao flowDefineDao;
//	private final FlowCacheManager flowCacheManager;
//
//	public FlowDefineModelManager(FlowDefineDao flowDefineDao, FlowCacheManager flowCacheManager) {
//		this.flowDefineDao = flowDefineDao;
//		this.flowCacheManager = flowCacheManager;
//	}
//
//	public FlowDefineModel getById(String defineId) {
//		FlowDefineDO flowDefineDO = flowDefineDao.getById(defineId);
//		return loadModel(flowDefineDO);
//	}
//
//	public FlowDefineModel getByCode(String code){
//		FlowDefineDO flowDefineDO = flowDefineDao.findByCodeAndState(code, FlowConst.DEFINE_STATE_ISSUE);
//		return loadModel(flowDefineDO);
//	}
//
//	public FlowDefineModel getByCode(String code, Integer version){
//		FlowDefineDO flowDefineDO = flowDefineDao.findByCodeAndVersion(code, version);
//		return loadModel(flowDefineDO);
//	}
//
//	public FlowDefineModel reload(FlowDefineDO flowDefineDO){
//		if(flowDefineDO!=null){
//			FlowDefineModel defineModel = FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
//			Cache cache = flowCacheManager.getDefineCache();
//			if(cache!=null){
//				String cacheKey = flowDefineDO.getCode()+"-"+flowDefineDO.getVersion();
//				defineModel = FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
//				cache.put(cacheKey, defineModel);
//			}
//			return defineModel;
//		}
//		return null;
//	}
//
//	public FlowDefineModel loadModel(FlowDefineDO flowDefineDO) {
//		if(flowDefineDO==null){
//			return null;
//		}
//		FlowDefineModel defineModel = null;
//		Cache cache = flowCacheManager.getDefineCache();
//		if(cache!=null){
//			String cacheKey = flowDefineDO.getCode()+"-"+flowDefineDO.getVersion();
//			defineModel = cache.get(cacheKey, FlowDefineModel.class);
//			if(defineModel==null){
//				defineModel = FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
//				cache.put(cacheKey, defineModel);
//			}
//			return defineModel;
//		}
//		return FlowDefineParser.parse(flowDefineDO, flowDefineDO.getDefJson());
//	}
//
//}
