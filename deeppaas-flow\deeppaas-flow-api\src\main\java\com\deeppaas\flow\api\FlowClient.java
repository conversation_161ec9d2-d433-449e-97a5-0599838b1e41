package com.deeppaas.flow.api;

import com.deeppaas.flow.api.model.ExecuteResult;
import com.deeppaas.flow.api.model.PastNodeInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/21
 */
public interface FlowClient {
    /**
     * @Title: startInstance
     * @Description: 创建流程实例
     * @param defineId
     * @param orderId
     * @param userId
     * @return
     * <AUTHOR>
     */
    Long startInstance(Long defineId, String orderId, String userId);

    /**
     * 模拟流转
     * @param instanceId
     * @param userId
     * @return
     */
    ExecuteResult mockForwardExecute(Long instanceId, String userId);
    /**
     * @Title: forwardExecute
     * @Description: 流程向前流转
     * @param instanceId
     * @param userId
     * @return
     * <AUTHOR>
     */
    ExecuteResult forwardExecute(Long instanceId, String userId);

    /**
     * @Title: forwardExecuteCheck
     * @Description: 审批检查，是否需要指派，是否需要必填审批意见
     * @param taskId
     * @param userId
     * @return
     * <AUTHOR>
     */
    ExecuteResult forwardExecuteCheck(Long taskId, String userId);
    /**
     * @Title: forwardExecute
     * @Description: 审批任务并向前流转
     * @param taskId
     * @param userId
     * @param comment
     * @return
     * <AUTHOR>
     */
    ExecuteResult forwardExecute(Long taskId, String userId, String comment);

    /**
     * @Title: closeInstance
     * @Description: 关闭流程实例
     * @param instanceId
     * @param userId
     * @param comment
     * @return
     * <AUTHOR>
     */
    ExecuteResult closeInstance(Long instanceId, String userId, String comment);

    /**
     * @Description: 获取可驳回节点
     * @param taskId
     * @param userId
     * @return java.util.List<com.lh.oa.flow.define.entity.FlowNode>
     * <AUTHOR>
     */
    List<PastNodeInfo> getBackNodes(Long taskId, String userId);

    /**
     * @Description: 驳回
     * @param taskId
     * @param backNodeCode
     * @param userId
     * @param comment
     * @return com.lh.oa.flow.ExecuteResult
     * <AUTHOR>
     */
    ExecuteResult backwardExecute(Long taskId, String backNodeCode, String userId, String comment);

    /**
     * @Description: 撤回
     * @param instanceId
     * @param userId
     * @param comment
     * @return com.lh.oa.flow.ExecuteResult
     * <AUTHOR>
     */
    ExecuteResult taskTakeBackExecute(Long instanceId, Long taskId,  String userId, String comment);

}
